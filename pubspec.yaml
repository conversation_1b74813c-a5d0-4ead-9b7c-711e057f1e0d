name: build_mate
description: "A new Flutter project."
publish_to: "none"
version: 0.1.0

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  freezed: ^3.0.4
  freezed_annotation: ^3.0.0
  permission_handler: ^11.0.1
  image_picker: ^1.0.4
  file_picker: ^8.1.4
  shimmer: ^3.0.0

  # Keep your other dependencies as they are
  path_provider: ^2.1.1
  path: ^1.8.3
  hooks_riverpod: ^2.6.1
  flutter_svg: ^2.1.0
  share_plus: ^11.0.0
  intl: ^0.20.2
  go_router: ^15.1.1
  timeago: ^3.2.2
  google_sign_in: ^6.3.0
  change_app_package_name: ^1.5.0
  supabase_flutter: ^2.9.0
  shared_preferences: ^2.5.3
  geolocator: ^14.0.0
  geocoding: ^3.0.0
  google_maps_flutter: ^2.12.2
  onesignal_flutter: ^5.3.3
  firebase_core: ^3.13.0
  cloud_firestore: ^5.6.7
  flutter_staggered_animations: ^1.1.1
  lottie: ^3.3.1
  google_sign_in_web: ^0.12.4+4
  multiple_result: ^5.2.0
  pretty_dio_logger: ^1.4.0
  dio: ^5.8.0+1
  crypto: ^3.0.6
  bcrypt: ^1.1.3 # Updated package for password hashing
  cached_network_image: ^3.4.1
  image: ^4.5.4
  url_launcher: ^6.3.2

  # Remove the conflicting packages
  # google_maps_webservice: ^0.0.20-nullsafety.5
  # flutter_google_places: ^0.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.4

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/svg/
    - assets/json/
    - assets/map_styles/

  fonts:
    - family: NebulaSans
      fonts:
        - asset: assets/fonts/NebulaSans-Black.ttf
          # weight: 700
        - asset: assets/fonts/NebulaSans-Bold.ttf
        - asset: assets/fonts/NebulaSans-Light.ttf
        - asset: assets/fonts/NebulaSans-Medium.ttf
        - asset: assets/fonts/NebulaSans-Semibold.ttf
