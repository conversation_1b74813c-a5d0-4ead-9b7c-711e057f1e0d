import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductsNotifier extends StateNotifier<List<ProductModel>> {
  ProductsNotifier() : super([]);
  
  void loadProducts() {
    // Simulate loading products
    state = List.generate(4, (index) => ProductModel(
      imageUrl: _getProductImage(index),
      title: 'Cotton Brooklynn',
      subtitle: 'Denim Jacket',
      price: '\$33.00',
      isPremium: true,
    ));
  }

  static String _getProductImage(int index) {
    switch (index) {
      case 0: return 'assets/images/grid_1.jpeg';
      case 1: return 'assets/images/grid_2.jpeg';
      case 2: return 'assets/images/grid_3.jpg';
      case 3: return 'assets/images/grid_4.jpeg';
      default: return 'assets/images/grid_5.jpeg';
    }
  }
}

final productsProvider = StateNotifierProvider<ProductsNotifier, List<ProductModel>>((ref) {
  return ProductsNotifier();
});

class ProductModel {
  final String imageUrl;
  final String title;
  final String subtitle;
  final String price;
  final bool isPremium;

  ProductModel({
    required this.imageUrl,
    required this.title,
    required this.subtitle,
    required this.price,
    required this.isPremium,
  });
}
