import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:dio/dio.dart';

class CustomMapMarkers {
  /// Cached version with Dio
  static final Map<String, BitmapDescriptor> _markerCache = {};

  /// Creates a custom marker bitmap from a network image using Dio
  static Future<BitmapDescriptor> createCustomMarkerBitmap(
    String imageUrl, {
    String? label,
    Color labelColor = Colors.white,
    Color backgroundColor = Colors.blue,
    double size = 150.0,
    bool addBorder = true,
    Color borderColor = Colors.white,
    double borderWidth = 5.0,
    Dio? dioInstance,
  }) async {
    // Create Dio instance if not provided
    final dio = dioInstance ?? Dio();

    try {
      // Load the network image using Dio
      final Response response = await dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      final Uint8List imageData = response.data;
      final ui.Codec codec = await ui.instantiateImageCodec(imageData);
      final ui.FrameInfo frame = await codec.getNextFrame();
      final ui.Image image = frame.image;
      // Create a marker with the following steps:
      // 1. Draw background/border
      // 2. Draw inner circle
      // 3. Draw image within the circle
      final double radius = size / 2;
      final pictureRecorder = ui.PictureRecorder();
      final canvas = Canvas(pictureRecorder);
      final paint = Paint()..isAntiAlias = true;

      // First draw the border if needed (the outer circle)
      if (addBorder) {
        paint.color = borderColor;
        canvas.drawCircle(Offset(radius, radius), radius, paint);
      }

      // Draw the inner background circle
      final innerRadius = addBorder ? radius - borderWidth : radius;
      paint.color = backgroundColor;
      canvas.drawCircle(Offset(radius, radius), innerRadius, paint);

      // Save canvas state before clipping
      canvas.save();

      // Create a clipping path for the image
      final Path clipPath =
          Path()..addOval(
            Rect.fromCircle(
              center: Offset(radius, radius),
              radius: innerRadius,
            ),
          );
      canvas.clipPath(clipPath);

      // Calculate the scale to cover the entire circle
      double scaledWidth, scaledHeight;

      // Calculate scales for both dimensions
      final double scaleX = (innerRadius * 2) / image.width;
      final double scaleY = (innerRadius * 2) / image.height;

      // Use the larger scale to ensure full circle coverage
      final double scale = math.max(scaleX, scaleY);

      // Apply the scale to both dimensions
      scaledWidth = image.width * scale;
      scaledHeight = image.height * scale;

      // Position to center the image within the circle
      final double left = radius - (scaledWidth / 2);
      final double top = radius - (scaledHeight / 2);

      // Draw the image centered and scaled to cover the entire circle
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        Rect.fromLTWH(left, top, scaledWidth, scaledHeight),
        Paint(),
      );

      // Restore canvas state after drawing the image
      canvas.restore();
      // final double top = radius - (scaledHeight / 2);

      // Draw the image centered and scaled properly
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        Rect.fromLTWH(left, top, scaledWidth, scaledHeight),
        paint,
      );

      if (label != null && label.isNotEmpty) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: label,
            style: TextStyle(
              color: labelColor,
              fontSize: innerRadius * 0.4,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            radius - textPainter.width / 2,
            radius - textPainter.height / 2,
          ),
        );
      }

      final ui.Picture pic = pictureRecorder.endRecording();
      final ui.Image markerImage = await pic.toImage(
        size.toInt(),
        size.toInt(),
      );
      final ByteData? byteData = await markerImage.toByteData(
        format: ui.ImageByteFormat.png,
      );
      final Uint8List byteList = byteData!.buffer.asUint8List();

      return BitmapDescriptor.bytes(byteList);
    } on DioException catch (e) {
      // Handle Dio-specific errors
      debugPrint('Error fetching image with Dio: ${e.message}');
      // Return a default marker if the network image fails to load
      return BitmapDescriptor.defaultMarker;
    } catch (e) {
      // Handle other errors
      debugPrint('Error creating custom marker: $e');
      return BitmapDescriptor.defaultMarker;
    }
  }

  static Future<BitmapDescriptor> getCachedMarker(
    String imageUrl, {
    String? label,
    Color labelColor = Colors.white,
    Color backgroundColor = Colors.orange,
    double size = 80.0,
    bool addBorder = true,
    Color borderColor = Colors.white,
    double borderWidth = 1.0,
    Dio? dioInstance,
  }) async {
    final cacheKey = '$imageUrl-$label-$size';

    if (!_markerCache.containsKey(cacheKey)) {
      final marker = await createCustomMarkerBitmap(
        imageUrl,
        label: label,
        labelColor: labelColor,
        backgroundColor: backgroundColor,
        size: size,
        addBorder: addBorder,
        borderColor: borderColor,
        borderWidth: borderWidth,
        dioInstance: dioInstance,
      );
      _markerCache[cacheKey] = marker;
    }

    return _markerCache[cacheKey]!;
  }
}
