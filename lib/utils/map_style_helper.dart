import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MapStyleHelper {
  static String? _darkMapStyle;
  
  /// Load the dark map style from assets
  static Future<String> getDarkMapStyle() async {
    if (_darkMapStyle != null) {
      return _darkMapStyle!;
    }
    
    try {
      final String styleString = await rootBundle.loadString('assets/map_styles/dark_map_style.json');
      _darkMapStyle = styleString;
      return styleString;
    } catch (e) {
      debugPrint('Error loading dark map style: $e');
      // Return a basic dark style as fallback
      return _getBasicDarkStyle();
    }
  }
  
  /// Get map style based on theme mode
  static Future<String?> getMapStyleForTheme(ThemeMode themeMode) async {
    switch (themeMode) {
      case ThemeMode.dark:
        return await getDarkMapStyle();
      case ThemeMode.light:
        return null; // Use default Google Maps style
      case ThemeMode.system:
        // Check system brightness
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        if (brightness == Brightness.dark) {
          return await getDarkMapStyle();
        }
        return null;
    }
  }
  
  /// Basic dark style as fallback
  static String _getBasicDarkStyle() {
    return jsonEncode([
      {
        "elementType": "geometry",
        "stylers": [
          {"color": "#212121"}
        ]
      },
      {
        "elementType": "labels.text.fill",
        "stylers": [
          {"color": "#757575"}
        ]
      },
      {
        "elementType": "labels.text.stroke",
        "stylers": [
          {"color": "#212121"}
        ]
      },
      {
        "featureType": "road",
        "elementType": "geometry.fill",
        "stylers": [
          {"color": "#2c2c2c"}
        ]
      },
      {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [
          {"color": "#000000"}
        ]
      }
    ]);
  }
  
  /// Clear cached styles (useful for testing or memory management)
  static void clearCache() {
    _darkMapStyle = null;
  }
}
