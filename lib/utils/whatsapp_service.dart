import 'package:url_launcher/url_launcher.dart';

class WhatsAppUtils {
  static Future<void> openWhatsApp({
    required String phoneNumber,
    required String message,
  }) async {
    // Clean and format the phone number to Zimbabwe format (+263)
    String cleanNumber = _formatPhoneNumber(phoneNumber);

    final Uri url = Uri.parse(
      'https://wa.me/$cleanNumber?text=${Uri.encodeComponent(message)}',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      throw Exception('Could not launch WhatsApp');
    }
  }

  /// Formats phone number to Zimbabwe international format (+263)
  static String _formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters except +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Remove any existing + at the beginning
    if (cleanNumber.startsWith('+')) {
      cleanNumber = cleanNumber.substring(1);
    }

    // Handle Zimbabwe local numbers starting with 0
    if (cleanNumber.startsWith('0')) {
      cleanNumber = '263${cleanNumber.substring(1)}';
    }

    // Handle numbers that already have Zimbabwe country code
    if (cleanNumber.startsWith('263')) {
      // Already has country code, just ensure it's clean
      cleanNumber = cleanNumber;
    } else if (cleanNumber.length >= 9 && !cleanNumber.startsWith('263')) {
      // Assume it's a local number without leading 0, add country code
      cleanNumber = '263$cleanNumber';
    }

    // Always return with + prefix
    return '+$cleanNumber';
  }
}
