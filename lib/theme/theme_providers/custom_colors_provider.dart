import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final customColorsProvider = Provider<CustomColors>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  final brightness =
      WidgetsBinding.instance.platformDispatcher.platformBrightness;

  // Handle system theme mode
  final isDark =
      themeMode == ThemeMode.dark ||
      (themeMode == ThemeMode.system && brightness == Brightness.dark);

  return isDark ? const CustomColors.dark() : const CustomColors.light();
});

class CustomColors {
  final Color successColor;
  final Color warningColor;
  final Color errorColor;
  final Color infoColor;
  final Color surfaceVariant;
  final Color primaryContainer;
  final Color secondaryContainer;
  final Color textPrimaryColor;

  const CustomColors.light()
    : successColor = const Color(0xFF4CAF50),
      warningColor = const Color(0xFFFF9800),
      errorColor = const Color(0xFFF44336),
      infoColor = const Color(0xFF2196F3),
      surfaceVariant = const Color(0xFFF5F5F5),
      primaryContainer = const Color(0xFFE3F2FD),
      secondaryContainer = const Color(0xFFF3E5F5),
      textPrimaryColor = const Color(0xFF232023);

  const CustomColors.dark()
    : successColor = const Color(0xFF66BB6A),
      warningColor = const Color(0xFFFFB74D),
      errorColor = const Color(0xFFEF5350),
      infoColor = const Color(0xFF42A5F5),
      surfaceVariant = const Color(0xFF2A2A2A),
      primaryContainer = const Color(0xFF1A237E),
      secondaryContainer = const Color(0xFF4A148C),
      textPrimaryColor = const Color(0xFFFFFFFF);
}
