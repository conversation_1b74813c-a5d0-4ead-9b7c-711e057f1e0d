// ignore_for_file: constant_identifier_names, non_constant_identifier_names

import 'package:flutter/material.dart';

const String NEBULA_SANS_FONT_FAMILY = 'NebulaSans';

class MyTypography {
  // Private constructor to prevent instantiation
  MyTypography._();

  static const TextStyle Regular = TextStyle(
    fontFamily: NEBULA_SANS_FONT_FAMILY,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle Bold = TextStyle(
    fontFamily: NEBULA_SANS_FONT_FAMILY,
    fontWeight: FontWeight.w700,
  );

  static const TextStyle Light = TextStyle(
    fontFamily: NEBULA_SANS_FONT_FAMILY,
    fontWeight: FontWeight.w300,
  );

  static const TextStyle Medium = TextStyle(
    fontFamily: NEBULA_SANS_FONT_FAMILY,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle SemiBold = TextStyle(
    fontFamily: NEBULA_SANS_FONT_FAMILY,
    fontWeight: FontWeight.w600,
  );
}
