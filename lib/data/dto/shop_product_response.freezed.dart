// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shop_product_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ShopProductResponse {

 int get id;@JsonKey(name: 'branch_id') int? get branchId;@JsonKey(name: 'hardware_sub_category_id') int? get hardwareSubCategoryId; String? get name; String? get brand;@JsonKey(name: 'model_number') String? get modelNumber; String? get description; String? get sku; double? get price;@JsonKey(name: 'current_stock') int? get currentStock;@JsonKey(name: 'min_stock_level') int? get minStockLevel;@JsonKey(name: 'unit_type') String? get unitType;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'hardware_sub_categories') HardwareSubCategory? get hardwareSubCategory;@JsonKey(name: 'product_images') List<ProductImage>? get productImages;
/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShopProductResponseCopyWith<ShopProductResponse> get copyWith => _$ShopProductResponseCopyWithImpl<ShopProductResponse>(this as ShopProductResponse, _$identity);

  /// Serializes this ShopProductResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShopProductResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&(identical(other.hardwareSubCategoryId, hardwareSubCategoryId) || other.hardwareSubCategoryId == hardwareSubCategoryId)&&(identical(other.name, name) || other.name == name)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.modelNumber, modelNumber) || other.modelNumber == modelNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.price, price) || other.price == price)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.unitType, unitType) || other.unitType == unitType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.hardwareSubCategory, hardwareSubCategory) || other.hardwareSubCategory == hardwareSubCategory)&&const DeepCollectionEquality().equals(other.productImages, productImages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchId,hardwareSubCategoryId,name,brand,modelNumber,description,sku,price,currentStock,minStockLevel,unitType,createdAt,updatedAt,hardwareSubCategory,const DeepCollectionEquality().hash(productImages));

@override
String toString() {
  return 'ShopProductResponse(id: $id, branchId: $branchId, hardwareSubCategoryId: $hardwareSubCategoryId, name: $name, brand: $brand, modelNumber: $modelNumber, description: $description, sku: $sku, price: $price, currentStock: $currentStock, minStockLevel: $minStockLevel, unitType: $unitType, createdAt: $createdAt, updatedAt: $updatedAt, hardwareSubCategory: $hardwareSubCategory, productImages: $productImages)';
}


}

/// @nodoc
abstract mixin class $ShopProductResponseCopyWith<$Res>  {
  factory $ShopProductResponseCopyWith(ShopProductResponse value, $Res Function(ShopProductResponse) _then) = _$ShopProductResponseCopyWithImpl;
@useResult
$Res call({
 int id,@JsonKey(name: 'branch_id') int? branchId,@JsonKey(name: 'hardware_sub_category_id') int? hardwareSubCategoryId, String? name, String? brand,@JsonKey(name: 'model_number') String? modelNumber, String? description, String? sku, double? price,@JsonKey(name: 'current_stock') int? currentStock,@JsonKey(name: 'min_stock_level') int? minStockLevel,@JsonKey(name: 'unit_type') String? unitType,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'hardware_sub_categories') HardwareSubCategory? hardwareSubCategory,@JsonKey(name: 'product_images') List<ProductImage>? productImages
});


$HardwareSubCategoryCopyWith<$Res>? get hardwareSubCategory;

}
/// @nodoc
class _$ShopProductResponseCopyWithImpl<$Res>
    implements $ShopProductResponseCopyWith<$Res> {
  _$ShopProductResponseCopyWithImpl(this._self, this._then);

  final ShopProductResponse _self;
  final $Res Function(ShopProductResponse) _then;

/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? branchId = freezed,Object? hardwareSubCategoryId = freezed,Object? name = freezed,Object? brand = freezed,Object? modelNumber = freezed,Object? description = freezed,Object? sku = freezed,Object? price = freezed,Object? currentStock = freezed,Object? minStockLevel = freezed,Object? unitType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? hardwareSubCategory = freezed,Object? productImages = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,branchId: freezed == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int?,hardwareSubCategoryId: freezed == hardwareSubCategoryId ? _self.hardwareSubCategoryId : hardwareSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,modelNumber: freezed == modelNumber ? _self.modelNumber : modelNumber // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currentStock: freezed == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int?,minStockLevel: freezed == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int?,unitType: freezed == unitType ? _self.unitType : unitType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,hardwareSubCategory: freezed == hardwareSubCategory ? _self.hardwareSubCategory : hardwareSubCategory // ignore: cast_nullable_to_non_nullable
as HardwareSubCategory?,productImages: freezed == productImages ? _self.productImages : productImages // ignore: cast_nullable_to_non_nullable
as List<ProductImage>?,
  ));
}
/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryCopyWith<$Res>? get hardwareSubCategory {
    if (_self.hardwareSubCategory == null) {
    return null;
  }

  return $HardwareSubCategoryCopyWith<$Res>(_self.hardwareSubCategory!, (value) {
    return _then(_self.copyWith(hardwareSubCategory: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ShopProductResponse implements ShopProductResponse {
  const _ShopProductResponse({required this.id, @JsonKey(name: 'branch_id') this.branchId, @JsonKey(name: 'hardware_sub_category_id') this.hardwareSubCategoryId, this.name, this.brand, @JsonKey(name: 'model_number') this.modelNumber, this.description, this.sku, this.price, @JsonKey(name: 'current_stock') this.currentStock, @JsonKey(name: 'min_stock_level') this.minStockLevel, @JsonKey(name: 'unit_type') this.unitType, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'hardware_sub_categories') this.hardwareSubCategory, @JsonKey(name: 'product_images') final  List<ProductImage>? productImages}): _productImages = productImages;
  factory _ShopProductResponse.fromJson(Map<String, dynamic> json) => _$ShopProductResponseFromJson(json);

@override final  int id;
@override@JsonKey(name: 'branch_id') final  int? branchId;
@override@JsonKey(name: 'hardware_sub_category_id') final  int? hardwareSubCategoryId;
@override final  String? name;
@override final  String? brand;
@override@JsonKey(name: 'model_number') final  String? modelNumber;
@override final  String? description;
@override final  String? sku;
@override final  double? price;
@override@JsonKey(name: 'current_stock') final  int? currentStock;
@override@JsonKey(name: 'min_stock_level') final  int? minStockLevel;
@override@JsonKey(name: 'unit_type') final  String? unitType;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'hardware_sub_categories') final  HardwareSubCategory? hardwareSubCategory;
 final  List<ProductImage>? _productImages;
@override@JsonKey(name: 'product_images') List<ProductImage>? get productImages {
  final value = _productImages;
  if (value == null) return null;
  if (_productImages is EqualUnmodifiableListView) return _productImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShopProductResponseCopyWith<_ShopProductResponse> get copyWith => __$ShopProductResponseCopyWithImpl<_ShopProductResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShopProductResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShopProductResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&(identical(other.hardwareSubCategoryId, hardwareSubCategoryId) || other.hardwareSubCategoryId == hardwareSubCategoryId)&&(identical(other.name, name) || other.name == name)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.modelNumber, modelNumber) || other.modelNumber == modelNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.price, price) || other.price == price)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.unitType, unitType) || other.unitType == unitType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.hardwareSubCategory, hardwareSubCategory) || other.hardwareSubCategory == hardwareSubCategory)&&const DeepCollectionEquality().equals(other._productImages, _productImages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchId,hardwareSubCategoryId,name,brand,modelNumber,description,sku,price,currentStock,minStockLevel,unitType,createdAt,updatedAt,hardwareSubCategory,const DeepCollectionEquality().hash(_productImages));

@override
String toString() {
  return 'ShopProductResponse(id: $id, branchId: $branchId, hardwareSubCategoryId: $hardwareSubCategoryId, name: $name, brand: $brand, modelNumber: $modelNumber, description: $description, sku: $sku, price: $price, currentStock: $currentStock, minStockLevel: $minStockLevel, unitType: $unitType, createdAt: $createdAt, updatedAt: $updatedAt, hardwareSubCategory: $hardwareSubCategory, productImages: $productImages)';
}


}

/// @nodoc
abstract mixin class _$ShopProductResponseCopyWith<$Res> implements $ShopProductResponseCopyWith<$Res> {
  factory _$ShopProductResponseCopyWith(_ShopProductResponse value, $Res Function(_ShopProductResponse) _then) = __$ShopProductResponseCopyWithImpl;
@override @useResult
$Res call({
 int id,@JsonKey(name: 'branch_id') int? branchId,@JsonKey(name: 'hardware_sub_category_id') int? hardwareSubCategoryId, String? name, String? brand,@JsonKey(name: 'model_number') String? modelNumber, String? description, String? sku, double? price,@JsonKey(name: 'current_stock') int? currentStock,@JsonKey(name: 'min_stock_level') int? minStockLevel,@JsonKey(name: 'unit_type') String? unitType,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'hardware_sub_categories') HardwareSubCategory? hardwareSubCategory,@JsonKey(name: 'product_images') List<ProductImage>? productImages
});


@override $HardwareSubCategoryCopyWith<$Res>? get hardwareSubCategory;

}
/// @nodoc
class __$ShopProductResponseCopyWithImpl<$Res>
    implements _$ShopProductResponseCopyWith<$Res> {
  __$ShopProductResponseCopyWithImpl(this._self, this._then);

  final _ShopProductResponse _self;
  final $Res Function(_ShopProductResponse) _then;

/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? branchId = freezed,Object? hardwareSubCategoryId = freezed,Object? name = freezed,Object? brand = freezed,Object? modelNumber = freezed,Object? description = freezed,Object? sku = freezed,Object? price = freezed,Object? currentStock = freezed,Object? minStockLevel = freezed,Object? unitType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? hardwareSubCategory = freezed,Object? productImages = freezed,}) {
  return _then(_ShopProductResponse(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,branchId: freezed == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int?,hardwareSubCategoryId: freezed == hardwareSubCategoryId ? _self.hardwareSubCategoryId : hardwareSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,modelNumber: freezed == modelNumber ? _self.modelNumber : modelNumber // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currentStock: freezed == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int?,minStockLevel: freezed == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int?,unitType: freezed == unitType ? _self.unitType : unitType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,hardwareSubCategory: freezed == hardwareSubCategory ? _self.hardwareSubCategory : hardwareSubCategory // ignore: cast_nullable_to_non_nullable
as HardwareSubCategory?,productImages: freezed == productImages ? _self._productImages : productImages // ignore: cast_nullable_to_non_nullable
as List<ProductImage>?,
  ));
}

/// Create a copy of ShopProductResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryCopyWith<$Res>? get hardwareSubCategory {
    if (_self.hardwareSubCategory == null) {
    return null;
  }

  return $HardwareSubCategoryCopyWith<$Res>(_self.hardwareSubCategory!, (value) {
    return _then(_self.copyWith(hardwareSubCategory: value));
  });
}
}


/// @nodoc
mixin _$HardwareSubCategory {

 String? get name;
/// Create a copy of HardwareSubCategory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareSubCategoryCopyWith<HardwareSubCategory> get copyWith => _$HardwareSubCategoryCopyWithImpl<HardwareSubCategory>(this as HardwareSubCategory, _$identity);

  /// Serializes this HardwareSubCategory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareSubCategory&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'HardwareSubCategory(name: $name)';
}


}

/// @nodoc
abstract mixin class $HardwareSubCategoryCopyWith<$Res>  {
  factory $HardwareSubCategoryCopyWith(HardwareSubCategory value, $Res Function(HardwareSubCategory) _then) = _$HardwareSubCategoryCopyWithImpl;
@useResult
$Res call({
 String? name
});




}
/// @nodoc
class _$HardwareSubCategoryCopyWithImpl<$Res>
    implements $HardwareSubCategoryCopyWith<$Res> {
  _$HardwareSubCategoryCopyWithImpl(this._self, this._then);

  final HardwareSubCategory _self;
  final $Res Function(HardwareSubCategory) _then;

/// Create a copy of HardwareSubCategory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareSubCategory implements HardwareSubCategory {
  const _HardwareSubCategory({this.name});
  factory _HardwareSubCategory.fromJson(Map<String, dynamic> json) => _$HardwareSubCategoryFromJson(json);

@override final  String? name;

/// Create a copy of HardwareSubCategory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareSubCategoryCopyWith<_HardwareSubCategory> get copyWith => __$HardwareSubCategoryCopyWithImpl<_HardwareSubCategory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareSubCategoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareSubCategory&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'HardwareSubCategory(name: $name)';
}


}

/// @nodoc
abstract mixin class _$HardwareSubCategoryCopyWith<$Res> implements $HardwareSubCategoryCopyWith<$Res> {
  factory _$HardwareSubCategoryCopyWith(_HardwareSubCategory value, $Res Function(_HardwareSubCategory) _then) = __$HardwareSubCategoryCopyWithImpl;
@override @useResult
$Res call({
 String? name
});




}
/// @nodoc
class __$HardwareSubCategoryCopyWithImpl<$Res>
    implements _$HardwareSubCategoryCopyWith<$Res> {
  __$HardwareSubCategoryCopyWithImpl(this._self, this._then);

  final _HardwareSubCategory _self;
  final $Res Function(_HardwareSubCategory) _then;

/// Create a copy of HardwareSubCategory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,}) {
  return _then(_HardwareSubCategory(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ProductImage {

 int? get id;@JsonKey(name: 'image_url') String? get imageUrl;
/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductImageCopyWith<ProductImage> get copyWith => _$ProductImageCopyWithImpl<ProductImage>(this as ProductImage, _$identity);

  /// Serializes this ProductImage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ProductImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $ProductImageCopyWith<$Res>  {
  factory $ProductImageCopyWith(ProductImage value, $Res Function(ProductImage) _then) = _$ProductImageCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class _$ProductImageCopyWithImpl<$Res>
    implements $ProductImageCopyWith<$Res> {
  _$ProductImageCopyWithImpl(this._self, this._then);

  final ProductImage _self;
  final $Res Function(ProductImage) _then;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductImage implements ProductImage {
  const _ProductImage({this.id, @JsonKey(name: 'image_url') this.imageUrl});
  factory _ProductImage.fromJson(Map<String, dynamic> json) => _$ProductImageFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'image_url') final  String? imageUrl;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductImageCopyWith<_ProductImage> get copyWith => __$ProductImageCopyWithImpl<_ProductImage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductImageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ProductImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$ProductImageCopyWith<$Res> implements $ProductImageCopyWith<$Res> {
  factory _$ProductImageCopyWith(_ProductImage value, $Res Function(_ProductImage) _then) = __$ProductImageCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class __$ProductImageCopyWithImpl<$Res>
    implements _$ProductImageCopyWith<$Res> {
  __$ProductImageCopyWithImpl(this._self, this._then);

  final _ProductImage _self;
  final $Res Function(_ProductImage) _then;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_ProductImage(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
