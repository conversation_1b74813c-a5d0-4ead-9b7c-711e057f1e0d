// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_details_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_JobDetailsResponse _$JobDetailsResponseFromJson(Map<String, dynamic> json) =>
    _JobDetailsResponse(
      id: (json['id'] as num?)?.toInt(),
      clientId: (json['client_id'] as num?)?.toInt(),
      serviceId: (json['service_id'] as num?)?.toInt(),
      jobDescription: json['job_description'] as String?,
      budget: (json['budget'] as num?)?.toDouble(),
      status: json['status'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      serviceDate: json['service_date'] as String?,
      postDate: json['post_date'] as String?,
      service:
          json['service'] == null
              ? null
              : ServiceModel.fromJson(json['service'] as Map<String, dynamic>),
      client:
          json['client'] == null
              ? null
              : ClientModel.fromJson(json['client'] as Map<String, dynamic>),
      jobTags:
          (json['job_tags'] as List<dynamic>?)
              ?.map((e) => JobTagModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      jobImages:
          (json['job_images'] as List<dynamic>?)
              ?.map((e) => JobImageModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      bids:
          (json['bids'] as List<dynamic>?)
              ?.map((e) => BidModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$JobDetailsResponseToJson(_JobDetailsResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'client_id': instance.clientId,
      'service_id': instance.serviceId,
      'job_description': instance.jobDescription,
      'budget': instance.budget,
      'status': instance.status,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'service_date': instance.serviceDate,
      'post_date': instance.postDate,
      'service': instance.service,
      'client': instance.client,
      'job_tags': instance.jobTags,
      'job_images': instance.jobImages,
      'bids': instance.bids,
    };

_ServiceModel _$ServiceModelFromJson(Map<String, dynamic> json) =>
    _ServiceModel(name: json['name'] as String?);

Map<String, dynamic> _$ServiceModelToJson(_ServiceModel instance) =>
    <String, dynamic>{'name': instance.name};

_ClientModel _$ClientModelFromJson(Map<String, dynamic> json) => _ClientModel(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
  avatar: json['avatar'] as String?,
);

Map<String, dynamic> _$ClientModelToJson(_ClientModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatar': instance.avatar,
    };

_JobTagModel _$JobTagModelFromJson(Map<String, dynamic> json) => _JobTagModel(
  subCategoryId: (json['sub_category_id'] as num?)?.toInt(),
  subCategory:
      json['sub_category'] == null
          ? null
          : SubCategoryModel.fromJson(
            json['sub_category'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$JobTagModelToJson(_JobTagModel instance) =>
    <String, dynamic>{
      'sub_category_id': instance.subCategoryId,
      'sub_category': instance.subCategory,
    };

_SubCategoryModel _$SubCategoryModelFromJson(Map<String, dynamic> json) =>
    _SubCategoryModel(name: json['name'] as String?);

Map<String, dynamic> _$SubCategoryModelToJson(_SubCategoryModel instance) =>
    <String, dynamic>{'name': instance.name};

_JobImageModel _$JobImageModelFromJson(Map<String, dynamic> json) =>
    _JobImageModel(imageUrl: json['image_url'] as String?);

Map<String, dynamic> _$JobImageModelToJson(_JobImageModel instance) =>
    <String, dynamic>{'image_url': instance.imageUrl};

_BidModel _$BidModelFromJson(Map<String, dynamic> json) => _BidModel(
  id: (json['id'] as num?)?.toInt(),
  artisanId: (json['artisan_id'] as num?)?.toInt(),
  amount: (json['amount'] as num?)?.toDouble(),
  status: json['status'] as String?,
  createdAt: json['created_at'] as String?,
  isSelected: json['is_selected'] as bool?,
  artisan:
      json['artisan'] == null
          ? null
          : ArtisanModel.fromJson(json['artisan'] as Map<String, dynamic>),
);

Map<String, dynamic> _$BidModelToJson(_BidModel instance) => <String, dynamic>{
  'id': instance.id,
  'artisan_id': instance.artisanId,
  'amount': instance.amount,
  'status': instance.status,
  'created_at': instance.createdAt,
  'is_selected': instance.isSelected,
  'artisan': instance.artisan,
};

_ArtisanModel _$ArtisanModelFromJson(Map<String, dynamic> json) =>
    _ArtisanModel(
      name: json['name'] as String?,
      avatar: json['avatar'] as String?,
    );

Map<String, dynamic> _$ArtisanModelToJson(_ArtisanModel instance) =>
    <String, dynamic>{'name': instance.name, 'avatar': instance.avatar};
