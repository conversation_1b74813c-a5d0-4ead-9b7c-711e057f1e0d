import 'package:freezed_annotation/freezed_annotation.dart';

part 'service_icons_response.freezed.dart';
part 'service_icons_response.g.dart';

@freezed
abstract class ServiceIconsResponse with _$ServiceIconsResponse {
  factory ServiceIconsResponse({int? id, String? name, String? icon}) =
      _ServiceIconsResponse;
  factory ServiceIconsResponse.fromJson(Map<String, dynamic> json) =>
      _$ServiceIconsResponseFromJson(json);
}
