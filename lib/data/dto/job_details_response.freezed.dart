// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'job_details_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$JobDetailsResponse {

 int? get id;@JsonKey(name: 'client_id') int? get clientId;@JsonKey(name: 'service_id') int? get serviceId;@JsonKey(name: 'job_description') String? get jobDescription; double? get budget; String? get status;@JsonKey(name: 'created_at') String? get createdAt;@JsonKey(name: 'updated_at') String? get updatedAt;@JsonKey(name: 'service_date') String? get serviceDate;@JsonKey(name: 'post_date') String? get postDate; ServiceModel? get service; ClientModel? get client;@JsonKey(name: 'job_tags') List<JobTagModel>? get jobTags;@JsonKey(name: 'job_images') List<JobImageModel>? get jobImages; List<BidModel>? get bids;
/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobDetailsResponseCopyWith<JobDetailsResponse> get copyWith => _$JobDetailsResponseCopyWithImpl<JobDetailsResponse>(this as JobDetailsResponse, _$identity);

  /// Serializes this JobDetailsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobDetailsResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.jobDescription, jobDescription) || other.jobDescription == jobDescription)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&(identical(other.postDate, postDate) || other.postDate == postDate)&&(identical(other.service, service) || other.service == service)&&(identical(other.client, client) || other.client == client)&&const DeepCollectionEquality().equals(other.jobTags, jobTags)&&const DeepCollectionEquality().equals(other.jobImages, jobImages)&&const DeepCollectionEquality().equals(other.bids, bids));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,clientId,serviceId,jobDescription,budget,status,createdAt,updatedAt,serviceDate,postDate,service,client,const DeepCollectionEquality().hash(jobTags),const DeepCollectionEquality().hash(jobImages),const DeepCollectionEquality().hash(bids));

@override
String toString() {
  return 'JobDetailsResponse(id: $id, clientId: $clientId, serviceId: $serviceId, jobDescription: $jobDescription, budget: $budget, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, serviceDate: $serviceDate, postDate: $postDate, service: $service, client: $client, jobTags: $jobTags, jobImages: $jobImages, bids: $bids)';
}


}

/// @nodoc
abstract mixin class $JobDetailsResponseCopyWith<$Res>  {
  factory $JobDetailsResponseCopyWith(JobDetailsResponse value, $Res Function(JobDetailsResponse) _then) = _$JobDetailsResponseCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'client_id') int? clientId,@JsonKey(name: 'service_id') int? serviceId,@JsonKey(name: 'job_description') String? jobDescription, double? budget, String? status,@JsonKey(name: 'created_at') String? createdAt,@JsonKey(name: 'updated_at') String? updatedAt,@JsonKey(name: 'service_date') String? serviceDate,@JsonKey(name: 'post_date') String? postDate, ServiceModel? service, ClientModel? client,@JsonKey(name: 'job_tags') List<JobTagModel>? jobTags,@JsonKey(name: 'job_images') List<JobImageModel>? jobImages, List<BidModel>? bids
});


$ServiceModelCopyWith<$Res>? get service;$ClientModelCopyWith<$Res>? get client;

}
/// @nodoc
class _$JobDetailsResponseCopyWithImpl<$Res>
    implements $JobDetailsResponseCopyWith<$Res> {
  _$JobDetailsResponseCopyWithImpl(this._self, this._then);

  final JobDetailsResponse _self;
  final $Res Function(JobDetailsResponse) _then;

/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? clientId = freezed,Object? serviceId = freezed,Object? jobDescription = freezed,Object? budget = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? serviceDate = freezed,Object? postDate = freezed,Object? service = freezed,Object? client = freezed,Object? jobTags = freezed,Object? jobImages = freezed,Object? bids = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,clientId: freezed == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int?,serviceId: freezed == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int?,jobDescription: freezed == jobDescription ? _self.jobDescription : jobDescription // ignore: cast_nullable_to_non_nullable
as String?,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as String?,serviceDate: freezed == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as String?,postDate: freezed == postDate ? _self.postDate : postDate // ignore: cast_nullable_to_non_nullable
as String?,service: freezed == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as ServiceModel?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel?,jobTags: freezed == jobTags ? _self.jobTags : jobTags // ignore: cast_nullable_to_non_nullable
as List<JobTagModel>?,jobImages: freezed == jobImages ? _self.jobImages : jobImages // ignore: cast_nullable_to_non_nullable
as List<JobImageModel>?,bids: freezed == bids ? _self.bids : bids // ignore: cast_nullable_to_non_nullable
as List<BidModel>?,
  ));
}
/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ServiceModelCopyWith<$Res>? get service {
    if (_self.service == null) {
    return null;
  }

  return $ServiceModelCopyWith<$Res>(_self.service!, (value) {
    return _then(_self.copyWith(service: value));
  });
}/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientModelCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _JobDetailsResponse implements JobDetailsResponse {
   _JobDetailsResponse({this.id, @JsonKey(name: 'client_id') this.clientId, @JsonKey(name: 'service_id') this.serviceId, @JsonKey(name: 'job_description') this.jobDescription, this.budget, this.status, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'service_date') this.serviceDate, @JsonKey(name: 'post_date') this.postDate, this.service, this.client, @JsonKey(name: 'job_tags') final  List<JobTagModel>? jobTags, @JsonKey(name: 'job_images') final  List<JobImageModel>? jobImages, final  List<BidModel>? bids}): _jobTags = jobTags,_jobImages = jobImages,_bids = bids;
  factory _JobDetailsResponse.fromJson(Map<String, dynamic> json) => _$JobDetailsResponseFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'client_id') final  int? clientId;
@override@JsonKey(name: 'service_id') final  int? serviceId;
@override@JsonKey(name: 'job_description') final  String? jobDescription;
@override final  double? budget;
@override final  String? status;
@override@JsonKey(name: 'created_at') final  String? createdAt;
@override@JsonKey(name: 'updated_at') final  String? updatedAt;
@override@JsonKey(name: 'service_date') final  String? serviceDate;
@override@JsonKey(name: 'post_date') final  String? postDate;
@override final  ServiceModel? service;
@override final  ClientModel? client;
 final  List<JobTagModel>? _jobTags;
@override@JsonKey(name: 'job_tags') List<JobTagModel>? get jobTags {
  final value = _jobTags;
  if (value == null) return null;
  if (_jobTags is EqualUnmodifiableListView) return _jobTags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<JobImageModel>? _jobImages;
@override@JsonKey(name: 'job_images') List<JobImageModel>? get jobImages {
  final value = _jobImages;
  if (value == null) return null;
  if (_jobImages is EqualUnmodifiableListView) return _jobImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<BidModel>? _bids;
@override List<BidModel>? get bids {
  final value = _bids;
  if (value == null) return null;
  if (_bids is EqualUnmodifiableListView) return _bids;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobDetailsResponseCopyWith<_JobDetailsResponse> get copyWith => __$JobDetailsResponseCopyWithImpl<_JobDetailsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JobDetailsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobDetailsResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.jobDescription, jobDescription) || other.jobDescription == jobDescription)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&(identical(other.postDate, postDate) || other.postDate == postDate)&&(identical(other.service, service) || other.service == service)&&(identical(other.client, client) || other.client == client)&&const DeepCollectionEquality().equals(other._jobTags, _jobTags)&&const DeepCollectionEquality().equals(other._jobImages, _jobImages)&&const DeepCollectionEquality().equals(other._bids, _bids));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,clientId,serviceId,jobDescription,budget,status,createdAt,updatedAt,serviceDate,postDate,service,client,const DeepCollectionEquality().hash(_jobTags),const DeepCollectionEquality().hash(_jobImages),const DeepCollectionEquality().hash(_bids));

@override
String toString() {
  return 'JobDetailsResponse(id: $id, clientId: $clientId, serviceId: $serviceId, jobDescription: $jobDescription, budget: $budget, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, serviceDate: $serviceDate, postDate: $postDate, service: $service, client: $client, jobTags: $jobTags, jobImages: $jobImages, bids: $bids)';
}


}

/// @nodoc
abstract mixin class _$JobDetailsResponseCopyWith<$Res> implements $JobDetailsResponseCopyWith<$Res> {
  factory _$JobDetailsResponseCopyWith(_JobDetailsResponse value, $Res Function(_JobDetailsResponse) _then) = __$JobDetailsResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'client_id') int? clientId,@JsonKey(name: 'service_id') int? serviceId,@JsonKey(name: 'job_description') String? jobDescription, double? budget, String? status,@JsonKey(name: 'created_at') String? createdAt,@JsonKey(name: 'updated_at') String? updatedAt,@JsonKey(name: 'service_date') String? serviceDate,@JsonKey(name: 'post_date') String? postDate, ServiceModel? service, ClientModel? client,@JsonKey(name: 'job_tags') List<JobTagModel>? jobTags,@JsonKey(name: 'job_images') List<JobImageModel>? jobImages, List<BidModel>? bids
});


@override $ServiceModelCopyWith<$Res>? get service;@override $ClientModelCopyWith<$Res>? get client;

}
/// @nodoc
class __$JobDetailsResponseCopyWithImpl<$Res>
    implements _$JobDetailsResponseCopyWith<$Res> {
  __$JobDetailsResponseCopyWithImpl(this._self, this._then);

  final _JobDetailsResponse _self;
  final $Res Function(_JobDetailsResponse) _then;

/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? clientId = freezed,Object? serviceId = freezed,Object? jobDescription = freezed,Object? budget = freezed,Object? status = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? serviceDate = freezed,Object? postDate = freezed,Object? service = freezed,Object? client = freezed,Object? jobTags = freezed,Object? jobImages = freezed,Object? bids = freezed,}) {
  return _then(_JobDetailsResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,clientId: freezed == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int?,serviceId: freezed == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int?,jobDescription: freezed == jobDescription ? _self.jobDescription : jobDescription // ignore: cast_nullable_to_non_nullable
as String?,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as String?,serviceDate: freezed == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as String?,postDate: freezed == postDate ? _self.postDate : postDate // ignore: cast_nullable_to_non_nullable
as String?,service: freezed == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as ServiceModel?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel?,jobTags: freezed == jobTags ? _self._jobTags : jobTags // ignore: cast_nullable_to_non_nullable
as List<JobTagModel>?,jobImages: freezed == jobImages ? _self._jobImages : jobImages // ignore: cast_nullable_to_non_nullable
as List<JobImageModel>?,bids: freezed == bids ? _self._bids : bids // ignore: cast_nullable_to_non_nullable
as List<BidModel>?,
  ));
}

/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ServiceModelCopyWith<$Res>? get service {
    if (_self.service == null) {
    return null;
  }

  return $ServiceModelCopyWith<$Res>(_self.service!, (value) {
    return _then(_self.copyWith(service: value));
  });
}/// Create a copy of JobDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientModelCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
mixin _$ServiceModel {

 String? get name;
/// Create a copy of ServiceModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServiceModelCopyWith<ServiceModel> get copyWith => _$ServiceModelCopyWithImpl<ServiceModel>(this as ServiceModel, _$identity);

  /// Serializes this ServiceModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServiceModel&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'ServiceModel(name: $name)';
}


}

/// @nodoc
abstract mixin class $ServiceModelCopyWith<$Res>  {
  factory $ServiceModelCopyWith(ServiceModel value, $Res Function(ServiceModel) _then) = _$ServiceModelCopyWithImpl;
@useResult
$Res call({
 String? name
});




}
/// @nodoc
class _$ServiceModelCopyWithImpl<$Res>
    implements $ServiceModelCopyWith<$Res> {
  _$ServiceModelCopyWithImpl(this._self, this._then);

  final ServiceModel _self;
  final $Res Function(ServiceModel) _then;

/// Create a copy of ServiceModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ServiceModel implements ServiceModel {
   _ServiceModel({this.name});
  factory _ServiceModel.fromJson(Map<String, dynamic> json) => _$ServiceModelFromJson(json);

@override final  String? name;

/// Create a copy of ServiceModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServiceModelCopyWith<_ServiceModel> get copyWith => __$ServiceModelCopyWithImpl<_ServiceModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServiceModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServiceModel&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'ServiceModel(name: $name)';
}


}

/// @nodoc
abstract mixin class _$ServiceModelCopyWith<$Res> implements $ServiceModelCopyWith<$Res> {
  factory _$ServiceModelCopyWith(_ServiceModel value, $Res Function(_ServiceModel) _then) = __$ServiceModelCopyWithImpl;
@override @useResult
$Res call({
 String? name
});




}
/// @nodoc
class __$ServiceModelCopyWithImpl<$Res>
    implements _$ServiceModelCopyWith<$Res> {
  __$ServiceModelCopyWithImpl(this._self, this._then);

  final _ServiceModel _self;
  final $Res Function(_ServiceModel) _then;

/// Create a copy of ServiceModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,}) {
  return _then(_ServiceModel(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ClientModel {

 int? get id; String? get name; String? get avatar;
/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientModelCopyWith<ClientModel> get copyWith => _$ClientModelCopyWithImpl<ClientModel>(this as ClientModel, _$identity);

  /// Serializes this ClientModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatar);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class $ClientModelCopyWith<$Res>  {
  factory $ClientModelCopyWith(ClientModel value, $Res Function(ClientModel) _then) = _$ClientModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, String? avatar
});




}
/// @nodoc
class _$ClientModelCopyWithImpl<$Res>
    implements $ClientModelCopyWith<$Res> {
  _$ClientModelCopyWithImpl(this._self, this._then);

  final ClientModel _self;
  final $Res Function(ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ClientModel implements ClientModel {
   _ClientModel({this.id, this.name, this.avatar});
  factory _ClientModel.fromJson(Map<String, dynamic> json) => _$ClientModelFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  String? avatar;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientModelCopyWith<_ClientModel> get copyWith => __$ClientModelCopyWithImpl<_ClientModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatar);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class _$ClientModelCopyWith<$Res> implements $ClientModelCopyWith<$Res> {
  factory _$ClientModelCopyWith(_ClientModel value, $Res Function(_ClientModel) _then) = __$ClientModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, String? avatar
});




}
/// @nodoc
class __$ClientModelCopyWithImpl<$Res>
    implements _$ClientModelCopyWith<$Res> {
  __$ClientModelCopyWithImpl(this._self, this._then);

  final _ClientModel _self;
  final $Res Function(_ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_ClientModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$JobTagModel {

@JsonKey(name: 'sub_category_id') int? get subCategoryId;@JsonKey(name: 'sub_category') SubCategoryModel? get subCategory;
/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobTagModelCopyWith<JobTagModel> get copyWith => _$JobTagModelCopyWithImpl<JobTagModel>(this as JobTagModel, _$identity);

  /// Serializes this JobTagModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobTagModel&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId)&&(identical(other.subCategory, subCategory) || other.subCategory == subCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId,subCategory);

@override
String toString() {
  return 'JobTagModel(subCategoryId: $subCategoryId, subCategory: $subCategory)';
}


}

/// @nodoc
abstract mixin class $JobTagModelCopyWith<$Res>  {
  factory $JobTagModelCopyWith(JobTagModel value, $Res Function(JobTagModel) _then) = _$JobTagModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'sub_category_id') int? subCategoryId,@JsonKey(name: 'sub_category') SubCategoryModel? subCategory
});


$SubCategoryModelCopyWith<$Res>? get subCategory;

}
/// @nodoc
class _$JobTagModelCopyWithImpl<$Res>
    implements $JobTagModelCopyWith<$Res> {
  _$JobTagModelCopyWithImpl(this._self, this._then);

  final JobTagModel _self;
  final $Res Function(JobTagModel) _then;

/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? subCategoryId = freezed,Object? subCategory = freezed,}) {
  return _then(_self.copyWith(
subCategoryId: freezed == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int?,subCategory: freezed == subCategory ? _self.subCategory : subCategory // ignore: cast_nullable_to_non_nullable
as SubCategoryModel?,
  ));
}
/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SubCategoryModelCopyWith<$Res>? get subCategory {
    if (_self.subCategory == null) {
    return null;
  }

  return $SubCategoryModelCopyWith<$Res>(_self.subCategory!, (value) {
    return _then(_self.copyWith(subCategory: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _JobTagModel implements JobTagModel {
   _JobTagModel({@JsonKey(name: 'sub_category_id') this.subCategoryId, @JsonKey(name: 'sub_category') this.subCategory});
  factory _JobTagModel.fromJson(Map<String, dynamic> json) => _$JobTagModelFromJson(json);

@override@JsonKey(name: 'sub_category_id') final  int? subCategoryId;
@override@JsonKey(name: 'sub_category') final  SubCategoryModel? subCategory;

/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobTagModelCopyWith<_JobTagModel> get copyWith => __$JobTagModelCopyWithImpl<_JobTagModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JobTagModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobTagModel&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId)&&(identical(other.subCategory, subCategory) || other.subCategory == subCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId,subCategory);

@override
String toString() {
  return 'JobTagModel(subCategoryId: $subCategoryId, subCategory: $subCategory)';
}


}

/// @nodoc
abstract mixin class _$JobTagModelCopyWith<$Res> implements $JobTagModelCopyWith<$Res> {
  factory _$JobTagModelCopyWith(_JobTagModel value, $Res Function(_JobTagModel) _then) = __$JobTagModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'sub_category_id') int? subCategoryId,@JsonKey(name: 'sub_category') SubCategoryModel? subCategory
});


@override $SubCategoryModelCopyWith<$Res>? get subCategory;

}
/// @nodoc
class __$JobTagModelCopyWithImpl<$Res>
    implements _$JobTagModelCopyWith<$Res> {
  __$JobTagModelCopyWithImpl(this._self, this._then);

  final _JobTagModel _self;
  final $Res Function(_JobTagModel) _then;

/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? subCategoryId = freezed,Object? subCategory = freezed,}) {
  return _then(_JobTagModel(
subCategoryId: freezed == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int?,subCategory: freezed == subCategory ? _self.subCategory : subCategory // ignore: cast_nullable_to_non_nullable
as SubCategoryModel?,
  ));
}

/// Create a copy of JobTagModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SubCategoryModelCopyWith<$Res>? get subCategory {
    if (_self.subCategory == null) {
    return null;
  }

  return $SubCategoryModelCopyWith<$Res>(_self.subCategory!, (value) {
    return _then(_self.copyWith(subCategory: value));
  });
}
}


/// @nodoc
mixin _$SubCategoryModel {

 String? get name;
/// Create a copy of SubCategoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubCategoryModelCopyWith<SubCategoryModel> get copyWith => _$SubCategoryModelCopyWithImpl<SubCategoryModel>(this as SubCategoryModel, _$identity);

  /// Serializes this SubCategoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubCategoryModel&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'SubCategoryModel(name: $name)';
}


}

/// @nodoc
abstract mixin class $SubCategoryModelCopyWith<$Res>  {
  factory $SubCategoryModelCopyWith(SubCategoryModel value, $Res Function(SubCategoryModel) _then) = _$SubCategoryModelCopyWithImpl;
@useResult
$Res call({
 String? name
});




}
/// @nodoc
class _$SubCategoryModelCopyWithImpl<$Res>
    implements $SubCategoryModelCopyWith<$Res> {
  _$SubCategoryModelCopyWithImpl(this._self, this._then);

  final SubCategoryModel _self;
  final $Res Function(SubCategoryModel) _then;

/// Create a copy of SubCategoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SubCategoryModel implements SubCategoryModel {
   _SubCategoryModel({this.name});
  factory _SubCategoryModel.fromJson(Map<String, dynamic> json) => _$SubCategoryModelFromJson(json);

@override final  String? name;

/// Create a copy of SubCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubCategoryModelCopyWith<_SubCategoryModel> get copyWith => __$SubCategoryModelCopyWithImpl<_SubCategoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubCategoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubCategoryModel&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'SubCategoryModel(name: $name)';
}


}

/// @nodoc
abstract mixin class _$SubCategoryModelCopyWith<$Res> implements $SubCategoryModelCopyWith<$Res> {
  factory _$SubCategoryModelCopyWith(_SubCategoryModel value, $Res Function(_SubCategoryModel) _then) = __$SubCategoryModelCopyWithImpl;
@override @useResult
$Res call({
 String? name
});




}
/// @nodoc
class __$SubCategoryModelCopyWithImpl<$Res>
    implements _$SubCategoryModelCopyWith<$Res> {
  __$SubCategoryModelCopyWithImpl(this._self, this._then);

  final _SubCategoryModel _self;
  final $Res Function(_SubCategoryModel) _then;

/// Create a copy of SubCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,}) {
  return _then(_SubCategoryModel(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$JobImageModel {

@JsonKey(name: 'image_url') String? get imageUrl;
/// Create a copy of JobImageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobImageModelCopyWith<JobImageModel> get copyWith => _$JobImageModelCopyWithImpl<JobImageModel>(this as JobImageModel, _$identity);

  /// Serializes this JobImageModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobImageModel&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,imageUrl);

@override
String toString() {
  return 'JobImageModel(imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $JobImageModelCopyWith<$Res>  {
  factory $JobImageModelCopyWith(JobImageModel value, $Res Function(JobImageModel) _then) = _$JobImageModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class _$JobImageModelCopyWithImpl<$Res>
    implements $JobImageModelCopyWith<$Res> {
  _$JobImageModelCopyWithImpl(this._self, this._then);

  final JobImageModel _self;
  final $Res Function(JobImageModel) _then;

/// Create a copy of JobImageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? imageUrl = freezed,}) {
  return _then(_self.copyWith(
imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _JobImageModel implements JobImageModel {
   _JobImageModel({@JsonKey(name: 'image_url') this.imageUrl});
  factory _JobImageModel.fromJson(Map<String, dynamic> json) => _$JobImageModelFromJson(json);

@override@JsonKey(name: 'image_url') final  String? imageUrl;

/// Create a copy of JobImageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobImageModelCopyWith<_JobImageModel> get copyWith => __$JobImageModelCopyWithImpl<_JobImageModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JobImageModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobImageModel&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,imageUrl);

@override
String toString() {
  return 'JobImageModel(imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$JobImageModelCopyWith<$Res> implements $JobImageModelCopyWith<$Res> {
  factory _$JobImageModelCopyWith(_JobImageModel value, $Res Function(_JobImageModel) _then) = __$JobImageModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class __$JobImageModelCopyWithImpl<$Res>
    implements _$JobImageModelCopyWith<$Res> {
  __$JobImageModelCopyWithImpl(this._self, this._then);

  final _JobImageModel _self;
  final $Res Function(_JobImageModel) _then;

/// Create a copy of JobImageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? imageUrl = freezed,}) {
  return _then(_JobImageModel(
imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$BidModel {

 int? get id;@JsonKey(name: 'artisan_id') int? get artisanId; double? get amount; String? get status;@JsonKey(name: 'created_at') String? get createdAt;@JsonKey(name: 'is_selected') bool? get isSelected; ArtisanModel? get artisan;
/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BidModelCopyWith<BidModel> get copyWith => _$BidModelCopyWithImpl<BidModel>(this as BidModel, _$identity);

  /// Serializes this BidModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BidModel&&(identical(other.id, id) || other.id == id)&&(identical(other.artisanId, artisanId) || other.artisanId == artisanId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.artisan, artisan) || other.artisan == artisan));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,artisanId,amount,status,createdAt,isSelected,artisan);

@override
String toString() {
  return 'BidModel(id: $id, artisanId: $artisanId, amount: $amount, status: $status, createdAt: $createdAt, isSelected: $isSelected, artisan: $artisan)';
}


}

/// @nodoc
abstract mixin class $BidModelCopyWith<$Res>  {
  factory $BidModelCopyWith(BidModel value, $Res Function(BidModel) _then) = _$BidModelCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'artisan_id') int? artisanId, double? amount, String? status,@JsonKey(name: 'created_at') String? createdAt,@JsonKey(name: 'is_selected') bool? isSelected, ArtisanModel? artisan
});


$ArtisanModelCopyWith<$Res>? get artisan;

}
/// @nodoc
class _$BidModelCopyWithImpl<$Res>
    implements $BidModelCopyWith<$Res> {
  _$BidModelCopyWithImpl(this._self, this._then);

  final BidModel _self;
  final $Res Function(BidModel) _then;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? artisanId = freezed,Object? amount = freezed,Object? status = freezed,Object? createdAt = freezed,Object? isSelected = freezed,Object? artisan = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,artisanId: freezed == artisanId ? _self.artisanId : artisanId // ignore: cast_nullable_to_non_nullable
as int?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,isSelected: freezed == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool?,artisan: freezed == artisan ? _self.artisan : artisan // ignore: cast_nullable_to_non_nullable
as ArtisanModel?,
  ));
}
/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<$Res>? get artisan {
    if (_self.artisan == null) {
    return null;
  }

  return $ArtisanModelCopyWith<$Res>(_self.artisan!, (value) {
    return _then(_self.copyWith(artisan: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _BidModel implements BidModel {
   _BidModel({this.id, @JsonKey(name: 'artisan_id') this.artisanId, this.amount, this.status, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'is_selected') this.isSelected, this.artisan});
  factory _BidModel.fromJson(Map<String, dynamic> json) => _$BidModelFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'artisan_id') final  int? artisanId;
@override final  double? amount;
@override final  String? status;
@override@JsonKey(name: 'created_at') final  String? createdAt;
@override@JsonKey(name: 'is_selected') final  bool? isSelected;
@override final  ArtisanModel? artisan;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BidModelCopyWith<_BidModel> get copyWith => __$BidModelCopyWithImpl<_BidModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BidModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BidModel&&(identical(other.id, id) || other.id == id)&&(identical(other.artisanId, artisanId) || other.artisanId == artisanId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.artisan, artisan) || other.artisan == artisan));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,artisanId,amount,status,createdAt,isSelected,artisan);

@override
String toString() {
  return 'BidModel(id: $id, artisanId: $artisanId, amount: $amount, status: $status, createdAt: $createdAt, isSelected: $isSelected, artisan: $artisan)';
}


}

/// @nodoc
abstract mixin class _$BidModelCopyWith<$Res> implements $BidModelCopyWith<$Res> {
  factory _$BidModelCopyWith(_BidModel value, $Res Function(_BidModel) _then) = __$BidModelCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'artisan_id') int? artisanId, double? amount, String? status,@JsonKey(name: 'created_at') String? createdAt,@JsonKey(name: 'is_selected') bool? isSelected, ArtisanModel? artisan
});


@override $ArtisanModelCopyWith<$Res>? get artisan;

}
/// @nodoc
class __$BidModelCopyWithImpl<$Res>
    implements _$BidModelCopyWith<$Res> {
  __$BidModelCopyWithImpl(this._self, this._then);

  final _BidModel _self;
  final $Res Function(_BidModel) _then;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? artisanId = freezed,Object? amount = freezed,Object? status = freezed,Object? createdAt = freezed,Object? isSelected = freezed,Object? artisan = freezed,}) {
  return _then(_BidModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,artisanId: freezed == artisanId ? _self.artisanId : artisanId // ignore: cast_nullable_to_non_nullable
as int?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,isSelected: freezed == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool?,artisan: freezed == artisan ? _self.artisan : artisan // ignore: cast_nullable_to_non_nullable
as ArtisanModel?,
  ));
}

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<$Res>? get artisan {
    if (_self.artisan == null) {
    return null;
  }

  return $ArtisanModelCopyWith<$Res>(_self.artisan!, (value) {
    return _then(_self.copyWith(artisan: value));
  });
}
}


/// @nodoc
mixin _$ArtisanModel {

 String? get name; String? get avatar;
/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<ArtisanModel> get copyWith => _$ArtisanModelCopyWithImpl<ArtisanModel>(this as ArtisanModel, _$identity);

  /// Serializes this ArtisanModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanModel&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,avatar);

@override
String toString() {
  return 'ArtisanModel(name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class $ArtisanModelCopyWith<$Res>  {
  factory $ArtisanModelCopyWith(ArtisanModel value, $Res Function(ArtisanModel) _then) = _$ArtisanModelCopyWithImpl;
@useResult
$Res call({
 String? name, String? avatar
});




}
/// @nodoc
class _$ArtisanModelCopyWithImpl<$Res>
    implements $ArtisanModelCopyWith<$Res> {
  _$ArtisanModelCopyWithImpl(this._self, this._then);

  final ArtisanModel _self;
  final $Res Function(ArtisanModel) _then;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanModel implements ArtisanModel {
   _ArtisanModel({this.name, this.avatar});
  factory _ArtisanModel.fromJson(Map<String, dynamic> json) => _$ArtisanModelFromJson(json);

@override final  String? name;
@override final  String? avatar;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanModelCopyWith<_ArtisanModel> get copyWith => __$ArtisanModelCopyWithImpl<_ArtisanModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanModel&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,avatar);

@override
String toString() {
  return 'ArtisanModel(name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class _$ArtisanModelCopyWith<$Res> implements $ArtisanModelCopyWith<$Res> {
  factory _$ArtisanModelCopyWith(_ArtisanModel value, $Res Function(_ArtisanModel) _then) = __$ArtisanModelCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? avatar
});




}
/// @nodoc
class __$ArtisanModelCopyWithImpl<$Res>
    implements _$ArtisanModelCopyWith<$Res> {
  __$ArtisanModelCopyWithImpl(this._self, this._then);

  final _ArtisanModel _self;
  final $Res Function(_ArtisanModel) _then;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_ArtisanModel(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
