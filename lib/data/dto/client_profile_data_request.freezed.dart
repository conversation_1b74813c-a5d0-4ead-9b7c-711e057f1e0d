// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'client_profile_data_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ClientProfileDataRequest {

 ClientModel get client; List<PhoneNumberModel> get phonenumbers; LocationModel get location;
/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientProfileDataRequestCopyWith<ClientProfileDataRequest> get copyWith => _$ClientProfileDataRequestCopyWithImpl<ClientProfileDataRequest>(this as ClientProfileDataRequest, _$identity);

  /// Serializes this ClientProfileDataRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientProfileDataRequest&&(identical(other.client, client) || other.client == client)&&const DeepCollectionEquality().equals(other.phonenumbers, phonenumbers)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,client,const DeepCollectionEquality().hash(phonenumbers),location);

@override
String toString() {
  return 'ClientProfileDataRequest(client: $client, phonenumbers: $phonenumbers, location: $location)';
}


}

/// @nodoc
abstract mixin class $ClientProfileDataRequestCopyWith<$Res>  {
  factory $ClientProfileDataRequestCopyWith(ClientProfileDataRequest value, $Res Function(ClientProfileDataRequest) _then) = _$ClientProfileDataRequestCopyWithImpl;
@useResult
$Res call({
 ClientModel client, List<PhoneNumberModel> phonenumbers, LocationModel location
});


$ClientModelCopyWith<$Res> get client;$LocationModelCopyWith<$Res> get location;

}
/// @nodoc
class _$ClientProfileDataRequestCopyWithImpl<$Res>
    implements $ClientProfileDataRequestCopyWith<$Res> {
  _$ClientProfileDataRequestCopyWithImpl(this._self, this._then);

  final ClientProfileDataRequest _self;
  final $Res Function(ClientProfileDataRequest) _then;

/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? client = null,Object? phonenumbers = null,Object? location = null,}) {
  return _then(_self.copyWith(
client: null == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel,phonenumbers: null == phonenumbers ? _self.phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<PhoneNumberModel>,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LocationModel,
  ));
}
/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res> get client {
  
  return $ClientModelCopyWith<$Res>(_self.client, (value) {
    return _then(_self.copyWith(client: value));
  });
}/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationModelCopyWith<$Res> get location {
  
  return $LocationModelCopyWith<$Res>(_self.location, (value) {
    return _then(_self.copyWith(location: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ClientProfileDataRequest implements ClientProfileDataRequest {
  const _ClientProfileDataRequest({required this.client, required final  List<PhoneNumberModel> phonenumbers, required this.location}): _phonenumbers = phonenumbers;
  factory _ClientProfileDataRequest.fromJson(Map<String, dynamic> json) => _$ClientProfileDataRequestFromJson(json);

@override final  ClientModel client;
 final  List<PhoneNumberModel> _phonenumbers;
@override List<PhoneNumberModel> get phonenumbers {
  if (_phonenumbers is EqualUnmodifiableListView) return _phonenumbers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_phonenumbers);
}

@override final  LocationModel location;

/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientProfileDataRequestCopyWith<_ClientProfileDataRequest> get copyWith => __$ClientProfileDataRequestCopyWithImpl<_ClientProfileDataRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientProfileDataRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientProfileDataRequest&&(identical(other.client, client) || other.client == client)&&const DeepCollectionEquality().equals(other._phonenumbers, _phonenumbers)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,client,const DeepCollectionEquality().hash(_phonenumbers),location);

@override
String toString() {
  return 'ClientProfileDataRequest(client: $client, phonenumbers: $phonenumbers, location: $location)';
}


}

/// @nodoc
abstract mixin class _$ClientProfileDataRequestCopyWith<$Res> implements $ClientProfileDataRequestCopyWith<$Res> {
  factory _$ClientProfileDataRequestCopyWith(_ClientProfileDataRequest value, $Res Function(_ClientProfileDataRequest) _then) = __$ClientProfileDataRequestCopyWithImpl;
@override @useResult
$Res call({
 ClientModel client, List<PhoneNumberModel> phonenumbers, LocationModel location
});


@override $ClientModelCopyWith<$Res> get client;@override $LocationModelCopyWith<$Res> get location;

}
/// @nodoc
class __$ClientProfileDataRequestCopyWithImpl<$Res>
    implements _$ClientProfileDataRequestCopyWith<$Res> {
  __$ClientProfileDataRequestCopyWithImpl(this._self, this._then);

  final _ClientProfileDataRequest _self;
  final $Res Function(_ClientProfileDataRequest) _then;

/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? client = null,Object? phonenumbers = null,Object? location = null,}) {
  return _then(_ClientProfileDataRequest(
client: null == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel,phonenumbers: null == phonenumbers ? _self._phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<PhoneNumberModel>,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LocationModel,
  ));
}

/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res> get client {
  
  return $ClientModelCopyWith<$Res>(_self.client, (value) {
    return _then(_self.copyWith(client: value));
  });
}/// Create a copy of ClientProfileDataRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationModelCopyWith<$Res> get location {
  
  return $LocationModelCopyWith<$Res>(_self.location, (value) {
    return _then(_self.copyWith(location: value));
  });
}
}


/// @nodoc
mixin _$ClientModel {

 int? get id; String get name; String get email; String get avatar; String? get address;@JsonKey(name: 'national_id') String? get nationalId;@JsonKey(name: 'supabase_id') String? get supabaseId;@JsonKey(name: 'whatsapp_number') String? get whatsappNumber; String? get phone;
/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientModelCopyWith<ClientModel> get copyWith => _$ClientModelCopyWithImpl<ClientModel>(this as ClientModel, _$identity);

  /// Serializes this ClientModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.phone, phone) || other.phone == phone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,avatar,address,nationalId,supabaseId,whatsappNumber,phone);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, email: $email, avatar: $avatar, address: $address, nationalId: $nationalId, supabaseId: $supabaseId, whatsappNumber: $whatsappNumber, phone: $phone)';
}


}

/// @nodoc
abstract mixin class $ClientModelCopyWith<$Res>  {
  factory $ClientModelCopyWith(ClientModel value, $Res Function(ClientModel) _then) = _$ClientModelCopyWithImpl;
@useResult
$Res call({
 int? id, String name, String email, String avatar, String? address,@JsonKey(name: 'national_id') String? nationalId,@JsonKey(name: 'supabase_id') String? supabaseId,@JsonKey(name: 'whatsapp_number') String? whatsappNumber, String? phone
});




}
/// @nodoc
class _$ClientModelCopyWithImpl<$Res>
    implements $ClientModelCopyWith<$Res> {
  _$ClientModelCopyWithImpl(this._self, this._then);

  final ClientModel _self;
  final $Res Function(ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = null,Object? email = null,Object? avatar = null,Object? address = freezed,Object? nationalId = freezed,Object? supabaseId = freezed,Object? whatsappNumber = freezed,Object? phone = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,avatar: null == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,nationalId: freezed == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String?,supabaseId: freezed == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String?,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ClientModel implements ClientModel {
  const _ClientModel({this.id, required this.name, required this.email, required this.avatar, this.address, @JsonKey(name: 'national_id') this.nationalId, @JsonKey(name: 'supabase_id') this.supabaseId, @JsonKey(name: 'whatsapp_number') this.whatsappNumber, this.phone});
  factory _ClientModel.fromJson(Map<String, dynamic> json) => _$ClientModelFromJson(json);

@override final  int? id;
@override final  String name;
@override final  String email;
@override final  String avatar;
@override final  String? address;
@override@JsonKey(name: 'national_id') final  String? nationalId;
@override@JsonKey(name: 'supabase_id') final  String? supabaseId;
@override@JsonKey(name: 'whatsapp_number') final  String? whatsappNumber;
@override final  String? phone;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientModelCopyWith<_ClientModel> get copyWith => __$ClientModelCopyWithImpl<_ClientModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.phone, phone) || other.phone == phone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,avatar,address,nationalId,supabaseId,whatsappNumber,phone);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, email: $email, avatar: $avatar, address: $address, nationalId: $nationalId, supabaseId: $supabaseId, whatsappNumber: $whatsappNumber, phone: $phone)';
}


}

/// @nodoc
abstract mixin class _$ClientModelCopyWith<$Res> implements $ClientModelCopyWith<$Res> {
  factory _$ClientModelCopyWith(_ClientModel value, $Res Function(_ClientModel) _then) = __$ClientModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String name, String email, String avatar, String? address,@JsonKey(name: 'national_id') String? nationalId,@JsonKey(name: 'supabase_id') String? supabaseId,@JsonKey(name: 'whatsapp_number') String? whatsappNumber, String? phone
});




}
/// @nodoc
class __$ClientModelCopyWithImpl<$Res>
    implements _$ClientModelCopyWith<$Res> {
  __$ClientModelCopyWithImpl(this._self, this._then);

  final _ClientModel _self;
  final $Res Function(_ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = null,Object? email = null,Object? avatar = null,Object? address = freezed,Object? nationalId = freezed,Object? supabaseId = freezed,Object? whatsappNumber = freezed,Object? phone = freezed,}) {
  return _then(_ClientModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,avatar: null == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,nationalId: freezed == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String?,supabaseId: freezed == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String?,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PhoneNumberModel {

 String get phonenumber;
/// Create a copy of PhoneNumberModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PhoneNumberModelCopyWith<PhoneNumberModel> get copyWith => _$PhoneNumberModelCopyWithImpl<PhoneNumberModel>(this as PhoneNumberModel, _$identity);

  /// Serializes this PhoneNumberModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PhoneNumberModel&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber);

@override
String toString() {
  return 'PhoneNumberModel(phonenumber: $phonenumber)';
}


}

/// @nodoc
abstract mixin class $PhoneNumberModelCopyWith<$Res>  {
  factory $PhoneNumberModelCopyWith(PhoneNumberModel value, $Res Function(PhoneNumberModel) _then) = _$PhoneNumberModelCopyWithImpl;
@useResult
$Res call({
 String phonenumber
});




}
/// @nodoc
class _$PhoneNumberModelCopyWithImpl<$Res>
    implements $PhoneNumberModelCopyWith<$Res> {
  _$PhoneNumberModelCopyWithImpl(this._self, this._then);

  final PhoneNumberModel _self;
  final $Res Function(PhoneNumberModel) _then;

/// Create a copy of PhoneNumberModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phonenumber = null,}) {
  return _then(_self.copyWith(
phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PhoneNumberModel implements PhoneNumberModel {
  const _PhoneNumberModel({required this.phonenumber});
  factory _PhoneNumberModel.fromJson(Map<String, dynamic> json) => _$PhoneNumberModelFromJson(json);

@override final  String phonenumber;

/// Create a copy of PhoneNumberModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneNumberModelCopyWith<_PhoneNumberModel> get copyWith => __$PhoneNumberModelCopyWithImpl<_PhoneNumberModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PhoneNumberModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneNumberModel&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber);

@override
String toString() {
  return 'PhoneNumberModel(phonenumber: $phonenumber)';
}


}

/// @nodoc
abstract mixin class _$PhoneNumberModelCopyWith<$Res> implements $PhoneNumberModelCopyWith<$Res> {
  factory _$PhoneNumberModelCopyWith(_PhoneNumberModel value, $Res Function(_PhoneNumberModel) _then) = __$PhoneNumberModelCopyWithImpl;
@override @useResult
$Res call({
 String phonenumber
});




}
/// @nodoc
class __$PhoneNumberModelCopyWithImpl<$Res>
    implements _$PhoneNumberModelCopyWith<$Res> {
  __$PhoneNumberModelCopyWithImpl(this._self, this._then);

  final _PhoneNumberModel _self;
  final $Res Function(_PhoneNumberModel) _then;

/// Create a copy of PhoneNumberModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phonenumber = null,}) {
  return _then(_PhoneNumberModel(
phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$LocationModel {

 double get latitude; double get longitude; String get address;
/// Create a copy of LocationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LocationModelCopyWith<LocationModel> get copyWith => _$LocationModelCopyWithImpl<LocationModel>(this as LocationModel, _$identity);

  /// Serializes this LocationModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationModel&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,address);

@override
String toString() {
  return 'LocationModel(latitude: $latitude, longitude: $longitude, address: $address)';
}


}

/// @nodoc
abstract mixin class $LocationModelCopyWith<$Res>  {
  factory $LocationModelCopyWith(LocationModel value, $Res Function(LocationModel) _then) = _$LocationModelCopyWithImpl;
@useResult
$Res call({
 double latitude, double longitude, String address
});




}
/// @nodoc
class _$LocationModelCopyWithImpl<$Res>
    implements $LocationModelCopyWith<$Res> {
  _$LocationModelCopyWithImpl(this._self, this._then);

  final LocationModel _self;
  final $Res Function(LocationModel) _then;

/// Create a copy of LocationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? latitude = null,Object? longitude = null,Object? address = null,}) {
  return _then(_self.copyWith(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LocationModel implements LocationModel {
  const _LocationModel({required this.latitude, required this.longitude, required this.address});
  factory _LocationModel.fromJson(Map<String, dynamic> json) => _$LocationModelFromJson(json);

@override final  double latitude;
@override final  double longitude;
@override final  String address;

/// Create a copy of LocationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LocationModelCopyWith<_LocationModel> get copyWith => __$LocationModelCopyWithImpl<_LocationModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LocationModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LocationModel&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,address);

@override
String toString() {
  return 'LocationModel(latitude: $latitude, longitude: $longitude, address: $address)';
}


}

/// @nodoc
abstract mixin class _$LocationModelCopyWith<$Res> implements $LocationModelCopyWith<$Res> {
  factory _$LocationModelCopyWith(_LocationModel value, $Res Function(_LocationModel) _then) = __$LocationModelCopyWithImpl;
@override @useResult
$Res call({
 double latitude, double longitude, String address
});




}
/// @nodoc
class __$LocationModelCopyWithImpl<$Res>
    implements _$LocationModelCopyWith<$Res> {
  __$LocationModelCopyWithImpl(this._self, this._then);

  final _LocationModel _self;
  final $Res Function(_LocationModel) _then;

/// Create a copy of LocationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? latitude = null,Object? longitude = null,Object? address = null,}) {
  return _then(_LocationModel(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
