// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hardware_items_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_HardwareItem _$HardwareItemFromJson(Map<String, dynamic> json) =>
    _HardwareItem(
      id: (json['id'] as num?)?.toInt(),
      branchId: (json['branch_id'] as num?)?.toInt(),
      hardwareSubCategoryId:
          (json['hardware_sub_category_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      brand: json['brand'] as String?,
      modelNumber: json['model_number'] as String?,
      description: json['description'] as String?,
      sku: json['sku'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      currentStock: (json['current_stock'] as num?)?.toInt(),
      minStockLevel: (json['min_stock_level'] as num?)?.toInt(),
      unitType: json['unit_type'] as String?,
      createdAt:
          json['created_at'] == null
              ? null
              : DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] == null
              ? null
              : DateTime.parse(json['updated_at'] as String),
      branch:
          json['branch'] == null
              ? null
              : ProductBranch.fromJson(json['branch'] as Map<String, dynamic>),
      productImages:
          (json['product_images'] as List<dynamic>?)
              ?.map((e) => ProductImage.fromJson(e as Map<String, dynamic>))
              .toList(),
      hardwareSubCategory:
          json['hardware_sub_category'] == null
              ? null
              : HardwareSubCategoryData.fromJson(
                json['hardware_sub_category'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$HardwareItemToJson(_HardwareItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'branch_id': instance.branchId,
      'hardware_sub_category_id': instance.hardwareSubCategoryId,
      'name': instance.name,
      'brand': instance.brand,
      'model_number': instance.modelNumber,
      'description': instance.description,
      'sku': instance.sku,
      'price': instance.price,
      'current_stock': instance.currentStock,
      'min_stock_level': instance.minStockLevel,
      'unit_type': instance.unitType,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'branch': instance.branch,
      'product_images': instance.productImages,
      'hardware_sub_category': instance.hardwareSubCategory,
    };

_HardwareSubCategoryData _$HardwareSubCategoryDataFromJson(
  Map<String, dynamic> json,
) => _HardwareSubCategoryData(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
  hardwareCategory:
      json['hardware_category'] == null
          ? null
          : HardwareCategoryData.fromJson(
            json['hardware_category'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$HardwareSubCategoryDataToJson(
  _HardwareSubCategoryData instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'hardware_category': instance.hardwareCategory,
};

_HardwareCategoryData _$HardwareCategoryDataFromJson(
  Map<String, dynamic> json,
) => _HardwareCategoryData(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
);

Map<String, dynamic> _$HardwareCategoryDataToJson(
  _HardwareCategoryData instance,
) => <String, dynamic>{'id': instance.id, 'name': instance.name};

_ProductImage _$ProductImageFromJson(Map<String, dynamic> json) =>
    _ProductImage(
      id: (json['id'] as num?)?.toInt(),
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$ProductImageToJson(_ProductImage instance) =>
    <String, dynamic>{'id': instance.id, 'image_url': instance.imageUrl};

_ProductBranch _$ProductBranchFromJson(Map<String, dynamic> json) =>
    _ProductBranch(
      branchName: json['branch_name'] as String?,
      hardwareShopId: (json['hardware_shop_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProductBranchToJson(_ProductBranch instance) =>
    <String, dynamic>{
      'branch_name': instance.branchName,
      'hardware_shop_id': instance.hardwareShopId,
    };

_HardwareItemsResponse _$HardwareItemsResponseFromJson(
  Map<String, dynamic> json,
) => _HardwareItemsResponse(
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => HardwareItem.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$HardwareItemsResponseToJson(
  _HardwareItemsResponse instance,
) => <String, dynamic>{'items': instance.items};
