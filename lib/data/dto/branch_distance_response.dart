import 'package:freezed_annotation/freezed_annotation.dart';

part 'branch_distance_response.freezed.dart';
part 'branch_distance_response.g.dart';

@freezed
abstract class BranchDistanceResponse with _$BranchDistanceResponse {
  const factory BranchDistanceResponse({
    bool? success,
    List<BranchDistance>? data,
    int? count,
  }) = _BranchDistanceResponse;

  factory BranchDistanceResponse.fromJson(Map<String, dynamic> json) =>
      _$BranchDistanceResponseFromJson(json);
}

@freezed
abstract class BranchDistance with _$BranchDistance {
  const factory BranchDistance({
    int? id,
    String? name,
    String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'point_coordinates') String? pointCoordinates,
    @J<PERSON><PERSON><PERSON>(name: 'distance_km') double? distanceKm,
    @JsonKey(name: 'hardware_shop') HardwareShopDetails? hardwareShop,
  }) = _BranchDistance;

  factory BranchDistance.fromJson(Map<String, dynamic> json) =>
      _$BranchDistanceFromJson(json);
}

@freezed
abstract class HardwareShopDetails with _$HardwareShopDetails {
  const factory HardwareShopDetails({
    int? id,
    String? name,
    @Json<PERSON><PERSON>(name: 'contact_email') String? contactEmail,
    @JsonKey(name: 'phone_number') String? phoneNumber,
    @JsonKey(name: 'owner_name') String? ownerName,
    @JsonKey(name: 'main_address') String? mainAddress,
    @JsonKey(name: 'created_at') DateTime? createdAt,
    List<String>? images,
    List<String>? emails,
    List<String>? phonenumbers,
  }) = _HardwareShopDetails;

  factory HardwareShopDetails.fromJson(Map<String, dynamic> json) =>
      _$HardwareShopDetailsFromJson(json);
}
