// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_bids_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GetBidResponse _$GetBidResponseFromJson(Map<String, dynamic> json) =>
    _GetBidResponse(
      id: (json['id'] as num?)?.toInt(),
      amount: (json['amount'] as num?)?.toDouble(),
      status: json['status'] as String?,
      createdAt:
          json['created_at'] == null
              ? null
              : DateTime.parse(json['created_at'] as String),
      isSelected: json['is_selected'] as bool?,
      artisan:
          json['artisan'] == null
              ? null
              : ArtisanModel.fromJson(json['artisan'] as Map<String, dynamic>),
      job:
          json['job'] == null
              ? null
              : JobPreviewModel.fromJson(json['job'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetBidResponseToJson(_GetBidResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'status': instance.status,
      'created_at': instance.createdAt?.toIso8601String(),
      'is_selected': instance.isSelected,
      'artisan': instance.artisan,
      'job': instance.job,
    };

_ArtisanModel _$ArtisanModelFromJson(Map<String, dynamic> json) =>
    _ArtisanModel(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      avatarUrl: json['avatar'] as String?,
      about: json['about'] as String?,
    );

Map<String, dynamic> _$ArtisanModelToJson(_ArtisanModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatar': instance.avatarUrl,
      'about': instance.about,
    };

_JobPreviewModel _$JobPreviewModelFromJson(Map<String, dynamic> json) =>
    _JobPreviewModel(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      budget: (json['budget'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$JobPreviewModelToJson(_JobPreviewModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'budget': instance.budget,
    };
