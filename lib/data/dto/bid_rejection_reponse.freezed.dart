// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bid_rejection_reponse.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BidRejectionResponse {

 bool? get success;@JsonKey(name: 'job_id') int? get bidId;@JsonKey(name: 'artisan_id') int? get artisanId;@JsonKey(name: 'status') String? get newStatus; String? get errorMessage;
/// Create a copy of BidRejectionResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BidRejectionResponseCopyWith<BidRejectionResponse> get copyWith => _$BidRejectionResponseCopyWithImpl<BidRejectionResponse>(this as BidRejectionResponse, _$identity);

  /// Serializes this BidRejectionResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BidRejectionResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.bidId, bidId) || other.bidId == bidId)&&(identical(other.artisanId, artisanId) || other.artisanId == artisanId)&&(identical(other.newStatus, newStatus) || other.newStatus == newStatus)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,bidId,artisanId,newStatus,errorMessage);

@override
String toString() {
  return 'BidRejectionResponse(success: $success, bidId: $bidId, artisanId: $artisanId, newStatus: $newStatus, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $BidRejectionResponseCopyWith<$Res>  {
  factory $BidRejectionResponseCopyWith(BidRejectionResponse value, $Res Function(BidRejectionResponse) _then) = _$BidRejectionResponseCopyWithImpl;
@useResult
$Res call({
 bool? success,@JsonKey(name: 'job_id') int? bidId,@JsonKey(name: 'artisan_id') int? artisanId,@JsonKey(name: 'status') String? newStatus, String? errorMessage
});




}
/// @nodoc
class _$BidRejectionResponseCopyWithImpl<$Res>
    implements $BidRejectionResponseCopyWith<$Res> {
  _$BidRejectionResponseCopyWithImpl(this._self, this._then);

  final BidRejectionResponse _self;
  final $Res Function(BidRejectionResponse) _then;

/// Create a copy of BidRejectionResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = freezed,Object? bidId = freezed,Object? artisanId = freezed,Object? newStatus = freezed,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool?,bidId: freezed == bidId ? _self.bidId : bidId // ignore: cast_nullable_to_non_nullable
as int?,artisanId: freezed == artisanId ? _self.artisanId : artisanId // ignore: cast_nullable_to_non_nullable
as int?,newStatus: freezed == newStatus ? _self.newStatus : newStatus // ignore: cast_nullable_to_non_nullable
as String?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BidRejectionResponse implements BidRejectionResponse {
  const _BidRejectionResponse({this.success, @JsonKey(name: 'job_id') this.bidId, @JsonKey(name: 'artisan_id') this.artisanId, @JsonKey(name: 'status') this.newStatus, this.errorMessage});
  factory _BidRejectionResponse.fromJson(Map<String, dynamic> json) => _$BidRejectionResponseFromJson(json);

@override final  bool? success;
@override@JsonKey(name: 'job_id') final  int? bidId;
@override@JsonKey(name: 'artisan_id') final  int? artisanId;
@override@JsonKey(name: 'status') final  String? newStatus;
@override final  String? errorMessage;

/// Create a copy of BidRejectionResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BidRejectionResponseCopyWith<_BidRejectionResponse> get copyWith => __$BidRejectionResponseCopyWithImpl<_BidRejectionResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BidRejectionResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BidRejectionResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.bidId, bidId) || other.bidId == bidId)&&(identical(other.artisanId, artisanId) || other.artisanId == artisanId)&&(identical(other.newStatus, newStatus) || other.newStatus == newStatus)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,bidId,artisanId,newStatus,errorMessage);

@override
String toString() {
  return 'BidRejectionResponse(success: $success, bidId: $bidId, artisanId: $artisanId, newStatus: $newStatus, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$BidRejectionResponseCopyWith<$Res> implements $BidRejectionResponseCopyWith<$Res> {
  factory _$BidRejectionResponseCopyWith(_BidRejectionResponse value, $Res Function(_BidRejectionResponse) _then) = __$BidRejectionResponseCopyWithImpl;
@override @useResult
$Res call({
 bool? success,@JsonKey(name: 'job_id') int? bidId,@JsonKey(name: 'artisan_id') int? artisanId,@JsonKey(name: 'status') String? newStatus, String? errorMessage
});




}
/// @nodoc
class __$BidRejectionResponseCopyWithImpl<$Res>
    implements _$BidRejectionResponseCopyWith<$Res> {
  __$BidRejectionResponseCopyWithImpl(this._self, this._then);

  final _BidRejectionResponse _self;
  final $Res Function(_BidRejectionResponse) _then;

/// Create a copy of BidRejectionResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = freezed,Object? bidId = freezed,Object? artisanId = freezed,Object? newStatus = freezed,Object? errorMessage = freezed,}) {
  return _then(_BidRejectionResponse(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool?,bidId: freezed == bidId ? _self.bidId : bidId // ignore: cast_nullable_to_non_nullable
as int?,artisanId: freezed == artisanId ? _self.artisanId : artisanId // ignore: cast_nullable_to_non_nullable
as int?,newStatus: freezed == newStatus ? _self.newStatus : newStatus // ignore: cast_nullable_to_non_nullable
as String?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
