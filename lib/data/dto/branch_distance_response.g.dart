// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_distance_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BranchDistanceResponse _$BranchDistanceResponseFromJson(
  Map<String, dynamic> json,
) => _BranchDistanceResponse(
  success: json['success'] as bool?,
  data:
      (json['data'] as List<dynamic>?)
          ?.map((e) => BranchDistance.fromJson(e as Map<String, dynamic>))
          .toList(),
  count: (json['count'] as num?)?.toInt(),
);

Map<String, dynamic> _$BranchDistanceResponseToJson(
  _BranchDistanceResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data,
  'count': instance.count,
};

_BranchDistance _$BranchDistanceFromJson(Map<String, dynamic> json) =>
    _BranchDistance(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      address: json['address'] as String?,
      pointCoordinates: json['point_coordinates'] as String?,
      distanceKm: (json['distance_km'] as num?)?.toDouble(),
      hardwareShop:
          json['hardware_shop'] == null
              ? null
              : HardwareShopDetails.fromJson(
                json['hardware_shop'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$BranchDistanceToJson(_BranchDistance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'point_coordinates': instance.pointCoordinates,
      'distance_km': instance.distanceKm,
      'hardware_shop': instance.hardwareShop,
    };

_HardwareShopDetails _$HardwareShopDetailsFromJson(
  Map<String, dynamic> json,
) => _HardwareShopDetails(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
  contactEmail: json['contact_email'] as String?,
  phoneNumber: json['phone_number'] as String?,
  ownerName: json['owner_name'] as String?,
  mainAddress: json['main_address'] as String?,
  createdAt:
      json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
  images: (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
  emails: (json['emails'] as List<dynamic>?)?.map((e) => e as String).toList(),
  phonenumbers:
      (json['phonenumbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
);

Map<String, dynamic> _$HardwareShopDetailsToJson(
  _HardwareShopDetails instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'contact_email': instance.contactEmail,
  'phone_number': instance.phoneNumber,
  'owner_name': instance.ownerName,
  'main_address': instance.mainAddress,
  'created_at': instance.createdAt?.toIso8601String(),
  'images': instance.images,
  'emails': instance.emails,
  'phonenumbers': instance.phonenumbers,
};
