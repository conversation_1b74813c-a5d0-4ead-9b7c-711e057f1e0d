// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_staff_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BranchStaffResponse _$BranchStaffResponseFromJson(Map<String, dynamic> json) =>
    _BranchStaffResponse(
      name: json['name'] as String?,
      email: json['email'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
      branch:
          json['branch'] == null
              ? null
              : Branch.fromJson(json['branch'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BranchStaffResponseToJson(
  _BranchStaffResponse instance,
) => <String, dynamic>{
  'name': instance.name,
  'email': instance.email,
  'user_id': instance.userId,
  'branch': instance.branch,
};

_Branch _$BranchFromJson(Map<String, dynamic> json) => _Branch(
  id: (json['id'] as num?)?.toInt(),
  name: json['branch_name'] as String?,
  address: json['address'] as String?,
);

Map<String, dynamic> _$BranchToJson(_Branch instance) => <String, dynamic>{
  'id': instance.id,
  'branch_name': instance.name,
  'address': instance.address,
};
