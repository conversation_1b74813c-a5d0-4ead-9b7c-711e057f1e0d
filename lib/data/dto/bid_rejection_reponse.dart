// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'bid_rejection_reponse.freezed.dart';
part 'bid_rejection_reponse.g.dart';

@freezed
abstract class BidRejectionResponse with _$BidRejectionResponse {
  const factory BidRejectionResponse({
    bool? success,
    @JsonKey(name: 'job_id') int? bidId,
    @JsonKey(name: 'artisan_id') int? artisanId,
    @Json<PERSON>ey(name: 'status') String? newStatus,
    String? errorMessage,
  }) = _BidRejectionResponse;

  factory BidRejectionResponse.fromJson(Map<String, dynamic> json) =>
      _$BidRejectionResponseFromJson(json);
}
