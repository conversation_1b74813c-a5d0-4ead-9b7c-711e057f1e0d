// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_icons_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServiceIconsResponse {

 int? get id; String? get name; String? get icon;
/// Create a copy of ServiceIconsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServiceIconsResponseCopyWith<ServiceIconsResponse> get copyWith => _$ServiceIconsResponseCopyWithImpl<ServiceIconsResponse>(this as ServiceIconsResponse, _$identity);

  /// Serializes this ServiceIconsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServiceIconsResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.icon, icon) || other.icon == icon));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,icon);

@override
String toString() {
  return 'ServiceIconsResponse(id: $id, name: $name, icon: $icon)';
}


}

/// @nodoc
abstract mixin class $ServiceIconsResponseCopyWith<$Res>  {
  factory $ServiceIconsResponseCopyWith(ServiceIconsResponse value, $Res Function(ServiceIconsResponse) _then) = _$ServiceIconsResponseCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, String? icon
});




}
/// @nodoc
class _$ServiceIconsResponseCopyWithImpl<$Res>
    implements $ServiceIconsResponseCopyWith<$Res> {
  _$ServiceIconsResponseCopyWithImpl(this._self, this._then);

  final ServiceIconsResponse _self;
  final $Res Function(ServiceIconsResponse) _then;

/// Create a copy of ServiceIconsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? icon = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ServiceIconsResponse implements ServiceIconsResponse {
   _ServiceIconsResponse({this.id, this.name, this.icon});
  factory _ServiceIconsResponse.fromJson(Map<String, dynamic> json) => _$ServiceIconsResponseFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  String? icon;

/// Create a copy of ServiceIconsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServiceIconsResponseCopyWith<_ServiceIconsResponse> get copyWith => __$ServiceIconsResponseCopyWithImpl<_ServiceIconsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServiceIconsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServiceIconsResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.icon, icon) || other.icon == icon));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,icon);

@override
String toString() {
  return 'ServiceIconsResponse(id: $id, name: $name, icon: $icon)';
}


}

/// @nodoc
abstract mixin class _$ServiceIconsResponseCopyWith<$Res> implements $ServiceIconsResponseCopyWith<$Res> {
  factory _$ServiceIconsResponseCopyWith(_ServiceIconsResponse value, $Res Function(_ServiceIconsResponse) _then) = __$ServiceIconsResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, String? icon
});




}
/// @nodoc
class __$ServiceIconsResponseCopyWithImpl<$Res>
    implements _$ServiceIconsResponseCopyWith<$Res> {
  __$ServiceIconsResponseCopyWithImpl(this._self, this._then);

  final _ServiceIconsResponse _self;
  final $Res Function(_ServiceIconsResponse) _then;

/// Create a copy of ServiceIconsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? icon = freezed,}) {
  return _then(_ServiceIconsResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
