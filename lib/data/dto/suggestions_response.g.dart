// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'suggestions_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SuggestionsResponse _$SuggestionsResponseFromJson(Map<String, dynamic> json) =>
    _SuggestionsResponse(
      suggestions:
          (json['suggestions'] as List<dynamic>?)
              ?.map((e) => Suggestion.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$SuggestionsResponseToJson(
  _SuggestionsResponse instance,
) => <String, dynamic>{'suggestions': instance.suggestions};

_Suggestion _$SuggestionFromJson(Map<String, dynamic> json) => _Suggestion(
  placePrediction:
      json['placePrediction'] == null
          ? null
          : PlacePrediction.fromJson(
            json['placePrediction'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$SuggestionToJson(_Suggestion instance) =>
    <String, dynamic>{'placePrediction': instance.placePrediction};

_PlacePrediction _$PlacePredictionFromJson(Map<String, dynamic> json) =>
    _PlacePrediction(
      place: json['place'] as String?,
      placeId: json['placeId'] as String?,
      text:
          json['text'] == null
              ? null
              : Text.fromJson(json['text'] as Map<String, dynamic>),
      structuredFormat:
          json['structuredFormat'] == null
              ? null
              : StructuredFormat.fromJson(
                json['structuredFormat'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$PlacePredictionToJson(_PlacePrediction instance) =>
    <String, dynamic>{
      'place': instance.place,
      'placeId': instance.placeId,
      'text': instance.text,
      'structuredFormat': instance.structuredFormat,
    };

_Text _$TextFromJson(Map<String, dynamic> json) =>
    _Text(text: json['text'] as String?);

Map<String, dynamic> _$TextToJson(_Text instance) => <String, dynamic>{
  'text': instance.text,
};

_StructuredFormat _$StructuredFormatFromJson(Map<String, dynamic> json) =>
    _StructuredFormat(
      mainText:
          json['mainText'] == null
              ? null
              : MainText.fromJson(json['mainText'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StructuredFormatToJson(_StructuredFormat instance) =>
    <String, dynamic>{'mainText': instance.mainText};

_MainText _$MainTextFromJson(Map<String, dynamic> json) =>
    _MainText(text: json['text'] as String?);

Map<String, dynamic> _$MainTextToJson(_MainText instance) => <String, dynamic>{
  'text': instance.text,
};
