// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'job_details_response.freezed.dart';
part 'job_details_response.g.dart';

@freezed
abstract class JobDetailsResponse with _$JobDetailsResponse {
  factory JobDetailsResponse({
    int? id,
    @Json<PERSON><PERSON>(name: 'client_id') int? clientId,
    @Json<PERSON><PERSON>(name: 'service_id') int? serviceId,
    @Json<PERSON><PERSON>(name: 'job_description') String? jobDescription,
    double? budget,
    String? status,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') String? createdAt,
    @J<PERSON><PERSON><PERSON>(name: 'updated_at') String? updatedAt,
    @Json<PERSON>ey(name: 'service_date') String? serviceDate,
    @J<PERSON><PERSON><PERSON>(name: 'post_date') String? postDate,
    ServiceModel? service,
    ClientModel? client,
    @JsonKey(name: 'job_tags') List<JobTagModel>? jobTags,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'job_images') List<JobImageModel>? jobImages,
    List<BidModel>? bids,
  }) = _JobDetailsResponse;

  factory JobDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$JobDetailsResponseFromJson(json);
}

@freezed
abstract class ServiceModel with _$ServiceModel {
  factory ServiceModel({
    String? name,
  }) = _ServiceModel;

  factory ServiceModel.fromJson(Map<String, dynamic> json) =>
      _$ServiceModelFromJson(json);
}

@freezed
abstract class ClientModel with _$ClientModel {
  factory ClientModel({
    int? id,
    String? name,
    String? avatar,
  }) = _ClientModel;

  factory ClientModel.fromJson(Map<String, dynamic> json) =>
      _$ClientModelFromJson(json);
}

@freezed
abstract class JobTagModel with _$JobTagModel {
  factory JobTagModel({
    @JsonKey(name: 'sub_category_id') int? subCategoryId,
    @JsonKey(name: 'sub_category') SubCategoryModel? subCategory,
  }) = _JobTagModel;

  factory JobTagModel.fromJson(Map<String, dynamic> json) =>
      _$JobTagModelFromJson(json);
}

@freezed
abstract class SubCategoryModel with _$SubCategoryModel {
  factory SubCategoryModel({
    String? name,
  }) = _SubCategoryModel;

  factory SubCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$SubCategoryModelFromJson(json);
}

@freezed
abstract class JobImageModel with _$JobImageModel {
  factory JobImageModel({
    @JsonKey(name: 'image_url') String? imageUrl,
  }) = _JobImageModel;

  factory JobImageModel.fromJson(Map<String, dynamic> json) =>
      _$JobImageModelFromJson(json);
}

@freezed
abstract class BidModel with _$BidModel {
  factory BidModel({
    int? id,
    @JsonKey(name: 'artisan_id') int? artisanId,
    double? amount,
    String? status,
    @JsonKey(name: 'created_at') String? createdAt,
    @JsonKey(name: 'is_selected') bool? isSelected,
    ArtisanModel? artisan,
  }) = _BidModel;

  factory BidModel.fromJson(Map<String, dynamic> json) =>
      _$BidModelFromJson(json);
}

@freezed
abstract class ArtisanModel with _$ArtisanModel {
  factory ArtisanModel({
    String? name,
    String? avatar,
  }) = _ArtisanModel;

  factory ArtisanModel.fromJson(Map<String, dynamic> json) =>
      _$ArtisanModelFromJson(json);
}