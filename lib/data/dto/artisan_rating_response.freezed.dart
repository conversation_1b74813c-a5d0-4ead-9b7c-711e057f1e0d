// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_rating_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ArtisanRatingResponse {

 int? get id; int? get rating; String? get comments;@JsonKey(name: 'created_at') String? get createdAt; ClientModel? get client;
/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanRatingResponseCopyWith<ArtisanRatingResponse> get copyWith => _$ArtisanRatingResponseCopyWithImpl<ArtisanRatingResponse>(this as ArtisanRatingResponse, _$identity);

  /// Serializes this ArtisanRatingResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanRatingResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.client, client) || other.client == client));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,rating,comments,createdAt,client);

@override
String toString() {
  return 'ArtisanRatingResponse(id: $id, rating: $rating, comments: $comments, createdAt: $createdAt, client: $client)';
}


}

/// @nodoc
abstract mixin class $ArtisanRatingResponseCopyWith<$Res>  {
  factory $ArtisanRatingResponseCopyWith(ArtisanRatingResponse value, $Res Function(ArtisanRatingResponse) _then) = _$ArtisanRatingResponseCopyWithImpl;
@useResult
$Res call({
 int? id, int? rating, String? comments,@JsonKey(name: 'created_at') String? createdAt, ClientModel? client
});


$ClientModelCopyWith<$Res>? get client;

}
/// @nodoc
class _$ArtisanRatingResponseCopyWithImpl<$Res>
    implements $ArtisanRatingResponseCopyWith<$Res> {
  _$ArtisanRatingResponseCopyWithImpl(this._self, this._then);

  final ArtisanRatingResponse _self;
  final $Res Function(ArtisanRatingResponse) _then;

/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? rating = freezed,Object? comments = freezed,Object? createdAt = freezed,Object? client = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comments: freezed == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel?,
  ));
}
/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientModelCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ArtisanRatingResponse implements ArtisanRatingResponse {
  const _ArtisanRatingResponse({this.id, this.rating, this.comments, @JsonKey(name: 'created_at') this.createdAt, this.client});
  factory _ArtisanRatingResponse.fromJson(Map<String, dynamic> json) => _$ArtisanRatingResponseFromJson(json);

@override final  int? id;
@override final  int? rating;
@override final  String? comments;
@override@JsonKey(name: 'created_at') final  String? createdAt;
@override final  ClientModel? client;

/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanRatingResponseCopyWith<_ArtisanRatingResponse> get copyWith => __$ArtisanRatingResponseCopyWithImpl<_ArtisanRatingResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanRatingResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanRatingResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.client, client) || other.client == client));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,rating,comments,createdAt,client);

@override
String toString() {
  return 'ArtisanRatingResponse(id: $id, rating: $rating, comments: $comments, createdAt: $createdAt, client: $client)';
}


}

/// @nodoc
abstract mixin class _$ArtisanRatingResponseCopyWith<$Res> implements $ArtisanRatingResponseCopyWith<$Res> {
  factory _$ArtisanRatingResponseCopyWith(_ArtisanRatingResponse value, $Res Function(_ArtisanRatingResponse) _then) = __$ArtisanRatingResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? rating, String? comments,@JsonKey(name: 'created_at') String? createdAt, ClientModel? client
});


@override $ClientModelCopyWith<$Res>? get client;

}
/// @nodoc
class __$ArtisanRatingResponseCopyWithImpl<$Res>
    implements _$ArtisanRatingResponseCopyWith<$Res> {
  __$ArtisanRatingResponseCopyWithImpl(this._self, this._then);

  final _ArtisanRatingResponse _self;
  final $Res Function(_ArtisanRatingResponse) _then;

/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? rating = freezed,Object? comments = freezed,Object? createdAt = freezed,Object? client = freezed,}) {
  return _then(_ArtisanRatingResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comments: freezed == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientModel?,
  ));
}

/// Create a copy of ArtisanRatingResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientModelCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientModelCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
mixin _$ClientModel {

 int? get id; String? get name; String? get avatar;
/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientModelCopyWith<ClientModel> get copyWith => _$ClientModelCopyWithImpl<ClientModel>(this as ClientModel, _$identity);

  /// Serializes this ClientModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatar);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class $ClientModelCopyWith<$Res>  {
  factory $ClientModelCopyWith(ClientModel value, $Res Function(ClientModel) _then) = _$ClientModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, String? avatar
});




}
/// @nodoc
class _$ClientModelCopyWithImpl<$Res>
    implements $ClientModelCopyWith<$Res> {
  _$ClientModelCopyWithImpl(this._self, this._then);

  final ClientModel _self;
  final $Res Function(ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ClientModel implements ClientModel {
  const _ClientModel({this.id, this.name, this.avatar});
  factory _ClientModel.fromJson(Map<String, dynamic> json) => _$ClientModelFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  String? avatar;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientModelCopyWith<_ClientModel> get copyWith => __$ClientModelCopyWithImpl<_ClientModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatar);

@override
String toString() {
  return 'ClientModel(id: $id, name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class _$ClientModelCopyWith<$Res> implements $ClientModelCopyWith<$Res> {
  factory _$ClientModelCopyWith(_ClientModel value, $Res Function(_ClientModel) _then) = __$ClientModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, String? avatar
});




}
/// @nodoc
class __$ClientModelCopyWithImpl<$Res>
    implements _$ClientModelCopyWith<$Res> {
  __$ClientModelCopyWithImpl(this._self, this._then);

  final _ClientModel _self;
  final $Res Function(_ClientModel) _then;

/// Create a copy of ClientModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_ClientModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
