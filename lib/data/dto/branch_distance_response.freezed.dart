// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branch_distance_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BranchDistanceResponse {

 bool? get success; List<BranchDistance>? get data; int? get count;
/// Create a copy of BranchDistanceResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchDistanceResponseCopyWith<BranchDistanceResponse> get copyWith => _$BranchDistanceResponseCopyWithImpl<BranchDistanceResponse>(this as BranchDistanceResponse, _$identity);

  /// Serializes this BranchDistanceResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchDistanceResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(data),count);

@override
String toString() {
  return 'BranchDistanceResponse(success: $success, data: $data, count: $count)';
}


}

/// @nodoc
abstract mixin class $BranchDistanceResponseCopyWith<$Res>  {
  factory $BranchDistanceResponseCopyWith(BranchDistanceResponse value, $Res Function(BranchDistanceResponse) _then) = _$BranchDistanceResponseCopyWithImpl;
@useResult
$Res call({
 bool? success, List<BranchDistance>? data, int? count
});




}
/// @nodoc
class _$BranchDistanceResponseCopyWithImpl<$Res>
    implements $BranchDistanceResponseCopyWith<$Res> {
  _$BranchDistanceResponseCopyWithImpl(this._self, this._then);

  final BranchDistanceResponse _self;
  final $Res Function(BranchDistanceResponse) _then;

/// Create a copy of BranchDistanceResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = freezed,Object? data = freezed,Object? count = freezed,}) {
  return _then(_self.copyWith(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<BranchDistance>?,count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BranchDistanceResponse implements BranchDistanceResponse {
  const _BranchDistanceResponse({this.success, final  List<BranchDistance>? data, this.count}): _data = data;
  factory _BranchDistanceResponse.fromJson(Map<String, dynamic> json) => _$BranchDistanceResponseFromJson(json);

@override final  bool? success;
 final  List<BranchDistance>? _data;
@override List<BranchDistance>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? count;

/// Create a copy of BranchDistanceResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchDistanceResponseCopyWith<_BranchDistanceResponse> get copyWith => __$BranchDistanceResponseCopyWithImpl<_BranchDistanceResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchDistanceResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchDistanceResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other._data, _data)&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(_data),count);

@override
String toString() {
  return 'BranchDistanceResponse(success: $success, data: $data, count: $count)';
}


}

/// @nodoc
abstract mixin class _$BranchDistanceResponseCopyWith<$Res> implements $BranchDistanceResponseCopyWith<$Res> {
  factory _$BranchDistanceResponseCopyWith(_BranchDistanceResponse value, $Res Function(_BranchDistanceResponse) _then) = __$BranchDistanceResponseCopyWithImpl;
@override @useResult
$Res call({
 bool? success, List<BranchDistance>? data, int? count
});




}
/// @nodoc
class __$BranchDistanceResponseCopyWithImpl<$Res>
    implements _$BranchDistanceResponseCopyWith<$Res> {
  __$BranchDistanceResponseCopyWithImpl(this._self, this._then);

  final _BranchDistanceResponse _self;
  final $Res Function(_BranchDistanceResponse) _then;

/// Create a copy of BranchDistanceResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = freezed,Object? data = freezed,Object? count = freezed,}) {
  return _then(_BranchDistanceResponse(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool?,data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<BranchDistance>?,count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$BranchDistance {

 int? get id; String? get name; String? get address;@JsonKey(name: 'point_coordinates') String? get pointCoordinates;@JsonKey(name: 'distance_km') double? get distanceKm;@JsonKey(name: 'hardware_shop') HardwareShopDetails? get hardwareShop;
/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchDistanceCopyWith<BranchDistance> get copyWith => _$BranchDistanceCopyWithImpl<BranchDistance>(this as BranchDistance, _$identity);

  /// Serializes this BranchDistance to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchDistance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address)&&(identical(other.pointCoordinates, pointCoordinates) || other.pointCoordinates == pointCoordinates)&&(identical(other.distanceKm, distanceKm) || other.distanceKm == distanceKm)&&(identical(other.hardwareShop, hardwareShop) || other.hardwareShop == hardwareShop));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address,pointCoordinates,distanceKm,hardwareShop);

@override
String toString() {
  return 'BranchDistance(id: $id, name: $name, address: $address, pointCoordinates: $pointCoordinates, distanceKm: $distanceKm, hardwareShop: $hardwareShop)';
}


}

/// @nodoc
abstract mixin class $BranchDistanceCopyWith<$Res>  {
  factory $BranchDistanceCopyWith(BranchDistance value, $Res Function(BranchDistance) _then) = _$BranchDistanceCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, String? address,@JsonKey(name: 'point_coordinates') String? pointCoordinates,@JsonKey(name: 'distance_km') double? distanceKm,@JsonKey(name: 'hardware_shop') HardwareShopDetails? hardwareShop
});


$HardwareShopDetailsCopyWith<$Res>? get hardwareShop;

}
/// @nodoc
class _$BranchDistanceCopyWithImpl<$Res>
    implements $BranchDistanceCopyWith<$Res> {
  _$BranchDistanceCopyWithImpl(this._self, this._then);

  final BranchDistance _self;
  final $Res Function(BranchDistance) _then;

/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? address = freezed,Object? pointCoordinates = freezed,Object? distanceKm = freezed,Object? hardwareShop = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,pointCoordinates: freezed == pointCoordinates ? _self.pointCoordinates : pointCoordinates // ignore: cast_nullable_to_non_nullable
as String?,distanceKm: freezed == distanceKm ? _self.distanceKm : distanceKm // ignore: cast_nullable_to_non_nullable
as double?,hardwareShop: freezed == hardwareShop ? _self.hardwareShop : hardwareShop // ignore: cast_nullable_to_non_nullable
as HardwareShopDetails?,
  ));
}
/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareShopDetailsCopyWith<$Res>? get hardwareShop {
    if (_self.hardwareShop == null) {
    return null;
  }

  return $HardwareShopDetailsCopyWith<$Res>(_self.hardwareShop!, (value) {
    return _then(_self.copyWith(hardwareShop: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _BranchDistance implements BranchDistance {
  const _BranchDistance({this.id, this.name, this.address, @JsonKey(name: 'point_coordinates') this.pointCoordinates, @JsonKey(name: 'distance_km') this.distanceKm, @JsonKey(name: 'hardware_shop') this.hardwareShop});
  factory _BranchDistance.fromJson(Map<String, dynamic> json) => _$BranchDistanceFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  String? address;
@override@JsonKey(name: 'point_coordinates') final  String? pointCoordinates;
@override@JsonKey(name: 'distance_km') final  double? distanceKm;
@override@JsonKey(name: 'hardware_shop') final  HardwareShopDetails? hardwareShop;

/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchDistanceCopyWith<_BranchDistance> get copyWith => __$BranchDistanceCopyWithImpl<_BranchDistance>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchDistanceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchDistance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address)&&(identical(other.pointCoordinates, pointCoordinates) || other.pointCoordinates == pointCoordinates)&&(identical(other.distanceKm, distanceKm) || other.distanceKm == distanceKm)&&(identical(other.hardwareShop, hardwareShop) || other.hardwareShop == hardwareShop));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address,pointCoordinates,distanceKm,hardwareShop);

@override
String toString() {
  return 'BranchDistance(id: $id, name: $name, address: $address, pointCoordinates: $pointCoordinates, distanceKm: $distanceKm, hardwareShop: $hardwareShop)';
}


}

/// @nodoc
abstract mixin class _$BranchDistanceCopyWith<$Res> implements $BranchDistanceCopyWith<$Res> {
  factory _$BranchDistanceCopyWith(_BranchDistance value, $Res Function(_BranchDistance) _then) = __$BranchDistanceCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, String? address,@JsonKey(name: 'point_coordinates') String? pointCoordinates,@JsonKey(name: 'distance_km') double? distanceKm,@JsonKey(name: 'hardware_shop') HardwareShopDetails? hardwareShop
});


@override $HardwareShopDetailsCopyWith<$Res>? get hardwareShop;

}
/// @nodoc
class __$BranchDistanceCopyWithImpl<$Res>
    implements _$BranchDistanceCopyWith<$Res> {
  __$BranchDistanceCopyWithImpl(this._self, this._then);

  final _BranchDistance _self;
  final $Res Function(_BranchDistance) _then;

/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? address = freezed,Object? pointCoordinates = freezed,Object? distanceKm = freezed,Object? hardwareShop = freezed,}) {
  return _then(_BranchDistance(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,pointCoordinates: freezed == pointCoordinates ? _self.pointCoordinates : pointCoordinates // ignore: cast_nullable_to_non_nullable
as String?,distanceKm: freezed == distanceKm ? _self.distanceKm : distanceKm // ignore: cast_nullable_to_non_nullable
as double?,hardwareShop: freezed == hardwareShop ? _self.hardwareShop : hardwareShop // ignore: cast_nullable_to_non_nullable
as HardwareShopDetails?,
  ));
}

/// Create a copy of BranchDistance
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareShopDetailsCopyWith<$Res>? get hardwareShop {
    if (_self.hardwareShop == null) {
    return null;
  }

  return $HardwareShopDetailsCopyWith<$Res>(_self.hardwareShop!, (value) {
    return _then(_self.copyWith(hardwareShop: value));
  });
}
}


/// @nodoc
mixin _$HardwareShopDetails {

 int? get id; String? get name;@JsonKey(name: 'contact_email') String? get contactEmail;@JsonKey(name: 'phone_number') String? get phoneNumber;@JsonKey(name: 'owner_name') String? get ownerName;@JsonKey(name: 'main_address') String? get mainAddress;@JsonKey(name: 'created_at') DateTime? get createdAt; List<String>? get images; List<String>? get emails; List<String>? get phonenumbers;
/// Create a copy of HardwareShopDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareShopDetailsCopyWith<HardwareShopDetails> get copyWith => _$HardwareShopDetailsCopyWithImpl<HardwareShopDetails>(this as HardwareShopDetails, _$identity);

  /// Serializes this HardwareShopDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareShopDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.emails, emails)&&const DeepCollectionEquality().equals(other.phonenumbers, phonenumbers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,contactEmail,phoneNumber,ownerName,mainAddress,createdAt,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(emails),const DeepCollectionEquality().hash(phonenumbers));

@override
String toString() {
  return 'HardwareShopDetails(id: $id, name: $name, contactEmail: $contactEmail, phoneNumber: $phoneNumber, ownerName: $ownerName, mainAddress: $mainAddress, createdAt: $createdAt, images: $images, emails: $emails, phonenumbers: $phonenumbers)';
}


}

/// @nodoc
abstract mixin class $HardwareShopDetailsCopyWith<$Res>  {
  factory $HardwareShopDetailsCopyWith(HardwareShopDetails value, $Res Function(HardwareShopDetails) _then) = _$HardwareShopDetailsCopyWithImpl;
@useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'contact_email') String? contactEmail,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'owner_name') String? ownerName,@JsonKey(name: 'main_address') String? mainAddress,@JsonKey(name: 'created_at') DateTime? createdAt, List<String>? images, List<String>? emails, List<String>? phonenumbers
});




}
/// @nodoc
class _$HardwareShopDetailsCopyWithImpl<$Res>
    implements $HardwareShopDetailsCopyWith<$Res> {
  _$HardwareShopDetailsCopyWithImpl(this._self, this._then);

  final HardwareShopDetails _self;
  final $Res Function(HardwareShopDetails) _then;

/// Create a copy of HardwareShopDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? contactEmail = freezed,Object? phoneNumber = freezed,Object? ownerName = freezed,Object? mainAddress = freezed,Object? createdAt = freezed,Object? images = freezed,Object? emails = freezed,Object? phonenumbers = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,ownerName: freezed == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String?,mainAddress: freezed == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,images: freezed == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,emails: freezed == emails ? _self.emails : emails // ignore: cast_nullable_to_non_nullable
as List<String>?,phonenumbers: freezed == phonenumbers ? _self.phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareShopDetails implements HardwareShopDetails {
  const _HardwareShopDetails({this.id, this.name, @JsonKey(name: 'contact_email') this.contactEmail, @JsonKey(name: 'phone_number') this.phoneNumber, @JsonKey(name: 'owner_name') this.ownerName, @JsonKey(name: 'main_address') this.mainAddress, @JsonKey(name: 'created_at') this.createdAt, final  List<String>? images, final  List<String>? emails, final  List<String>? phonenumbers}): _images = images,_emails = emails,_phonenumbers = phonenumbers;
  factory _HardwareShopDetails.fromJson(Map<String, dynamic> json) => _$HardwareShopDetailsFromJson(json);

@override final  int? id;
@override final  String? name;
@override@JsonKey(name: 'contact_email') final  String? contactEmail;
@override@JsonKey(name: 'phone_number') final  String? phoneNumber;
@override@JsonKey(name: 'owner_name') final  String? ownerName;
@override@JsonKey(name: 'main_address') final  String? mainAddress;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
 final  List<String>? _images;
@override List<String>? get images {
  final value = _images;
  if (value == null) return null;
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _emails;
@override List<String>? get emails {
  final value = _emails;
  if (value == null) return null;
  if (_emails is EqualUnmodifiableListView) return _emails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _phonenumbers;
@override List<String>? get phonenumbers {
  final value = _phonenumbers;
  if (value == null) return null;
  if (_phonenumbers is EqualUnmodifiableListView) return _phonenumbers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of HardwareShopDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareShopDetailsCopyWith<_HardwareShopDetails> get copyWith => __$HardwareShopDetailsCopyWithImpl<_HardwareShopDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareShopDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareShopDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._emails, _emails)&&const DeepCollectionEquality().equals(other._phonenumbers, _phonenumbers));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,contactEmail,phoneNumber,ownerName,mainAddress,createdAt,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_emails),const DeepCollectionEquality().hash(_phonenumbers));

@override
String toString() {
  return 'HardwareShopDetails(id: $id, name: $name, contactEmail: $contactEmail, phoneNumber: $phoneNumber, ownerName: $ownerName, mainAddress: $mainAddress, createdAt: $createdAt, images: $images, emails: $emails, phonenumbers: $phonenumbers)';
}


}

/// @nodoc
abstract mixin class _$HardwareShopDetailsCopyWith<$Res> implements $HardwareShopDetailsCopyWith<$Res> {
  factory _$HardwareShopDetailsCopyWith(_HardwareShopDetails value, $Res Function(_HardwareShopDetails) _then) = __$HardwareShopDetailsCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'contact_email') String? contactEmail,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'owner_name') String? ownerName,@JsonKey(name: 'main_address') String? mainAddress,@JsonKey(name: 'created_at') DateTime? createdAt, List<String>? images, List<String>? emails, List<String>? phonenumbers
});




}
/// @nodoc
class __$HardwareShopDetailsCopyWithImpl<$Res>
    implements _$HardwareShopDetailsCopyWith<$Res> {
  __$HardwareShopDetailsCopyWithImpl(this._self, this._then);

  final _HardwareShopDetails _self;
  final $Res Function(_HardwareShopDetails) _then;

/// Create a copy of HardwareShopDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? contactEmail = freezed,Object? phoneNumber = freezed,Object? ownerName = freezed,Object? mainAddress = freezed,Object? createdAt = freezed,Object? images = freezed,Object? emails = freezed,Object? phonenumbers = freezed,}) {
  return _then(_HardwareShopDetails(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,ownerName: freezed == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String?,mainAddress: freezed == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,images: freezed == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,emails: freezed == emails ? _self._emails : emails // ignore: cast_nullable_to_non_nullable
as List<String>?,phonenumbers: freezed == phonenumbers ? _self._phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}

// dart format on
