// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'suggestions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SuggestionsResponse {

 List<Suggestion>? get suggestions;
/// Create a copy of SuggestionsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SuggestionsResponseCopyWith<SuggestionsResponse> get copyWith => _$SuggestionsResponseCopyWithImpl<SuggestionsResponse>(this as SuggestionsResponse, _$identity);

  /// Serializes this SuggestionsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SuggestionsResponse&&const DeepCollectionEquality().equals(other.suggestions, suggestions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(suggestions));

@override
String toString() {
  return 'SuggestionsResponse(suggestions: $suggestions)';
}


}

/// @nodoc
abstract mixin class $SuggestionsResponseCopyWith<$Res>  {
  factory $SuggestionsResponseCopyWith(SuggestionsResponse value, $Res Function(SuggestionsResponse) _then) = _$SuggestionsResponseCopyWithImpl;
@useResult
$Res call({
 List<Suggestion>? suggestions
});




}
/// @nodoc
class _$SuggestionsResponseCopyWithImpl<$Res>
    implements $SuggestionsResponseCopyWith<$Res> {
  _$SuggestionsResponseCopyWithImpl(this._self, this._then);

  final SuggestionsResponse _self;
  final $Res Function(SuggestionsResponse) _then;

/// Create a copy of SuggestionsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? suggestions = freezed,}) {
  return _then(_self.copyWith(
suggestions: freezed == suggestions ? _self.suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<Suggestion>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SuggestionsResponse implements SuggestionsResponse {
   _SuggestionsResponse({final  List<Suggestion>? suggestions}): _suggestions = suggestions;
  factory _SuggestionsResponse.fromJson(Map<String, dynamic> json) => _$SuggestionsResponseFromJson(json);

 final  List<Suggestion>? _suggestions;
@override List<Suggestion>? get suggestions {
  final value = _suggestions;
  if (value == null) return null;
  if (_suggestions is EqualUnmodifiableListView) return _suggestions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of SuggestionsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SuggestionsResponseCopyWith<_SuggestionsResponse> get copyWith => __$SuggestionsResponseCopyWithImpl<_SuggestionsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SuggestionsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SuggestionsResponse&&const DeepCollectionEquality().equals(other._suggestions, _suggestions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_suggestions));

@override
String toString() {
  return 'SuggestionsResponse(suggestions: $suggestions)';
}


}

/// @nodoc
abstract mixin class _$SuggestionsResponseCopyWith<$Res> implements $SuggestionsResponseCopyWith<$Res> {
  factory _$SuggestionsResponseCopyWith(_SuggestionsResponse value, $Res Function(_SuggestionsResponse) _then) = __$SuggestionsResponseCopyWithImpl;
@override @useResult
$Res call({
 List<Suggestion>? suggestions
});




}
/// @nodoc
class __$SuggestionsResponseCopyWithImpl<$Res>
    implements _$SuggestionsResponseCopyWith<$Res> {
  __$SuggestionsResponseCopyWithImpl(this._self, this._then);

  final _SuggestionsResponse _self;
  final $Res Function(_SuggestionsResponse) _then;

/// Create a copy of SuggestionsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? suggestions = freezed,}) {
  return _then(_SuggestionsResponse(
suggestions: freezed == suggestions ? _self._suggestions : suggestions // ignore: cast_nullable_to_non_nullable
as List<Suggestion>?,
  ));
}


}


/// @nodoc
mixin _$Suggestion {

 PlacePrediction? get placePrediction;
/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SuggestionCopyWith<Suggestion> get copyWith => _$SuggestionCopyWithImpl<Suggestion>(this as Suggestion, _$identity);

  /// Serializes this Suggestion to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Suggestion&&(identical(other.placePrediction, placePrediction) || other.placePrediction == placePrediction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,placePrediction);

@override
String toString() {
  return 'Suggestion(placePrediction: $placePrediction)';
}


}

/// @nodoc
abstract mixin class $SuggestionCopyWith<$Res>  {
  factory $SuggestionCopyWith(Suggestion value, $Res Function(Suggestion) _then) = _$SuggestionCopyWithImpl;
@useResult
$Res call({
 PlacePrediction? placePrediction
});


$PlacePredictionCopyWith<$Res>? get placePrediction;

}
/// @nodoc
class _$SuggestionCopyWithImpl<$Res>
    implements $SuggestionCopyWith<$Res> {
  _$SuggestionCopyWithImpl(this._self, this._then);

  final Suggestion _self;
  final $Res Function(Suggestion) _then;

/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? placePrediction = freezed,}) {
  return _then(_self.copyWith(
placePrediction: freezed == placePrediction ? _self.placePrediction : placePrediction // ignore: cast_nullable_to_non_nullable
as PlacePrediction?,
  ));
}
/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PlacePredictionCopyWith<$Res>? get placePrediction {
    if (_self.placePrediction == null) {
    return null;
  }

  return $PlacePredictionCopyWith<$Res>(_self.placePrediction!, (value) {
    return _then(_self.copyWith(placePrediction: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _Suggestion implements Suggestion {
   _Suggestion({this.placePrediction});
  factory _Suggestion.fromJson(Map<String, dynamic> json) => _$SuggestionFromJson(json);

@override final  PlacePrediction? placePrediction;

/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SuggestionCopyWith<_Suggestion> get copyWith => __$SuggestionCopyWithImpl<_Suggestion>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SuggestionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Suggestion&&(identical(other.placePrediction, placePrediction) || other.placePrediction == placePrediction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,placePrediction);

@override
String toString() {
  return 'Suggestion(placePrediction: $placePrediction)';
}


}

/// @nodoc
abstract mixin class _$SuggestionCopyWith<$Res> implements $SuggestionCopyWith<$Res> {
  factory _$SuggestionCopyWith(_Suggestion value, $Res Function(_Suggestion) _then) = __$SuggestionCopyWithImpl;
@override @useResult
$Res call({
 PlacePrediction? placePrediction
});


@override $PlacePredictionCopyWith<$Res>? get placePrediction;

}
/// @nodoc
class __$SuggestionCopyWithImpl<$Res>
    implements _$SuggestionCopyWith<$Res> {
  __$SuggestionCopyWithImpl(this._self, this._then);

  final _Suggestion _self;
  final $Res Function(_Suggestion) _then;

/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? placePrediction = freezed,}) {
  return _then(_Suggestion(
placePrediction: freezed == placePrediction ? _self.placePrediction : placePrediction // ignore: cast_nullable_to_non_nullable
as PlacePrediction?,
  ));
}

/// Create a copy of Suggestion
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PlacePredictionCopyWith<$Res>? get placePrediction {
    if (_self.placePrediction == null) {
    return null;
  }

  return $PlacePredictionCopyWith<$Res>(_self.placePrediction!, (value) {
    return _then(_self.copyWith(placePrediction: value));
  });
}
}


/// @nodoc
mixin _$PlacePrediction {

 String? get place; String? get placeId; Text? get text; StructuredFormat? get structuredFormat;
/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PlacePredictionCopyWith<PlacePrediction> get copyWith => _$PlacePredictionCopyWithImpl<PlacePrediction>(this as PlacePrediction, _$identity);

  /// Serializes this PlacePrediction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlacePrediction&&(identical(other.place, place) || other.place == place)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.text, text) || other.text == text)&&(identical(other.structuredFormat, structuredFormat) || other.structuredFormat == structuredFormat));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,place,placeId,text,structuredFormat);

@override
String toString() {
  return 'PlacePrediction(place: $place, placeId: $placeId, text: $text, structuredFormat: $structuredFormat)';
}


}

/// @nodoc
abstract mixin class $PlacePredictionCopyWith<$Res>  {
  factory $PlacePredictionCopyWith(PlacePrediction value, $Res Function(PlacePrediction) _then) = _$PlacePredictionCopyWithImpl;
@useResult
$Res call({
 String? place, String? placeId, Text? text, StructuredFormat? structuredFormat
});


$TextCopyWith<$Res>? get text;$StructuredFormatCopyWith<$Res>? get structuredFormat;

}
/// @nodoc
class _$PlacePredictionCopyWithImpl<$Res>
    implements $PlacePredictionCopyWith<$Res> {
  _$PlacePredictionCopyWithImpl(this._self, this._then);

  final PlacePrediction _self;
  final $Res Function(PlacePrediction) _then;

/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? place = freezed,Object? placeId = freezed,Object? text = freezed,Object? structuredFormat = freezed,}) {
  return _then(_self.copyWith(
place: freezed == place ? _self.place : place // ignore: cast_nullable_to_non_nullable
as String?,placeId: freezed == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String?,text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as Text?,structuredFormat: freezed == structuredFormat ? _self.structuredFormat : structuredFormat // ignore: cast_nullable_to_non_nullable
as StructuredFormat?,
  ));
}
/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TextCopyWith<$Res>? get text {
    if (_self.text == null) {
    return null;
  }

  return $TextCopyWith<$Res>(_self.text!, (value) {
    return _then(_self.copyWith(text: value));
  });
}/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$StructuredFormatCopyWith<$Res>? get structuredFormat {
    if (_self.structuredFormat == null) {
    return null;
  }

  return $StructuredFormatCopyWith<$Res>(_self.structuredFormat!, (value) {
    return _then(_self.copyWith(structuredFormat: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _PlacePrediction implements PlacePrediction {
   _PlacePrediction({this.place, this.placeId, this.text, this.structuredFormat});
  factory _PlacePrediction.fromJson(Map<String, dynamic> json) => _$PlacePredictionFromJson(json);

@override final  String? place;
@override final  String? placeId;
@override final  Text? text;
@override final  StructuredFormat? structuredFormat;

/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PlacePredictionCopyWith<_PlacePrediction> get copyWith => __$PlacePredictionCopyWithImpl<_PlacePrediction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PlacePredictionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PlacePrediction&&(identical(other.place, place) || other.place == place)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.text, text) || other.text == text)&&(identical(other.structuredFormat, structuredFormat) || other.structuredFormat == structuredFormat));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,place,placeId,text,structuredFormat);

@override
String toString() {
  return 'PlacePrediction(place: $place, placeId: $placeId, text: $text, structuredFormat: $structuredFormat)';
}


}

/// @nodoc
abstract mixin class _$PlacePredictionCopyWith<$Res> implements $PlacePredictionCopyWith<$Res> {
  factory _$PlacePredictionCopyWith(_PlacePrediction value, $Res Function(_PlacePrediction) _then) = __$PlacePredictionCopyWithImpl;
@override @useResult
$Res call({
 String? place, String? placeId, Text? text, StructuredFormat? structuredFormat
});


@override $TextCopyWith<$Res>? get text;@override $StructuredFormatCopyWith<$Res>? get structuredFormat;

}
/// @nodoc
class __$PlacePredictionCopyWithImpl<$Res>
    implements _$PlacePredictionCopyWith<$Res> {
  __$PlacePredictionCopyWithImpl(this._self, this._then);

  final _PlacePrediction _self;
  final $Res Function(_PlacePrediction) _then;

/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? place = freezed,Object? placeId = freezed,Object? text = freezed,Object? structuredFormat = freezed,}) {
  return _then(_PlacePrediction(
place: freezed == place ? _self.place : place // ignore: cast_nullable_to_non_nullable
as String?,placeId: freezed == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String?,text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as Text?,structuredFormat: freezed == structuredFormat ? _self.structuredFormat : structuredFormat // ignore: cast_nullable_to_non_nullable
as StructuredFormat?,
  ));
}

/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TextCopyWith<$Res>? get text {
    if (_self.text == null) {
    return null;
  }

  return $TextCopyWith<$Res>(_self.text!, (value) {
    return _then(_self.copyWith(text: value));
  });
}/// Create a copy of PlacePrediction
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$StructuredFormatCopyWith<$Res>? get structuredFormat {
    if (_self.structuredFormat == null) {
    return null;
  }

  return $StructuredFormatCopyWith<$Res>(_self.structuredFormat!, (value) {
    return _then(_self.copyWith(structuredFormat: value));
  });
}
}


/// @nodoc
mixin _$Text {

 String? get text;
/// Create a copy of Text
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TextCopyWith<Text> get copyWith => _$TextCopyWithImpl<Text>(this as Text, _$identity);

  /// Serializes this Text to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Text&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text);

@override
String toString() {
  return 'Text(text: $text)';
}


}

/// @nodoc
abstract mixin class $TextCopyWith<$Res>  {
  factory $TextCopyWith(Text value, $Res Function(Text) _then) = _$TextCopyWithImpl;
@useResult
$Res call({
 String? text
});




}
/// @nodoc
class _$TextCopyWithImpl<$Res>
    implements $TextCopyWith<$Res> {
  _$TextCopyWithImpl(this._self, this._then);

  final Text _self;
  final $Res Function(Text) _then;

/// Create a copy of Text
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? text = freezed,}) {
  return _then(_self.copyWith(
text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Text implements Text {
   _Text({this.text});
  factory _Text.fromJson(Map<String, dynamic> json) => _$TextFromJson(json);

@override final  String? text;

/// Create a copy of Text
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TextCopyWith<_Text> get copyWith => __$TextCopyWithImpl<_Text>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TextToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Text&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text);

@override
String toString() {
  return 'Text(text: $text)';
}


}

/// @nodoc
abstract mixin class _$TextCopyWith<$Res> implements $TextCopyWith<$Res> {
  factory _$TextCopyWith(_Text value, $Res Function(_Text) _then) = __$TextCopyWithImpl;
@override @useResult
$Res call({
 String? text
});




}
/// @nodoc
class __$TextCopyWithImpl<$Res>
    implements _$TextCopyWith<$Res> {
  __$TextCopyWithImpl(this._self, this._then);

  final _Text _self;
  final $Res Function(_Text) _then;

/// Create a copy of Text
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? text = freezed,}) {
  return _then(_Text(
text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$StructuredFormat {

 MainText? get mainText;
/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StructuredFormatCopyWith<StructuredFormat> get copyWith => _$StructuredFormatCopyWithImpl<StructuredFormat>(this as StructuredFormat, _$identity);

  /// Serializes this StructuredFormat to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StructuredFormat&&(identical(other.mainText, mainText) || other.mainText == mainText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,mainText);

@override
String toString() {
  return 'StructuredFormat(mainText: $mainText)';
}


}

/// @nodoc
abstract mixin class $StructuredFormatCopyWith<$Res>  {
  factory $StructuredFormatCopyWith(StructuredFormat value, $Res Function(StructuredFormat) _then) = _$StructuredFormatCopyWithImpl;
@useResult
$Res call({
 MainText? mainText
});


$MainTextCopyWith<$Res>? get mainText;

}
/// @nodoc
class _$StructuredFormatCopyWithImpl<$Res>
    implements $StructuredFormatCopyWith<$Res> {
  _$StructuredFormatCopyWithImpl(this._self, this._then);

  final StructuredFormat _self;
  final $Res Function(StructuredFormat) _then;

/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? mainText = freezed,}) {
  return _then(_self.copyWith(
mainText: freezed == mainText ? _self.mainText : mainText // ignore: cast_nullable_to_non_nullable
as MainText?,
  ));
}
/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MainTextCopyWith<$Res>? get mainText {
    if (_self.mainText == null) {
    return null;
  }

  return $MainTextCopyWith<$Res>(_self.mainText!, (value) {
    return _then(_self.copyWith(mainText: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _StructuredFormat implements StructuredFormat {
   _StructuredFormat({this.mainText});
  factory _StructuredFormat.fromJson(Map<String, dynamic> json) => _$StructuredFormatFromJson(json);

@override final  MainText? mainText;

/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StructuredFormatCopyWith<_StructuredFormat> get copyWith => __$StructuredFormatCopyWithImpl<_StructuredFormat>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StructuredFormatToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StructuredFormat&&(identical(other.mainText, mainText) || other.mainText == mainText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,mainText);

@override
String toString() {
  return 'StructuredFormat(mainText: $mainText)';
}


}

/// @nodoc
abstract mixin class _$StructuredFormatCopyWith<$Res> implements $StructuredFormatCopyWith<$Res> {
  factory _$StructuredFormatCopyWith(_StructuredFormat value, $Res Function(_StructuredFormat) _then) = __$StructuredFormatCopyWithImpl;
@override @useResult
$Res call({
 MainText? mainText
});


@override $MainTextCopyWith<$Res>? get mainText;

}
/// @nodoc
class __$StructuredFormatCopyWithImpl<$Res>
    implements _$StructuredFormatCopyWith<$Res> {
  __$StructuredFormatCopyWithImpl(this._self, this._then);

  final _StructuredFormat _self;
  final $Res Function(_StructuredFormat) _then;

/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? mainText = freezed,}) {
  return _then(_StructuredFormat(
mainText: freezed == mainText ? _self.mainText : mainText // ignore: cast_nullable_to_non_nullable
as MainText?,
  ));
}

/// Create a copy of StructuredFormat
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MainTextCopyWith<$Res>? get mainText {
    if (_self.mainText == null) {
    return null;
  }

  return $MainTextCopyWith<$Res>(_self.mainText!, (value) {
    return _then(_self.copyWith(mainText: value));
  });
}
}


/// @nodoc
mixin _$MainText {

 String? get text;
/// Create a copy of MainText
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MainTextCopyWith<MainText> get copyWith => _$MainTextCopyWithImpl<MainText>(this as MainText, _$identity);

  /// Serializes this MainText to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MainText&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text);

@override
String toString() {
  return 'MainText(text: $text)';
}


}

/// @nodoc
abstract mixin class $MainTextCopyWith<$Res>  {
  factory $MainTextCopyWith(MainText value, $Res Function(MainText) _then) = _$MainTextCopyWithImpl;
@useResult
$Res call({
 String? text
});




}
/// @nodoc
class _$MainTextCopyWithImpl<$Res>
    implements $MainTextCopyWith<$Res> {
  _$MainTextCopyWithImpl(this._self, this._then);

  final MainText _self;
  final $Res Function(MainText) _then;

/// Create a copy of MainText
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? text = freezed,}) {
  return _then(_self.copyWith(
text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _MainText implements MainText {
   _MainText({this.text});
  factory _MainText.fromJson(Map<String, dynamic> json) => _$MainTextFromJson(json);

@override final  String? text;

/// Create a copy of MainText
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MainTextCopyWith<_MainText> get copyWith => __$MainTextCopyWithImpl<_MainText>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MainTextToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MainText&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text);

@override
String toString() {
  return 'MainText(text: $text)';
}


}

/// @nodoc
abstract mixin class _$MainTextCopyWith<$Res> implements $MainTextCopyWith<$Res> {
  factory _$MainTextCopyWith(_MainText value, $Res Function(_MainText) _then) = __$MainTextCopyWithImpl;
@override @useResult
$Res call({
 String? text
});




}
/// @nodoc
class __$MainTextCopyWithImpl<$Res>
    implements _$MainTextCopyWith<$Res> {
  __$MainTextCopyWithImpl(this._self, this._then);

  final _MainText _self;
  final $Res Function(_MainText) _then;

/// Create a copy of MainText
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? text = freezed,}) {
  return _then(_MainText(
text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
