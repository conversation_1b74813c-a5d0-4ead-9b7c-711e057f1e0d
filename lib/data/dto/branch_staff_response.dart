import 'package:freezed_annotation/freezed_annotation.dart';

part 'branch_staff_response.freezed.dart';
part 'branch_staff_response.g.dart';

@freezed
abstract class BranchStaffResponse with _$BranchStaffResponse {
  const factory BranchStaffResponse({
    String? name,
    String? email,
    @J<PERSON><PERSON>ey(name: 'user_id') int? userId,
    Branch? branch,
  }) = _BranchStaffResponse;

  factory BranchStaffResponse.fromJson(Map<String, dynamic> json) =>
      _$BranchStaffResponseFromJson(json);
}

@freezed
abstract class Branch with _$Branch {
  const factory Branch({
    int? id,
    @JsonKey(name: 'branch_name') String? name,
    String? address,
  }) = _Branch;

  factory Branch.fromJson(Map<String, dynamic> json) => _$BranchFrom<PERSON>son(json);
}
