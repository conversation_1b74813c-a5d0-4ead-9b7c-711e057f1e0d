// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branch_staff_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BranchStaffResponse {

 String? get name; String? get email;@JsonKey(name: 'user_id') int? get userId; Branch? get branch;
/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchStaffResponseCopyWith<BranchStaffResponse> get copyWith => _$BranchStaffResponseCopyWithImpl<BranchStaffResponse>(this as BranchStaffResponse, _$identity);

  /// Serializes this BranchStaffResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchStaffResponse&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.branch, branch) || other.branch == branch));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,email,userId,branch);

@override
String toString() {
  return 'BranchStaffResponse(name: $name, email: $email, userId: $userId, branch: $branch)';
}


}

/// @nodoc
abstract mixin class $BranchStaffResponseCopyWith<$Res>  {
  factory $BranchStaffResponseCopyWith(BranchStaffResponse value, $Res Function(BranchStaffResponse) _then) = _$BranchStaffResponseCopyWithImpl;
@useResult
$Res call({
 String? name, String? email,@JsonKey(name: 'user_id') int? userId, Branch? branch
});


$BranchCopyWith<$Res>? get branch;

}
/// @nodoc
class _$BranchStaffResponseCopyWithImpl<$Res>
    implements $BranchStaffResponseCopyWith<$Res> {
  _$BranchStaffResponseCopyWithImpl(this._self, this._then);

  final BranchStaffResponse _self;
  final $Res Function(BranchStaffResponse) _then;

/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? email = freezed,Object? userId = freezed,Object? branch = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,branch: freezed == branch ? _self.branch : branch // ignore: cast_nullable_to_non_nullable
as Branch?,
  ));
}
/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BranchCopyWith<$Res>? get branch {
    if (_self.branch == null) {
    return null;
  }

  return $BranchCopyWith<$Res>(_self.branch!, (value) {
    return _then(_self.copyWith(branch: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _BranchStaffResponse implements BranchStaffResponse {
  const _BranchStaffResponse({this.name, this.email, @JsonKey(name: 'user_id') this.userId, this.branch});
  factory _BranchStaffResponse.fromJson(Map<String, dynamic> json) => _$BranchStaffResponseFromJson(json);

@override final  String? name;
@override final  String? email;
@override@JsonKey(name: 'user_id') final  int? userId;
@override final  Branch? branch;

/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchStaffResponseCopyWith<_BranchStaffResponse> get copyWith => __$BranchStaffResponseCopyWithImpl<_BranchStaffResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchStaffResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchStaffResponse&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.branch, branch) || other.branch == branch));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,email,userId,branch);

@override
String toString() {
  return 'BranchStaffResponse(name: $name, email: $email, userId: $userId, branch: $branch)';
}


}

/// @nodoc
abstract mixin class _$BranchStaffResponseCopyWith<$Res> implements $BranchStaffResponseCopyWith<$Res> {
  factory _$BranchStaffResponseCopyWith(_BranchStaffResponse value, $Res Function(_BranchStaffResponse) _then) = __$BranchStaffResponseCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? email,@JsonKey(name: 'user_id') int? userId, Branch? branch
});


@override $BranchCopyWith<$Res>? get branch;

}
/// @nodoc
class __$BranchStaffResponseCopyWithImpl<$Res>
    implements _$BranchStaffResponseCopyWith<$Res> {
  __$BranchStaffResponseCopyWithImpl(this._self, this._then);

  final _BranchStaffResponse _self;
  final $Res Function(_BranchStaffResponse) _then;

/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? email = freezed,Object? userId = freezed,Object? branch = freezed,}) {
  return _then(_BranchStaffResponse(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,branch: freezed == branch ? _self.branch : branch // ignore: cast_nullable_to_non_nullable
as Branch?,
  ));
}

/// Create a copy of BranchStaffResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BranchCopyWith<$Res>? get branch {
    if (_self.branch == null) {
    return null;
  }

  return $BranchCopyWith<$Res>(_self.branch!, (value) {
    return _then(_self.copyWith(branch: value));
  });
}
}


/// @nodoc
mixin _$Branch {

 int? get id;@JsonKey(name: 'branch_name') String? get name; String? get address;
/// Create a copy of Branch
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchCopyWith<Branch> get copyWith => _$BranchCopyWithImpl<Branch>(this as Branch, _$identity);

  /// Serializes this Branch to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Branch&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address);

@override
String toString() {
  return 'Branch(id: $id, name: $name, address: $address)';
}


}

/// @nodoc
abstract mixin class $BranchCopyWith<$Res>  {
  factory $BranchCopyWith(Branch value, $Res Function(Branch) _then) = _$BranchCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'branch_name') String? name, String? address
});




}
/// @nodoc
class _$BranchCopyWithImpl<$Res>
    implements $BranchCopyWith<$Res> {
  _$BranchCopyWithImpl(this._self, this._then);

  final Branch _self;
  final $Res Function(Branch) _then;

/// Create a copy of Branch
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? address = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Branch implements Branch {
  const _Branch({this.id, @JsonKey(name: 'branch_name') this.name, this.address});
  factory _Branch.fromJson(Map<String, dynamic> json) => _$BranchFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'branch_name') final  String? name;
@override final  String? address;

/// Create a copy of Branch
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchCopyWith<_Branch> get copyWith => __$BranchCopyWithImpl<_Branch>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Branch&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address);

@override
String toString() {
  return 'Branch(id: $id, name: $name, address: $address)';
}


}

/// @nodoc
abstract mixin class _$BranchCopyWith<$Res> implements $BranchCopyWith<$Res> {
  factory _$BranchCopyWith(_Branch value, $Res Function(_Branch) _then) = __$BranchCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'branch_name') String? name, String? address
});




}
/// @nodoc
class __$BranchCopyWithImpl<$Res>
    implements _$BranchCopyWith<$Res> {
  __$BranchCopyWithImpl(this._self, this._then);

  final _Branch _self;
  final $Res Function(_Branch) _then;

/// Create a copy of Branch
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? address = freezed,}) {
  return _then(_Branch(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
