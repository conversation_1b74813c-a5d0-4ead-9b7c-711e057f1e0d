import 'package:freezed_annotation/freezed_annotation.dart';

part 'hardware_items_response.freezed.dart';
part 'hardware_items_response.g.dart';

@freezed
abstract class HardwareItem with _$HardwareItem {
  const factory HardwareItem({
    int? id,
    @J<PERSON><PERSON><PERSON>(name: 'branch_id') int? branchId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'hardware_sub_category_id') int? hardwareSubCategoryId,
    String? name,
    String? brand,
    @Json<PERSON>ey(name: 'model_number') String? modelNumber,
    String? description,
    String? sku,
    double? price,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'current_stock') int? currentStock,
    @Json<PERSON>ey(name: 'min_stock_level') int? minStockLevel,
    @Json<PERSON>ey(name: 'unit_type') String? unitType,
    @J<PERSON><PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') DateTime? updatedAt,
    ProductBranch? branch,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'product_images') List<ProductImage>? productImages,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'hardware_sub_category')
    HardwareSubCategoryData? hardwareSubCategory,
  }) = _HardwareItem;

  factory HardwareItem.fromJson(Map<String, dynamic> json) =>
      _$HardwareItemFromJson(json);
}

@freezed
abstract class HardwareSubCategoryData with _$HardwareSubCategoryData {
  const factory HardwareSubCategoryData({
    int? id,
    String? name,
    @JsonKey(name: 'hardware_category') HardwareCategoryData? hardwareCategory,
  }) = _HardwareSubCategoryData;

  factory HardwareSubCategoryData.fromJson(Map<String, dynamic> json) =>
      _$HardwareSubCategoryDataFromJson(json);
}

@freezed
abstract class HardwareCategoryData with _$HardwareCategoryData {
  const factory HardwareCategoryData({int? id,String? name}) = _HardwareCategoryData;

  factory HardwareCategoryData.fromJson(Map<String, dynamic> json) =>
      _$HardwareCategoryDataFromJson(json);
}

@freezed
abstract class ProductImage with _$ProductImage {
  const factory ProductImage({
    int? id,
    @JsonKey(name: 'image_url') String? imageUrl,
  }) = _ProductImage;

  factory ProductImage.fromJson(Map<String, dynamic> json) =>
      _$ProductImageFromJson(json);
}

@freezed
abstract class ProductBranch with _$ProductBranch {
  const factory ProductBranch({
    @JsonKey(name: 'branch_name') String? branchName,
    @JsonKey(name: 'hardware_shop_id') int? hardwareShopId,
  }) = _ProductBranch;

  factory ProductBranch.fromJson(Map<String, dynamic> json) =>
      _$ProductBranchFromJson(json);
}

// Response wrapper for the list
@freezed
abstract class HardwareItemsResponse with _$HardwareItemsResponse {
  const factory HardwareItemsResponse({List<HardwareItem>? items}) =
      _HardwareItemsResponse;

  factory HardwareItemsResponse.fromJson(List<dynamic> json) =>
      HardwareItemsResponse(
        items: json.map((item) => HardwareItem.fromJson(item)).toList(),
      );
}
