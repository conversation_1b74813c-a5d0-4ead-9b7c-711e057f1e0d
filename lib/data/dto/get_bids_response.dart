// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_bids_response.freezed.dart';
part 'get_bids_response.g.dart';

@freezed
abstract class GetBidResponse with _$GetBidResponse {
  const factory GetBidResponse({
    required int? id,
    required double? amount,
    required String? status,
    @JsonKey(name: 'created_at') required DateTime? createdAt,
    @JsonKey(name: 'is_selected') required bool? isSelected,
    required ArtisanModel? artisan,
    required JobPreviewModel? job,
  }) = _GetBidResponse;

  factory GetBidResponse.fromJson(Map<String, dynamic> json) =>
      _$GetBidResponseFromJson(json);
}

@freezed
abstract class ArtisanModel with _$ArtisanModel {
  const factory ArtisanModel({
    required int? id,
    required String? name,
    @JsonKey(name: 'avatar') String? avatarUrl,
    required String? about,
  }) = _ArtisanModel;

  factory ArtisanModel.fromJson(Map<String, dynamic> json) =>
      _$ArtisanModelFromJson(json);
}

@freezed
abstract class JobPreviewModel with _$JobPreviewModel {
  const factory JobPreviewModel({
    required int? id,
    required String? title,
    double? budget,
  }) = _JobPreviewModel;

  factory JobPreviewModel.fromJson(Map<String, dynamic> json) =>
      _$JobPreviewModelFromJson(json);
}