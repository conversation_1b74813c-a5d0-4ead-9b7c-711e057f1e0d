// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_profile_data_response.freezed.dart';
part 'artisan_profile_data_response.g.dart';

@freezed
abstract class ArtisanProfileDataResponse with _$ArtisanProfileDataResponse {
  const factory ArtisanProfileDataResponse({
    int? id,
    String? name,
    String? email,
    String? avatar,
    String? address,
    @JsonKey(name: 'supabase_id') String? supabaseId,
    @Json<PERSON>ey(name: 'whatsapp_number') String? whatsappNumber,
    @Json<PERSON>ey(name: 'is_company') int? isCompany,
    @Json<PERSON>ey(name: 'cover_photo') String? coverPhoto,
    String? about,
    // @Json<PERSON>ey(name: 'artisan_locations') List<ArtisanLocation>? artisanLocations,
    @JsonKey(name: 'artisan_numbers') List<ArtisanNumber>? artisanNumbers,
    List<Specialization>? specializations,
    @Json<PERSON>ey(name: 'artisan_images') List<ArtisanImage>? artisanImages,
    @JsonKey(name: 'artisan_ratings') List<ArtisanRatingData>? artisanRatings
  }) = _ArtisanProfileDataResponse;

  factory ArtisanProfileDataResponse.fromJson(Map<String, dynamic> json) =>
      _$ArtisanProfileDataResponseFromJson(json);
}

// @freezed
// abstract class ArtisanLocation with _$ArtisanLocation {
//   const factory ArtisanLocation({
//     String? address,
//     List<double>? location,
//   }) = _ArtisanLocation;

//   factory ArtisanLocation.fromJson(Map<String, dynamic> json) => _$ArtisanLocationFromJson(json);
// }

@freezed
abstract class ArtisanNumber with _$ArtisanNumber {
  const factory ArtisanNumber({
    String? phonenumber,
    @JsonKey(name: 'second_phonenumber') String? secondPhonenumber,
  }) = _ArtisanNumber;

  factory ArtisanNumber.fromJson(Map<String, dynamic> json) =>
      _$ArtisanNumberFromJson(json);
}

@freezed
abstract class Specialization with _$Specialization {
  const factory Specialization({
    @JsonKey(name: 'service_id') int? serviceId,
    ArtisanServiceType? services,
    @JsonKey(name: 'specialization_tags')
    List<SpecializationTag>? specializationTags,
  }) = _Specialization;

  factory Specialization.fromJson(Map<String, dynamic> json) =>
      _$SpecializationFromJson(json);
}

@freezed
abstract class ArtisanServiceType with _$ArtisanServiceType {
  factory ArtisanServiceType({int? id, String? name}) = _ArtisanServiceType;
  factory ArtisanServiceType.fromJson(Map<String, dynamic> json) =>
      _$ArtisanServiceTypeFromJson(json);
}

@freezed
abstract class SpecializationTag with _$SpecializationTag {
  const factory SpecializationTag({
    @JsonKey(name: 'sub_category_id') int? subCategoryId,
    @JsonKey(name: 'sub_categories') SubCategory? subCategories,
  }) = _SpecializationTag;

  factory SpecializationTag.fromJson(Map<String, dynamic> json) =>
      _$SpecializationTagFromJson(json);
}

@freezed
abstract class SubCategory with _$SubCategory {
  const factory SubCategory({int? id, String? name}) = _SubCategory;

  factory SubCategory.fromJson(Map<String, dynamic> json) =>
      _$SubCategoryFromJson(json);
}

@freezed
abstract class ArtisanImage with _$ArtisanImage {
  const factory ArtisanImage({
    int? id,
    @JsonKey(name: 'image_url') String? imageUrl,
  }) = _ArtisanImage;

  factory ArtisanImage.fromJson(Map<String, dynamic> json) =>
      _$ArtisanImageFromJson(json);
}

@freezed
abstract class ArtisanRatingData with _$ArtisanRatingData {
  factory ArtisanRatingData({
    int? id,
    int? rating,
    String? comments,
    @JsonKey(name: 'created_at') String? createdAt,
    ClientRatingData? client,
  }) = _ArtisanRatingData;
  factory ArtisanRatingData.fromJson(Map<String, dynamic> json) =>
      _$ArtisanRatingDataFromJson(json);
}

@freezed
abstract class ClientRatingData with _$ClientRatingData {
  factory ClientRatingData({String? name, String? avatar}) = _ClientRatingData;
  factory ClientRatingData.fromJson(Map<String, dynamic> json) =>
      _$ClientRatingDataFromJson(json);
}
