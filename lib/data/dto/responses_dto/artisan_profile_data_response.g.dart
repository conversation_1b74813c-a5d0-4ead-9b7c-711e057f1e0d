// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'artisan_profile_data_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ArtisanProfileDataResponse _$ArtisanProfileDataResponseFromJson(
  Map<String, dynamic> json,
) => _ArtisanProfileDataResponse(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
  email: json['email'] as String?,
  avatar: json['avatar'] as String?,
  address: json['address'] as String?,
  supabaseId: json['supabase_id'] as String?,
  whatsappNumber: json['whatsapp_number'] as String?,
  isCompany: (json['is_company'] as num?)?.toInt(),
  coverPhoto: json['cover_photo'] as String?,
  about: json['about'] as String?,
  artisanNumbers:
      (json['artisan_numbers'] as List<dynamic>?)
          ?.map((e) => ArtisanNumber.fromJson(e as Map<String, dynamic>))
          .toList(),
  specializations:
      (json['specializations'] as List<dynamic>?)
          ?.map((e) => Specialization.fromJson(e as Map<String, dynamic>))
          .toList(),
  artisanImages:
      (json['artisan_images'] as List<dynamic>?)
          ?.map((e) => ArtisanImage.fromJson(e as Map<String, dynamic>))
          .toList(),
  artisanRatings:
      (json['artisan_ratings'] as List<dynamic>?)
          ?.map((e) => ArtisanRatingData.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$ArtisanProfileDataResponseToJson(
  _ArtisanProfileDataResponse instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'avatar': instance.avatar,
  'address': instance.address,
  'supabase_id': instance.supabaseId,
  'whatsapp_number': instance.whatsappNumber,
  'is_company': instance.isCompany,
  'cover_photo': instance.coverPhoto,
  'about': instance.about,
  'artisan_numbers': instance.artisanNumbers,
  'specializations': instance.specializations,
  'artisan_images': instance.artisanImages,
  'artisan_ratings': instance.artisanRatings,
};

_ArtisanNumber _$ArtisanNumberFromJson(Map<String, dynamic> json) =>
    _ArtisanNumber(
      phonenumber: json['phonenumber'] as String?,
      secondPhonenumber: json['second_phonenumber'] as String?,
    );

Map<String, dynamic> _$ArtisanNumberToJson(_ArtisanNumber instance) =>
    <String, dynamic>{
      'phonenumber': instance.phonenumber,
      'second_phonenumber': instance.secondPhonenumber,
    };

_Specialization _$SpecializationFromJson(Map<String, dynamic> json) =>
    _Specialization(
      serviceId: (json['service_id'] as num?)?.toInt(),
      services:
          json['services'] == null
              ? null
              : ArtisanServiceType.fromJson(
                json['services'] as Map<String, dynamic>,
              ),
      specializationTags:
          (json['specialization_tags'] as List<dynamic>?)
              ?.map(
                (e) => SpecializationTag.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    );

Map<String, dynamic> _$SpecializationToJson(_Specialization instance) =>
    <String, dynamic>{
      'service_id': instance.serviceId,
      'services': instance.services,
      'specialization_tags': instance.specializationTags,
    };

_ArtisanServiceType _$ArtisanServiceTypeFromJson(Map<String, dynamic> json) =>
    _ArtisanServiceType(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
    );

Map<String, dynamic> _$ArtisanServiceTypeToJson(_ArtisanServiceType instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};

_SpecializationTag _$SpecializationTagFromJson(Map<String, dynamic> json) =>
    _SpecializationTag(
      subCategoryId: (json['sub_category_id'] as num?)?.toInt(),
      subCategories:
          json['sub_categories'] == null
              ? null
              : SubCategory.fromJson(
                json['sub_categories'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$SpecializationTagToJson(_SpecializationTag instance) =>
    <String, dynamic>{
      'sub_category_id': instance.subCategoryId,
      'sub_categories': instance.subCategories,
    };

_SubCategory _$SubCategoryFromJson(Map<String, dynamic> json) => _SubCategory(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
);

Map<String, dynamic> _$SubCategoryToJson(_SubCategory instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};

_ArtisanImage _$ArtisanImageFromJson(Map<String, dynamic> json) =>
    _ArtisanImage(
      id: (json['id'] as num?)?.toInt(),
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$ArtisanImageToJson(_ArtisanImage instance) =>
    <String, dynamic>{'id': instance.id, 'image_url': instance.imageUrl};

_ArtisanRatingData _$ArtisanRatingDataFromJson(Map<String, dynamic> json) =>
    _ArtisanRatingData(
      id: (json['id'] as num?)?.toInt(),
      rating: (json['rating'] as num?)?.toInt(),
      comments: json['comments'] as String?,
      createdAt: json['created_at'] as String?,
      client:
          json['client'] == null
              ? null
              : ClientRatingData.fromJson(
                json['client'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$ArtisanRatingDataToJson(_ArtisanRatingData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rating': instance.rating,
      'comments': instance.comments,
      'created_at': instance.createdAt,
      'client': instance.client,
    };

_ClientRatingData _$ClientRatingDataFromJson(Map<String, dynamic> json) =>
    _ClientRatingData(
      name: json['name'] as String?,
      avatar: json['avatar'] as String?,
    );

Map<String, dynamic> _$ClientRatingDataToJson(_ClientRatingData instance) =>
    <String, dynamic>{'name': instance.name, 'avatar': instance.avatar};
