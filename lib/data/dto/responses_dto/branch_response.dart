// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'branch_response.freezed.dart';
part 'branch_response.g.dart';

@freezed
abstract class BranchResponse with _$BranchResponse {
  factory BranchResponse({
    int? id,
    @Json<PERSON>ey(name: 'branch_name') String? branchName,
    String? address,
    String? city,
    @JsonKey(name: 'branch_phonenumbers')
    List<BranchPhoneNumber>? branchPhonenumbers,
    @J<PERSON><PERSON><PERSON>(name: 'branch_emails') List<BranchEmail>? branchEmails,
    @J<PERSON><PERSON>ey(name: 'branch_users') @Default([]) List<BranchCount> branchCount,
  }) = _BranchResponse;

  factory BranchResponse.fromJson(Map<String, dynamic> json) =>
      _$BranchResponseFromJson(json);
}

@freezed
abstract class BranchCount with _$BranchCount {
  factory BranchCount({int? count}) = _BrancCounte;
  factory BranchCount.fromJson(Map<String, dynamic> json) =>
      _$BranchCountFromJson(json);
}

@freezed
abstract class BranchPhoneNumber with _$BranchPhoneNumber {
  factory BranchPhoneNumber({int? id, String? phonenumber}) =
      _BranchPhoneNumber;

  factory BranchPhoneNumber.fromJson(Map<String, dynamic> json) =>
      _$BranchPhoneNumberFromJson(json);
}

@freezed
abstract class BranchEmail with _$BranchEmail {
  factory BranchEmail({int? id, String? email}) = _BranchEmail;

  factory BranchEmail.fromJson(Map<String, dynamic> json) =>
      _$BranchEmailFromJson(json);
}
