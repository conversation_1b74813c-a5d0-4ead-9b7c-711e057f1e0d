class BranchUserResponse {
  final int? id;
  final String? name;
  final String? email;
  final String? branchName;
  final int? branchId;
  final String? role;
  final DateTime? createdAt;

  BranchUserResponse({
    this.id,
    this.name,
    this.email,
    this.branchName,
    this.branchId,
    this.role,
    this.createdAt,
  });

  factory BranchUserResponse.fromJson(Map<String, dynamic> json) {
    return BranchUserResponse(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      branchName: json['branch_name'],
      branchId: json['branch_id'],
      role: json['role'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }
}
