// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branch_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BranchResponse {

 int? get id;@JsonKey(name: 'branch_name') String? get branchName; String? get address; String? get city;@JsonKey(name: 'branch_phonenumbers') List<BranchPhoneNumber>? get branchPhonenumbers;@JsonKey(name: 'branch_emails') List<BranchEmail>? get branchEmails;@JsonKey(name: 'branch_users') List<BranchCount> get branchCount;
/// Create a copy of BranchResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchResponseCopyWith<BranchResponse> get copyWith => _$BranchResponseCopyWithImpl<BranchResponse>(this as BranchResponse, _$identity);

  /// Serializes this BranchResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&const DeepCollectionEquality().equals(other.branchPhonenumbers, branchPhonenumbers)&&const DeepCollectionEquality().equals(other.branchEmails, branchEmails)&&const DeepCollectionEquality().equals(other.branchCount, branchCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchName,address,city,const DeepCollectionEquality().hash(branchPhonenumbers),const DeepCollectionEquality().hash(branchEmails),const DeepCollectionEquality().hash(branchCount));

@override
String toString() {
  return 'BranchResponse(id: $id, branchName: $branchName, address: $address, city: $city, branchPhonenumbers: $branchPhonenumbers, branchEmails: $branchEmails, branchCount: $branchCount)';
}


}

/// @nodoc
abstract mixin class $BranchResponseCopyWith<$Res>  {
  factory $BranchResponseCopyWith(BranchResponse value, $Res Function(BranchResponse) _then) = _$BranchResponseCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'branch_name') String? branchName, String? address, String? city,@JsonKey(name: 'branch_phonenumbers') List<BranchPhoneNumber>? branchPhonenumbers,@JsonKey(name: 'branch_emails') List<BranchEmail>? branchEmails,@JsonKey(name: 'branch_users') List<BranchCount> branchCount
});




}
/// @nodoc
class _$BranchResponseCopyWithImpl<$Res>
    implements $BranchResponseCopyWith<$Res> {
  _$BranchResponseCopyWithImpl(this._self, this._then);

  final BranchResponse _self;
  final $Res Function(BranchResponse) _then;

/// Create a copy of BranchResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? branchName = freezed,Object? address = freezed,Object? city = freezed,Object? branchPhonenumbers = freezed,Object? branchEmails = freezed,Object? branchCount = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,branchName: freezed == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,branchPhonenumbers: freezed == branchPhonenumbers ? _self.branchPhonenumbers : branchPhonenumbers // ignore: cast_nullable_to_non_nullable
as List<BranchPhoneNumber>?,branchEmails: freezed == branchEmails ? _self.branchEmails : branchEmails // ignore: cast_nullable_to_non_nullable
as List<BranchEmail>?,branchCount: null == branchCount ? _self.branchCount : branchCount // ignore: cast_nullable_to_non_nullable
as List<BranchCount>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BranchResponse implements BranchResponse {
   _BranchResponse({this.id, @JsonKey(name: 'branch_name') this.branchName, this.address, this.city, @JsonKey(name: 'branch_phonenumbers') final  List<BranchPhoneNumber>? branchPhonenumbers, @JsonKey(name: 'branch_emails') final  List<BranchEmail>? branchEmails, @JsonKey(name: 'branch_users') final  List<BranchCount> branchCount = const []}): _branchPhonenumbers = branchPhonenumbers,_branchEmails = branchEmails,_branchCount = branchCount;
  factory _BranchResponse.fromJson(Map<String, dynamic> json) => _$BranchResponseFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'branch_name') final  String? branchName;
@override final  String? address;
@override final  String? city;
 final  List<BranchPhoneNumber>? _branchPhonenumbers;
@override@JsonKey(name: 'branch_phonenumbers') List<BranchPhoneNumber>? get branchPhonenumbers {
  final value = _branchPhonenumbers;
  if (value == null) return null;
  if (_branchPhonenumbers is EqualUnmodifiableListView) return _branchPhonenumbers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<BranchEmail>? _branchEmails;
@override@JsonKey(name: 'branch_emails') List<BranchEmail>? get branchEmails {
  final value = _branchEmails;
  if (value == null) return null;
  if (_branchEmails is EqualUnmodifiableListView) return _branchEmails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<BranchCount> _branchCount;
@override@JsonKey(name: 'branch_users') List<BranchCount> get branchCount {
  if (_branchCount is EqualUnmodifiableListView) return _branchCount;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branchCount);
}


/// Create a copy of BranchResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchResponseCopyWith<_BranchResponse> get copyWith => __$BranchResponseCopyWithImpl<_BranchResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&const DeepCollectionEquality().equals(other._branchPhonenumbers, _branchPhonenumbers)&&const DeepCollectionEquality().equals(other._branchEmails, _branchEmails)&&const DeepCollectionEquality().equals(other._branchCount, _branchCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchName,address,city,const DeepCollectionEquality().hash(_branchPhonenumbers),const DeepCollectionEquality().hash(_branchEmails),const DeepCollectionEquality().hash(_branchCount));

@override
String toString() {
  return 'BranchResponse(id: $id, branchName: $branchName, address: $address, city: $city, branchPhonenumbers: $branchPhonenumbers, branchEmails: $branchEmails, branchCount: $branchCount)';
}


}

/// @nodoc
abstract mixin class _$BranchResponseCopyWith<$Res> implements $BranchResponseCopyWith<$Res> {
  factory _$BranchResponseCopyWith(_BranchResponse value, $Res Function(_BranchResponse) _then) = __$BranchResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'branch_name') String? branchName, String? address, String? city,@JsonKey(name: 'branch_phonenumbers') List<BranchPhoneNumber>? branchPhonenumbers,@JsonKey(name: 'branch_emails') List<BranchEmail>? branchEmails,@JsonKey(name: 'branch_users') List<BranchCount> branchCount
});




}
/// @nodoc
class __$BranchResponseCopyWithImpl<$Res>
    implements _$BranchResponseCopyWith<$Res> {
  __$BranchResponseCopyWithImpl(this._self, this._then);

  final _BranchResponse _self;
  final $Res Function(_BranchResponse) _then;

/// Create a copy of BranchResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? branchName = freezed,Object? address = freezed,Object? city = freezed,Object? branchPhonenumbers = freezed,Object? branchEmails = freezed,Object? branchCount = null,}) {
  return _then(_BranchResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,branchName: freezed == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,branchPhonenumbers: freezed == branchPhonenumbers ? _self._branchPhonenumbers : branchPhonenumbers // ignore: cast_nullable_to_non_nullable
as List<BranchPhoneNumber>?,branchEmails: freezed == branchEmails ? _self._branchEmails : branchEmails // ignore: cast_nullable_to_non_nullable
as List<BranchEmail>?,branchCount: null == branchCount ? _self._branchCount : branchCount // ignore: cast_nullable_to_non_nullable
as List<BranchCount>,
  ));
}


}

BranchCount _$BranchCountFromJson(
  Map<String, dynamic> json
) {
    return _BrancCounte.fromJson(
      json
    );
}

/// @nodoc
mixin _$BranchCount {

 int? get count;
/// Create a copy of BranchCount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchCountCopyWith<BranchCount> get copyWith => _$BranchCountCopyWithImpl<BranchCount>(this as BranchCount, _$identity);

  /// Serializes this BranchCount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchCount&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,count);

@override
String toString() {
  return 'BranchCount(count: $count)';
}


}

/// @nodoc
abstract mixin class $BranchCountCopyWith<$Res>  {
  factory $BranchCountCopyWith(BranchCount value, $Res Function(BranchCount) _then) = _$BranchCountCopyWithImpl;
@useResult
$Res call({
 int? count
});




}
/// @nodoc
class _$BranchCountCopyWithImpl<$Res>
    implements $BranchCountCopyWith<$Res> {
  _$BranchCountCopyWithImpl(this._self, this._then);

  final BranchCount _self;
  final $Res Function(BranchCount) _then;

/// Create a copy of BranchCount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? count = freezed,}) {
  return _then(_self.copyWith(
count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BrancCounte implements BranchCount {
   _BrancCounte({this.count});
  factory _BrancCounte.fromJson(Map<String, dynamic> json) => _$BrancCounteFromJson(json);

@override final  int? count;

/// Create a copy of BranchCount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BrancCounteCopyWith<_BrancCounte> get copyWith => __$BrancCounteCopyWithImpl<_BrancCounte>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BrancCounteToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BrancCounte&&(identical(other.count, count) || other.count == count));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,count);

@override
String toString() {
  return 'BranchCount(count: $count)';
}


}

/// @nodoc
abstract mixin class _$BrancCounteCopyWith<$Res> implements $BranchCountCopyWith<$Res> {
  factory _$BrancCounteCopyWith(_BrancCounte value, $Res Function(_BrancCounte) _then) = __$BrancCounteCopyWithImpl;
@override @useResult
$Res call({
 int? count
});




}
/// @nodoc
class __$BrancCounteCopyWithImpl<$Res>
    implements _$BrancCounteCopyWith<$Res> {
  __$BrancCounteCopyWithImpl(this._self, this._then);

  final _BrancCounte _self;
  final $Res Function(_BrancCounte) _then;

/// Create a copy of BranchCount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? count = freezed,}) {
  return _then(_BrancCounte(
count: freezed == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$BranchPhoneNumber {

 int? get id; String? get phonenumber;
/// Create a copy of BranchPhoneNumber
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchPhoneNumberCopyWith<BranchPhoneNumber> get copyWith => _$BranchPhoneNumberCopyWithImpl<BranchPhoneNumber>(this as BranchPhoneNumber, _$identity);

  /// Serializes this BranchPhoneNumber to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchPhoneNumber&&(identical(other.id, id) || other.id == id)&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,phonenumber);

@override
String toString() {
  return 'BranchPhoneNumber(id: $id, phonenumber: $phonenumber)';
}


}

/// @nodoc
abstract mixin class $BranchPhoneNumberCopyWith<$Res>  {
  factory $BranchPhoneNumberCopyWith(BranchPhoneNumber value, $Res Function(BranchPhoneNumber) _then) = _$BranchPhoneNumberCopyWithImpl;
@useResult
$Res call({
 int? id, String? phonenumber
});




}
/// @nodoc
class _$BranchPhoneNumberCopyWithImpl<$Res>
    implements $BranchPhoneNumberCopyWith<$Res> {
  _$BranchPhoneNumberCopyWithImpl(this._self, this._then);

  final BranchPhoneNumber _self;
  final $Res Function(BranchPhoneNumber) _then;

/// Create a copy of BranchPhoneNumber
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? phonenumber = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,phonenumber: freezed == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BranchPhoneNumber implements BranchPhoneNumber {
   _BranchPhoneNumber({this.id, this.phonenumber});
  factory _BranchPhoneNumber.fromJson(Map<String, dynamic> json) => _$BranchPhoneNumberFromJson(json);

@override final  int? id;
@override final  String? phonenumber;

/// Create a copy of BranchPhoneNumber
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchPhoneNumberCopyWith<_BranchPhoneNumber> get copyWith => __$BranchPhoneNumberCopyWithImpl<_BranchPhoneNumber>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchPhoneNumberToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchPhoneNumber&&(identical(other.id, id) || other.id == id)&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,phonenumber);

@override
String toString() {
  return 'BranchPhoneNumber(id: $id, phonenumber: $phonenumber)';
}


}

/// @nodoc
abstract mixin class _$BranchPhoneNumberCopyWith<$Res> implements $BranchPhoneNumberCopyWith<$Res> {
  factory _$BranchPhoneNumberCopyWith(_BranchPhoneNumber value, $Res Function(_BranchPhoneNumber) _then) = __$BranchPhoneNumberCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? phonenumber
});




}
/// @nodoc
class __$BranchPhoneNumberCopyWithImpl<$Res>
    implements _$BranchPhoneNumberCopyWith<$Res> {
  __$BranchPhoneNumberCopyWithImpl(this._self, this._then);

  final _BranchPhoneNumber _self;
  final $Res Function(_BranchPhoneNumber) _then;

/// Create a copy of BranchPhoneNumber
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? phonenumber = freezed,}) {
  return _then(_BranchPhoneNumber(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,phonenumber: freezed == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$BranchEmail {

 int? get id; String? get email;
/// Create a copy of BranchEmail
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchEmailCopyWith<BranchEmail> get copyWith => _$BranchEmailCopyWithImpl<BranchEmail>(this as BranchEmail, _$identity);

  /// Serializes this BranchEmail to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchEmail&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email);

@override
String toString() {
  return 'BranchEmail(id: $id, email: $email)';
}


}

/// @nodoc
abstract mixin class $BranchEmailCopyWith<$Res>  {
  factory $BranchEmailCopyWith(BranchEmail value, $Res Function(BranchEmail) _then) = _$BranchEmailCopyWithImpl;
@useResult
$Res call({
 int? id, String? email
});




}
/// @nodoc
class _$BranchEmailCopyWithImpl<$Res>
    implements $BranchEmailCopyWith<$Res> {
  _$BranchEmailCopyWithImpl(this._self, this._then);

  final BranchEmail _self;
  final $Res Function(BranchEmail) _then;

/// Create a copy of BranchEmail
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? email = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BranchEmail implements BranchEmail {
   _BranchEmail({this.id, this.email});
  factory _BranchEmail.fromJson(Map<String, dynamic> json) => _$BranchEmailFromJson(json);

@override final  int? id;
@override final  String? email;

/// Create a copy of BranchEmail
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchEmailCopyWith<_BranchEmail> get copyWith => __$BranchEmailCopyWithImpl<_BranchEmail>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchEmailToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchEmail&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email);

@override
String toString() {
  return 'BranchEmail(id: $id, email: $email)';
}


}

/// @nodoc
abstract mixin class _$BranchEmailCopyWith<$Res> implements $BranchEmailCopyWith<$Res> {
  factory _$BranchEmailCopyWith(_BranchEmail value, $Res Function(_BranchEmail) _then) = __$BranchEmailCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? email
});




}
/// @nodoc
class __$BranchEmailCopyWithImpl<$Res>
    implements _$BranchEmailCopyWith<$Res> {
  __$BranchEmailCopyWithImpl(this._self, this._then);

  final _BranchEmail _self;
  final $Res Function(_BranchEmail) _then;

/// Create a copy of BranchEmail
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? email = freezed,}) {
  return _then(_BranchEmail(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
