// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BranchResponse _$BranchResponseFromJson(Map<String, dynamic> json) =>
    _BranchResponse(
      id: (json['id'] as num?)?.toInt(),
      branchName: json['branch_name'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      branchPhonenumbers:
          (json['branch_phonenumbers'] as List<dynamic>?)
              ?.map(
                (e) => BranchPhoneNumber.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      branchEmails:
          (json['branch_emails'] as List<dynamic>?)
              ?.map((e) => BranchEmail.fromJson(e as Map<String, dynamic>))
              .toList(),
      branchCount:
          (json['branch_users'] as List<dynamic>?)
              ?.map((e) => BranchCount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$BranchResponseToJson(_BranchResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'branch_name': instance.branchName,
      'address': instance.address,
      'city': instance.city,
      'branch_phonenumbers': instance.branchPhonenumbers,
      'branch_emails': instance.branchEmails,
      'branch_users': instance.branchCount,
    };

_BrancCounte _$BrancCounteFromJson(Map<String, dynamic> json) =>
    _BrancCounte(count: (json['count'] as num?)?.toInt());

Map<String, dynamic> _$BrancCounteToJson(_BrancCounte instance) =>
    <String, dynamic>{'count': instance.count};

_BranchPhoneNumber _$BranchPhoneNumberFromJson(Map<String, dynamic> json) =>
    _BranchPhoneNumber(
      id: (json['id'] as num?)?.toInt(),
      phonenumber: json['phonenumber'] as String?,
    );

Map<String, dynamic> _$BranchPhoneNumberToJson(_BranchPhoneNumber instance) =>
    <String, dynamic>{'id': instance.id, 'phonenumber': instance.phonenumber};

_BranchEmail _$BranchEmailFromJson(Map<String, dynamic> json) => _BranchEmail(
  id: (json['id'] as num?)?.toInt(),
  email: json['email'] as String?,
);

Map<String, dynamic> _$BranchEmailToJson(_BranchEmail instance) =>
    <String, dynamic>{'id': instance.id, 'email': instance.email};
