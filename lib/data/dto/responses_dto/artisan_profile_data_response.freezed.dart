// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_profile_data_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ArtisanProfileDataResponse {

 int? get id; String? get name; String? get email; String? get avatar; String? get address;@JsonKey(name: 'supabase_id') String? get supabaseId;@JsonKey(name: 'whatsapp_number') String? get whatsappNumber;@JsonKey(name: 'is_company') int? get isCompany;@JsonKey(name: 'cover_photo') String? get coverPhoto; String? get about;// @JsonKey(name: 'artisan_locations') List<ArtisanLocation>? artisanLocations,
@JsonKey(name: 'artisan_numbers') List<ArtisanNumber>? get artisanNumbers; List<Specialization>? get specializations;@JsonKey(name: 'artisan_images') List<ArtisanImage>? get artisanImages;@JsonKey(name: 'artisan_ratings') List<ArtisanRatingData>? get artisanRatings;
/// Create a copy of ArtisanProfileDataResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanProfileDataResponseCopyWith<ArtisanProfileDataResponse> get copyWith => _$ArtisanProfileDataResponseCopyWithImpl<ArtisanProfileDataResponse>(this as ArtisanProfileDataResponse, _$identity);

  /// Serializes this ArtisanProfileDataResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanProfileDataResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&(identical(other.address, address) || other.address == address)&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.isCompany, isCompany) || other.isCompany == isCompany)&&(identical(other.coverPhoto, coverPhoto) || other.coverPhoto == coverPhoto)&&(identical(other.about, about) || other.about == about)&&const DeepCollectionEquality().equals(other.artisanNumbers, artisanNumbers)&&const DeepCollectionEquality().equals(other.specializations, specializations)&&const DeepCollectionEquality().equals(other.artisanImages, artisanImages)&&const DeepCollectionEquality().equals(other.artisanRatings, artisanRatings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,avatar,address,supabaseId,whatsappNumber,isCompany,coverPhoto,about,const DeepCollectionEquality().hash(artisanNumbers),const DeepCollectionEquality().hash(specializations),const DeepCollectionEquality().hash(artisanImages),const DeepCollectionEquality().hash(artisanRatings));

@override
String toString() {
  return 'ArtisanProfileDataResponse(id: $id, name: $name, email: $email, avatar: $avatar, address: $address, supabaseId: $supabaseId, whatsappNumber: $whatsappNumber, isCompany: $isCompany, coverPhoto: $coverPhoto, about: $about, artisanNumbers: $artisanNumbers, specializations: $specializations, artisanImages: $artisanImages, artisanRatings: $artisanRatings)';
}


}

/// @nodoc
abstract mixin class $ArtisanProfileDataResponseCopyWith<$Res>  {
  factory $ArtisanProfileDataResponseCopyWith(ArtisanProfileDataResponse value, $Res Function(ArtisanProfileDataResponse) _then) = _$ArtisanProfileDataResponseCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, String? email, String? avatar, String? address,@JsonKey(name: 'supabase_id') String? supabaseId,@JsonKey(name: 'whatsapp_number') String? whatsappNumber,@JsonKey(name: 'is_company') int? isCompany,@JsonKey(name: 'cover_photo') String? coverPhoto, String? about,@JsonKey(name: 'artisan_numbers') List<ArtisanNumber>? artisanNumbers, List<Specialization>? specializations,@JsonKey(name: 'artisan_images') List<ArtisanImage>? artisanImages,@JsonKey(name: 'artisan_ratings') List<ArtisanRatingData>? artisanRatings
});




}
/// @nodoc
class _$ArtisanProfileDataResponseCopyWithImpl<$Res>
    implements $ArtisanProfileDataResponseCopyWith<$Res> {
  _$ArtisanProfileDataResponseCopyWithImpl(this._self, this._then);

  final ArtisanProfileDataResponse _self;
  final $Res Function(ArtisanProfileDataResponse) _then;

/// Create a copy of ArtisanProfileDataResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? email = freezed,Object? avatar = freezed,Object? address = freezed,Object? supabaseId = freezed,Object? whatsappNumber = freezed,Object? isCompany = freezed,Object? coverPhoto = freezed,Object? about = freezed,Object? artisanNumbers = freezed,Object? specializations = freezed,Object? artisanImages = freezed,Object? artisanRatings = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,supabaseId: freezed == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String?,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,isCompany: freezed == isCompany ? _self.isCompany : isCompany // ignore: cast_nullable_to_non_nullable
as int?,coverPhoto: freezed == coverPhoto ? _self.coverPhoto : coverPhoto // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,artisanNumbers: freezed == artisanNumbers ? _self.artisanNumbers : artisanNumbers // ignore: cast_nullable_to_non_nullable
as List<ArtisanNumber>?,specializations: freezed == specializations ? _self.specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<Specialization>?,artisanImages: freezed == artisanImages ? _self.artisanImages : artisanImages // ignore: cast_nullable_to_non_nullable
as List<ArtisanImage>?,artisanRatings: freezed == artisanRatings ? _self.artisanRatings : artisanRatings // ignore: cast_nullable_to_non_nullable
as List<ArtisanRatingData>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanProfileDataResponse implements ArtisanProfileDataResponse {
  const _ArtisanProfileDataResponse({this.id, this.name, this.email, this.avatar, this.address, @JsonKey(name: 'supabase_id') this.supabaseId, @JsonKey(name: 'whatsapp_number') this.whatsappNumber, @JsonKey(name: 'is_company') this.isCompany, @JsonKey(name: 'cover_photo') this.coverPhoto, this.about, @JsonKey(name: 'artisan_numbers') final  List<ArtisanNumber>? artisanNumbers, final  List<Specialization>? specializations, @JsonKey(name: 'artisan_images') final  List<ArtisanImage>? artisanImages, @JsonKey(name: 'artisan_ratings') final  List<ArtisanRatingData>? artisanRatings}): _artisanNumbers = artisanNumbers,_specializations = specializations,_artisanImages = artisanImages,_artisanRatings = artisanRatings;
  factory _ArtisanProfileDataResponse.fromJson(Map<String, dynamic> json) => _$ArtisanProfileDataResponseFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  String? email;
@override final  String? avatar;
@override final  String? address;
@override@JsonKey(name: 'supabase_id') final  String? supabaseId;
@override@JsonKey(name: 'whatsapp_number') final  String? whatsappNumber;
@override@JsonKey(name: 'is_company') final  int? isCompany;
@override@JsonKey(name: 'cover_photo') final  String? coverPhoto;
@override final  String? about;
// @JsonKey(name: 'artisan_locations') List<ArtisanLocation>? artisanLocations,
 final  List<ArtisanNumber>? _artisanNumbers;
// @JsonKey(name: 'artisan_locations') List<ArtisanLocation>? artisanLocations,
@override@JsonKey(name: 'artisan_numbers') List<ArtisanNumber>? get artisanNumbers {
  final value = _artisanNumbers;
  if (value == null) return null;
  if (_artisanNumbers is EqualUnmodifiableListView) return _artisanNumbers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<Specialization>? _specializations;
@override List<Specialization>? get specializations {
  final value = _specializations;
  if (value == null) return null;
  if (_specializations is EqualUnmodifiableListView) return _specializations;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ArtisanImage>? _artisanImages;
@override@JsonKey(name: 'artisan_images') List<ArtisanImage>? get artisanImages {
  final value = _artisanImages;
  if (value == null) return null;
  if (_artisanImages is EqualUnmodifiableListView) return _artisanImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ArtisanRatingData>? _artisanRatings;
@override@JsonKey(name: 'artisan_ratings') List<ArtisanRatingData>? get artisanRatings {
  final value = _artisanRatings;
  if (value == null) return null;
  if (_artisanRatings is EqualUnmodifiableListView) return _artisanRatings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ArtisanProfileDataResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanProfileDataResponseCopyWith<_ArtisanProfileDataResponse> get copyWith => __$ArtisanProfileDataResponseCopyWithImpl<_ArtisanProfileDataResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanProfileDataResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanProfileDataResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&(identical(other.address, address) || other.address == address)&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.isCompany, isCompany) || other.isCompany == isCompany)&&(identical(other.coverPhoto, coverPhoto) || other.coverPhoto == coverPhoto)&&(identical(other.about, about) || other.about == about)&&const DeepCollectionEquality().equals(other._artisanNumbers, _artisanNumbers)&&const DeepCollectionEquality().equals(other._specializations, _specializations)&&const DeepCollectionEquality().equals(other._artisanImages, _artisanImages)&&const DeepCollectionEquality().equals(other._artisanRatings, _artisanRatings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,avatar,address,supabaseId,whatsappNumber,isCompany,coverPhoto,about,const DeepCollectionEquality().hash(_artisanNumbers),const DeepCollectionEquality().hash(_specializations),const DeepCollectionEquality().hash(_artisanImages),const DeepCollectionEquality().hash(_artisanRatings));

@override
String toString() {
  return 'ArtisanProfileDataResponse(id: $id, name: $name, email: $email, avatar: $avatar, address: $address, supabaseId: $supabaseId, whatsappNumber: $whatsappNumber, isCompany: $isCompany, coverPhoto: $coverPhoto, about: $about, artisanNumbers: $artisanNumbers, specializations: $specializations, artisanImages: $artisanImages, artisanRatings: $artisanRatings)';
}


}

/// @nodoc
abstract mixin class _$ArtisanProfileDataResponseCopyWith<$Res> implements $ArtisanProfileDataResponseCopyWith<$Res> {
  factory _$ArtisanProfileDataResponseCopyWith(_ArtisanProfileDataResponse value, $Res Function(_ArtisanProfileDataResponse) _then) = __$ArtisanProfileDataResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, String? email, String? avatar, String? address,@JsonKey(name: 'supabase_id') String? supabaseId,@JsonKey(name: 'whatsapp_number') String? whatsappNumber,@JsonKey(name: 'is_company') int? isCompany,@JsonKey(name: 'cover_photo') String? coverPhoto, String? about,@JsonKey(name: 'artisan_numbers') List<ArtisanNumber>? artisanNumbers, List<Specialization>? specializations,@JsonKey(name: 'artisan_images') List<ArtisanImage>? artisanImages,@JsonKey(name: 'artisan_ratings') List<ArtisanRatingData>? artisanRatings
});




}
/// @nodoc
class __$ArtisanProfileDataResponseCopyWithImpl<$Res>
    implements _$ArtisanProfileDataResponseCopyWith<$Res> {
  __$ArtisanProfileDataResponseCopyWithImpl(this._self, this._then);

  final _ArtisanProfileDataResponse _self;
  final $Res Function(_ArtisanProfileDataResponse) _then;

/// Create a copy of ArtisanProfileDataResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? email = freezed,Object? avatar = freezed,Object? address = freezed,Object? supabaseId = freezed,Object? whatsappNumber = freezed,Object? isCompany = freezed,Object? coverPhoto = freezed,Object? about = freezed,Object? artisanNumbers = freezed,Object? specializations = freezed,Object? artisanImages = freezed,Object? artisanRatings = freezed,}) {
  return _then(_ArtisanProfileDataResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,supabaseId: freezed == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String?,whatsappNumber: freezed == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String?,isCompany: freezed == isCompany ? _self.isCompany : isCompany // ignore: cast_nullable_to_non_nullable
as int?,coverPhoto: freezed == coverPhoto ? _self.coverPhoto : coverPhoto // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,artisanNumbers: freezed == artisanNumbers ? _self._artisanNumbers : artisanNumbers // ignore: cast_nullable_to_non_nullable
as List<ArtisanNumber>?,specializations: freezed == specializations ? _self._specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<Specialization>?,artisanImages: freezed == artisanImages ? _self._artisanImages : artisanImages // ignore: cast_nullable_to_non_nullable
as List<ArtisanImage>?,artisanRatings: freezed == artisanRatings ? _self._artisanRatings : artisanRatings // ignore: cast_nullable_to_non_nullable
as List<ArtisanRatingData>?,
  ));
}


}


/// @nodoc
mixin _$ArtisanNumber {

 String? get phonenumber;@JsonKey(name: 'second_phonenumber') String? get secondPhonenumber;
/// Create a copy of ArtisanNumber
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanNumberCopyWith<ArtisanNumber> get copyWith => _$ArtisanNumberCopyWithImpl<ArtisanNumber>(this as ArtisanNumber, _$identity);

  /// Serializes this ArtisanNumber to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanNumber&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber,secondPhonenumber);

@override
String toString() {
  return 'ArtisanNumber(phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber)';
}


}

/// @nodoc
abstract mixin class $ArtisanNumberCopyWith<$Res>  {
  factory $ArtisanNumberCopyWith(ArtisanNumber value, $Res Function(ArtisanNumber) _then) = _$ArtisanNumberCopyWithImpl;
@useResult
$Res call({
 String? phonenumber,@JsonKey(name: 'second_phonenumber') String? secondPhonenumber
});




}
/// @nodoc
class _$ArtisanNumberCopyWithImpl<$Res>
    implements $ArtisanNumberCopyWith<$Res> {
  _$ArtisanNumberCopyWithImpl(this._self, this._then);

  final ArtisanNumber _self;
  final $Res Function(ArtisanNumber) _then;

/// Create a copy of ArtisanNumber
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phonenumber = freezed,Object? secondPhonenumber = freezed,}) {
  return _then(_self.copyWith(
phonenumber: freezed == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String?,secondPhonenumber: freezed == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanNumber implements ArtisanNumber {
  const _ArtisanNumber({this.phonenumber, @JsonKey(name: 'second_phonenumber') this.secondPhonenumber});
  factory _ArtisanNumber.fromJson(Map<String, dynamic> json) => _$ArtisanNumberFromJson(json);

@override final  String? phonenumber;
@override@JsonKey(name: 'second_phonenumber') final  String? secondPhonenumber;

/// Create a copy of ArtisanNumber
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanNumberCopyWith<_ArtisanNumber> get copyWith => __$ArtisanNumberCopyWithImpl<_ArtisanNumber>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanNumberToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanNumber&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber,secondPhonenumber);

@override
String toString() {
  return 'ArtisanNumber(phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber)';
}


}

/// @nodoc
abstract mixin class _$ArtisanNumberCopyWith<$Res> implements $ArtisanNumberCopyWith<$Res> {
  factory _$ArtisanNumberCopyWith(_ArtisanNumber value, $Res Function(_ArtisanNumber) _then) = __$ArtisanNumberCopyWithImpl;
@override @useResult
$Res call({
 String? phonenumber,@JsonKey(name: 'second_phonenumber') String? secondPhonenumber
});




}
/// @nodoc
class __$ArtisanNumberCopyWithImpl<$Res>
    implements _$ArtisanNumberCopyWith<$Res> {
  __$ArtisanNumberCopyWithImpl(this._self, this._then);

  final _ArtisanNumber _self;
  final $Res Function(_ArtisanNumber) _then;

/// Create a copy of ArtisanNumber
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phonenumber = freezed,Object? secondPhonenumber = freezed,}) {
  return _then(_ArtisanNumber(
phonenumber: freezed == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String?,secondPhonenumber: freezed == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$Specialization {

@JsonKey(name: 'service_id') int? get serviceId; ArtisanServiceType? get services;@JsonKey(name: 'specialization_tags') List<SpecializationTag>? get specializationTags;
/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpecializationCopyWith<Specialization> get copyWith => _$SpecializationCopyWithImpl<Specialization>(this as Specialization, _$identity);

  /// Serializes this Specialization to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Specialization&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.services, services) || other.services == services)&&const DeepCollectionEquality().equals(other.specializationTags, specializationTags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serviceId,services,const DeepCollectionEquality().hash(specializationTags));

@override
String toString() {
  return 'Specialization(serviceId: $serviceId, services: $services, specializationTags: $specializationTags)';
}


}

/// @nodoc
abstract mixin class $SpecializationCopyWith<$Res>  {
  factory $SpecializationCopyWith(Specialization value, $Res Function(Specialization) _then) = _$SpecializationCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'service_id') int? serviceId, ArtisanServiceType? services,@JsonKey(name: 'specialization_tags') List<SpecializationTag>? specializationTags
});


$ArtisanServiceTypeCopyWith<$Res>? get services;

}
/// @nodoc
class _$SpecializationCopyWithImpl<$Res>
    implements $SpecializationCopyWith<$Res> {
  _$SpecializationCopyWithImpl(this._self, this._then);

  final Specialization _self;
  final $Res Function(Specialization) _then;

/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? serviceId = freezed,Object? services = freezed,Object? specializationTags = freezed,}) {
  return _then(_self.copyWith(
serviceId: freezed == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int?,services: freezed == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as ArtisanServiceType?,specializationTags: freezed == specializationTags ? _self.specializationTags : specializationTags // ignore: cast_nullable_to_non_nullable
as List<SpecializationTag>?,
  ));
}
/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanServiceTypeCopyWith<$Res>? get services {
    if (_self.services == null) {
    return null;
  }

  return $ArtisanServiceTypeCopyWith<$Res>(_self.services!, (value) {
    return _then(_self.copyWith(services: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _Specialization implements Specialization {
  const _Specialization({@JsonKey(name: 'service_id') this.serviceId, this.services, @JsonKey(name: 'specialization_tags') final  List<SpecializationTag>? specializationTags}): _specializationTags = specializationTags;
  factory _Specialization.fromJson(Map<String, dynamic> json) => _$SpecializationFromJson(json);

@override@JsonKey(name: 'service_id') final  int? serviceId;
@override final  ArtisanServiceType? services;
 final  List<SpecializationTag>? _specializationTags;
@override@JsonKey(name: 'specialization_tags') List<SpecializationTag>? get specializationTags {
  final value = _specializationTags;
  if (value == null) return null;
  if (_specializationTags is EqualUnmodifiableListView) return _specializationTags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpecializationCopyWith<_Specialization> get copyWith => __$SpecializationCopyWithImpl<_Specialization>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SpecializationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Specialization&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.services, services) || other.services == services)&&const DeepCollectionEquality().equals(other._specializationTags, _specializationTags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serviceId,services,const DeepCollectionEquality().hash(_specializationTags));

@override
String toString() {
  return 'Specialization(serviceId: $serviceId, services: $services, specializationTags: $specializationTags)';
}


}

/// @nodoc
abstract mixin class _$SpecializationCopyWith<$Res> implements $SpecializationCopyWith<$Res> {
  factory _$SpecializationCopyWith(_Specialization value, $Res Function(_Specialization) _then) = __$SpecializationCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'service_id') int? serviceId, ArtisanServiceType? services,@JsonKey(name: 'specialization_tags') List<SpecializationTag>? specializationTags
});


@override $ArtisanServiceTypeCopyWith<$Res>? get services;

}
/// @nodoc
class __$SpecializationCopyWithImpl<$Res>
    implements _$SpecializationCopyWith<$Res> {
  __$SpecializationCopyWithImpl(this._self, this._then);

  final _Specialization _self;
  final $Res Function(_Specialization) _then;

/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? serviceId = freezed,Object? services = freezed,Object? specializationTags = freezed,}) {
  return _then(_Specialization(
serviceId: freezed == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int?,services: freezed == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as ArtisanServiceType?,specializationTags: freezed == specializationTags ? _self._specializationTags : specializationTags // ignore: cast_nullable_to_non_nullable
as List<SpecializationTag>?,
  ));
}

/// Create a copy of Specialization
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanServiceTypeCopyWith<$Res>? get services {
    if (_self.services == null) {
    return null;
  }

  return $ArtisanServiceTypeCopyWith<$Res>(_self.services!, (value) {
    return _then(_self.copyWith(services: value));
  });
}
}


/// @nodoc
mixin _$ArtisanServiceType {

 int? get id; String? get name;
/// Create a copy of ArtisanServiceType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanServiceTypeCopyWith<ArtisanServiceType> get copyWith => _$ArtisanServiceTypeCopyWithImpl<ArtisanServiceType>(this as ArtisanServiceType, _$identity);

  /// Serializes this ArtisanServiceType to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanServiceType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'ArtisanServiceType(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $ArtisanServiceTypeCopyWith<$Res>  {
  factory $ArtisanServiceTypeCopyWith(ArtisanServiceType value, $Res Function(ArtisanServiceType) _then) = _$ArtisanServiceTypeCopyWithImpl;
@useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class _$ArtisanServiceTypeCopyWithImpl<$Res>
    implements $ArtisanServiceTypeCopyWith<$Res> {
  _$ArtisanServiceTypeCopyWithImpl(this._self, this._then);

  final ArtisanServiceType _self;
  final $Res Function(ArtisanServiceType) _then;

/// Create a copy of ArtisanServiceType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanServiceType implements ArtisanServiceType {
   _ArtisanServiceType({this.id, this.name});
  factory _ArtisanServiceType.fromJson(Map<String, dynamic> json) => _$ArtisanServiceTypeFromJson(json);

@override final  int? id;
@override final  String? name;

/// Create a copy of ArtisanServiceType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanServiceTypeCopyWith<_ArtisanServiceType> get copyWith => __$ArtisanServiceTypeCopyWithImpl<_ArtisanServiceType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanServiceTypeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanServiceType&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'ArtisanServiceType(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$ArtisanServiceTypeCopyWith<$Res> implements $ArtisanServiceTypeCopyWith<$Res> {
  factory _$ArtisanServiceTypeCopyWith(_ArtisanServiceType value, $Res Function(_ArtisanServiceType) _then) = __$ArtisanServiceTypeCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class __$ArtisanServiceTypeCopyWithImpl<$Res>
    implements _$ArtisanServiceTypeCopyWith<$Res> {
  __$ArtisanServiceTypeCopyWithImpl(this._self, this._then);

  final _ArtisanServiceType _self;
  final $Res Function(_ArtisanServiceType) _then;

/// Create a copy of ArtisanServiceType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_ArtisanServiceType(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$SpecializationTag {

@JsonKey(name: 'sub_category_id') int? get subCategoryId;@JsonKey(name: 'sub_categories') SubCategory? get subCategories;
/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpecializationTagCopyWith<SpecializationTag> get copyWith => _$SpecializationTagCopyWithImpl<SpecializationTag>(this as SpecializationTag, _$identity);

  /// Serializes this SpecializationTag to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SpecializationTag&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId)&&(identical(other.subCategories, subCategories) || other.subCategories == subCategories));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId,subCategories);

@override
String toString() {
  return 'SpecializationTag(subCategoryId: $subCategoryId, subCategories: $subCategories)';
}


}

/// @nodoc
abstract mixin class $SpecializationTagCopyWith<$Res>  {
  factory $SpecializationTagCopyWith(SpecializationTag value, $Res Function(SpecializationTag) _then) = _$SpecializationTagCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'sub_category_id') int? subCategoryId,@JsonKey(name: 'sub_categories') SubCategory? subCategories
});


$SubCategoryCopyWith<$Res>? get subCategories;

}
/// @nodoc
class _$SpecializationTagCopyWithImpl<$Res>
    implements $SpecializationTagCopyWith<$Res> {
  _$SpecializationTagCopyWithImpl(this._self, this._then);

  final SpecializationTag _self;
  final $Res Function(SpecializationTag) _then;

/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? subCategoryId = freezed,Object? subCategories = freezed,}) {
  return _then(_self.copyWith(
subCategoryId: freezed == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int?,subCategories: freezed == subCategories ? _self.subCategories : subCategories // ignore: cast_nullable_to_non_nullable
as SubCategory?,
  ));
}
/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SubCategoryCopyWith<$Res>? get subCategories {
    if (_self.subCategories == null) {
    return null;
  }

  return $SubCategoryCopyWith<$Res>(_self.subCategories!, (value) {
    return _then(_self.copyWith(subCategories: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _SpecializationTag implements SpecializationTag {
  const _SpecializationTag({@JsonKey(name: 'sub_category_id') this.subCategoryId, @JsonKey(name: 'sub_categories') this.subCategories});
  factory _SpecializationTag.fromJson(Map<String, dynamic> json) => _$SpecializationTagFromJson(json);

@override@JsonKey(name: 'sub_category_id') final  int? subCategoryId;
@override@JsonKey(name: 'sub_categories') final  SubCategory? subCategories;

/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpecializationTagCopyWith<_SpecializationTag> get copyWith => __$SpecializationTagCopyWithImpl<_SpecializationTag>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SpecializationTagToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SpecializationTag&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId)&&(identical(other.subCategories, subCategories) || other.subCategories == subCategories));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId,subCategories);

@override
String toString() {
  return 'SpecializationTag(subCategoryId: $subCategoryId, subCategories: $subCategories)';
}


}

/// @nodoc
abstract mixin class _$SpecializationTagCopyWith<$Res> implements $SpecializationTagCopyWith<$Res> {
  factory _$SpecializationTagCopyWith(_SpecializationTag value, $Res Function(_SpecializationTag) _then) = __$SpecializationTagCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'sub_category_id') int? subCategoryId,@JsonKey(name: 'sub_categories') SubCategory? subCategories
});


@override $SubCategoryCopyWith<$Res>? get subCategories;

}
/// @nodoc
class __$SpecializationTagCopyWithImpl<$Res>
    implements _$SpecializationTagCopyWith<$Res> {
  __$SpecializationTagCopyWithImpl(this._self, this._then);

  final _SpecializationTag _self;
  final $Res Function(_SpecializationTag) _then;

/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? subCategoryId = freezed,Object? subCategories = freezed,}) {
  return _then(_SpecializationTag(
subCategoryId: freezed == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int?,subCategories: freezed == subCategories ? _self.subCategories : subCategories // ignore: cast_nullable_to_non_nullable
as SubCategory?,
  ));
}

/// Create a copy of SpecializationTag
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SubCategoryCopyWith<$Res>? get subCategories {
    if (_self.subCategories == null) {
    return null;
  }

  return $SubCategoryCopyWith<$Res>(_self.subCategories!, (value) {
    return _then(_self.copyWith(subCategories: value));
  });
}
}


/// @nodoc
mixin _$SubCategory {

 int? get id; String? get name;
/// Create a copy of SubCategory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubCategoryCopyWith<SubCategory> get copyWith => _$SubCategoryCopyWithImpl<SubCategory>(this as SubCategory, _$identity);

  /// Serializes this SubCategory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubCategory&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'SubCategory(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $SubCategoryCopyWith<$Res>  {
  factory $SubCategoryCopyWith(SubCategory value, $Res Function(SubCategory) _then) = _$SubCategoryCopyWithImpl;
@useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class _$SubCategoryCopyWithImpl<$Res>
    implements $SubCategoryCopyWith<$Res> {
  _$SubCategoryCopyWithImpl(this._self, this._then);

  final SubCategory _self;
  final $Res Function(SubCategory) _then;

/// Create a copy of SubCategory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SubCategory implements SubCategory {
  const _SubCategory({this.id, this.name});
  factory _SubCategory.fromJson(Map<String, dynamic> json) => _$SubCategoryFromJson(json);

@override final  int? id;
@override final  String? name;

/// Create a copy of SubCategory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubCategoryCopyWith<_SubCategory> get copyWith => __$SubCategoryCopyWithImpl<_SubCategory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubCategoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubCategory&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'SubCategory(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$SubCategoryCopyWith<$Res> implements $SubCategoryCopyWith<$Res> {
  factory _$SubCategoryCopyWith(_SubCategory value, $Res Function(_SubCategory) _then) = __$SubCategoryCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class __$SubCategoryCopyWithImpl<$Res>
    implements _$SubCategoryCopyWith<$Res> {
  __$SubCategoryCopyWithImpl(this._self, this._then);

  final _SubCategory _self;
  final $Res Function(_SubCategory) _then;

/// Create a copy of SubCategory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_SubCategory(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ArtisanImage {

 int? get id;@JsonKey(name: 'image_url') String? get imageUrl;
/// Create a copy of ArtisanImage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanImageCopyWith<ArtisanImage> get copyWith => _$ArtisanImageCopyWithImpl<ArtisanImage>(this as ArtisanImage, _$identity);

  /// Serializes this ArtisanImage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ArtisanImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $ArtisanImageCopyWith<$Res>  {
  factory $ArtisanImageCopyWith(ArtisanImage value, $Res Function(ArtisanImage) _then) = _$ArtisanImageCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class _$ArtisanImageCopyWithImpl<$Res>
    implements $ArtisanImageCopyWith<$Res> {
  _$ArtisanImageCopyWithImpl(this._self, this._then);

  final ArtisanImage _self;
  final $Res Function(ArtisanImage) _then;

/// Create a copy of ArtisanImage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanImage implements ArtisanImage {
  const _ArtisanImage({this.id, @JsonKey(name: 'image_url') this.imageUrl});
  factory _ArtisanImage.fromJson(Map<String, dynamic> json) => _$ArtisanImageFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'image_url') final  String? imageUrl;

/// Create a copy of ArtisanImage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanImageCopyWith<_ArtisanImage> get copyWith => __$ArtisanImageCopyWithImpl<_ArtisanImage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanImageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ArtisanImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$ArtisanImageCopyWith<$Res> implements $ArtisanImageCopyWith<$Res> {
  factory _$ArtisanImageCopyWith(_ArtisanImage value, $Res Function(_ArtisanImage) _then) = __$ArtisanImageCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class __$ArtisanImageCopyWithImpl<$Res>
    implements _$ArtisanImageCopyWith<$Res> {
  __$ArtisanImageCopyWithImpl(this._self, this._then);

  final _ArtisanImage _self;
  final $Res Function(_ArtisanImage) _then;

/// Create a copy of ArtisanImage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_ArtisanImage(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ArtisanRatingData {

 int? get id; int? get rating; String? get comments;@JsonKey(name: 'created_at') String? get createdAt; ClientRatingData? get client;
/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanRatingDataCopyWith<ArtisanRatingData> get copyWith => _$ArtisanRatingDataCopyWithImpl<ArtisanRatingData>(this as ArtisanRatingData, _$identity);

  /// Serializes this ArtisanRatingData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanRatingData&&(identical(other.id, id) || other.id == id)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.client, client) || other.client == client));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,rating,comments,createdAt,client);

@override
String toString() {
  return 'ArtisanRatingData(id: $id, rating: $rating, comments: $comments, createdAt: $createdAt, client: $client)';
}


}

/// @nodoc
abstract mixin class $ArtisanRatingDataCopyWith<$Res>  {
  factory $ArtisanRatingDataCopyWith(ArtisanRatingData value, $Res Function(ArtisanRatingData) _then) = _$ArtisanRatingDataCopyWithImpl;
@useResult
$Res call({
 int? id, int? rating, String? comments,@JsonKey(name: 'created_at') String? createdAt, ClientRatingData? client
});


$ClientRatingDataCopyWith<$Res>? get client;

}
/// @nodoc
class _$ArtisanRatingDataCopyWithImpl<$Res>
    implements $ArtisanRatingDataCopyWith<$Res> {
  _$ArtisanRatingDataCopyWithImpl(this._self, this._then);

  final ArtisanRatingData _self;
  final $Res Function(ArtisanRatingData) _then;

/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? rating = freezed,Object? comments = freezed,Object? createdAt = freezed,Object? client = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comments: freezed == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientRatingData?,
  ));
}
/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientRatingDataCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientRatingDataCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ArtisanRatingData implements ArtisanRatingData {
   _ArtisanRatingData({this.id, this.rating, this.comments, @JsonKey(name: 'created_at') this.createdAt, this.client});
  factory _ArtisanRatingData.fromJson(Map<String, dynamic> json) => _$ArtisanRatingDataFromJson(json);

@override final  int? id;
@override final  int? rating;
@override final  String? comments;
@override@JsonKey(name: 'created_at') final  String? createdAt;
@override final  ClientRatingData? client;

/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanRatingDataCopyWith<_ArtisanRatingData> get copyWith => __$ArtisanRatingDataCopyWithImpl<_ArtisanRatingData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanRatingDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanRatingData&&(identical(other.id, id) || other.id == id)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comments, comments) || other.comments == comments)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.client, client) || other.client == client));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,rating,comments,createdAt,client);

@override
String toString() {
  return 'ArtisanRatingData(id: $id, rating: $rating, comments: $comments, createdAt: $createdAt, client: $client)';
}


}

/// @nodoc
abstract mixin class _$ArtisanRatingDataCopyWith<$Res> implements $ArtisanRatingDataCopyWith<$Res> {
  factory _$ArtisanRatingDataCopyWith(_ArtisanRatingData value, $Res Function(_ArtisanRatingData) _then) = __$ArtisanRatingDataCopyWithImpl;
@override @useResult
$Res call({
 int? id, int? rating, String? comments,@JsonKey(name: 'created_at') String? createdAt, ClientRatingData? client
});


@override $ClientRatingDataCopyWith<$Res>? get client;

}
/// @nodoc
class __$ArtisanRatingDataCopyWithImpl<$Res>
    implements _$ArtisanRatingDataCopyWith<$Res> {
  __$ArtisanRatingDataCopyWithImpl(this._self, this._then);

  final _ArtisanRatingData _self;
  final $Res Function(_ArtisanRatingData) _then;

/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? rating = freezed,Object? comments = freezed,Object? createdAt = freezed,Object? client = freezed,}) {
  return _then(_ArtisanRatingData(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as int?,comments: freezed == comments ? _self.comments : comments // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as String?,client: freezed == client ? _self.client : client // ignore: cast_nullable_to_non_nullable
as ClientRatingData?,
  ));
}

/// Create a copy of ArtisanRatingData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientRatingDataCopyWith<$Res>? get client {
    if (_self.client == null) {
    return null;
  }

  return $ClientRatingDataCopyWith<$Res>(_self.client!, (value) {
    return _then(_self.copyWith(client: value));
  });
}
}


/// @nodoc
mixin _$ClientRatingData {

 String? get name; String? get avatar;
/// Create a copy of ClientRatingData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientRatingDataCopyWith<ClientRatingData> get copyWith => _$ClientRatingDataCopyWithImpl<ClientRatingData>(this as ClientRatingData, _$identity);

  /// Serializes this ClientRatingData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientRatingData&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,avatar);

@override
String toString() {
  return 'ClientRatingData(name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class $ClientRatingDataCopyWith<$Res>  {
  factory $ClientRatingDataCopyWith(ClientRatingData value, $Res Function(ClientRatingData) _then) = _$ClientRatingDataCopyWithImpl;
@useResult
$Res call({
 String? name, String? avatar
});




}
/// @nodoc
class _$ClientRatingDataCopyWithImpl<$Res>
    implements $ClientRatingDataCopyWith<$Res> {
  _$ClientRatingDataCopyWithImpl(this._self, this._then);

  final ClientRatingData _self;
  final $Res Function(ClientRatingData) _then;

/// Create a copy of ClientRatingData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ClientRatingData implements ClientRatingData {
   _ClientRatingData({this.name, this.avatar});
  factory _ClientRatingData.fromJson(Map<String, dynamic> json) => _$ClientRatingDataFromJson(json);

@override final  String? name;
@override final  String? avatar;

/// Create a copy of ClientRatingData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientRatingDataCopyWith<_ClientRatingData> get copyWith => __$ClientRatingDataCopyWithImpl<_ClientRatingData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientRatingDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientRatingData&&(identical(other.name, name) || other.name == name)&&(identical(other.avatar, avatar) || other.avatar == avatar));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,avatar);

@override
String toString() {
  return 'ClientRatingData(name: $name, avatar: $avatar)';
}


}

/// @nodoc
abstract mixin class _$ClientRatingDataCopyWith<$Res> implements $ClientRatingDataCopyWith<$Res> {
  factory _$ClientRatingDataCopyWith(_ClientRatingData value, $Res Function(_ClientRatingData) _then) = __$ClientRatingDataCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? avatar
});




}
/// @nodoc
class __$ClientRatingDataCopyWithImpl<$Res>
    implements _$ClientRatingDataCopyWith<$Res> {
  __$ClientRatingDataCopyWithImpl(this._self, this._then);

  final _ClientRatingData _self;
  final $Res Function(_ClientRatingData) _then;

/// Create a copy of ClientRatingData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? avatar = freezed,}) {
  return _then(_ClientRatingData(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatar: freezed == avatar ? _self.avatar : avatar // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
