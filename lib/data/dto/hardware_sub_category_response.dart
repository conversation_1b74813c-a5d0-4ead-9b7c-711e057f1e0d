import 'package:freezed_annotation/freezed_annotation.dart';



part 'hardware_sub_category_response.freezed.dart';
part 'hardware_sub_category_response.g.dart';

@freezed
abstract class HardwareSubCategoryResponse with _$HardwareSubCategoryResponse {
   factory HardwareSubCategoryResponse({
    int? id,
    @Json<PERSON>ey(name: 'hardware_category_id') int? hardwareCategoryId,
    String? name,
  }) = _HardwareSubCategoryResponse;
   factory HardwareSubCategoryResponse.fromJson(Map<String, dynamic> json) => _$HardwareSubCategoryResponseFromJson(json);
}
