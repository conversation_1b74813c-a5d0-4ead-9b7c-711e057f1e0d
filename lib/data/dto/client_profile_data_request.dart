// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'client_profile_data_request.freezed.dart';
part 'client_profile_data_request.g.dart';

@freezed
abstract class ClientProfileDataRequest with _$ClientProfileDataRequest {
  const factory ClientProfileDataRequest({
    required ClientModel client,
    required List<PhoneNumberModel> phonenumbers,
    required LocationModel location,
  }) = _ClientProfileDataRequest;

  factory ClientProfileDataRequest.fromJson(Map<String, dynamic> json) =>
      _$ClientProfileDataRequestFromJson(json);
}

@freezed
abstract class ClientModel with _$ClientModel {
  const factory ClientModel({
     int? id,
    required String name,
    required String email,
    required String avatar,
    String? address,
    @JsonKey(name: 'national_id') String? nationalId,
    @JsonKey(name: 'supabase_id') String? supabaseId,
    @JsonKey(name: 'whatsapp_number') String? whatsappNumber,
    String? phone,
  }) = _ClientModel;

  factory ClientModel.fromJson(Map<String, dynamic> json) =>
      _$ClientModelFromJson(json);
}

@freezed
abstract class PhoneNumberModel with _$PhoneNumberModel {
  const factory PhoneNumberModel({required String phonenumber}) =
      _PhoneNumberModel;

  factory PhoneNumberModel.fromJson(Map<String, dynamic> json) =>
      _$PhoneNumberModelFromJson(json);
}

@freezed
abstract class LocationModel with _$LocationModel {
  const factory LocationModel({
    required double latitude,
    required double longitude,
    required String address,
  }) = _LocationModel;

  factory LocationModel.fromJson(Map<String, dynamic> json) =>
      _$LocationModelFromJson(json);
}
