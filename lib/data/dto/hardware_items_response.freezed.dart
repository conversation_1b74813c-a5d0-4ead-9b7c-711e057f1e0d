// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hardware_items_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HardwareItem {

 int? get id;@JsonKey(name: 'branch_id') int? get branchId;@JsonKey(name: 'hardware_sub_category_id') int? get hardwareSubCategoryId; String? get name; String? get brand;@JsonKey(name: 'model_number') String? get modelNumber; String? get description; String? get sku; double? get price;@JsonKey(name: 'current_stock') int? get currentStock;@JsonKey(name: 'min_stock_level') int? get minStockLevel;@JsonKey(name: 'unit_type') String? get unitType;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt; ProductBranch? get branch;@JsonKey(name: 'product_images') List<ProductImage>? get productImages;@JsonKey(name: 'hardware_sub_category') HardwareSubCategoryData? get hardwareSubCategory;
/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareItemCopyWith<HardwareItem> get copyWith => _$HardwareItemCopyWithImpl<HardwareItem>(this as HardwareItem, _$identity);

  /// Serializes this HardwareItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareItem&&(identical(other.id, id) || other.id == id)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&(identical(other.hardwareSubCategoryId, hardwareSubCategoryId) || other.hardwareSubCategoryId == hardwareSubCategoryId)&&(identical(other.name, name) || other.name == name)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.modelNumber, modelNumber) || other.modelNumber == modelNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.price, price) || other.price == price)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.unitType, unitType) || other.unitType == unitType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.branch, branch) || other.branch == branch)&&const DeepCollectionEquality().equals(other.productImages, productImages)&&(identical(other.hardwareSubCategory, hardwareSubCategory) || other.hardwareSubCategory == hardwareSubCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchId,hardwareSubCategoryId,name,brand,modelNumber,description,sku,price,currentStock,minStockLevel,unitType,createdAt,updatedAt,branch,const DeepCollectionEquality().hash(productImages),hardwareSubCategory);

@override
String toString() {
  return 'HardwareItem(id: $id, branchId: $branchId, hardwareSubCategoryId: $hardwareSubCategoryId, name: $name, brand: $brand, modelNumber: $modelNumber, description: $description, sku: $sku, price: $price, currentStock: $currentStock, minStockLevel: $minStockLevel, unitType: $unitType, createdAt: $createdAt, updatedAt: $updatedAt, branch: $branch, productImages: $productImages, hardwareSubCategory: $hardwareSubCategory)';
}


}

/// @nodoc
abstract mixin class $HardwareItemCopyWith<$Res>  {
  factory $HardwareItemCopyWith(HardwareItem value, $Res Function(HardwareItem) _then) = _$HardwareItemCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'branch_id') int? branchId,@JsonKey(name: 'hardware_sub_category_id') int? hardwareSubCategoryId, String? name, String? brand,@JsonKey(name: 'model_number') String? modelNumber, String? description, String? sku, double? price,@JsonKey(name: 'current_stock') int? currentStock,@JsonKey(name: 'min_stock_level') int? minStockLevel,@JsonKey(name: 'unit_type') String? unitType,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt, ProductBranch? branch,@JsonKey(name: 'product_images') List<ProductImage>? productImages,@JsonKey(name: 'hardware_sub_category') HardwareSubCategoryData? hardwareSubCategory
});


$ProductBranchCopyWith<$Res>? get branch;$HardwareSubCategoryDataCopyWith<$Res>? get hardwareSubCategory;

}
/// @nodoc
class _$HardwareItemCopyWithImpl<$Res>
    implements $HardwareItemCopyWith<$Res> {
  _$HardwareItemCopyWithImpl(this._self, this._then);

  final HardwareItem _self;
  final $Res Function(HardwareItem) _then;

/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? branchId = freezed,Object? hardwareSubCategoryId = freezed,Object? name = freezed,Object? brand = freezed,Object? modelNumber = freezed,Object? description = freezed,Object? sku = freezed,Object? price = freezed,Object? currentStock = freezed,Object? minStockLevel = freezed,Object? unitType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? branch = freezed,Object? productImages = freezed,Object? hardwareSubCategory = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,branchId: freezed == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int?,hardwareSubCategoryId: freezed == hardwareSubCategoryId ? _self.hardwareSubCategoryId : hardwareSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,modelNumber: freezed == modelNumber ? _self.modelNumber : modelNumber // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currentStock: freezed == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int?,minStockLevel: freezed == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int?,unitType: freezed == unitType ? _self.unitType : unitType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,branch: freezed == branch ? _self.branch : branch // ignore: cast_nullable_to_non_nullable
as ProductBranch?,productImages: freezed == productImages ? _self.productImages : productImages // ignore: cast_nullable_to_non_nullable
as List<ProductImage>?,hardwareSubCategory: freezed == hardwareSubCategory ? _self.hardwareSubCategory : hardwareSubCategory // ignore: cast_nullable_to_non_nullable
as HardwareSubCategoryData?,
  ));
}
/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductBranchCopyWith<$Res>? get branch {
    if (_self.branch == null) {
    return null;
  }

  return $ProductBranchCopyWith<$Res>(_self.branch!, (value) {
    return _then(_self.copyWith(branch: value));
  });
}/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryDataCopyWith<$Res>? get hardwareSubCategory {
    if (_self.hardwareSubCategory == null) {
    return null;
  }

  return $HardwareSubCategoryDataCopyWith<$Res>(_self.hardwareSubCategory!, (value) {
    return _then(_self.copyWith(hardwareSubCategory: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _HardwareItem implements HardwareItem {
  const _HardwareItem({this.id, @JsonKey(name: 'branch_id') this.branchId, @JsonKey(name: 'hardware_sub_category_id') this.hardwareSubCategoryId, this.name, this.brand, @JsonKey(name: 'model_number') this.modelNumber, this.description, this.sku, this.price, @JsonKey(name: 'current_stock') this.currentStock, @JsonKey(name: 'min_stock_level') this.minStockLevel, @JsonKey(name: 'unit_type') this.unitType, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, this.branch, @JsonKey(name: 'product_images') final  List<ProductImage>? productImages, @JsonKey(name: 'hardware_sub_category') this.hardwareSubCategory}): _productImages = productImages;
  factory _HardwareItem.fromJson(Map<String, dynamic> json) => _$HardwareItemFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'branch_id') final  int? branchId;
@override@JsonKey(name: 'hardware_sub_category_id') final  int? hardwareSubCategoryId;
@override final  String? name;
@override final  String? brand;
@override@JsonKey(name: 'model_number') final  String? modelNumber;
@override final  String? description;
@override final  String? sku;
@override final  double? price;
@override@JsonKey(name: 'current_stock') final  int? currentStock;
@override@JsonKey(name: 'min_stock_level') final  int? minStockLevel;
@override@JsonKey(name: 'unit_type') final  String? unitType;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override final  ProductBranch? branch;
 final  List<ProductImage>? _productImages;
@override@JsonKey(name: 'product_images') List<ProductImage>? get productImages {
  final value = _productImages;
  if (value == null) return null;
  if (_productImages is EqualUnmodifiableListView) return _productImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(name: 'hardware_sub_category') final  HardwareSubCategoryData? hardwareSubCategory;

/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareItemCopyWith<_HardwareItem> get copyWith => __$HardwareItemCopyWithImpl<_HardwareItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareItem&&(identical(other.id, id) || other.id == id)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&(identical(other.hardwareSubCategoryId, hardwareSubCategoryId) || other.hardwareSubCategoryId == hardwareSubCategoryId)&&(identical(other.name, name) || other.name == name)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.modelNumber, modelNumber) || other.modelNumber == modelNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.price, price) || other.price == price)&&(identical(other.currentStock, currentStock) || other.currentStock == currentStock)&&(identical(other.minStockLevel, minStockLevel) || other.minStockLevel == minStockLevel)&&(identical(other.unitType, unitType) || other.unitType == unitType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.branch, branch) || other.branch == branch)&&const DeepCollectionEquality().equals(other._productImages, _productImages)&&(identical(other.hardwareSubCategory, hardwareSubCategory) || other.hardwareSubCategory == hardwareSubCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,branchId,hardwareSubCategoryId,name,brand,modelNumber,description,sku,price,currentStock,minStockLevel,unitType,createdAt,updatedAt,branch,const DeepCollectionEquality().hash(_productImages),hardwareSubCategory);

@override
String toString() {
  return 'HardwareItem(id: $id, branchId: $branchId, hardwareSubCategoryId: $hardwareSubCategoryId, name: $name, brand: $brand, modelNumber: $modelNumber, description: $description, sku: $sku, price: $price, currentStock: $currentStock, minStockLevel: $minStockLevel, unitType: $unitType, createdAt: $createdAt, updatedAt: $updatedAt, branch: $branch, productImages: $productImages, hardwareSubCategory: $hardwareSubCategory)';
}


}

/// @nodoc
abstract mixin class _$HardwareItemCopyWith<$Res> implements $HardwareItemCopyWith<$Res> {
  factory _$HardwareItemCopyWith(_HardwareItem value, $Res Function(_HardwareItem) _then) = __$HardwareItemCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'branch_id') int? branchId,@JsonKey(name: 'hardware_sub_category_id') int? hardwareSubCategoryId, String? name, String? brand,@JsonKey(name: 'model_number') String? modelNumber, String? description, String? sku, double? price,@JsonKey(name: 'current_stock') int? currentStock,@JsonKey(name: 'min_stock_level') int? minStockLevel,@JsonKey(name: 'unit_type') String? unitType,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt, ProductBranch? branch,@JsonKey(name: 'product_images') List<ProductImage>? productImages,@JsonKey(name: 'hardware_sub_category') HardwareSubCategoryData? hardwareSubCategory
});


@override $ProductBranchCopyWith<$Res>? get branch;@override $HardwareSubCategoryDataCopyWith<$Res>? get hardwareSubCategory;

}
/// @nodoc
class __$HardwareItemCopyWithImpl<$Res>
    implements _$HardwareItemCopyWith<$Res> {
  __$HardwareItemCopyWithImpl(this._self, this._then);

  final _HardwareItem _self;
  final $Res Function(_HardwareItem) _then;

/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? branchId = freezed,Object? hardwareSubCategoryId = freezed,Object? name = freezed,Object? brand = freezed,Object? modelNumber = freezed,Object? description = freezed,Object? sku = freezed,Object? price = freezed,Object? currentStock = freezed,Object? minStockLevel = freezed,Object? unitType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? branch = freezed,Object? productImages = freezed,Object? hardwareSubCategory = freezed,}) {
  return _then(_HardwareItem(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,branchId: freezed == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int?,hardwareSubCategoryId: freezed == hardwareSubCategoryId ? _self.hardwareSubCategoryId : hardwareSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,modelNumber: freezed == modelNumber ? _self.modelNumber : modelNumber // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,currentStock: freezed == currentStock ? _self.currentStock : currentStock // ignore: cast_nullable_to_non_nullable
as int?,minStockLevel: freezed == minStockLevel ? _self.minStockLevel : minStockLevel // ignore: cast_nullable_to_non_nullable
as int?,unitType: freezed == unitType ? _self.unitType : unitType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,branch: freezed == branch ? _self.branch : branch // ignore: cast_nullable_to_non_nullable
as ProductBranch?,productImages: freezed == productImages ? _self._productImages : productImages // ignore: cast_nullable_to_non_nullable
as List<ProductImage>?,hardwareSubCategory: freezed == hardwareSubCategory ? _self.hardwareSubCategory : hardwareSubCategory // ignore: cast_nullable_to_non_nullable
as HardwareSubCategoryData?,
  ));
}

/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductBranchCopyWith<$Res>? get branch {
    if (_self.branch == null) {
    return null;
  }

  return $ProductBranchCopyWith<$Res>(_self.branch!, (value) {
    return _then(_self.copyWith(branch: value));
  });
}/// Create a copy of HardwareItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryDataCopyWith<$Res>? get hardwareSubCategory {
    if (_self.hardwareSubCategory == null) {
    return null;
  }

  return $HardwareSubCategoryDataCopyWith<$Res>(_self.hardwareSubCategory!, (value) {
    return _then(_self.copyWith(hardwareSubCategory: value));
  });
}
}


/// @nodoc
mixin _$HardwareSubCategoryData {

 int? get id; String? get name;@JsonKey(name: 'hardware_category') HardwareCategoryData? get hardwareCategory;
/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareSubCategoryDataCopyWith<HardwareSubCategoryData> get copyWith => _$HardwareSubCategoryDataCopyWithImpl<HardwareSubCategoryData>(this as HardwareSubCategoryData, _$identity);

  /// Serializes this HardwareSubCategoryData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareSubCategoryData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.hardwareCategory, hardwareCategory) || other.hardwareCategory == hardwareCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,hardwareCategory);

@override
String toString() {
  return 'HardwareSubCategoryData(id: $id, name: $name, hardwareCategory: $hardwareCategory)';
}


}

/// @nodoc
abstract mixin class $HardwareSubCategoryDataCopyWith<$Res>  {
  factory $HardwareSubCategoryDataCopyWith(HardwareSubCategoryData value, $Res Function(HardwareSubCategoryData) _then) = _$HardwareSubCategoryDataCopyWithImpl;
@useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'hardware_category') HardwareCategoryData? hardwareCategory
});


$HardwareCategoryDataCopyWith<$Res>? get hardwareCategory;

}
/// @nodoc
class _$HardwareSubCategoryDataCopyWithImpl<$Res>
    implements $HardwareSubCategoryDataCopyWith<$Res> {
  _$HardwareSubCategoryDataCopyWithImpl(this._self, this._then);

  final HardwareSubCategoryData _self;
  final $Res Function(HardwareSubCategoryData) _then;

/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? hardwareCategory = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,hardwareCategory: freezed == hardwareCategory ? _self.hardwareCategory : hardwareCategory // ignore: cast_nullable_to_non_nullable
as HardwareCategoryData?,
  ));
}
/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareCategoryDataCopyWith<$Res>? get hardwareCategory {
    if (_self.hardwareCategory == null) {
    return null;
  }

  return $HardwareCategoryDataCopyWith<$Res>(_self.hardwareCategory!, (value) {
    return _then(_self.copyWith(hardwareCategory: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _HardwareSubCategoryData implements HardwareSubCategoryData {
  const _HardwareSubCategoryData({this.id, this.name, @JsonKey(name: 'hardware_category') this.hardwareCategory});
  factory _HardwareSubCategoryData.fromJson(Map<String, dynamic> json) => _$HardwareSubCategoryDataFromJson(json);

@override final  int? id;
@override final  String? name;
@override@JsonKey(name: 'hardware_category') final  HardwareCategoryData? hardwareCategory;

/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareSubCategoryDataCopyWith<_HardwareSubCategoryData> get copyWith => __$HardwareSubCategoryDataCopyWithImpl<_HardwareSubCategoryData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareSubCategoryDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareSubCategoryData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.hardwareCategory, hardwareCategory) || other.hardwareCategory == hardwareCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,hardwareCategory);

@override
String toString() {
  return 'HardwareSubCategoryData(id: $id, name: $name, hardwareCategory: $hardwareCategory)';
}


}

/// @nodoc
abstract mixin class _$HardwareSubCategoryDataCopyWith<$Res> implements $HardwareSubCategoryDataCopyWith<$Res> {
  factory _$HardwareSubCategoryDataCopyWith(_HardwareSubCategoryData value, $Res Function(_HardwareSubCategoryData) _then) = __$HardwareSubCategoryDataCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'hardware_category') HardwareCategoryData? hardwareCategory
});


@override $HardwareCategoryDataCopyWith<$Res>? get hardwareCategory;

}
/// @nodoc
class __$HardwareSubCategoryDataCopyWithImpl<$Res>
    implements _$HardwareSubCategoryDataCopyWith<$Res> {
  __$HardwareSubCategoryDataCopyWithImpl(this._self, this._then);

  final _HardwareSubCategoryData _self;
  final $Res Function(_HardwareSubCategoryData) _then;

/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? hardwareCategory = freezed,}) {
  return _then(_HardwareSubCategoryData(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,hardwareCategory: freezed == hardwareCategory ? _self.hardwareCategory : hardwareCategory // ignore: cast_nullable_to_non_nullable
as HardwareCategoryData?,
  ));
}

/// Create a copy of HardwareSubCategoryData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareCategoryDataCopyWith<$Res>? get hardwareCategory {
    if (_self.hardwareCategory == null) {
    return null;
  }

  return $HardwareCategoryDataCopyWith<$Res>(_self.hardwareCategory!, (value) {
    return _then(_self.copyWith(hardwareCategory: value));
  });
}
}


/// @nodoc
mixin _$HardwareCategoryData {

 int? get id; String? get name;
/// Create a copy of HardwareCategoryData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareCategoryDataCopyWith<HardwareCategoryData> get copyWith => _$HardwareCategoryDataCopyWithImpl<HardwareCategoryData>(this as HardwareCategoryData, _$identity);

  /// Serializes this HardwareCategoryData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareCategoryData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'HardwareCategoryData(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class $HardwareCategoryDataCopyWith<$Res>  {
  factory $HardwareCategoryDataCopyWith(HardwareCategoryData value, $Res Function(HardwareCategoryData) _then) = _$HardwareCategoryDataCopyWithImpl;
@useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class _$HardwareCategoryDataCopyWithImpl<$Res>
    implements $HardwareCategoryDataCopyWith<$Res> {
  _$HardwareCategoryDataCopyWithImpl(this._self, this._then);

  final HardwareCategoryData _self;
  final $Res Function(HardwareCategoryData) _then;

/// Create a copy of HardwareCategoryData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareCategoryData implements HardwareCategoryData {
  const _HardwareCategoryData({this.id, this.name});
  factory _HardwareCategoryData.fromJson(Map<String, dynamic> json) => _$HardwareCategoryDataFromJson(json);

@override final  int? id;
@override final  String? name;

/// Create a copy of HardwareCategoryData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareCategoryDataCopyWith<_HardwareCategoryData> get copyWith => __$HardwareCategoryDataCopyWithImpl<_HardwareCategoryData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareCategoryDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareCategoryData&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name);

@override
String toString() {
  return 'HardwareCategoryData(id: $id, name: $name)';
}


}

/// @nodoc
abstract mixin class _$HardwareCategoryDataCopyWith<$Res> implements $HardwareCategoryDataCopyWith<$Res> {
  factory _$HardwareCategoryDataCopyWith(_HardwareCategoryData value, $Res Function(_HardwareCategoryData) _then) = __$HardwareCategoryDataCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name
});




}
/// @nodoc
class __$HardwareCategoryDataCopyWithImpl<$Res>
    implements _$HardwareCategoryDataCopyWith<$Res> {
  __$HardwareCategoryDataCopyWithImpl(this._self, this._then);

  final _HardwareCategoryData _self;
  final $Res Function(_HardwareCategoryData) _then;

/// Create a copy of HardwareCategoryData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,}) {
  return _then(_HardwareCategoryData(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ProductImage {

 int? get id;@JsonKey(name: 'image_url') String? get imageUrl;
/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductImageCopyWith<ProductImage> get copyWith => _$ProductImageCopyWithImpl<ProductImage>(this as ProductImage, _$identity);

  /// Serializes this ProductImage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ProductImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $ProductImageCopyWith<$Res>  {
  factory $ProductImageCopyWith(ProductImage value, $Res Function(ProductImage) _then) = _$ProductImageCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class _$ProductImageCopyWithImpl<$Res>
    implements $ProductImageCopyWith<$Res> {
  _$ProductImageCopyWithImpl(this._self, this._then);

  final ProductImage _self;
  final $Res Function(ProductImage) _then;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductImage implements ProductImage {
  const _ProductImage({this.id, @JsonKey(name: 'image_url') this.imageUrl});
  factory _ProductImage.fromJson(Map<String, dynamic> json) => _$ProductImageFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'image_url') final  String? imageUrl;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductImageCopyWith<_ProductImage> get copyWith => __$ProductImageCopyWithImpl<_ProductImage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductImageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl);

@override
String toString() {
  return 'ProductImage(id: $id, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$ProductImageCopyWith<$Res> implements $ProductImageCopyWith<$Res> {
  factory _$ProductImageCopyWith(_ProductImage value, $Res Function(_ProductImage) _then) = __$ProductImageCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'image_url') String? imageUrl
});




}
/// @nodoc
class __$ProductImageCopyWithImpl<$Res>
    implements _$ProductImageCopyWith<$Res> {
  __$ProductImageCopyWithImpl(this._self, this._then);

  final _ProductImage _self;
  final $Res Function(_ProductImage) _then;

/// Create a copy of ProductImage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? imageUrl = freezed,}) {
  return _then(_ProductImage(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ProductBranch {

@JsonKey(name: 'branch_name') String? get branchName;@JsonKey(name: 'hardware_shop_id') int? get hardwareShopId;
/// Create a copy of ProductBranch
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductBranchCopyWith<ProductBranch> get copyWith => _$ProductBranchCopyWithImpl<ProductBranch>(this as ProductBranch, _$identity);

  /// Serializes this ProductBranch to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductBranch&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.hardwareShopId, hardwareShopId) || other.hardwareShopId == hardwareShopId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,branchName,hardwareShopId);

@override
String toString() {
  return 'ProductBranch(branchName: $branchName, hardwareShopId: $hardwareShopId)';
}


}

/// @nodoc
abstract mixin class $ProductBranchCopyWith<$Res>  {
  factory $ProductBranchCopyWith(ProductBranch value, $Res Function(ProductBranch) _then) = _$ProductBranchCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'branch_name') String? branchName,@JsonKey(name: 'hardware_shop_id') int? hardwareShopId
});




}
/// @nodoc
class _$ProductBranchCopyWithImpl<$Res>
    implements $ProductBranchCopyWith<$Res> {
  _$ProductBranchCopyWithImpl(this._self, this._then);

  final ProductBranch _self;
  final $Res Function(ProductBranch) _then;

/// Create a copy of ProductBranch
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? branchName = freezed,Object? hardwareShopId = freezed,}) {
  return _then(_self.copyWith(
branchName: freezed == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String?,hardwareShopId: freezed == hardwareShopId ? _self.hardwareShopId : hardwareShopId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductBranch implements ProductBranch {
  const _ProductBranch({@JsonKey(name: 'branch_name') this.branchName, @JsonKey(name: 'hardware_shop_id') this.hardwareShopId});
  factory _ProductBranch.fromJson(Map<String, dynamic> json) => _$ProductBranchFromJson(json);

@override@JsonKey(name: 'branch_name') final  String? branchName;
@override@JsonKey(name: 'hardware_shop_id') final  int? hardwareShopId;

/// Create a copy of ProductBranch
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductBranchCopyWith<_ProductBranch> get copyWith => __$ProductBranchCopyWithImpl<_ProductBranch>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductBranchToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductBranch&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.hardwareShopId, hardwareShopId) || other.hardwareShopId == hardwareShopId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,branchName,hardwareShopId);

@override
String toString() {
  return 'ProductBranch(branchName: $branchName, hardwareShopId: $hardwareShopId)';
}


}

/// @nodoc
abstract mixin class _$ProductBranchCopyWith<$Res> implements $ProductBranchCopyWith<$Res> {
  factory _$ProductBranchCopyWith(_ProductBranch value, $Res Function(_ProductBranch) _then) = __$ProductBranchCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'branch_name') String? branchName,@JsonKey(name: 'hardware_shop_id') int? hardwareShopId
});




}
/// @nodoc
class __$ProductBranchCopyWithImpl<$Res>
    implements _$ProductBranchCopyWith<$Res> {
  __$ProductBranchCopyWithImpl(this._self, this._then);

  final _ProductBranch _self;
  final $Res Function(_ProductBranch) _then;

/// Create a copy of ProductBranch
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? branchName = freezed,Object? hardwareShopId = freezed,}) {
  return _then(_ProductBranch(
branchName: freezed == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String?,hardwareShopId: freezed == hardwareShopId ? _self.hardwareShopId : hardwareShopId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$HardwareItemsResponse {

 List<HardwareItem>? get items;
/// Create a copy of HardwareItemsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareItemsResponseCopyWith<HardwareItemsResponse> get copyWith => _$HardwareItemsResponseCopyWithImpl<HardwareItemsResponse>(this as HardwareItemsResponse, _$identity);

  /// Serializes this HardwareItemsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareItemsResponse&&const DeepCollectionEquality().equals(other.items, items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(items));

@override
String toString() {
  return 'HardwareItemsResponse(items: $items)';
}


}

/// @nodoc
abstract mixin class $HardwareItemsResponseCopyWith<$Res>  {
  factory $HardwareItemsResponseCopyWith(HardwareItemsResponse value, $Res Function(HardwareItemsResponse) _then) = _$HardwareItemsResponseCopyWithImpl;
@useResult
$Res call({
 List<HardwareItem>? items
});




}
/// @nodoc
class _$HardwareItemsResponseCopyWithImpl<$Res>
    implements $HardwareItemsResponseCopyWith<$Res> {
  _$HardwareItemsResponseCopyWithImpl(this._self, this._then);

  final HardwareItemsResponse _self;
  final $Res Function(HardwareItemsResponse) _then;

/// Create a copy of HardwareItemsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? items = freezed,}) {
  return _then(_self.copyWith(
items: freezed == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<HardwareItem>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareItemsResponse implements HardwareItemsResponse {
  const _HardwareItemsResponse({final  List<HardwareItem>? items}): _items = items;
  factory _HardwareItemsResponse.fromJson(Map<String, dynamic> json) => _$HardwareItemsResponseFromJson(json);

 final  List<HardwareItem>? _items;
@override List<HardwareItem>? get items {
  final value = _items;
  if (value == null) return null;
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of HardwareItemsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareItemsResponseCopyWith<_HardwareItemsResponse> get copyWith => __$HardwareItemsResponseCopyWithImpl<_HardwareItemsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareItemsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareItemsResponse&&const DeepCollectionEquality().equals(other._items, _items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_items));

@override
String toString() {
  return 'HardwareItemsResponse(items: $items)';
}


}

/// @nodoc
abstract mixin class _$HardwareItemsResponseCopyWith<$Res> implements $HardwareItemsResponseCopyWith<$Res> {
  factory _$HardwareItemsResponseCopyWith(_HardwareItemsResponse value, $Res Function(_HardwareItemsResponse) _then) = __$HardwareItemsResponseCopyWithImpl;
@override @useResult
$Res call({
 List<HardwareItem>? items
});




}
/// @nodoc
class __$HardwareItemsResponseCopyWithImpl<$Res>
    implements _$HardwareItemsResponseCopyWith<$Res> {
  __$HardwareItemsResponseCopyWithImpl(this._self, this._then);

  final _HardwareItemsResponse _self;
  final $Res Function(_HardwareItemsResponse) _then;

/// Create a copy of HardwareItemsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? items = freezed,}) {
  return _then(_HardwareItemsResponse(
items: freezed == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<HardwareItem>?,
  ));
}


}

// dart format on
