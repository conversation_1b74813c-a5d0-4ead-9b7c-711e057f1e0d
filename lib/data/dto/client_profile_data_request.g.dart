// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'client_profile_data_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ClientProfileDataRequest _$ClientProfileDataRequestFromJson(
  Map<String, dynamic> json,
) => _ClientProfileDataRequest(
  client: ClientModel.fromJson(json['client'] as Map<String, dynamic>),
  phonenumbers:
      (json['phonenumbers'] as List<dynamic>)
          .map((e) => PhoneNumberModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  location: LocationModel.fromJson(json['location'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ClientProfileDataRequestToJson(
  _ClientProfileDataRequest instance,
) => <String, dynamic>{
  'client': instance.client,
  'phonenumbers': instance.phonenumbers,
  'location': instance.location,
};

_ClientModel _$ClientModelFromJson(Map<String, dynamic> json) => _ClientModel(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  email: json['email'] as String,
  avatar: json['avatar'] as String,
  address: json['address'] as String?,
  nationalId: json['national_id'] as String?,
  supabaseId: json['supabase_id'] as String?,
  whatsappNumber: json['whatsapp_number'] as String?,
  phone: json['phone'] as String?,
);

Map<String, dynamic> _$ClientModelToJson(_ClientModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'avatar': instance.avatar,
      'address': instance.address,
      'national_id': instance.nationalId,
      'supabase_id': instance.supabaseId,
      'whatsapp_number': instance.whatsappNumber,
      'phone': instance.phone,
    };

_PhoneNumberModel _$PhoneNumberModelFromJson(Map<String, dynamic> json) =>
    _PhoneNumberModel(phonenumber: json['phonenumber'] as String);

Map<String, dynamic> _$PhoneNumberModelToJson(_PhoneNumberModel instance) =>
    <String, dynamic>{'phonenumber': instance.phonenumber};

_LocationModel _$LocationModelFromJson(Map<String, dynamic> json) =>
    _LocationModel(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
    );

Map<String, dynamic> _$LocationModelToJson(_LocationModel instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
    };
