// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shop_product_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ShopProductResponse _$ShopProductResponseFromJson(Map<String, dynamic> json) =>
    _ShopProductResponse(
      id: (json['id'] as num).toInt(),
      branchId: (json['branch_id'] as num?)?.toInt(),
      hardwareSubCategoryId:
          (json['hardware_sub_category_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      brand: json['brand'] as String?,
      modelNumber: json['model_number'] as String?,
      description: json['description'] as String?,
      sku: json['sku'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      currentStock: (json['current_stock'] as num?)?.toInt(),
      minStockLevel: (json['min_stock_level'] as num?)?.toInt(),
      unitType: json['unit_type'] as String?,
      createdAt:
          json['created_at'] == null
              ? null
              : DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] == null
              ? null
              : DateTime.parse(json['updated_at'] as String),
      hardwareSubCategory:
          json['hardware_sub_categories'] == null
              ? null
              : HardwareSubCategory.fromJson(
                json['hardware_sub_categories'] as Map<String, dynamic>,
              ),
      productImages:
          (json['product_images'] as List<dynamic>?)
              ?.map((e) => ProductImage.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$ShopProductResponseToJson(
  _ShopProductResponse instance,
) => <String, dynamic>{
  'id': instance.id,
  'branch_id': instance.branchId,
  'hardware_sub_category_id': instance.hardwareSubCategoryId,
  'name': instance.name,
  'brand': instance.brand,
  'model_number': instance.modelNumber,
  'description': instance.description,
  'sku': instance.sku,
  'price': instance.price,
  'current_stock': instance.currentStock,
  'min_stock_level': instance.minStockLevel,
  'unit_type': instance.unitType,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'hardware_sub_categories': instance.hardwareSubCategory,
  'product_images': instance.productImages,
};

_HardwareSubCategory _$HardwareSubCategoryFromJson(Map<String, dynamic> json) =>
    _HardwareSubCategory(name: json['name'] as String?);

Map<String, dynamic> _$HardwareSubCategoryToJson(
  _HardwareSubCategory instance,
) => <String, dynamic>{'name': instance.name};

_ProductImage _$ProductImageFromJson(Map<String, dynamic> json) =>
    _ProductImage(
      id: (json['id'] as num?)?.toInt(),
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$ProductImageToJson(_ProductImage instance) =>
    <String, dynamic>{'id': instance.id, 'image_url': instance.imageUrl};
