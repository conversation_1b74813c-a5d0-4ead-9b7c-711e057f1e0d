import 'package:freezed_annotation/freezed_annotation.dart';

part 'suggestions_response.freezed.dart';
part 'suggestions_response.g.dart';

@freezed
abstract class SuggestionsResponse with _$SuggestionsResponse {
   factory SuggestionsResponse({
    List<Suggestion>? suggestions,
  }) = _SuggestionsResponse;

  factory SuggestionsResponse.fromJson(Map<String, dynamic> json) =>
      _$SuggestionsResponseFromJson(json);
}

@freezed
abstract class Suggestion with _$Suggestion {
   factory Suggestion({
    PlacePrediction? placePrediction,
  }) = _Suggestion;

  factory Suggestion.fromJson(Map<String, dynamic> json) =>
      _$SuggestionFromJson(json);
}

@freezed
abstract class PlacePrediction with _$PlacePrediction {
   factory PlacePrediction({
    String? place,
    String? placeId,
    Text? text,
    StructuredFormat? structuredFormat,
  }) = _PlacePrediction;

  factory PlacePrediction.fromJson(Map<String, dynamic> json) =>
      _$PlacePredictionFromJson(json);
}

@freezed
abstract class Text with _$Text {
   factory Text({
    String? text,
  }) = _Text;

  factory Text.fromJson(Map<String, dynamic> json) => _$TextFromJson(json);
}

@freezed
abstract class StructuredFormat with _$StructuredFormat {
   factory StructuredFormat({
    MainText? mainText,
  }) = _StructuredFormat;

  factory StructuredFormat.fromJson(Map<String, dynamic> json) =>
      _$StructuredFormatFromJson(json);
}

@freezed
abstract class MainText with _$MainText {
   factory MainText({
    String? text,
  }) = _MainText;

  factory MainText.fromJson(Map<String, dynamic> json) =>
      _$MainTextFromJson(json);
}
