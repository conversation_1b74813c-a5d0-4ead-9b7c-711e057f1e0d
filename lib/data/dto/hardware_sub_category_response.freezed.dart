// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hardware_sub_category_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HardwareSubCategoryResponse {

 int? get id;@JsonKey(name: 'hardware_category_id') int? get hardwareCategoryId; String? get name;
/// Create a copy of HardwareSubCategoryResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareSubCategoryResponseCopyWith<HardwareSubCategoryResponse> get copyWith => _$HardwareSubCategoryResponseCopyWithImpl<HardwareSubCategoryResponse>(this as HardwareSubCategoryResponse, _$identity);

  /// Serializes this HardwareSubCategoryResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareSubCategoryResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.hardwareCategoryId, hardwareCategoryId) || other.hardwareCategoryId == hardwareCategoryId)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,hardwareCategoryId,name);

@override
String toString() {
  return 'HardwareSubCategoryResponse(id: $id, hardwareCategoryId: $hardwareCategoryId, name: $name)';
}


}

/// @nodoc
abstract mixin class $HardwareSubCategoryResponseCopyWith<$Res>  {
  factory $HardwareSubCategoryResponseCopyWith(HardwareSubCategoryResponse value, $Res Function(HardwareSubCategoryResponse) _then) = _$HardwareSubCategoryResponseCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'hardware_category_id') int? hardwareCategoryId, String? name
});




}
/// @nodoc
class _$HardwareSubCategoryResponseCopyWithImpl<$Res>
    implements $HardwareSubCategoryResponseCopyWith<$Res> {
  _$HardwareSubCategoryResponseCopyWithImpl(this._self, this._then);

  final HardwareSubCategoryResponse _self;
  final $Res Function(HardwareSubCategoryResponse) _then;

/// Create a copy of HardwareSubCategoryResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? hardwareCategoryId = freezed,Object? name = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,hardwareCategoryId: freezed == hardwareCategoryId ? _self.hardwareCategoryId : hardwareCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareSubCategoryResponse implements HardwareSubCategoryResponse {
   _HardwareSubCategoryResponse({this.id, @JsonKey(name: 'hardware_category_id') this.hardwareCategoryId, this.name});
  factory _HardwareSubCategoryResponse.fromJson(Map<String, dynamic> json) => _$HardwareSubCategoryResponseFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'hardware_category_id') final  int? hardwareCategoryId;
@override final  String? name;

/// Create a copy of HardwareSubCategoryResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareSubCategoryResponseCopyWith<_HardwareSubCategoryResponse> get copyWith => __$HardwareSubCategoryResponseCopyWithImpl<_HardwareSubCategoryResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareSubCategoryResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareSubCategoryResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.hardwareCategoryId, hardwareCategoryId) || other.hardwareCategoryId == hardwareCategoryId)&&(identical(other.name, name) || other.name == name));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,hardwareCategoryId,name);

@override
String toString() {
  return 'HardwareSubCategoryResponse(id: $id, hardwareCategoryId: $hardwareCategoryId, name: $name)';
}


}

/// @nodoc
abstract mixin class _$HardwareSubCategoryResponseCopyWith<$Res> implements $HardwareSubCategoryResponseCopyWith<$Res> {
  factory _$HardwareSubCategoryResponseCopyWith(_HardwareSubCategoryResponse value, $Res Function(_HardwareSubCategoryResponse) _then) = __$HardwareSubCategoryResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'hardware_category_id') int? hardwareCategoryId, String? name
});




}
/// @nodoc
class __$HardwareSubCategoryResponseCopyWithImpl<$Res>
    implements _$HardwareSubCategoryResponseCopyWith<$Res> {
  __$HardwareSubCategoryResponseCopyWithImpl(this._self, this._then);

  final _HardwareSubCategoryResponse _self;
  final $Res Function(_HardwareSubCategoryResponse) _then;

/// Create a copy of HardwareSubCategoryResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? hardwareCategoryId = freezed,Object? name = freezed,}) {
  return _then(_HardwareSubCategoryResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,hardwareCategoryId: freezed == hardwareCategoryId ? _self.hardwareCategoryId : hardwareCategoryId // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
