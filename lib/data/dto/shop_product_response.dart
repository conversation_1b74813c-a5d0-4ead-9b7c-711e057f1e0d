import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_product_response.freezed.dart';
part 'shop_product_response.g.dart';

@freezed
abstract class ShopProductResponse with _$ShopProductResponse {
  const factory ShopProductResponse({
    required int id,
    @J<PERSON><PERSON><PERSON>(name: 'branch_id') int? branchId,
    @Json<PERSON><PERSON>(name: 'hardware_sub_category_id') int? hardwareSubCategoryId,
    String? name,
    String? brand,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'model_number') String? modelNumber,
    String? description,
    String? sku,
    double? price,
    @Json<PERSON>ey(name: 'current_stock') int? currentStock,
    @J<PERSON><PERSON><PERSON>(name: 'min_stock_level') int? minStockLevel,
    @J<PERSON><PERSON><PERSON>(name: 'unit_type') String? unitType,
    @Json<PERSON>ey(name: 'created_at') DateTime? createdAt,
    @Json<PERSON>ey(name: 'updated_at') DateTime? updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'hardware_sub_categories')
    HardwareSubCategory? hardwareSubCategory,
    @J<PERSON><PERSON><PERSON>(name: 'product_images') List<ProductImage>? productImages,
  }) = _ShopProductResponse;

  factory ShopProductResponse.fromJson(Map<String, dynamic> json) =>
      _$ShopProductResponseFromJson(json);
}

@freezed
abstract class HardwareSubCategory with _$HardwareSubCategory {
  const factory HardwareSubCategory({String? name}) = _HardwareSubCategory;

  factory HardwareSubCategory.fromJson(Map<String, dynamic> json) =>
      _$HardwareSubCategoryFromJson(json);
}

@freezed
abstract class ProductImage with _$ProductImage {
  const factory ProductImage({
    int? id,
    @JsonKey(name: 'image_url') String? imageUrl,
  }) = _ProductImage;

  factory ProductImage.fromJson(Map<String, dynamic> json) =>
      _$ProductImageFromJson(json);
}
