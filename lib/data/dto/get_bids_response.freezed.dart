// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_bids_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetBidResponse {

 int? get id; double? get amount; String? get status;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'is_selected') bool? get isSelected; ArtisanModel? get artisan; JobPreviewModel? get job;
/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetBidResponseCopyWith<GetBidResponse> get copyWith => _$GetBidResponseCopyWithImpl<GetBidResponse>(this as GetBidResponse, _$identity);

  /// Serializes this GetBidResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetBidResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.artisan, artisan) || other.artisan == artisan)&&(identical(other.job, job) || other.job == job));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,amount,status,createdAt,isSelected,artisan,job);

@override
String toString() {
  return 'GetBidResponse(id: $id, amount: $amount, status: $status, createdAt: $createdAt, isSelected: $isSelected, artisan: $artisan, job: $job)';
}


}

/// @nodoc
abstract mixin class $GetBidResponseCopyWith<$Res>  {
  factory $GetBidResponseCopyWith(GetBidResponse value, $Res Function(GetBidResponse) _then) = _$GetBidResponseCopyWithImpl;
@useResult
$Res call({
 int? id, double? amount, String? status,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'is_selected') bool? isSelected, ArtisanModel? artisan, JobPreviewModel? job
});


$ArtisanModelCopyWith<$Res>? get artisan;$JobPreviewModelCopyWith<$Res>? get job;

}
/// @nodoc
class _$GetBidResponseCopyWithImpl<$Res>
    implements $GetBidResponseCopyWith<$Res> {
  _$GetBidResponseCopyWithImpl(this._self, this._then);

  final GetBidResponse _self;
  final $Res Function(GetBidResponse) _then;

/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? amount = freezed,Object? status = freezed,Object? createdAt = freezed,Object? isSelected = freezed,Object? artisan = freezed,Object? job = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSelected: freezed == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool?,artisan: freezed == artisan ? _self.artisan : artisan // ignore: cast_nullable_to_non_nullable
as ArtisanModel?,job: freezed == job ? _self.job : job // ignore: cast_nullable_to_non_nullable
as JobPreviewModel?,
  ));
}
/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<$Res>? get artisan {
    if (_self.artisan == null) {
    return null;
  }

  return $ArtisanModelCopyWith<$Res>(_self.artisan!, (value) {
    return _then(_self.copyWith(artisan: value));
  });
}/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobPreviewModelCopyWith<$Res>? get job {
    if (_self.job == null) {
    return null;
  }

  return $JobPreviewModelCopyWith<$Res>(_self.job!, (value) {
    return _then(_self.copyWith(job: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _GetBidResponse implements GetBidResponse {
  const _GetBidResponse({required this.id, required this.amount, required this.status, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'is_selected') required this.isSelected, required this.artisan, required this.job});
  factory _GetBidResponse.fromJson(Map<String, dynamic> json) => _$GetBidResponseFromJson(json);

@override final  int? id;
@override final  double? amount;
@override final  String? status;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'is_selected') final  bool? isSelected;
@override final  ArtisanModel? artisan;
@override final  JobPreviewModel? job;

/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetBidResponseCopyWith<_GetBidResponse> get copyWith => __$GetBidResponseCopyWithImpl<_GetBidResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetBidResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetBidResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.artisan, artisan) || other.artisan == artisan)&&(identical(other.job, job) || other.job == job));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,amount,status,createdAt,isSelected,artisan,job);

@override
String toString() {
  return 'GetBidResponse(id: $id, amount: $amount, status: $status, createdAt: $createdAt, isSelected: $isSelected, artisan: $artisan, job: $job)';
}


}

/// @nodoc
abstract mixin class _$GetBidResponseCopyWith<$Res> implements $GetBidResponseCopyWith<$Res> {
  factory _$GetBidResponseCopyWith(_GetBidResponse value, $Res Function(_GetBidResponse) _then) = __$GetBidResponseCopyWithImpl;
@override @useResult
$Res call({
 int? id, double? amount, String? status,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'is_selected') bool? isSelected, ArtisanModel? artisan, JobPreviewModel? job
});


@override $ArtisanModelCopyWith<$Res>? get artisan;@override $JobPreviewModelCopyWith<$Res>? get job;

}
/// @nodoc
class __$GetBidResponseCopyWithImpl<$Res>
    implements _$GetBidResponseCopyWith<$Res> {
  __$GetBidResponseCopyWithImpl(this._self, this._then);

  final _GetBidResponse _self;
  final $Res Function(_GetBidResponse) _then;

/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? amount = freezed,Object? status = freezed,Object? createdAt = freezed,Object? isSelected = freezed,Object? artisan = freezed,Object? job = freezed,}) {
  return _then(_GetBidResponse(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSelected: freezed == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool?,artisan: freezed == artisan ? _self.artisan : artisan // ignore: cast_nullable_to_non_nullable
as ArtisanModel?,job: freezed == job ? _self.job : job // ignore: cast_nullable_to_non_nullable
as JobPreviewModel?,
  ));
}

/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<$Res>? get artisan {
    if (_self.artisan == null) {
    return null;
  }

  return $ArtisanModelCopyWith<$Res>(_self.artisan!, (value) {
    return _then(_self.copyWith(artisan: value));
  });
}/// Create a copy of GetBidResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobPreviewModelCopyWith<$Res>? get job {
    if (_self.job == null) {
    return null;
  }

  return $JobPreviewModelCopyWith<$Res>(_self.job!, (value) {
    return _then(_self.copyWith(job: value));
  });
}
}


/// @nodoc
mixin _$ArtisanModel {

 int? get id; String? get name;@JsonKey(name: 'avatar') String? get avatarUrl; String? get about;
/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanModelCopyWith<ArtisanModel> get copyWith => _$ArtisanModelCopyWithImpl<ArtisanModel>(this as ArtisanModel, _$identity);

  /// Serializes this ArtisanModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.about, about) || other.about == about));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,about);

@override
String toString() {
  return 'ArtisanModel(id: $id, name: $name, avatarUrl: $avatarUrl, about: $about)';
}


}

/// @nodoc
abstract mixin class $ArtisanModelCopyWith<$Res>  {
  factory $ArtisanModelCopyWith(ArtisanModel value, $Res Function(ArtisanModel) _then) = _$ArtisanModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'avatar') String? avatarUrl, String? about
});




}
/// @nodoc
class _$ArtisanModelCopyWithImpl<$Res>
    implements $ArtisanModelCopyWith<$Res> {
  _$ArtisanModelCopyWithImpl(this._self, this._then);

  final ArtisanModel _self;
  final $Res Function(ArtisanModel) _then;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? avatarUrl = freezed,Object? about = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanModel implements ArtisanModel {
  const _ArtisanModel({required this.id, required this.name, @JsonKey(name: 'avatar') this.avatarUrl, required this.about});
  factory _ArtisanModel.fromJson(Map<String, dynamic> json) => _$ArtisanModelFromJson(json);

@override final  int? id;
@override final  String? name;
@override@JsonKey(name: 'avatar') final  String? avatarUrl;
@override final  String? about;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanModelCopyWith<_ArtisanModel> get copyWith => __$ArtisanModelCopyWithImpl<_ArtisanModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.about, about) || other.about == about));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,about);

@override
String toString() {
  return 'ArtisanModel(id: $id, name: $name, avatarUrl: $avatarUrl, about: $about)';
}


}

/// @nodoc
abstract mixin class _$ArtisanModelCopyWith<$Res> implements $ArtisanModelCopyWith<$Res> {
  factory _$ArtisanModelCopyWith(_ArtisanModel value, $Res Function(_ArtisanModel) _then) = __$ArtisanModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name,@JsonKey(name: 'avatar') String? avatarUrl, String? about
});




}
/// @nodoc
class __$ArtisanModelCopyWithImpl<$Res>
    implements _$ArtisanModelCopyWith<$Res> {
  __$ArtisanModelCopyWithImpl(this._self, this._then);

  final _ArtisanModel _self;
  final $Res Function(_ArtisanModel) _then;

/// Create a copy of ArtisanModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? avatarUrl = freezed,Object? about = freezed,}) {
  return _then(_ArtisanModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$JobPreviewModel {

 int? get id; String? get title; double? get budget;
/// Create a copy of JobPreviewModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobPreviewModelCopyWith<JobPreviewModel> get copyWith => _$JobPreviewModelCopyWithImpl<JobPreviewModel>(this as JobPreviewModel, _$identity);

  /// Serializes this JobPreviewModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobPreviewModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.budget, budget) || other.budget == budget));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,budget);

@override
String toString() {
  return 'JobPreviewModel(id: $id, title: $title, budget: $budget)';
}


}

/// @nodoc
abstract mixin class $JobPreviewModelCopyWith<$Res>  {
  factory $JobPreviewModelCopyWith(JobPreviewModel value, $Res Function(JobPreviewModel) _then) = _$JobPreviewModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? title, double? budget
});




}
/// @nodoc
class _$JobPreviewModelCopyWithImpl<$Res>
    implements $JobPreviewModelCopyWith<$Res> {
  _$JobPreviewModelCopyWithImpl(this._self, this._then);

  final JobPreviewModel _self;
  final $Res Function(JobPreviewModel) _then;

/// Create a copy of JobPreviewModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? title = freezed,Object? budget = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _JobPreviewModel implements JobPreviewModel {
  const _JobPreviewModel({required this.id, required this.title, this.budget});
  factory _JobPreviewModel.fromJson(Map<String, dynamic> json) => _$JobPreviewModelFromJson(json);

@override final  int? id;
@override final  String? title;
@override final  double? budget;

/// Create a copy of JobPreviewModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobPreviewModelCopyWith<_JobPreviewModel> get copyWith => __$JobPreviewModelCopyWithImpl<_JobPreviewModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JobPreviewModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobPreviewModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.budget, budget) || other.budget == budget));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,budget);

@override
String toString() {
  return 'JobPreviewModel(id: $id, title: $title, budget: $budget)';
}


}

/// @nodoc
abstract mixin class _$JobPreviewModelCopyWith<$Res> implements $JobPreviewModelCopyWith<$Res> {
  factory _$JobPreviewModelCopyWith(_JobPreviewModel value, $Res Function(_JobPreviewModel) _then) = __$JobPreviewModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? title, double? budget
});




}
/// @nodoc
class __$JobPreviewModelCopyWithImpl<$Res>
    implements _$JobPreviewModelCopyWith<$Res> {
  __$JobPreviewModelCopyWithImpl(this._self, this._then);

  final _JobPreviewModel _self;
  final $Res Function(_JobPreviewModel) _then;

/// Create a copy of JobPreviewModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? title = freezed,Object? budget = freezed,}) {
  return _then(_JobPreviewModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on
