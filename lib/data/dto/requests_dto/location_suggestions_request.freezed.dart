// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_suggestions_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationSuggestionsRequest {

 String get input; List<String> get includedRegionCodes; LocationBias get locationBias;
/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LocationSuggestionsRequestCopyWith<LocationSuggestionsRequest> get copyWith => _$LocationSuggestionsRequestCopyWithImpl<LocationSuggestionsRequest>(this as LocationSuggestionsRequest, _$identity);

  /// Serializes this LocationSuggestionsRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationSuggestionsRequest&&(identical(other.input, input) || other.input == input)&&const DeepCollectionEquality().equals(other.includedRegionCodes, includedRegionCodes)&&(identical(other.locationBias, locationBias) || other.locationBias == locationBias));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,input,const DeepCollectionEquality().hash(includedRegionCodes),locationBias);

@override
String toString() {
  return 'LocationSuggestionsRequest(input: $input, includedRegionCodes: $includedRegionCodes, locationBias: $locationBias)';
}


}

/// @nodoc
abstract mixin class $LocationSuggestionsRequestCopyWith<$Res>  {
  factory $LocationSuggestionsRequestCopyWith(LocationSuggestionsRequest value, $Res Function(LocationSuggestionsRequest) _then) = _$LocationSuggestionsRequestCopyWithImpl;
@useResult
$Res call({
 String input, List<String> includedRegionCodes, LocationBias locationBias
});


$LocationBiasCopyWith<$Res> get locationBias;

}
/// @nodoc
class _$LocationSuggestionsRequestCopyWithImpl<$Res>
    implements $LocationSuggestionsRequestCopyWith<$Res> {
  _$LocationSuggestionsRequestCopyWithImpl(this._self, this._then);

  final LocationSuggestionsRequest _self;
  final $Res Function(LocationSuggestionsRequest) _then;

/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? input = null,Object? includedRegionCodes = null,Object? locationBias = null,}) {
  return _then(_self.copyWith(
input: null == input ? _self.input : input // ignore: cast_nullable_to_non_nullable
as String,includedRegionCodes: null == includedRegionCodes ? _self.includedRegionCodes : includedRegionCodes // ignore: cast_nullable_to_non_nullable
as List<String>,locationBias: null == locationBias ? _self.locationBias : locationBias // ignore: cast_nullable_to_non_nullable
as LocationBias,
  ));
}
/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationBiasCopyWith<$Res> get locationBias {
  
  return $LocationBiasCopyWith<$Res>(_self.locationBias, (value) {
    return _then(_self.copyWith(locationBias: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _LocationSuggestionsRequest implements LocationSuggestionsRequest {
  const _LocationSuggestionsRequest({required this.input, required final  List<String> includedRegionCodes, required this.locationBias}): _includedRegionCodes = includedRegionCodes;
  factory _LocationSuggestionsRequest.fromJson(Map<String, dynamic> json) => _$LocationSuggestionsRequestFromJson(json);

@override final  String input;
 final  List<String> _includedRegionCodes;
@override List<String> get includedRegionCodes {
  if (_includedRegionCodes is EqualUnmodifiableListView) return _includedRegionCodes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_includedRegionCodes);
}

@override final  LocationBias locationBias;

/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LocationSuggestionsRequestCopyWith<_LocationSuggestionsRequest> get copyWith => __$LocationSuggestionsRequestCopyWithImpl<_LocationSuggestionsRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LocationSuggestionsRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LocationSuggestionsRequest&&(identical(other.input, input) || other.input == input)&&const DeepCollectionEquality().equals(other._includedRegionCodes, _includedRegionCodes)&&(identical(other.locationBias, locationBias) || other.locationBias == locationBias));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,input,const DeepCollectionEquality().hash(_includedRegionCodes),locationBias);

@override
String toString() {
  return 'LocationSuggestionsRequest(input: $input, includedRegionCodes: $includedRegionCodes, locationBias: $locationBias)';
}


}

/// @nodoc
abstract mixin class _$LocationSuggestionsRequestCopyWith<$Res> implements $LocationSuggestionsRequestCopyWith<$Res> {
  factory _$LocationSuggestionsRequestCopyWith(_LocationSuggestionsRequest value, $Res Function(_LocationSuggestionsRequest) _then) = __$LocationSuggestionsRequestCopyWithImpl;
@override @useResult
$Res call({
 String input, List<String> includedRegionCodes, LocationBias locationBias
});


@override $LocationBiasCopyWith<$Res> get locationBias;

}
/// @nodoc
class __$LocationSuggestionsRequestCopyWithImpl<$Res>
    implements _$LocationSuggestionsRequestCopyWith<$Res> {
  __$LocationSuggestionsRequestCopyWithImpl(this._self, this._then);

  final _LocationSuggestionsRequest _self;
  final $Res Function(_LocationSuggestionsRequest) _then;

/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? input = null,Object? includedRegionCodes = null,Object? locationBias = null,}) {
  return _then(_LocationSuggestionsRequest(
input: null == input ? _self.input : input // ignore: cast_nullable_to_non_nullable
as String,includedRegionCodes: null == includedRegionCodes ? _self._includedRegionCodes : includedRegionCodes // ignore: cast_nullable_to_non_nullable
as List<String>,locationBias: null == locationBias ? _self.locationBias : locationBias // ignore: cast_nullable_to_non_nullable
as LocationBias,
  ));
}

/// Create a copy of LocationSuggestionsRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationBiasCopyWith<$Res> get locationBias {
  
  return $LocationBiasCopyWith<$Res>(_self.locationBias, (value) {
    return _then(_self.copyWith(locationBias: value));
  });
}
}


/// @nodoc
mixin _$LocationBias {

 CircleModel get circle;
/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LocationBiasCopyWith<LocationBias> get copyWith => _$LocationBiasCopyWithImpl<LocationBias>(this as LocationBias, _$identity);

  /// Serializes this LocationBias to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationBias&&(identical(other.circle, circle) || other.circle == circle));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,circle);

@override
String toString() {
  return 'LocationBias(circle: $circle)';
}


}

/// @nodoc
abstract mixin class $LocationBiasCopyWith<$Res>  {
  factory $LocationBiasCopyWith(LocationBias value, $Res Function(LocationBias) _then) = _$LocationBiasCopyWithImpl;
@useResult
$Res call({
 CircleModel circle
});


$CircleModelCopyWith<$Res> get circle;

}
/// @nodoc
class _$LocationBiasCopyWithImpl<$Res>
    implements $LocationBiasCopyWith<$Res> {
  _$LocationBiasCopyWithImpl(this._self, this._then);

  final LocationBias _self;
  final $Res Function(LocationBias) _then;

/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? circle = null,}) {
  return _then(_self.copyWith(
circle: null == circle ? _self.circle : circle // ignore: cast_nullable_to_non_nullable
as CircleModel,
  ));
}
/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CircleModelCopyWith<$Res> get circle {
  
  return $CircleModelCopyWith<$Res>(_self.circle, (value) {
    return _then(_self.copyWith(circle: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _LocationBias implements LocationBias {
  const _LocationBias({required this.circle});
  factory _LocationBias.fromJson(Map<String, dynamic> json) => _$LocationBiasFromJson(json);

@override final  CircleModel circle;

/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LocationBiasCopyWith<_LocationBias> get copyWith => __$LocationBiasCopyWithImpl<_LocationBias>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LocationBiasToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LocationBias&&(identical(other.circle, circle) || other.circle == circle));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,circle);

@override
String toString() {
  return 'LocationBias(circle: $circle)';
}


}

/// @nodoc
abstract mixin class _$LocationBiasCopyWith<$Res> implements $LocationBiasCopyWith<$Res> {
  factory _$LocationBiasCopyWith(_LocationBias value, $Res Function(_LocationBias) _then) = __$LocationBiasCopyWithImpl;
@override @useResult
$Res call({
 CircleModel circle
});


@override $CircleModelCopyWith<$Res> get circle;

}
/// @nodoc
class __$LocationBiasCopyWithImpl<$Res>
    implements _$LocationBiasCopyWith<$Res> {
  __$LocationBiasCopyWithImpl(this._self, this._then);

  final _LocationBias _self;
  final $Res Function(_LocationBias) _then;

/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? circle = null,}) {
  return _then(_LocationBias(
circle: null == circle ? _self.circle : circle // ignore: cast_nullable_to_non_nullable
as CircleModel,
  ));
}

/// Create a copy of LocationBias
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CircleModelCopyWith<$Res> get circle {
  
  return $CircleModelCopyWith<$Res>(_self.circle, (value) {
    return _then(_self.copyWith(circle: value));
  });
}
}


/// @nodoc
mixin _$CircleModel {

 CenterModel get center; double get radius;
/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CircleModelCopyWith<CircleModel> get copyWith => _$CircleModelCopyWithImpl<CircleModel>(this as CircleModel, _$identity);

  /// Serializes this CircleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CircleModel&&(identical(other.center, center) || other.center == center)&&(identical(other.radius, radius) || other.radius == radius));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,center,radius);

@override
String toString() {
  return 'CircleModel(center: $center, radius: $radius)';
}


}

/// @nodoc
abstract mixin class $CircleModelCopyWith<$Res>  {
  factory $CircleModelCopyWith(CircleModel value, $Res Function(CircleModel) _then) = _$CircleModelCopyWithImpl;
@useResult
$Res call({
 CenterModel center, double radius
});


$CenterModelCopyWith<$Res> get center;

}
/// @nodoc
class _$CircleModelCopyWithImpl<$Res>
    implements $CircleModelCopyWith<$Res> {
  _$CircleModelCopyWithImpl(this._self, this._then);

  final CircleModel _self;
  final $Res Function(CircleModel) _then;

/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? center = null,Object? radius = null,}) {
  return _then(_self.copyWith(
center: null == center ? _self.center : center // ignore: cast_nullable_to_non_nullable
as CenterModel,radius: null == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double,
  ));
}
/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CenterModelCopyWith<$Res> get center {
  
  return $CenterModelCopyWith<$Res>(_self.center, (value) {
    return _then(_self.copyWith(center: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _CircleModel implements CircleModel {
  const _CircleModel({required this.center, required this.radius});
  factory _CircleModel.fromJson(Map<String, dynamic> json) => _$CircleModelFromJson(json);

@override final  CenterModel center;
@override final  double radius;

/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CircleModelCopyWith<_CircleModel> get copyWith => __$CircleModelCopyWithImpl<_CircleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CircleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CircleModel&&(identical(other.center, center) || other.center == center)&&(identical(other.radius, radius) || other.radius == radius));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,center,radius);

@override
String toString() {
  return 'CircleModel(center: $center, radius: $radius)';
}


}

/// @nodoc
abstract mixin class _$CircleModelCopyWith<$Res> implements $CircleModelCopyWith<$Res> {
  factory _$CircleModelCopyWith(_CircleModel value, $Res Function(_CircleModel) _then) = __$CircleModelCopyWithImpl;
@override @useResult
$Res call({
 CenterModel center, double radius
});


@override $CenterModelCopyWith<$Res> get center;

}
/// @nodoc
class __$CircleModelCopyWithImpl<$Res>
    implements _$CircleModelCopyWith<$Res> {
  __$CircleModelCopyWithImpl(this._self, this._then);

  final _CircleModel _self;
  final $Res Function(_CircleModel) _then;

/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? center = null,Object? radius = null,}) {
  return _then(_CircleModel(
center: null == center ? _self.center : center // ignore: cast_nullable_to_non_nullable
as CenterModel,radius: null == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

/// Create a copy of CircleModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CenterModelCopyWith<$Res> get center {
  
  return $CenterModelCopyWith<$Res>(_self.center, (value) {
    return _then(_self.copyWith(center: value));
  });
}
}


/// @nodoc
mixin _$CenterModel {

 double get latitude; double get longitude;
/// Create a copy of CenterModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CenterModelCopyWith<CenterModel> get copyWith => _$CenterModelCopyWithImpl<CenterModel>(this as CenterModel, _$identity);

  /// Serializes this CenterModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CenterModel&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude);

@override
String toString() {
  return 'CenterModel(latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class $CenterModelCopyWith<$Res>  {
  factory $CenterModelCopyWith(CenterModel value, $Res Function(CenterModel) _then) = _$CenterModelCopyWithImpl;
@useResult
$Res call({
 double latitude, double longitude
});




}
/// @nodoc
class _$CenterModelCopyWithImpl<$Res>
    implements $CenterModelCopyWith<$Res> {
  _$CenterModelCopyWithImpl(this._self, this._then);

  final CenterModel _self;
  final $Res Function(CenterModel) _then;

/// Create a copy of CenterModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? latitude = null,Object? longitude = null,}) {
  return _then(_self.copyWith(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CenterModel implements CenterModel {
  const _CenterModel({required this.latitude, required this.longitude});
  factory _CenterModel.fromJson(Map<String, dynamic> json) => _$CenterModelFromJson(json);

@override final  double latitude;
@override final  double longitude;

/// Create a copy of CenterModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CenterModelCopyWith<_CenterModel> get copyWith => __$CenterModelCopyWithImpl<_CenterModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CenterModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CenterModel&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude);

@override
String toString() {
  return 'CenterModel(latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class _$CenterModelCopyWith<$Res> implements $CenterModelCopyWith<$Res> {
  factory _$CenterModelCopyWith(_CenterModel value, $Res Function(_CenterModel) _then) = __$CenterModelCopyWithImpl;
@override @useResult
$Res call({
 double latitude, double longitude
});




}
/// @nodoc
class __$CenterModelCopyWithImpl<$Res>
    implements _$CenterModelCopyWith<$Res> {
  __$CenterModelCopyWithImpl(this._self, this._then);

  final _CenterModel _self;
  final $Res Function(_CenterModel) _then;

/// Create a copy of CenterModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? latitude = null,Object? longitude = null,}) {
  return _then(_CenterModel(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
