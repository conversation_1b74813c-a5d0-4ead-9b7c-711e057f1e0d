// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_suggestions_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LocationSuggestionsRequest _$LocationSuggestionsRequestFromJson(
  Map<String, dynamic> json,
) => _LocationSuggestionsRequest(
  input: json['input'] as String,
  includedRegionCodes:
      (json['includedRegionCodes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
  locationBias: LocationBias.fromJson(
    json['locationBias'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$LocationSuggestionsRequestToJson(
  _LocationSuggestionsRequest instance,
) => <String, dynamic>{
  'input': instance.input,
  'includedRegionCodes': instance.includedRegionCodes,
  'locationBias': instance.locationBias,
};

_LocationBias _$LocationBiasFromJson(Map<String, dynamic> json) =>
    _LocationBias(
      circle: CircleModel.fromJson(json['circle'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LocationBiasToJson(_LocationBias instance) =>
    <String, dynamic>{'circle': instance.circle};

_CircleModel _$CircleModelFromJson(Map<String, dynamic> json) => _CircleModel(
  center: CenterModel.fromJson(json['center'] as Map<String, dynamic>),
  radius: (json['radius'] as num).toDouble(),
);

Map<String, dynamic> _$CircleModelToJson(_CircleModel instance) =>
    <String, dynamic>{'center': instance.center, 'radius': instance.radius};

_CenterModel _$CenterModelFromJson(Map<String, dynamic> json) => _CenterModel(
  latitude: (json['latitude'] as num).toDouble(),
  longitude: (json['longitude'] as num).toDouble(),
);

Map<String, dynamic> _$CenterModelToJson(_CenterModel instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
