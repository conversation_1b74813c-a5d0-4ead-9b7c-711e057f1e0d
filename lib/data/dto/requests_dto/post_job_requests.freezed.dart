// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post_job_requests.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PostJobRequests {

 int get clientId; int get serviceId; String get description; double get budget; String get serviceDate; List<String> get images; List<int> get subcategoryIds;
/// Create a copy of PostJobRequests
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostJobRequestsCopyWith<PostJobRequests> get copyWith => _$PostJobRequestsCopyWithImpl<PostJobRequests>(this as PostJobRequests, _$identity);

  /// Serializes this PostJobRequests to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PostJobRequests&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.description, description) || other.description == description)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.subcategoryIds, subcategoryIds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,clientId,serviceId,description,budget,serviceDate,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(subcategoryIds));

@override
String toString() {
  return 'PostJobRequests(clientId: $clientId, serviceId: $serviceId, description: $description, budget: $budget, serviceDate: $serviceDate, images: $images, subcategoryIds: $subcategoryIds)';
}


}

/// @nodoc
abstract mixin class $PostJobRequestsCopyWith<$Res>  {
  factory $PostJobRequestsCopyWith(PostJobRequests value, $Res Function(PostJobRequests) _then) = _$PostJobRequestsCopyWithImpl;
@useResult
$Res call({
 int clientId, int serviceId, String description, double budget, String serviceDate, List<String> images, List<int> subcategoryIds
});




}
/// @nodoc
class _$PostJobRequestsCopyWithImpl<$Res>
    implements $PostJobRequestsCopyWith<$Res> {
  _$PostJobRequestsCopyWithImpl(this._self, this._then);

  final PostJobRequests _self;
  final $Res Function(PostJobRequests) _then;

/// Create a copy of PostJobRequests
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? clientId = null,Object? serviceId = null,Object? description = null,Object? budget = null,Object? serviceDate = null,Object? images = null,Object? subcategoryIds = null,}) {
  return _then(_self.copyWith(
clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,serviceId: null == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,budget: null == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double,serviceDate: null == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as String,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,subcategoryIds: null == subcategoryIds ? _self.subcategoryIds : subcategoryIds // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PostJobRequests implements PostJobRequests {
   _PostJobRequests({required this.clientId, required this.serviceId, required this.description, required this.budget, required this.serviceDate, final  List<String> images = const [], final  List<int> subcategoryIds = const []}): _images = images,_subcategoryIds = subcategoryIds;
  factory _PostJobRequests.fromJson(Map<String, dynamic> json) => _$PostJobRequestsFromJson(json);

@override final  int clientId;
@override final  int serviceId;
@override final  String description;
@override final  double budget;
@override final  String serviceDate;
 final  List<String> _images;
@override@JsonKey() List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

 final  List<int> _subcategoryIds;
@override@JsonKey() List<int> get subcategoryIds {
  if (_subcategoryIds is EqualUnmodifiableListView) return _subcategoryIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_subcategoryIds);
}


/// Create a copy of PostJobRequests
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostJobRequestsCopyWith<_PostJobRequests> get copyWith => __$PostJobRequestsCopyWithImpl<_PostJobRequests>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PostJobRequestsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PostJobRequests&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId)&&(identical(other.description, description) || other.description == description)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._subcategoryIds, _subcategoryIds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,clientId,serviceId,description,budget,serviceDate,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_subcategoryIds));

@override
String toString() {
  return 'PostJobRequests(clientId: $clientId, serviceId: $serviceId, description: $description, budget: $budget, serviceDate: $serviceDate, images: $images, subcategoryIds: $subcategoryIds)';
}


}

/// @nodoc
abstract mixin class _$PostJobRequestsCopyWith<$Res> implements $PostJobRequestsCopyWith<$Res> {
  factory _$PostJobRequestsCopyWith(_PostJobRequests value, $Res Function(_PostJobRequests) _then) = __$PostJobRequestsCopyWithImpl;
@override @useResult
$Res call({
 int clientId, int serviceId, String description, double budget, String serviceDate, List<String> images, List<int> subcategoryIds
});




}
/// @nodoc
class __$PostJobRequestsCopyWithImpl<$Res>
    implements _$PostJobRequestsCopyWith<$Res> {
  __$PostJobRequestsCopyWithImpl(this._self, this._then);

  final _PostJobRequests _self;
  final $Res Function(_PostJobRequests) _then;

/// Create a copy of PostJobRequests
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? clientId = null,Object? serviceId = null,Object? description = null,Object? budget = null,Object? serviceDate = null,Object? images = null,Object? subcategoryIds = null,}) {
  return _then(_PostJobRequests(
clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,serviceId: null == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,budget: null == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as double,serviceDate: null == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as String,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,subcategoryIds: null == subcategoryIds ? _self._subcategoryIds : subcategoryIds // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}


}

// dart format on
