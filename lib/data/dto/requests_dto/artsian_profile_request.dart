// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'artsian_profile_request.freezed.dart';
part 'artsian_profile_request.g.dart';

@freezed
abstract class ArtisanProfileRequest with _$ArtisanProfileRequest {
  factory ArtisanProfileRequest({
    required ArtisanDetails artisanDetails,
    required ArtisanSpecialization artisanSpecialization,
    required List<SpecializationTags> specializationTags,
    required List<Phonenumbers> phonenumbers,
    required ArtisanLocation artisanLocation,
  }) = _ArtisanProfileRequest;
  factory ArtisanProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$ArtisanProfileRequestFromJson(json);
}

@freezed
abstract class ArtisanDetails with _$ArtisanDetails {
  factory ArtisanDetails({
    @JsonKey(name: 'supabase_id') required String supabaseId,
    required String fullname,
    required String email,
    @JsonKey(name: 'whatsapp_number') required String whatsappNumber,
    required String address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'national_id') required String nationalId,
    @Json<PERSON>ey(name: 'avatar_url') required String avatarUrl,
    @Json<PERSON>ey(name: 'cover_image_url') required String coverImageUrl,
    @JsonKey(name: 'is_company') required int isCompany,
    required String about,
    @JsonKey(name: 'national_id_document') required String nationalIdDocument,
  }) = _ArtisanDetails;
  factory ArtisanDetails.fromJson(Map<String, dynamic> json) =>
      _$ArtisanDetailsFromJson(json);
}

@freezed
abstract class ArtisanSpecialization with _$ArtisanSpecialization {
  factory ArtisanSpecialization({
    @JsonKey(name: 'service_id') required int serviceId,
  }) = _ArtisanSpecialization;
  factory ArtisanSpecialization.fromJson(Map<String, dynamic> json) =>
      _$ArtisanSpecializationFromJson(json);
}


@freezed
abstract class SpecializationTags with _$SpecializationTags {
   factory SpecializationTags({
       @JsonKey(name: 'sub_category_id') required int subCategoryId,
   }) = _SpecializationTags;
   factory SpecializationTags.fromJson(Map<String, dynamic> json) => _$SpecializationTagsFromJson(json);
}


@freezed
abstract class Phonenumbers with _$Phonenumbers {
   factory Phonenumbers({
    @JsonKey(name: 'phonenumber') required String phonenumber,
    @JsonKey(name: 'second_phonenumber') required String secondPhonenumber,
   }) = _Phonenumbers;
   factory Phonenumbers.fromJson(Map<String, dynamic> json) => _$PhonenumbersFromJson(json);
}



@freezed
abstract class ArtisanLocation with _$ArtisanLocation {
   factory ArtisanLocation({
    required double latitude,
    required double longitude,
    required String address,
   }) = _ArtisanLocation;
   factory ArtisanLocation.fromJson(Map<String, dynamic> json) => _$ArtisanLocationFromJson(json);
}


