import 'package:freezed_annotation/freezed_annotation.dart';

part 'post_job_requests.freezed.dart';
part 'post_job_requests.g.dart';

@freezed
abstract class PostJobRequests with _$PostJobRequests {
  factory PostJobRequests({
    required int clientId,
    required int serviceId,
    required String description,
    required double budget,
    required String serviceDate,
    @Default([]) List<String> images,
    @Default([]) List<int> subcategoryIds,
  }) = _PostJobRequests;
  factory PostJobRequests.fromJson(Map<String, dynamic> json) =>
      _$PostJobRequestsFromJson(json);
}
