// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hardware_profile_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HardwareProfileDto {

 HardwareShopDetailsDto get hardwareShopDetails; List<HardwareShopImageDto> get hardwareShopimages; List<BranchDto> get branches;
/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareProfileDtoCopyWith<HardwareProfileDto> get copyWith => _$HardwareProfileDtoCopyWithImpl<HardwareProfileDto>(this as HardwareProfileDto, _$identity);

  /// Serializes this HardwareProfileDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareProfileDto&&(identical(other.hardwareShopDetails, hardwareShopDetails) || other.hardwareShopDetails == hardwareShopDetails)&&const DeepCollectionEquality().equals(other.hardwareShopimages, hardwareShopimages)&&const DeepCollectionEquality().equals(other.branches, branches));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,hardwareShopDetails,const DeepCollectionEquality().hash(hardwareShopimages),const DeepCollectionEquality().hash(branches));

@override
String toString() {
  return 'HardwareProfileDto(hardwareShopDetails: $hardwareShopDetails, hardwareShopimages: $hardwareShopimages, branches: $branches)';
}


}

/// @nodoc
abstract mixin class $HardwareProfileDtoCopyWith<$Res>  {
  factory $HardwareProfileDtoCopyWith(HardwareProfileDto value, $Res Function(HardwareProfileDto) _then) = _$HardwareProfileDtoCopyWithImpl;
@useResult
$Res call({
 HardwareShopDetailsDto hardwareShopDetails, List<HardwareShopImageDto> hardwareShopimages, List<BranchDto> branches
});


$HardwareShopDetailsDtoCopyWith<$Res> get hardwareShopDetails;

}
/// @nodoc
class _$HardwareProfileDtoCopyWithImpl<$Res>
    implements $HardwareProfileDtoCopyWith<$Res> {
  _$HardwareProfileDtoCopyWithImpl(this._self, this._then);

  final HardwareProfileDto _self;
  final $Res Function(HardwareProfileDto) _then;

/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? hardwareShopDetails = null,Object? hardwareShopimages = null,Object? branches = null,}) {
  return _then(_self.copyWith(
hardwareShopDetails: null == hardwareShopDetails ? _self.hardwareShopDetails : hardwareShopDetails // ignore: cast_nullable_to_non_nullable
as HardwareShopDetailsDto,hardwareShopimages: null == hardwareShopimages ? _self.hardwareShopimages : hardwareShopimages // ignore: cast_nullable_to_non_nullable
as List<HardwareShopImageDto>,branches: null == branches ? _self.branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchDto>,
  ));
}
/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareShopDetailsDtoCopyWith<$Res> get hardwareShopDetails {
  
  return $HardwareShopDetailsDtoCopyWith<$Res>(_self.hardwareShopDetails, (value) {
    return _then(_self.copyWith(hardwareShopDetails: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _HardwareProfileDto implements HardwareProfileDto {
   _HardwareProfileDto({required this.hardwareShopDetails, required final  List<HardwareShopImageDto> hardwareShopimages, required final  List<BranchDto> branches}): _hardwareShopimages = hardwareShopimages,_branches = branches;
  factory _HardwareProfileDto.fromJson(Map<String, dynamic> json) => _$HardwareProfileDtoFromJson(json);

@override final  HardwareShopDetailsDto hardwareShopDetails;
 final  List<HardwareShopImageDto> _hardwareShopimages;
@override List<HardwareShopImageDto> get hardwareShopimages {
  if (_hardwareShopimages is EqualUnmodifiableListView) return _hardwareShopimages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_hardwareShopimages);
}

 final  List<BranchDto> _branches;
@override List<BranchDto> get branches {
  if (_branches is EqualUnmodifiableListView) return _branches;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branches);
}


/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareProfileDtoCopyWith<_HardwareProfileDto> get copyWith => __$HardwareProfileDtoCopyWithImpl<_HardwareProfileDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareProfileDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareProfileDto&&(identical(other.hardwareShopDetails, hardwareShopDetails) || other.hardwareShopDetails == hardwareShopDetails)&&const DeepCollectionEquality().equals(other._hardwareShopimages, _hardwareShopimages)&&const DeepCollectionEquality().equals(other._branches, _branches));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,hardwareShopDetails,const DeepCollectionEquality().hash(_hardwareShopimages),const DeepCollectionEquality().hash(_branches));

@override
String toString() {
  return 'HardwareProfileDto(hardwareShopDetails: $hardwareShopDetails, hardwareShopimages: $hardwareShopimages, branches: $branches)';
}


}

/// @nodoc
abstract mixin class _$HardwareProfileDtoCopyWith<$Res> implements $HardwareProfileDtoCopyWith<$Res> {
  factory _$HardwareProfileDtoCopyWith(_HardwareProfileDto value, $Res Function(_HardwareProfileDto) _then) = __$HardwareProfileDtoCopyWithImpl;
@override @useResult
$Res call({
 HardwareShopDetailsDto hardwareShopDetails, List<HardwareShopImageDto> hardwareShopimages, List<BranchDto> branches
});


@override $HardwareShopDetailsDtoCopyWith<$Res> get hardwareShopDetails;

}
/// @nodoc
class __$HardwareProfileDtoCopyWithImpl<$Res>
    implements _$HardwareProfileDtoCopyWith<$Res> {
  __$HardwareProfileDtoCopyWithImpl(this._self, this._then);

  final _HardwareProfileDto _self;
  final $Res Function(_HardwareProfileDto) _then;

/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? hardwareShopDetails = null,Object? hardwareShopimages = null,Object? branches = null,}) {
  return _then(_HardwareProfileDto(
hardwareShopDetails: null == hardwareShopDetails ? _self.hardwareShopDetails : hardwareShopDetails // ignore: cast_nullable_to_non_nullable
as HardwareShopDetailsDto,hardwareShopimages: null == hardwareShopimages ? _self._hardwareShopimages : hardwareShopimages // ignore: cast_nullable_to_non_nullable
as List<HardwareShopImageDto>,branches: null == branches ? _self._branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchDto>,
  ));
}

/// Create a copy of HardwareProfileDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareShopDetailsDtoCopyWith<$Res> get hardwareShopDetails {
  
  return $HardwareShopDetailsDtoCopyWith<$Res>(_self.hardwareShopDetails, (value) {
    return _then(_self.copyWith(hardwareShopDetails: value));
  });
}
}


/// @nodoc
mixin _$HardwareShopDetailsDto {

 String get supabaseUUID; String get name; String get contactEmail; String get phoneNumber; String get ownerName; String get mainAddress;
/// Create a copy of HardwareShopDetailsDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareShopDetailsDtoCopyWith<HardwareShopDetailsDto> get copyWith => _$HardwareShopDetailsDtoCopyWithImpl<HardwareShopDetailsDto>(this as HardwareShopDetailsDto, _$identity);

  /// Serializes this HardwareShopDetailsDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareShopDetailsDto&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.name, name) || other.name == name)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,supabaseUUID,name,contactEmail,phoneNumber,ownerName,mainAddress);

@override
String toString() {
  return 'HardwareShopDetailsDto(supabaseUUID: $supabaseUUID, name: $name, contactEmail: $contactEmail, phoneNumber: $phoneNumber, ownerName: $ownerName, mainAddress: $mainAddress)';
}


}

/// @nodoc
abstract mixin class $HardwareShopDetailsDtoCopyWith<$Res>  {
  factory $HardwareShopDetailsDtoCopyWith(HardwareShopDetailsDto value, $Res Function(HardwareShopDetailsDto) _then) = _$HardwareShopDetailsDtoCopyWithImpl;
@useResult
$Res call({
 String supabaseUUID, String name, String contactEmail, String phoneNumber, String ownerName, String mainAddress
});




}
/// @nodoc
class _$HardwareShopDetailsDtoCopyWithImpl<$Res>
    implements $HardwareShopDetailsDtoCopyWith<$Res> {
  _$HardwareShopDetailsDtoCopyWithImpl(this._self, this._then);

  final HardwareShopDetailsDto _self;
  final $Res Function(HardwareShopDetailsDto) _then;

/// Create a copy of HardwareShopDetailsDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? supabaseUUID = null,Object? name = null,Object? contactEmail = null,Object? phoneNumber = null,Object? ownerName = null,Object? mainAddress = null,}) {
  return _then(_self.copyWith(
supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,ownerName: null == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareShopDetailsDto implements HardwareShopDetailsDto {
   _HardwareShopDetailsDto({required this.supabaseUUID, required this.name, required this.contactEmail, required this.phoneNumber, required this.ownerName, required this.mainAddress});
  factory _HardwareShopDetailsDto.fromJson(Map<String, dynamic> json) => _$HardwareShopDetailsDtoFromJson(json);

@override final  String supabaseUUID;
@override final  String name;
@override final  String contactEmail;
@override final  String phoneNumber;
@override final  String ownerName;
@override final  String mainAddress;

/// Create a copy of HardwareShopDetailsDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareShopDetailsDtoCopyWith<_HardwareShopDetailsDto> get copyWith => __$HardwareShopDetailsDtoCopyWithImpl<_HardwareShopDetailsDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareShopDetailsDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareShopDetailsDto&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.name, name) || other.name == name)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.ownerName, ownerName) || other.ownerName == ownerName)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,supabaseUUID,name,contactEmail,phoneNumber,ownerName,mainAddress);

@override
String toString() {
  return 'HardwareShopDetailsDto(supabaseUUID: $supabaseUUID, name: $name, contactEmail: $contactEmail, phoneNumber: $phoneNumber, ownerName: $ownerName, mainAddress: $mainAddress)';
}


}

/// @nodoc
abstract mixin class _$HardwareShopDetailsDtoCopyWith<$Res> implements $HardwareShopDetailsDtoCopyWith<$Res> {
  factory _$HardwareShopDetailsDtoCopyWith(_HardwareShopDetailsDto value, $Res Function(_HardwareShopDetailsDto) _then) = __$HardwareShopDetailsDtoCopyWithImpl;
@override @useResult
$Res call({
 String supabaseUUID, String name, String contactEmail, String phoneNumber, String ownerName, String mainAddress
});




}
/// @nodoc
class __$HardwareShopDetailsDtoCopyWithImpl<$Res>
    implements _$HardwareShopDetailsDtoCopyWith<$Res> {
  __$HardwareShopDetailsDtoCopyWithImpl(this._self, this._then);

  final _HardwareShopDetailsDto _self;
  final $Res Function(_HardwareShopDetailsDto) _then;

/// Create a copy of HardwareShopDetailsDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? supabaseUUID = null,Object? name = null,Object? contactEmail = null,Object? phoneNumber = null,Object? ownerName = null,Object? mainAddress = null,}) {
  return _then(_HardwareShopDetailsDto(
supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,ownerName: null == ownerName ? _self.ownerName : ownerName // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$HardwareShopImageDto {

 String get imageUrl;
/// Create a copy of HardwareShopImageDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareShopImageDtoCopyWith<HardwareShopImageDto> get copyWith => _$HardwareShopImageDtoCopyWithImpl<HardwareShopImageDto>(this as HardwareShopImageDto, _$identity);

  /// Serializes this HardwareShopImageDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareShopImageDto&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,imageUrl);

@override
String toString() {
  return 'HardwareShopImageDto(imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $HardwareShopImageDtoCopyWith<$Res>  {
  factory $HardwareShopImageDtoCopyWith(HardwareShopImageDto value, $Res Function(HardwareShopImageDto) _then) = _$HardwareShopImageDtoCopyWithImpl;
@useResult
$Res call({
 String imageUrl
});




}
/// @nodoc
class _$HardwareShopImageDtoCopyWithImpl<$Res>
    implements $HardwareShopImageDtoCopyWith<$Res> {
  _$HardwareShopImageDtoCopyWithImpl(this._self, this._then);

  final HardwareShopImageDto _self;
  final $Res Function(HardwareShopImageDto) _then;

/// Create a copy of HardwareShopImageDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? imageUrl = null,}) {
  return _then(_self.copyWith(
imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HardwareShopImageDto implements HardwareShopImageDto {
   _HardwareShopImageDto({required this.imageUrl});
  factory _HardwareShopImageDto.fromJson(Map<String, dynamic> json) => _$HardwareShopImageDtoFromJson(json);

@override final  String imageUrl;

/// Create a copy of HardwareShopImageDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareShopImageDtoCopyWith<_HardwareShopImageDto> get copyWith => __$HardwareShopImageDtoCopyWithImpl<_HardwareShopImageDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HardwareShopImageDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareShopImageDto&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,imageUrl);

@override
String toString() {
  return 'HardwareShopImageDto(imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$HardwareShopImageDtoCopyWith<$Res> implements $HardwareShopImageDtoCopyWith<$Res> {
  factory _$HardwareShopImageDtoCopyWith(_HardwareShopImageDto value, $Res Function(_HardwareShopImageDto) _then) = __$HardwareShopImageDtoCopyWithImpl;
@override @useResult
$Res call({
 String imageUrl
});




}
/// @nodoc
class __$HardwareShopImageDtoCopyWithImpl<$Res>
    implements _$HardwareShopImageDtoCopyWith<$Res> {
  __$HardwareShopImageDtoCopyWithImpl(this._self, this._then);

  final _HardwareShopImageDto _self;
  final $Res Function(_HardwareShopImageDto) _then;

/// Create a copy of HardwareShopImageDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? imageUrl = null,}) {
  return _then(_HardwareShopImageDto(
imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$BranchDto {

 String get branchName; String get address; String get city; String get phonenumberOne; String get phonenumberTwo; String get email; String get emailTwo; double get latitude; double get longitude;
/// Create a copy of BranchDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchDtoCopyWith<BranchDto> get copyWith => _$BranchDtoCopyWithImpl<BranchDto>(this as BranchDto, _$identity);

  /// Serializes this BranchDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchDto&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.phonenumberOne, phonenumberOne) || other.phonenumberOne == phonenumberOne)&&(identical(other.phonenumberTwo, phonenumberTwo) || other.phonenumberTwo == phonenumberTwo)&&(identical(other.email, email) || other.email == email)&&(identical(other.emailTwo, emailTwo) || other.emailTwo == emailTwo)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,branchName,address,city,phonenumberOne,phonenumberTwo,email,emailTwo,latitude,longitude);

@override
String toString() {
  return 'BranchDto(branchName: $branchName, address: $address, city: $city, phonenumberOne: $phonenumberOne, phonenumberTwo: $phonenumberTwo, email: $email, emailTwo: $emailTwo, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class $BranchDtoCopyWith<$Res>  {
  factory $BranchDtoCopyWith(BranchDto value, $Res Function(BranchDto) _then) = _$BranchDtoCopyWithImpl;
@useResult
$Res call({
 String branchName, String address, String city, String phonenumberOne, String phonenumberTwo, String email, String emailTwo, double latitude, double longitude
});




}
/// @nodoc
class _$BranchDtoCopyWithImpl<$Res>
    implements $BranchDtoCopyWith<$Res> {
  _$BranchDtoCopyWithImpl(this._self, this._then);

  final BranchDto _self;
  final $Res Function(BranchDto) _then;

/// Create a copy of BranchDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? branchName = null,Object? address = null,Object? city = null,Object? phonenumberOne = null,Object? phonenumberTwo = null,Object? email = null,Object? emailTwo = null,Object? latitude = null,Object? longitude = null,}) {
  return _then(_self.copyWith(
branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,phonenumberOne: null == phonenumberOne ? _self.phonenumberOne : phonenumberOne // ignore: cast_nullable_to_non_nullable
as String,phonenumberTwo: null == phonenumberTwo ? _self.phonenumberTwo : phonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,emailTwo: null == emailTwo ? _self.emailTwo : emailTwo // ignore: cast_nullable_to_non_nullable
as String,latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BranchDto implements BranchDto {
   _BranchDto({required this.branchName, required this.address, required this.city, required this.phonenumberOne, required this.phonenumberTwo, required this.email, required this.emailTwo, required this.latitude, required this.longitude});
  factory _BranchDto.fromJson(Map<String, dynamic> json) => _$BranchDtoFromJson(json);

@override final  String branchName;
@override final  String address;
@override final  String city;
@override final  String phonenumberOne;
@override final  String phonenumberTwo;
@override final  String email;
@override final  String emailTwo;
@override final  double latitude;
@override final  double longitude;

/// Create a copy of BranchDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchDtoCopyWith<_BranchDto> get copyWith => __$BranchDtoCopyWithImpl<_BranchDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BranchDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchDto&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.phonenumberOne, phonenumberOne) || other.phonenumberOne == phonenumberOne)&&(identical(other.phonenumberTwo, phonenumberTwo) || other.phonenumberTwo == phonenumberTwo)&&(identical(other.email, email) || other.email == email)&&(identical(other.emailTwo, emailTwo) || other.emailTwo == emailTwo)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,branchName,address,city,phonenumberOne,phonenumberTwo,email,emailTwo,latitude,longitude);

@override
String toString() {
  return 'BranchDto(branchName: $branchName, address: $address, city: $city, phonenumberOne: $phonenumberOne, phonenumberTwo: $phonenumberTwo, email: $email, emailTwo: $emailTwo, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class _$BranchDtoCopyWith<$Res> implements $BranchDtoCopyWith<$Res> {
  factory _$BranchDtoCopyWith(_BranchDto value, $Res Function(_BranchDto) _then) = __$BranchDtoCopyWithImpl;
@override @useResult
$Res call({
 String branchName, String address, String city, String phonenumberOne, String phonenumberTwo, String email, String emailTwo, double latitude, double longitude
});




}
/// @nodoc
class __$BranchDtoCopyWithImpl<$Res>
    implements _$BranchDtoCopyWith<$Res> {
  __$BranchDtoCopyWithImpl(this._self, this._then);

  final _BranchDto _self;
  final $Res Function(_BranchDto) _then;

/// Create a copy of BranchDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? branchName = null,Object? address = null,Object? city = null,Object? phonenumberOne = null,Object? phonenumberTwo = null,Object? email = null,Object? emailTwo = null,Object? latitude = null,Object? longitude = null,}) {
  return _then(_BranchDto(
branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,phonenumberOne: null == phonenumberOne ? _self.phonenumberOne : phonenumberOne // ignore: cast_nullable_to_non_nullable
as String,phonenumberTwo: null == phonenumberTwo ? _self.phonenumberTwo : phonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,emailTwo: null == emailTwo ? _self.emailTwo : emailTwo // ignore: cast_nullable_to_non_nullable
as String,latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
