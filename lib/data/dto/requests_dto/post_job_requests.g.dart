// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_job_requests.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PostJobRequests _$PostJobRequestsFromJson(Map<String, dynamic> json) =>
    _PostJobRequests(
      clientId: (json['clientId'] as num).toInt(),
      serviceId: (json['serviceId'] as num).toInt(),
      description: json['description'] as String,
      budget: (json['budget'] as num).toDouble(),
      serviceDate: json['serviceDate'] as String,
      images:
          (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      subcategoryIds:
          (json['subcategoryIds'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$PostJobRequestsToJson(_PostJobRequests instance) =>
    <String, dynamic>{
      'clientId': instance.clientId,
      'serviceId': instance.serviceId,
      'description': instance.description,
      'budget': instance.budget,
      'serviceDate': instance.serviceDate,
      'images': instance.images,
      'subcategoryIds': instance.subcategoryIds,
    };
