import 'package:freezed_annotation/freezed_annotation.dart';

part 'hardware_profile_dto.freezed.dart';
part 'hardware_profile_dto.g.dart';

@freezed
abstract class HardwareProfileDto with _$HardwareProfileDto {
  factory HardwareProfileDto({
    required HardwareShopDetailsDto hardwareShopDetails,
    required List<HardwareShopImageDto> hardwareShopimages,
    required List<BranchDto> branches,
    // required List<BranchEmailDto> branchEmails,
    // required List<BranchPhonenumberDto> branchPhonenumbers,
  }) = _HardwareProfileDto;
  factory HardwareProfileDto.fromJson(Map<String, dynamic> json) =>
      _$HardwareProfileDtoFromJson(json);
}

@freezed
abstract class HardwareShopDetailsDto with _$HardwareShopDetailsDto {
  factory HardwareShopDetailsDto({
    required String supabaseUUID,
    required String name,
    required String contactEmail,
    required String phoneNumber,
    required String ownerName,
    required String mainAddress,
  }) = _HardwareShopDetailsDto;
  factory HardwareShopDetailsDto.fromJson(Map<String, dynamic> json) =>
      _$HardwareShopDetailsDtoFromJson(json);
}

@freezed
abstract class HardwareShopImageDto with _$HardwareShopImageDto {
  factory HardwareShopImageDto({required String imageUrl}) =
      _HardwareShopImageDto;
  factory HardwareShopImageDto.fromJson(Map<String, dynamic> json) =>
      _$HardwareShopImageDtoFromJson(json);
}

@freezed
abstract class BranchDto with _$BranchDto {
  factory BranchDto({
    required String branchName,
    required String address,
    required String city,
    required String phonenumberOne,
    required String phonenumberTwo,
    required String email,
    required String emailTwo,
    required double latitude,
    required double longitude,
  }) = _BranchDto;
  factory BranchDto.fromJson(Map<String, dynamic> json) =>
      _$BranchDtoFromJson(json);
}

// @freezed
// abstract class BranchEmailDto with _$BranchEmailDto {
//   factory BranchEmailDto({required String email}) = _BranchEmailDto;
//   factory BranchEmailDto.fromJson(Map<String, dynamic> json) =>
//       _$BranchEmailDtoFromJson(json);
// }

// @freezed
// abstract class BranchPhonenumberDto with _$BranchPhonenumberDto {
//   factory BranchPhonenumberDto({required String phonenumber}) =
//       _BranchPhonenumberDto;
//   factory BranchPhonenumberDto.fromJson(Map<String, dynamic> json) =>
//       _$BranchPhonenumberDtoFromJson(json);
// }
