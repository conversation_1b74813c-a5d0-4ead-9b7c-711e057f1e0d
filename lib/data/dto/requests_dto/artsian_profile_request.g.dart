// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'artsian_profile_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ArtisanProfileRequest _$ArtisanProfileRequestFromJson(
  Map<String, dynamic> json,
) => _ArtisanProfileRequest(
  artisanDetails: ArtisanDetails.fromJson(
    json['artisanDetails'] as Map<String, dynamic>,
  ),
  artisanSpecialization: ArtisanSpecialization.fromJson(
    json['artisanSpecialization'] as Map<String, dynamic>,
  ),
  specializationTags:
      (json['specializationTags'] as List<dynamic>)
          .map((e) => SpecializationTags.fromJson(e as Map<String, dynamic>))
          .toList(),
  phonenumbers:
      (json['phonenumbers'] as List<dynamic>)
          .map((e) => Phonenumbers.fromJson(e as Map<String, dynamic>))
          .toList(),
  artisanLocation: ArtisanLocation.fromJson(
    json['artisanLocation'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$ArtisanProfileRequestToJson(
  _ArtisanProfileRequest instance,
) => <String, dynamic>{
  'artisanDetails': instance.artisanDetails,
  'artisanSpecialization': instance.artisanSpecialization,
  'specializationTags': instance.specializationTags,
  'phonenumbers': instance.phonenumbers,
  'artisanLocation': instance.artisanLocation,
};

_ArtisanDetails _$ArtisanDetailsFromJson(Map<String, dynamic> json) =>
    _ArtisanDetails(
      supabaseId: json['supabase_id'] as String,
      fullname: json['fullname'] as String,
      email: json['email'] as String,
      whatsappNumber: json['whatsapp_number'] as String,
      address: json['address'] as String,
      nationalId: json['national_id'] as String,
      avatarUrl: json['avatar_url'] as String,
      coverImageUrl: json['cover_image_url'] as String,
      isCompany: (json['is_company'] as num).toInt(),
      about: json['about'] as String,
      nationalIdDocument: json['national_id_document'] as String,
    );

Map<String, dynamic> _$ArtisanDetailsToJson(_ArtisanDetails instance) =>
    <String, dynamic>{
      'supabase_id': instance.supabaseId,
      'fullname': instance.fullname,
      'email': instance.email,
      'whatsapp_number': instance.whatsappNumber,
      'address': instance.address,
      'national_id': instance.nationalId,
      'avatar_url': instance.avatarUrl,
      'cover_image_url': instance.coverImageUrl,
      'is_company': instance.isCompany,
      'about': instance.about,
      'national_id_document': instance.nationalIdDocument,
    };

_ArtisanSpecialization _$ArtisanSpecializationFromJson(
  Map<String, dynamic> json,
) => _ArtisanSpecialization(serviceId: (json['service_id'] as num).toInt());

Map<String, dynamic> _$ArtisanSpecializationToJson(
  _ArtisanSpecialization instance,
) => <String, dynamic>{'service_id': instance.serviceId};

_SpecializationTags _$SpecializationTagsFromJson(Map<String, dynamic> json) =>
    _SpecializationTags(
      subCategoryId: (json['sub_category_id'] as num).toInt(),
    );

Map<String, dynamic> _$SpecializationTagsToJson(_SpecializationTags instance) =>
    <String, dynamic>{'sub_category_id': instance.subCategoryId};

_Phonenumbers _$PhonenumbersFromJson(Map<String, dynamic> json) =>
    _Phonenumbers(
      phonenumber: json['phonenumber'] as String,
      secondPhonenumber: json['second_phonenumber'] as String,
    );

Map<String, dynamic> _$PhonenumbersToJson(_Phonenumbers instance) =>
    <String, dynamic>{
      'phonenumber': instance.phonenumber,
      'second_phonenumber': instance.secondPhonenumber,
    };

_ArtisanLocation _$ArtisanLocationFromJson(Map<String, dynamic> json) =>
    _ArtisanLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
    );

Map<String, dynamic> _$ArtisanLocationToJson(_ArtisanLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
    };
