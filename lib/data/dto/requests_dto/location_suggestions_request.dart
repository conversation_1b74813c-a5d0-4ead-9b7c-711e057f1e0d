import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_suggestions_request.freezed.dart';
part 'location_suggestions_request.g.dart';

@freezed
abstract class LocationSuggestionsRequest with _$LocationSuggestionsRequest {
  const factory LocationSuggestionsRequest({
    required String input,
    required List<String> includedRegionCodes,
    required LocationBias locationBias,
  }) = _LocationSuggestionsRequest;

  factory LocationSuggestionsRequest.fromJson(Map<String, dynamic> json) =>
      _$LocationSuggestionsRequestFromJson(json);
}

@freezed
abstract class LocationBias with _$LocationBias {
  const factory LocationBias({
    required CircleModel circle,
  }) = _LocationBias;

  factory LocationBias.fromJson(Map<String, dynamic> json) =>
      _$LocationBiasFromJson(json);
}

@freezed
abstract class CircleModel with _$CircleModel {
  const factory CircleModel({
    required CenterModel center,
    required double radius,
  }) = _CircleModel;

  factory CircleModel.fromJson(Map<String, dynamic> json) =>
      _$CircleModelFromJson(json);
}

@freezed
abstract class CenterModel with _$CenterModel {
  const factory CenterModel({
    required double latitude,
    required double longitude,
  }) = _CenterModel;

  factory CenterModel.fromJson(Map<String, dynamic> json) =>
      _$CenterModelFromJson(json);
}
