// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hardware_profile_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_HardwareProfileDto _$HardwareProfileDtoFromJson(Map<String, dynamic> json) =>
    _HardwareProfileDto(
      hardwareShopDetails: HardwareShopDetailsDto.fromJson(
        json['hardwareShopDetails'] as Map<String, dynamic>,
      ),
      hardwareShopimages:
          (json['hardwareShopimages'] as List<dynamic>)
              .map(
                (e) => HardwareShopImageDto.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      branches:
          (json['branches'] as List<dynamic>)
              .map((e) => BranchDto.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$HardwareProfileDtoToJson(_HardwareProfileDto instance) =>
    <String, dynamic>{
      'hardwareShopDetails': instance.hardwareShopDetails,
      'hardwareShopimages': instance.hardwareShopimages,
      'branches': instance.branches,
    };

_HardwareShopDetailsDto _$HardwareShopDetailsDtoFromJson(
  Map<String, dynamic> json,
) => _HardwareShopDetailsDto(
  supabaseUUID: json['supabaseUUID'] as String,
  name: json['name'] as String,
  contactEmail: json['contactEmail'] as String,
  phoneNumber: json['phoneNumber'] as String,
  ownerName: json['ownerName'] as String,
  mainAddress: json['mainAddress'] as String,
);

Map<String, dynamic> _$HardwareShopDetailsDtoToJson(
  _HardwareShopDetailsDto instance,
) => <String, dynamic>{
  'supabaseUUID': instance.supabaseUUID,
  'name': instance.name,
  'contactEmail': instance.contactEmail,
  'phoneNumber': instance.phoneNumber,
  'ownerName': instance.ownerName,
  'mainAddress': instance.mainAddress,
};

_HardwareShopImageDto _$HardwareShopImageDtoFromJson(
  Map<String, dynamic> json,
) => _HardwareShopImageDto(imageUrl: json['imageUrl'] as String);

Map<String, dynamic> _$HardwareShopImageDtoToJson(
  _HardwareShopImageDto instance,
) => <String, dynamic>{'imageUrl': instance.imageUrl};

_BranchDto _$BranchDtoFromJson(Map<String, dynamic> json) => _BranchDto(
  branchName: json['branchName'] as String,
  address: json['address'] as String,
  city: json['city'] as String,
  phonenumberOne: json['phonenumberOne'] as String,
  phonenumberTwo: json['phonenumberTwo'] as String,
  email: json['email'] as String,
  emailTwo: json['emailTwo'] as String,
  latitude: (json['latitude'] as num).toDouble(),
  longitude: (json['longitude'] as num).toDouble(),
);

Map<String, dynamic> _$BranchDtoToJson(_BranchDto instance) =>
    <String, dynamic>{
      'branchName': instance.branchName,
      'address': instance.address,
      'city': instance.city,
      'phonenumberOne': instance.phonenumberOne,
      'phonenumberTwo': instance.phonenumberTwo,
      'email': instance.email,
      'emailTwo': instance.emailTwo,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
