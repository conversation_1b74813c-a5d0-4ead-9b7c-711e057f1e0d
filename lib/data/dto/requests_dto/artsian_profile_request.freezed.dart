// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artsian_profile_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ArtisanProfileRequest {

 ArtisanDetails get artisanDetails; ArtisanSpecialization get artisanSpecialization; List<SpecializationTags> get specializationTags; List<Phonenumbers> get phonenumbers; ArtisanLocation get artisanLocation;
/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanProfileRequestCopyWith<ArtisanProfileRequest> get copyWith => _$ArtisanProfileRequestCopyWithImpl<ArtisanProfileRequest>(this as ArtisanProfileRequest, _$identity);

  /// Serializes this ArtisanProfileRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanProfileRequest&&(identical(other.artisanDetails, artisanDetails) || other.artisanDetails == artisanDetails)&&(identical(other.artisanSpecialization, artisanSpecialization) || other.artisanSpecialization == artisanSpecialization)&&const DeepCollectionEquality().equals(other.specializationTags, specializationTags)&&const DeepCollectionEquality().equals(other.phonenumbers, phonenumbers)&&(identical(other.artisanLocation, artisanLocation) || other.artisanLocation == artisanLocation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,artisanDetails,artisanSpecialization,const DeepCollectionEquality().hash(specializationTags),const DeepCollectionEquality().hash(phonenumbers),artisanLocation);

@override
String toString() {
  return 'ArtisanProfileRequest(artisanDetails: $artisanDetails, artisanSpecialization: $artisanSpecialization, specializationTags: $specializationTags, phonenumbers: $phonenumbers, artisanLocation: $artisanLocation)';
}


}

/// @nodoc
abstract mixin class $ArtisanProfileRequestCopyWith<$Res>  {
  factory $ArtisanProfileRequestCopyWith(ArtisanProfileRequest value, $Res Function(ArtisanProfileRequest) _then) = _$ArtisanProfileRequestCopyWithImpl;
@useResult
$Res call({
 ArtisanDetails artisanDetails, ArtisanSpecialization artisanSpecialization, List<SpecializationTags> specializationTags, List<Phonenumbers> phonenumbers, ArtisanLocation artisanLocation
});


$ArtisanDetailsCopyWith<$Res> get artisanDetails;$ArtisanSpecializationCopyWith<$Res> get artisanSpecialization;$ArtisanLocationCopyWith<$Res> get artisanLocation;

}
/// @nodoc
class _$ArtisanProfileRequestCopyWithImpl<$Res>
    implements $ArtisanProfileRequestCopyWith<$Res> {
  _$ArtisanProfileRequestCopyWithImpl(this._self, this._then);

  final ArtisanProfileRequest _self;
  final $Res Function(ArtisanProfileRequest) _then;

/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? artisanDetails = null,Object? artisanSpecialization = null,Object? specializationTags = null,Object? phonenumbers = null,Object? artisanLocation = null,}) {
  return _then(_self.copyWith(
artisanDetails: null == artisanDetails ? _self.artisanDetails : artisanDetails // ignore: cast_nullable_to_non_nullable
as ArtisanDetails,artisanSpecialization: null == artisanSpecialization ? _self.artisanSpecialization : artisanSpecialization // ignore: cast_nullable_to_non_nullable
as ArtisanSpecialization,specializationTags: null == specializationTags ? _self.specializationTags : specializationTags // ignore: cast_nullable_to_non_nullable
as List<SpecializationTags>,phonenumbers: null == phonenumbers ? _self.phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<Phonenumbers>,artisanLocation: null == artisanLocation ? _self.artisanLocation : artisanLocation // ignore: cast_nullable_to_non_nullable
as ArtisanLocation,
  ));
}
/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanDetailsCopyWith<$Res> get artisanDetails {
  
  return $ArtisanDetailsCopyWith<$Res>(_self.artisanDetails, (value) {
    return _then(_self.copyWith(artisanDetails: value));
  });
}/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanSpecializationCopyWith<$Res> get artisanSpecialization {
  
  return $ArtisanSpecializationCopyWith<$Res>(_self.artisanSpecialization, (value) {
    return _then(_self.copyWith(artisanSpecialization: value));
  });
}/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanLocationCopyWith<$Res> get artisanLocation {
  
  return $ArtisanLocationCopyWith<$Res>(_self.artisanLocation, (value) {
    return _then(_self.copyWith(artisanLocation: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ArtisanProfileRequest implements ArtisanProfileRequest {
   _ArtisanProfileRequest({required this.artisanDetails, required this.artisanSpecialization, required final  List<SpecializationTags> specializationTags, required final  List<Phonenumbers> phonenumbers, required this.artisanLocation}): _specializationTags = specializationTags,_phonenumbers = phonenumbers;
  factory _ArtisanProfileRequest.fromJson(Map<String, dynamic> json) => _$ArtisanProfileRequestFromJson(json);

@override final  ArtisanDetails artisanDetails;
@override final  ArtisanSpecialization artisanSpecialization;
 final  List<SpecializationTags> _specializationTags;
@override List<SpecializationTags> get specializationTags {
  if (_specializationTags is EqualUnmodifiableListView) return _specializationTags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_specializationTags);
}

 final  List<Phonenumbers> _phonenumbers;
@override List<Phonenumbers> get phonenumbers {
  if (_phonenumbers is EqualUnmodifiableListView) return _phonenumbers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_phonenumbers);
}

@override final  ArtisanLocation artisanLocation;

/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanProfileRequestCopyWith<_ArtisanProfileRequest> get copyWith => __$ArtisanProfileRequestCopyWithImpl<_ArtisanProfileRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanProfileRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanProfileRequest&&(identical(other.artisanDetails, artisanDetails) || other.artisanDetails == artisanDetails)&&(identical(other.artisanSpecialization, artisanSpecialization) || other.artisanSpecialization == artisanSpecialization)&&const DeepCollectionEquality().equals(other._specializationTags, _specializationTags)&&const DeepCollectionEquality().equals(other._phonenumbers, _phonenumbers)&&(identical(other.artisanLocation, artisanLocation) || other.artisanLocation == artisanLocation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,artisanDetails,artisanSpecialization,const DeepCollectionEquality().hash(_specializationTags),const DeepCollectionEquality().hash(_phonenumbers),artisanLocation);

@override
String toString() {
  return 'ArtisanProfileRequest(artisanDetails: $artisanDetails, artisanSpecialization: $artisanSpecialization, specializationTags: $specializationTags, phonenumbers: $phonenumbers, artisanLocation: $artisanLocation)';
}


}

/// @nodoc
abstract mixin class _$ArtisanProfileRequestCopyWith<$Res> implements $ArtisanProfileRequestCopyWith<$Res> {
  factory _$ArtisanProfileRequestCopyWith(_ArtisanProfileRequest value, $Res Function(_ArtisanProfileRequest) _then) = __$ArtisanProfileRequestCopyWithImpl;
@override @useResult
$Res call({
 ArtisanDetails artisanDetails, ArtisanSpecialization artisanSpecialization, List<SpecializationTags> specializationTags, List<Phonenumbers> phonenumbers, ArtisanLocation artisanLocation
});


@override $ArtisanDetailsCopyWith<$Res> get artisanDetails;@override $ArtisanSpecializationCopyWith<$Res> get artisanSpecialization;@override $ArtisanLocationCopyWith<$Res> get artisanLocation;

}
/// @nodoc
class __$ArtisanProfileRequestCopyWithImpl<$Res>
    implements _$ArtisanProfileRequestCopyWith<$Res> {
  __$ArtisanProfileRequestCopyWithImpl(this._self, this._then);

  final _ArtisanProfileRequest _self;
  final $Res Function(_ArtisanProfileRequest) _then;

/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? artisanDetails = null,Object? artisanSpecialization = null,Object? specializationTags = null,Object? phonenumbers = null,Object? artisanLocation = null,}) {
  return _then(_ArtisanProfileRequest(
artisanDetails: null == artisanDetails ? _self.artisanDetails : artisanDetails // ignore: cast_nullable_to_non_nullable
as ArtisanDetails,artisanSpecialization: null == artisanSpecialization ? _self.artisanSpecialization : artisanSpecialization // ignore: cast_nullable_to_non_nullable
as ArtisanSpecialization,specializationTags: null == specializationTags ? _self._specializationTags : specializationTags // ignore: cast_nullable_to_non_nullable
as List<SpecializationTags>,phonenumbers: null == phonenumbers ? _self._phonenumbers : phonenumbers // ignore: cast_nullable_to_non_nullable
as List<Phonenumbers>,artisanLocation: null == artisanLocation ? _self.artisanLocation : artisanLocation // ignore: cast_nullable_to_non_nullable
as ArtisanLocation,
  ));
}

/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanDetailsCopyWith<$Res> get artisanDetails {
  
  return $ArtisanDetailsCopyWith<$Res>(_self.artisanDetails, (value) {
    return _then(_self.copyWith(artisanDetails: value));
  });
}/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanSpecializationCopyWith<$Res> get artisanSpecialization {
  
  return $ArtisanSpecializationCopyWith<$Res>(_self.artisanSpecialization, (value) {
    return _then(_self.copyWith(artisanSpecialization: value));
  });
}/// Create a copy of ArtisanProfileRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanLocationCopyWith<$Res> get artisanLocation {
  
  return $ArtisanLocationCopyWith<$Res>(_self.artisanLocation, (value) {
    return _then(_self.copyWith(artisanLocation: value));
  });
}
}


/// @nodoc
mixin _$ArtisanDetails {

@JsonKey(name: 'supabase_id') String get supabaseId; String get fullname; String get email;@JsonKey(name: 'whatsapp_number') String get whatsappNumber; String get address;@JsonKey(name: 'national_id') String get nationalId;@JsonKey(name: 'avatar_url') String get avatarUrl;@JsonKey(name: 'cover_image_url') String get coverImageUrl;@JsonKey(name: 'is_company') int get isCompany; String get about;@JsonKey(name: 'national_id_document') String get nationalIdDocument;
/// Create a copy of ArtisanDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanDetailsCopyWith<ArtisanDetails> get copyWith => _$ArtisanDetailsCopyWithImpl<ArtisanDetails>(this as ArtisanDetails, _$identity);

  /// Serializes this ArtisanDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanDetails&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.isCompany, isCompany) || other.isCompany == isCompany)&&(identical(other.about, about) || other.about == about)&&(identical(other.nationalIdDocument, nationalIdDocument) || other.nationalIdDocument == nationalIdDocument));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,supabaseId,fullname,email,whatsappNumber,address,nationalId,avatarUrl,coverImageUrl,isCompany,about,nationalIdDocument);

@override
String toString() {
  return 'ArtisanDetails(supabaseId: $supabaseId, fullname: $fullname, email: $email, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, coverImageUrl: $coverImageUrl, isCompany: $isCompany, about: $about, nationalIdDocument: $nationalIdDocument)';
}


}

/// @nodoc
abstract mixin class $ArtisanDetailsCopyWith<$Res>  {
  factory $ArtisanDetailsCopyWith(ArtisanDetails value, $Res Function(ArtisanDetails) _then) = _$ArtisanDetailsCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'supabase_id') String supabaseId, String fullname, String email,@JsonKey(name: 'whatsapp_number') String whatsappNumber, String address,@JsonKey(name: 'national_id') String nationalId,@JsonKey(name: 'avatar_url') String avatarUrl,@JsonKey(name: 'cover_image_url') String coverImageUrl,@JsonKey(name: 'is_company') int isCompany, String about,@JsonKey(name: 'national_id_document') String nationalIdDocument
});




}
/// @nodoc
class _$ArtisanDetailsCopyWithImpl<$Res>
    implements $ArtisanDetailsCopyWith<$Res> {
  _$ArtisanDetailsCopyWithImpl(this._self, this._then);

  final ArtisanDetails _self;
  final $Res Function(ArtisanDetails) _then;

/// Create a copy of ArtisanDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? supabaseId = null,Object? fullname = null,Object? email = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? coverImageUrl = null,Object? isCompany = null,Object? about = null,Object? nationalIdDocument = null,}) {
  return _then(_self.copyWith(
supabaseId: null == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String,fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,isCompany: null == isCompany ? _self.isCompany : isCompany // ignore: cast_nullable_to_non_nullable
as int,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nationalIdDocument: null == nationalIdDocument ? _self.nationalIdDocument : nationalIdDocument // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanDetails implements ArtisanDetails {
   _ArtisanDetails({@JsonKey(name: 'supabase_id') required this.supabaseId, required this.fullname, required this.email, @JsonKey(name: 'whatsapp_number') required this.whatsappNumber, required this.address, @JsonKey(name: 'national_id') required this.nationalId, @JsonKey(name: 'avatar_url') required this.avatarUrl, @JsonKey(name: 'cover_image_url') required this.coverImageUrl, @JsonKey(name: 'is_company') required this.isCompany, required this.about, @JsonKey(name: 'national_id_document') required this.nationalIdDocument});
  factory _ArtisanDetails.fromJson(Map<String, dynamic> json) => _$ArtisanDetailsFromJson(json);

@override@JsonKey(name: 'supabase_id') final  String supabaseId;
@override final  String fullname;
@override final  String email;
@override@JsonKey(name: 'whatsapp_number') final  String whatsappNumber;
@override final  String address;
@override@JsonKey(name: 'national_id') final  String nationalId;
@override@JsonKey(name: 'avatar_url') final  String avatarUrl;
@override@JsonKey(name: 'cover_image_url') final  String coverImageUrl;
@override@JsonKey(name: 'is_company') final  int isCompany;
@override final  String about;
@override@JsonKey(name: 'national_id_document') final  String nationalIdDocument;

/// Create a copy of ArtisanDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanDetailsCopyWith<_ArtisanDetails> get copyWith => __$ArtisanDetailsCopyWithImpl<_ArtisanDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanDetails&&(identical(other.supabaseId, supabaseId) || other.supabaseId == supabaseId)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.isCompany, isCompany) || other.isCompany == isCompany)&&(identical(other.about, about) || other.about == about)&&(identical(other.nationalIdDocument, nationalIdDocument) || other.nationalIdDocument == nationalIdDocument));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,supabaseId,fullname,email,whatsappNumber,address,nationalId,avatarUrl,coverImageUrl,isCompany,about,nationalIdDocument);

@override
String toString() {
  return 'ArtisanDetails(supabaseId: $supabaseId, fullname: $fullname, email: $email, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, coverImageUrl: $coverImageUrl, isCompany: $isCompany, about: $about, nationalIdDocument: $nationalIdDocument)';
}


}

/// @nodoc
abstract mixin class _$ArtisanDetailsCopyWith<$Res> implements $ArtisanDetailsCopyWith<$Res> {
  factory _$ArtisanDetailsCopyWith(_ArtisanDetails value, $Res Function(_ArtisanDetails) _then) = __$ArtisanDetailsCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'supabase_id') String supabaseId, String fullname, String email,@JsonKey(name: 'whatsapp_number') String whatsappNumber, String address,@JsonKey(name: 'national_id') String nationalId,@JsonKey(name: 'avatar_url') String avatarUrl,@JsonKey(name: 'cover_image_url') String coverImageUrl,@JsonKey(name: 'is_company') int isCompany, String about,@JsonKey(name: 'national_id_document') String nationalIdDocument
});




}
/// @nodoc
class __$ArtisanDetailsCopyWithImpl<$Res>
    implements _$ArtisanDetailsCopyWith<$Res> {
  __$ArtisanDetailsCopyWithImpl(this._self, this._then);

  final _ArtisanDetails _self;
  final $Res Function(_ArtisanDetails) _then;

/// Create a copy of ArtisanDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? supabaseId = null,Object? fullname = null,Object? email = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? coverImageUrl = null,Object? isCompany = null,Object? about = null,Object? nationalIdDocument = null,}) {
  return _then(_ArtisanDetails(
supabaseId: null == supabaseId ? _self.supabaseId : supabaseId // ignore: cast_nullable_to_non_nullable
as String,fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,isCompany: null == isCompany ? _self.isCompany : isCompany // ignore: cast_nullable_to_non_nullable
as int,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nationalIdDocument: null == nationalIdDocument ? _self.nationalIdDocument : nationalIdDocument // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ArtisanSpecialization {

@JsonKey(name: 'service_id') int get serviceId;
/// Create a copy of ArtisanSpecialization
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanSpecializationCopyWith<ArtisanSpecialization> get copyWith => _$ArtisanSpecializationCopyWithImpl<ArtisanSpecialization>(this as ArtisanSpecialization, _$identity);

  /// Serializes this ArtisanSpecialization to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanSpecialization&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serviceId);

@override
String toString() {
  return 'ArtisanSpecialization(serviceId: $serviceId)';
}


}

/// @nodoc
abstract mixin class $ArtisanSpecializationCopyWith<$Res>  {
  factory $ArtisanSpecializationCopyWith(ArtisanSpecialization value, $Res Function(ArtisanSpecialization) _then) = _$ArtisanSpecializationCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'service_id') int serviceId
});




}
/// @nodoc
class _$ArtisanSpecializationCopyWithImpl<$Res>
    implements $ArtisanSpecializationCopyWith<$Res> {
  _$ArtisanSpecializationCopyWithImpl(this._self, this._then);

  final ArtisanSpecialization _self;
  final $Res Function(ArtisanSpecialization) _then;

/// Create a copy of ArtisanSpecialization
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? serviceId = null,}) {
  return _then(_self.copyWith(
serviceId: null == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanSpecialization implements ArtisanSpecialization {
   _ArtisanSpecialization({@JsonKey(name: 'service_id') required this.serviceId});
  factory _ArtisanSpecialization.fromJson(Map<String, dynamic> json) => _$ArtisanSpecializationFromJson(json);

@override@JsonKey(name: 'service_id') final  int serviceId;

/// Create a copy of ArtisanSpecialization
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanSpecializationCopyWith<_ArtisanSpecialization> get copyWith => __$ArtisanSpecializationCopyWithImpl<_ArtisanSpecialization>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanSpecializationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanSpecialization&&(identical(other.serviceId, serviceId) || other.serviceId == serviceId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serviceId);

@override
String toString() {
  return 'ArtisanSpecialization(serviceId: $serviceId)';
}


}

/// @nodoc
abstract mixin class _$ArtisanSpecializationCopyWith<$Res> implements $ArtisanSpecializationCopyWith<$Res> {
  factory _$ArtisanSpecializationCopyWith(_ArtisanSpecialization value, $Res Function(_ArtisanSpecialization) _then) = __$ArtisanSpecializationCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'service_id') int serviceId
});




}
/// @nodoc
class __$ArtisanSpecializationCopyWithImpl<$Res>
    implements _$ArtisanSpecializationCopyWith<$Res> {
  __$ArtisanSpecializationCopyWithImpl(this._self, this._then);

  final _ArtisanSpecialization _self;
  final $Res Function(_ArtisanSpecialization) _then;

/// Create a copy of ArtisanSpecialization
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? serviceId = null,}) {
  return _then(_ArtisanSpecialization(
serviceId: null == serviceId ? _self.serviceId : serviceId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$SpecializationTags {

@JsonKey(name: 'sub_category_id') int get subCategoryId;
/// Create a copy of SpecializationTags
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpecializationTagsCopyWith<SpecializationTags> get copyWith => _$SpecializationTagsCopyWithImpl<SpecializationTags>(this as SpecializationTags, _$identity);

  /// Serializes this SpecializationTags to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SpecializationTags&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId);

@override
String toString() {
  return 'SpecializationTags(subCategoryId: $subCategoryId)';
}


}

/// @nodoc
abstract mixin class $SpecializationTagsCopyWith<$Res>  {
  factory $SpecializationTagsCopyWith(SpecializationTags value, $Res Function(SpecializationTags) _then) = _$SpecializationTagsCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'sub_category_id') int subCategoryId
});




}
/// @nodoc
class _$SpecializationTagsCopyWithImpl<$Res>
    implements $SpecializationTagsCopyWith<$Res> {
  _$SpecializationTagsCopyWithImpl(this._self, this._then);

  final SpecializationTags _self;
  final $Res Function(SpecializationTags) _then;

/// Create a copy of SpecializationTags
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? subCategoryId = null,}) {
  return _then(_self.copyWith(
subCategoryId: null == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SpecializationTags implements SpecializationTags {
   _SpecializationTags({@JsonKey(name: 'sub_category_id') required this.subCategoryId});
  factory _SpecializationTags.fromJson(Map<String, dynamic> json) => _$SpecializationTagsFromJson(json);

@override@JsonKey(name: 'sub_category_id') final  int subCategoryId;

/// Create a copy of SpecializationTags
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpecializationTagsCopyWith<_SpecializationTags> get copyWith => __$SpecializationTagsCopyWithImpl<_SpecializationTags>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SpecializationTagsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SpecializationTags&&(identical(other.subCategoryId, subCategoryId) || other.subCategoryId == subCategoryId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subCategoryId);

@override
String toString() {
  return 'SpecializationTags(subCategoryId: $subCategoryId)';
}


}

/// @nodoc
abstract mixin class _$SpecializationTagsCopyWith<$Res> implements $SpecializationTagsCopyWith<$Res> {
  factory _$SpecializationTagsCopyWith(_SpecializationTags value, $Res Function(_SpecializationTags) _then) = __$SpecializationTagsCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'sub_category_id') int subCategoryId
});




}
/// @nodoc
class __$SpecializationTagsCopyWithImpl<$Res>
    implements _$SpecializationTagsCopyWith<$Res> {
  __$SpecializationTagsCopyWithImpl(this._self, this._then);

  final _SpecializationTags _self;
  final $Res Function(_SpecializationTags) _then;

/// Create a copy of SpecializationTags
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? subCategoryId = null,}) {
  return _then(_SpecializationTags(
subCategoryId: null == subCategoryId ? _self.subCategoryId : subCategoryId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$Phonenumbers {

@JsonKey(name: 'phonenumber') String get phonenumber;@JsonKey(name: 'second_phonenumber') String get secondPhonenumber;
/// Create a copy of Phonenumbers
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PhonenumbersCopyWith<Phonenumbers> get copyWith => _$PhonenumbersCopyWithImpl<Phonenumbers>(this as Phonenumbers, _$identity);

  /// Serializes this Phonenumbers to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Phonenumbers&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber,secondPhonenumber);

@override
String toString() {
  return 'Phonenumbers(phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber)';
}


}

/// @nodoc
abstract mixin class $PhonenumbersCopyWith<$Res>  {
  factory $PhonenumbersCopyWith(Phonenumbers value, $Res Function(Phonenumbers) _then) = _$PhonenumbersCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'phonenumber') String phonenumber,@JsonKey(name: 'second_phonenumber') String secondPhonenumber
});




}
/// @nodoc
class _$PhonenumbersCopyWithImpl<$Res>
    implements $PhonenumbersCopyWith<$Res> {
  _$PhonenumbersCopyWithImpl(this._self, this._then);

  final Phonenumbers _self;
  final $Res Function(Phonenumbers) _then;

/// Create a copy of Phonenumbers
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phonenumber = null,Object? secondPhonenumber = null,}) {
  return _then(_self.copyWith(
phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,secondPhonenumber: null == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Phonenumbers implements Phonenumbers {
   _Phonenumbers({@JsonKey(name: 'phonenumber') required this.phonenumber, @JsonKey(name: 'second_phonenumber') required this.secondPhonenumber});
  factory _Phonenumbers.fromJson(Map<String, dynamic> json) => _$PhonenumbersFromJson(json);

@override@JsonKey(name: 'phonenumber') final  String phonenumber;
@override@JsonKey(name: 'second_phonenumber') final  String secondPhonenumber;

/// Create a copy of Phonenumbers
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhonenumbersCopyWith<_Phonenumbers> get copyWith => __$PhonenumbersCopyWithImpl<_Phonenumbers>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PhonenumbersToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Phonenumbers&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phonenumber,secondPhonenumber);

@override
String toString() {
  return 'Phonenumbers(phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber)';
}


}

/// @nodoc
abstract mixin class _$PhonenumbersCopyWith<$Res> implements $PhonenumbersCopyWith<$Res> {
  factory _$PhonenumbersCopyWith(_Phonenumbers value, $Res Function(_Phonenumbers) _then) = __$PhonenumbersCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'phonenumber') String phonenumber,@JsonKey(name: 'second_phonenumber') String secondPhonenumber
});




}
/// @nodoc
class __$PhonenumbersCopyWithImpl<$Res>
    implements _$PhonenumbersCopyWith<$Res> {
  __$PhonenumbersCopyWithImpl(this._self, this._then);

  final _Phonenumbers _self;
  final $Res Function(_Phonenumbers) _then;

/// Create a copy of Phonenumbers
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phonenumber = null,Object? secondPhonenumber = null,}) {
  return _then(_Phonenumbers(
phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,secondPhonenumber: null == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ArtisanLocation {

 double get latitude; double get longitude; String get address;
/// Create a copy of ArtisanLocation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanLocationCopyWith<ArtisanLocation> get copyWith => _$ArtisanLocationCopyWithImpl<ArtisanLocation>(this as ArtisanLocation, _$identity);

  /// Serializes this ArtisanLocation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanLocation&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,address);

@override
String toString() {
  return 'ArtisanLocation(latitude: $latitude, longitude: $longitude, address: $address)';
}


}

/// @nodoc
abstract mixin class $ArtisanLocationCopyWith<$Res>  {
  factory $ArtisanLocationCopyWith(ArtisanLocation value, $Res Function(ArtisanLocation) _then) = _$ArtisanLocationCopyWithImpl;
@useResult
$Res call({
 double latitude, double longitude, String address
});




}
/// @nodoc
class _$ArtisanLocationCopyWithImpl<$Res>
    implements $ArtisanLocationCopyWith<$Res> {
  _$ArtisanLocationCopyWithImpl(this._self, this._then);

  final ArtisanLocation _self;
  final $Res Function(ArtisanLocation) _then;

/// Create a copy of ArtisanLocation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? latitude = null,Object? longitude = null,Object? address = null,}) {
  return _then(_self.copyWith(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ArtisanLocation implements ArtisanLocation {
   _ArtisanLocation({required this.latitude, required this.longitude, required this.address});
  factory _ArtisanLocation.fromJson(Map<String, dynamic> json) => _$ArtisanLocationFromJson(json);

@override final  double latitude;
@override final  double longitude;
@override final  String address;

/// Create a copy of ArtisanLocation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanLocationCopyWith<_ArtisanLocation> get copyWith => __$ArtisanLocationCopyWithImpl<_ArtisanLocation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanLocationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanLocation&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.address, address) || other.address == address));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,address);

@override
String toString() {
  return 'ArtisanLocation(latitude: $latitude, longitude: $longitude, address: $address)';
}


}

/// @nodoc
abstract mixin class _$ArtisanLocationCopyWith<$Res> implements $ArtisanLocationCopyWith<$Res> {
  factory _$ArtisanLocationCopyWith(_ArtisanLocation value, $Res Function(_ArtisanLocation) _then) = __$ArtisanLocationCopyWithImpl;
@override @useResult
$Res call({
 double latitude, double longitude, String address
});




}
/// @nodoc
class __$ArtisanLocationCopyWithImpl<$Res>
    implements _$ArtisanLocationCopyWith<$Res> {
  __$ArtisanLocationCopyWithImpl(this._self, this._then);

  final _ArtisanLocation _self;
  final $Res Function(_ArtisanLocation) _then;

/// Create a copy of ArtisanLocation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? latitude = null,Object? longitude = null,Object? address = null,}) {
  return _then(_ArtisanLocation(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
