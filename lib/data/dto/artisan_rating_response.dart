// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_rating_response.freezed.dart';
part 'artisan_rating_response.g.dart';

@freezed
abstract class ArtisanRatingResponse with _$ArtisanRatingResponse {
  const factory ArtisanRatingResponse({
    int? id,
    int? rating,
    String? comments,
    @JsonKey(name: 'created_at') String? createdAt,
    ClientModel? client,
  }) = _ArtisanRatingResponse;

  factory ArtisanRatingResponse.fromJson(Map<String, dynamic> json) =>
      _$ArtisanRatingResponseFromJson(json);
}

@freezed
abstract class ClientModel with _$ClientModel {
  const factory ClientModel({int? id, String? name, String? avatar}) =
      _ClientModel;

  factory ClientModel.fromJson(Map<String, dynamic> json) =>
      _$ClientModelFromJson(json);
}
