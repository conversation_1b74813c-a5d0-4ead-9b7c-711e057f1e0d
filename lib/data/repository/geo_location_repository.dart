import 'dart:convert';
import 'dart:io';

import 'package:build_mate/core/dio_client.dart';
import 'package:build_mate/core/exception_handler.dart';
import 'package:build_mate/core/paths.dart';
import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/requests_dto/location_suggestions_request.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/utils/constants.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class GeoLocationRepository {
  Future<SuggestionsResponse> searchLocationSuggestions(
    LocationSuggestionsRequest request,
  ) async {
    try {
      final response = await DioClient.instance.post(
        placesUrl,
        data: jsonEncode(request),
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': Constants.GOOGLE_MAPS_API_KEY,
          },
        ),
      );

      return SuggestionsResponse.fromJson(response);
    } on DioException catch (e) {
      if (e.error is SocketException) {
        throw Failure(message: 'No Internet Connection', exception: e);
      }
      throw Failure(
        message: e.response?.statusMessage ?? 'Something went wrong',
        code: e.response?.statusCode,
      );
    }
  }

  Future<LocationResponse?> getLocationByPlaceId(
      {required String placeId}) async {
    try {
      final response = await DioClient.instance.get(
        '$placesBaseUrl/$placeId',
        queryParameters: {
          'fields': 'location',
          'key': Constants.GOOGLE_MAPS_API_KEY,
        },
      );
      if (kDebugMode) {
        print('CODE: ${response['code']}');
      }

      return LocationResponse.fromJson(response);
    } on DioException catch (e) {
      if (e.error is SocketException) {
        throw Failure(message: 'No Internet Connection', exception: e);
      }
      throw Failure(
          message: e.response?.statusMessage ?? 'Something went wrong',
          code: e.response?.statusCode);
    }
  }
}

final geoLocationRepoProvider = Provider<GeoLocationRepository>((ref) {
  return GeoLocationRepository();
});
