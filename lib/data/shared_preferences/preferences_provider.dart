import 'package:build_mate/data/shared_preferences/shared_utility_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';


/// A simple provider that exposes the SharedUtility methods
/// This provider doesn't use a state class, it directly provides access to the SharedUtility methods
final preferencesProvider = Provider<PreferencesService>((ref) {
  final sharedUtility = ref.watch(sharedUtilityProvider);
  return PreferencesService(sharedUtility);
});

/// Service class that wraps the SharedUtility functionality
class PreferencesService {
  PreferencesService(this._sharedUtility);

  final SharedUtility _sharedUtility;

  /// Sets the Supabase user ID in shared preferences
  void setSupabaseId({required String id}) {
    _sharedUtility.setSupabaseId(id: id);
  }

  /// Gets the Supabase user ID from shared preferences
  String getSupabaseId() {
    return _sharedUtility.getSupabaseId();
  }

  /// Sets whether the user is a client or service provider
  void setIsClient({required bool isClient}) {
    _sharedUtility.setIsClient(isClient: isClient);
  }

  /// Gets whether the user is a client or service provider
  /// Returns true if the user is a client, false if service provider
  bool getIsClient() {
    return _sharedUtility.getIsClient();
  }

}
