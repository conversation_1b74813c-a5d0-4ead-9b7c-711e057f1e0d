import 'package:build_mate/data/shared_preferences/pref_constants.dart';
import 'package:build_mate/main.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final sharedUtilityProvider = Provider<SharedUtility>((ref) {
  final sharedPrefs = ref.watch(sharedPreferencesProvider);
  final sharedPrefsAsync = ref.watch(asyncSharedPreferencesProvider);
  return SharedUtility(
    sharedPreferences: sharedPrefs,
    sharedPreferencesAsync: sharedPrefsAsync,
  );
});

class SharedUtility {
  SharedUtility({
    required this.sharedPreferencesAsync,
    required this.sharedPreferences,
  });

  final SharedPreferences sharedPreferences;
  final SharedPreferencesAsync sharedPreferencesAsync;

  String getSupabaseId() {
    return sharedPreferences.getString(PrefConstants.SUPABASE_USER_ID) ?? '';
  }

  void setSupabaseId({required String id}) {
    sharedPreferences.setString(PrefConstants.SUPABASE_USER_ID, id);
  }

  // New method to save user role
  void setIsClient({required bool isClient}) {
    sharedPreferences.setBool(PrefConstants.IS_CLIENT, isClient);
  }

  // New method to get user role
  bool getIsClient() {
    return sharedPreferences.getBool(PrefConstants.IS_CLIENT) ??
        true; // Default to client if not set
  }

  // Future<void> setCities(List<Map<String, dynamic>> cities) async {
  //   final jsonString = jsonEncode(cities);
  //   await sharedPreferencesAsync.setString(PrefConstants.CITIES, jsonString);
  // }

  // Future<List<Map<String, dynamic>>> getCities() async {
  //   return await sharedPreferencesAsync.getString(PrefConstants.CITIES).then((
  //     value,
  //   ) {
  //     final List<dynamic> jsonList = jsonDecode(value!);
  //     return jsonList.cast<Map<String, dynamic>>();
  //   });
  // }
}
