class ClientModel {
  final String id;
  final String supabaseId;
  final String? name;
  final String? avatar;

  ClientModel({
    required this.id,
    required this.supabaseId,
    this.name,
    this.avatar,
  });

  factory ClientModel.fromJson(Map<String, dynamic> json) {
    return ClientModel(
      id: json['id'].toString(),
      supabaseId: json['supabase_id'],
      name: json['name'],
      avatar: json['avatar'],
    );
  }
}
