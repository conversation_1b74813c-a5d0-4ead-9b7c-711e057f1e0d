
class ClientJobsModel {
  final int id;
  final String title;
  final String clientName;
  final String? clientAvatar;
  final String description;
  final double budget;
  final List<String> categories;
  final List<String> images;
  final String status;
  final DateTime serviceDate;
  final DateTime postDate;
  final List<dynamic> bids;
  final bool hasMyBid;
  final int? myBidId;
  bool isLoading;

  ClientJobsModel({
    required this.id,
    required this.title,
    required this.clientName,
    this.clientAvatar,
    required this.description,
    required this.budget,
    required this.categories,
    required this.images,
    required this.status,
    required this.serviceDate,
    required this.postDate,
    required this.bids,
    required this.hasMyBid,
    this.myBidId,
    this.isLoading = false,
  });
  
  


 
}
