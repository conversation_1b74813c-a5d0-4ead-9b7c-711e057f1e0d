// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_model_dto.freezed.dart';
part 'category_model_dto.g.dart';

@freezed
abstract class CategoryResponse with _$CategoryResponse {
  const factory CategoryResponse({
    int? id,
    String? name,
    @JsonKey(name: 'icon_name') String? iconName,
    @Json<PERSON>ey(name: 'display_order') int? displayOrder,
    @J<PERSON><PERSON><PERSON>(name: 'created_at') String? createdAt,
  }) = _CategoryResponse;

  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoryResponseFromJson(json);
}
