// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Conversation _$ConversationFromJson(Map<String, dynamic> json) =>
    _Conversation(
      id: (json['id'] as num).toInt(),
      clientId: (json['client_id'] as num).toInt(),
      artisanId: (json['artisan_id'] as num).toInt(),
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      client:
          json['client'] == null
              ? null
              : ClientModel.fromJson(json['client'] as Map<String, dynamic>),
      artisan:
          json['artisan'] == null
              ? null
              : ArtisanProfileDataResponse.fromJson(
                json['artisan'] as Map<String, dynamic>,
              ),
      lastMessage:
          json['lastMessage'] == null
              ? null
              : Message.fromJson(json['lastMessage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ConversationToJson(_Conversation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'client_id': instance.clientId,
      'artisan_id': instance.artisanId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'client': instance.client,
      'artisan': instance.artisan,
      'lastMessage': instance.lastMessage,
    };

_Message _$MessageFromJson(Map<String, dynamic> json) => _Message(
  id: (json['id'] as num).toInt(),
  conversationId: (json['conversation_id'] as num).toInt(),
  senderType: json['sender_type'] as String,
  senderId: (json['sender_id'] as num).toInt(),
  content: json['content'] as String,
  isRead: json['is_read'] as bool,
  createdAt: json['created_at'] as String,
);

Map<String, dynamic> _$MessageToJson(_Message instance) => <String, dynamic>{
  'id': instance.id,
  'conversation_id': instance.conversationId,
  'sender_type': instance.senderType,
  'sender_id': instance.senderId,
  'content': instance.content,
  'is_read': instance.isRead,
  'created_at': instance.createdAt,
};
