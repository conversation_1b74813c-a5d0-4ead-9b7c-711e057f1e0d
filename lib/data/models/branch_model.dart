class BranchModel {
  final String branchName;
  final String address;
  final String city;
  final double latitude;
  final double longitude;
  final String phonenumberOne;
  final String? phonenumberTwo;
  final String emailOne;
  final String? emailTwo;

  BranchModel({
    required this.branchName,
    required this.address,
    required this.city,
    required this.latitude,
    required this.longitude,
    required this.phonenumberOne,
    required this.phonenumberTwo,
    required this.emailOne,
    required this.emailTwo,
  });

  //Add to <PERSON><PERSON>,
  Map<String, dynamic> toJson() {
    return {
      'branchName': branchName,
      'address': address,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'phonenumberOne': phonenumberOne,
      'phonenumberTwo': phonenumberTwo,
      'emailOne': emailOne,
      'emailTwo': emailTwo,
    };
  }
}
