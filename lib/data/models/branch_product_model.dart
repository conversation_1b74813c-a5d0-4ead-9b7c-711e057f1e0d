class BranchProduct {
  final int id;
  final int branchId;
  final int? hardwareSubCategoryId;
  final String name;
  final String? brand;
  final String? modelNumber;
  final String? description;
  final String? sku;
  final double price;
  final int currentStock;
  final int? minStockLevel;
  final String? unitType;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BranchProduct({
    required this.id,
    required this.branchId,
    this.hardwareSubCategoryId,
    required this.name,
    this.brand,
    this.modelNumber,
    this.description,
    this.sku,
    required this.price,
    required this.currentStock,
    this.minStockLevel,
    this.unitType,
    this.createdAt,
    this.updatedAt,
  });

  factory BranchProduct.fromJson(Map<String, dynamic> json) {
    return BranchProduct(
      id: json['id'],
      branchId: json['branch_id'],
      hardwareSubCategoryId: json['hardware_sub_category_id'],
      name: json['name'],
      brand: json['brand'],
      modelNumber: json['model_number'],
      description: json['description'],
      sku: json['sku'],
      price: (json['price'] as num).toDouble(),
      currentStock: json['current_stock'],
      minStockLevel: json['min_stock_level'],
      unitType: json['unit_type'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }
}
