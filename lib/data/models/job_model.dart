import 'package:flutter/material.dart';

class JobModel {
  final int id;
  final String title;
  final String badgeText;
  final Color badgeColor;
  final List<String> categories;
  final String description;
  final String datePosted;
  final String status;
  final Color statusColor;
  final double budget;
  final List<String> images;

  JobModel({
    required this.id,
    required this.title,
    required this.badgeText,
    required this.badgeColor,
    required this.categories,
    required this.description,
    required this.datePosted,
    required this.status,
    required this.statusColor,
    required this.budget,
    required this.images,
  });
}