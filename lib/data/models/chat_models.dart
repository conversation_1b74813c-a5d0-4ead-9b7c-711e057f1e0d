// ignore_for_file: invalid_annotation_target

import 'package:build_mate/data/dto/client_profile_data_request.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_models.freezed.dart';
part 'chat_models.g.dart';

@freezed
abstract class Conversation with _$Conversation {
  const factory Conversation({
    required int id,
    @Json<PERSON><PERSON>(name: 'client_id') required int clientId,
    @Json<PERSON><PERSON>(name: 'artisan_id') required int artisanId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required String createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required String updatedAt,
    ClientModel? client,
    ArtisanProfileDataResponse? artisan,
    Message? lastMessage,
  }) = _Conversation;

  factory Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);
}

@freezed
abstract class Message with _$Message {
  const factory Message({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'conversation_id') required int conversationId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_type') required String senderType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id') required int senderId,
    required String content,
    @JsonKey(name: 'is_read') required bool isRead,
    @JsonKey(name: 'created_at') required String createdAt,
  }) = _Message;

  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);
}