import 'dart:io';

import 'package:build_mate/core/exception_handler.dart';
import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/requests_dto/location_suggestions_request.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/data/repository/geo_location_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

class GeoLocationService {
  GeoLocationService(this._geoLocationRepository);

  final GeoLocationRepository _geoLocationRepository;

  Future<Result<SuggestionsResponse, Exception>> searchLocationSuggestions(
    LocationSuggestionsRequest request,
  ) async {
    try {
      final response = await _geoLocationRepository.searchLocationSuggestions(
        request,
      );

      return Success(response);
    } on DioException catch (e) {
      if (e.error is SocketException) {
        throw Failure(message: 'No Internet Connection', exception: e);
      }
      throw Failure(
        message: e.response?.statusMessage ?? 'Something went wrong',
        code: e.response?.statusCode,
      );
    }
  }

  Future<Result<LocationResponse, Exception>> getLocationByPlaceId({
    required String placeId,
  }) async {
    try {
      final placeResponse = await _geoLocationRepository.getLocationByPlaceId(
        placeId: placeId,
      );

      return Success(placeResponse ?? LocationResponse());
    } on Failure catch (failure) {
      if (kDebugMode) {
        print(failure.message);
      }
      return Error(failure);
    }
  }
}

final geoLocationServiceProvider = Provider<GeoLocationService>((ref) {
  final geoLocationRepository = ref.watch(geoLocationRepoProvider);

  return GeoLocationService(geoLocationRepository);
});
