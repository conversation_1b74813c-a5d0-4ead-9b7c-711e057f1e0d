import 'package:build_mate/data/dto/client_profile_data_request.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:build_mate/data/models/chat_models.dart';
import 'package:flutter/foundation.dart';

class ChatService {
  final supabase = Supabase.instance.client;

  // Get or create a conversation between a client and an artisan
  Future<Conversation> getOrCreateConversation(
    int clientId,
    int artisanId,
  ) async {
    try {
      // Check if conversation exists
      final response =
          await supabase
              .from('conversations')
              .select('*')
              .eq('client_id', clientId)
              .eq('artisan_id', artisanId)
              .single();

      return Conversation.fromJson(response);
    } catch (e) {
      // Create new conversation
      final response =
          await supabase
              .from('conversations')
              .insert({'client_id': clientId, 'artisan_id': artisanId})
              .select()
              .single();

      return Conversation.from<PERSON><PERSON>(response);
    }
  }

  // Get all conversations for a user (client or artisan)
  Future<List<Conversation>> getConversations(bool isClient, int userId) async {
    final field = isClient ? 'client_id' : 'artisan_id';

    try {
      if (kDebugMode) {
        print('Fetching conversations for $field: $userId');
      }

      final response = await supabase
          .from('conversations')
          .select('''
            *,
            client:client_id(id, name, email, avatar),
            artisan:artisan_id(id, name, email, avatar),
            lastMessage:messages(
              id, 
              content, 
              sender_type, 
              sender_id, 
              is_read, 
              created_at
            )
          ''')
          .eq(field, userId)
          .order('updated_at', ascending: false);

      if (kDebugMode) {
        print('Raw conversation response: $response');
      }

      // Process to get only the last message
      final List<Conversation> conversations = [];
      for (final item in response) {
        try {
          // Process the lastMessage array to get only the first message
          final lastMessageData =
              item['lastMessage'] != null &&
                      (item['lastMessage'] as List).isNotEmpty
                  ? (item['lastMessage'] as List).first
                  : null;

          // Create a new item with the processed lastMessage
          final processedItem = {
            'id': item['id'],
            'client_id': item['client_id'],
            'artisan_id': item['artisan_id'],
            'created_at': item['created_at'],
            'updated_at': item['updated_at'],
            'client': item['client'],
            'artisan': item['artisan'],
            'lastMessage': lastMessageData,
          };

          if (kDebugMode) {
            print('Processing conversation: ${processedItem['id']}');
            print('Client: ${processedItem['client']}');
            print('Artisan: ${processedItem['artisan']}');
            print('Last message: $lastMessageData');
          }

          // Create the Conversation object
          final conversation = Conversation(
            id: processedItem['id'],
            clientId: processedItem['client_id'],
            artisanId: processedItem['artisan_id'],
            createdAt: processedItem['created_at'],
            updatedAt: processedItem['updated_at'],
            client:
                processedItem['client'] != null
                    ? ClientModel(
                      id: processedItem['client']['id'],
                      name: processedItem['client']['name'] ?? 'Unknown',
                      email: processedItem['client']['email'] ?? '',
                      avatar: processedItem['client']['avatar'] ?? '',
                    )
                    : null,
            artisan:
                processedItem['artisan'] != null
                    ? ArtisanProfileDataResponse(
                      id: processedItem['artisan']['id'],
                      name: processedItem['artisan']['name'] ?? 'Unknown',
                      email: processedItem['artisan']['email'] ?? '',
                      avatar: processedItem['artisan']['avatar'] ?? '',
                    )
                    : null,
            lastMessage:
                lastMessageData != null
                    ? Message(
                      id: lastMessageData['id'],
                      content: lastMessageData['content'] ?? '',
                      senderType: lastMessageData['sender_type'] ?? '',
                      senderId: lastMessageData['sender_id'],
                      isRead: lastMessageData['is_read'] ?? false,
                      createdAt: lastMessageData['created_at'],
                      conversationId: processedItem['id'],
                    )
                    : null,
          );

          conversations.add(conversation);

          if (kDebugMode) {
            print('Successfully processed conversation: ${conversation.id}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error processing conversation item: $e');
            print('Problematic item: $item');
          }
          // Skip this item and continue with others
          continue;
        }
      }

      if (kDebugMode) {
        print(
          'Found ${conversations.length} conversations for $field: $userId',
        );
      }

      return conversations;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getConversations: $e');
      }
      rethrow;
    }
  }

  // Helper method to ensure client data has all required fields
  // Map<String, dynamic> _sanitizeClientData(dynamic clientData) {
  //   if (clientData == null) return {};

  //   return {
  //     'id': clientData['id'] ?? 0,
  //     'name': clientData['name'] ?? 'Unknown',
  //     'avatar': clientData['avatar'] ?? '',
  //     'email': clientData['email'] ?? '',
  //     'phone': clientData['phone'] ?? '',
  //     // Add other required fields with default values
  //   };
  // }

  // Helper method to ensure artisan data has all required fields
  // Map<String, dynamic> _sanitizeArtisanData(dynamic artisanData) {
  //   if (artisanData == null) return {};

  //   return {
  //     'id': artisanData['id'] ?? 0,
  //     'name': artisanData['name'] ?? 'Unknown',
  //     'avatar': artisanData['avatar'] ?? '',
  //     'email': artisanData['email'] ?? '',
  //     'phone': artisanData['phone'] ?? '',
  //     // Add other required fields with default values
  //   };
  // }

  // Get messages for a conversation
  Future<List<Message>> getMessages(int conversationId) async {
    final response = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at');

    return response.map<Message>((json) => Message.fromJson(json)).toList();
  }

  // Send a message
  Future<Message> sendMessage({
    required int conversationId,
    required String senderType,
    required int senderId,
    required String content,
  }) async {
    final response =
        await supabase
            .from('messages')
            .insert({
              'conversation_id': conversationId,
              'sender_type': senderType,
              'sender_id': senderId,
              'content': content,
            })
            .select()
            .single();

    return Message.fromJson(response);
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(int conversationId, String senderType) async {
    // Only mark messages from the other party as read
    final otherSenderType = senderType == 'client' ? 'artisan' : 'client';

    await supabase
        .from('messages')
        .update({'is_read': true})
        .eq('conversation_id', conversationId)
        .eq('sender_type', otherSenderType)
        .eq('is_read', false);
  }

  // Subscribe to new messages in a conversation
  RealtimeChannel subscribeToConversation(
    int conversationId,
    Function(Message) onNewMessage,
  ) {
    final channel =
        supabase
            .channel('chat:$conversationId')
            .onBroadcast(
              event: 'INSERT',
              callback: (payload) {
                if (kDebugMode) {
                  print('Broadcast received: ${payload.toString()}');
                }
                final message = Message.fromJson(payload);
                onNewMessage(message);
              },
            )
            .subscribe();

    return channel;
  }

  // Delete a message
  Future<void> deleteMessage(int messageId) async {
    if (kDebugMode) {
      print('Deleting message with ID: $messageId');
    }
    try {
      // Get the message details before deleting (for broadcasting)
      final messageDetails =
          await supabase
              .from('messages')
              .select('id, conversation_id')
              .eq('id', messageId)
              .maybeSingle();

      if (messageDetails != null) {
        if (kDebugMode) {
          print('Found message to delete: $messageDetails');
        }
        final conversationId = messageDetails['conversation_id'];

        // Delete the message
        await supabase.from('messages').delete().eq('id', messageId);

        if (kDebugMode) {
          print('Message deleted successfully from database');
        }

        // Broadcast a deletion event to all clients
        final channelName = 'can$conversationId';
        if (kDebugMode) {
          print('Broadcasting deletion event to channel: $channelName');
        }

        try {
          // Using the latest Supabase SDK broadcast API
          await supabase.channel(channelName).sendBroadcastMessage(
            event: 'shout',
            payload: {
              'type': 'delete_message',
              'message_id': messageId,
              'conversation_id': conversationId,
              'timestamp': DateTime.now().toIso8601String(),
            },
          );
          if (kDebugMode) {
            print('Deletion broadcast sent successfully');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error sending deletion broadcast: $e');
          }
          // Continue even if broadcast fails
        }
      } else {
        if (kDebugMode) {
          print('Message with ID $messageId not found');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting message: $e');
      }
      rethrow;
    }
  }

  // Update a message
  Future<Message> updateMessage(int messageId, String newContent) async {
    if (kDebugMode) {
      print('Updating message with ID: $messageId to content: $newContent');
    }
    try {
      // First verify the message exists
      final existingMessage = await supabase
          .from('messages')
          .select()
          .eq('id', messageId)
          .maybeSingle();
      
      if (kDebugMode) {
        print('Found existing message: $existingMessage');
      }
      
      final response = await supabase
          .from('messages')
          .update({'content': newContent})
          .eq('id', messageId)
          .select()
          .single();

      if (kDebugMode) {
        print('Message updated successfully in database: $response');
      }
      return Message.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating message: $e');
      }
      rethrow;
    }
  }

  // Subscribe to broadcast events for a conversation with full event handling
  RealtimeChannel subscribeToBroadcastEvents(
    int conversationId, {
    Function(Message)? onNewMessage,
    Function(Message)? onUpdateMessage,
    Function(int)? onDeleteMessage,
  }) {
    if (kDebugMode) {
      print('Setting up broadcast subscription for conversation $conversationId');
    }

    // Use the same channel name format as in the Postgres function
    final channelName = 'can$conversationId';
    if (kDebugMode) {
      print('ChatService: Subscribing to channel: $channelName');
    }

    final channel = supabase
        .channel(channelName)
        .onBroadcast(
          event: 'INSERT',
          callback: (payload) {
            if (kDebugMode) {
              print('[$channelName] Received broadcast INSERT: $payload');
            }
            if (payload['table'] == 'messages' &&
                payload['schema'] == 'public' &&
                payload['record']['conversation_id'] == conversationId) {
              try {
                final message = Message.fromJson(payload['record']);
                if (kDebugMode) {
                  print(
                  '[$channelName] Processing broadcast new message: ${message.id}',
                );
                }
                if (onNewMessage != null) onNewMessage(message);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing broadcast message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: $payload');
                }
              }
            }
          },
        )
        .onBroadcast(
          event: 'UPDATE',
          callback: (payload) {
            if (kDebugMode) {
              print('[$channelName] Received broadcast UPDATE: $payload');
            }
            if (payload['table'] == 'messages' &&
                payload['schema'] == 'public' &&
                payload['record']['conversation_id'] == conversationId) {
              try {
                final message = Message.fromJson(payload['record']);
                if (kDebugMode) {
                  print(
                  '[$channelName] Processing broadcast updated message: ${message.id}',
                );
                }
                if (onUpdateMessage != null) onUpdateMessage(message);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing broadcast update: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: $payload');
                }
              }
            }
          },
        )
        .onBroadcast(
          event: 'DELETE',
          callback: (payload) {
            if (kDebugMode) {
              print('[$channelName] Received broadcast DELETE: $payload');
            }
            if (payload['table'] == 'messages' &&
                payload['schema'] == 'public' &&
                payload['old_record']['conversation_id'] == conversationId) {
              try {
                final messageId = payload['old_record']['id'];
                if (kDebugMode) {
                  print(
                  '[$channelName] Processing broadcast deleted message: $messageId',
                );
                }
                if (onDeleteMessage != null) onDeleteMessage(messageId);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing broadcast delete: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: $payload');
                }
              }
            }
          },
        )
        .subscribe((status, error) {
          if (error != null) {
            if (kDebugMode) {
              print(
              '[$channelName] Error subscribing to broadcast events: $error',
            );
            }
          } else {
            if (kDebugMode) {
              print(
              '[$channelName] Successfully subscribed to broadcast events: $status',
            );
            }
            if (kDebugMode) {
              print(
              '[$channelName] Channel is now active and listening for events',
            );
            }
          }
        });

    return channel;
  }
}
