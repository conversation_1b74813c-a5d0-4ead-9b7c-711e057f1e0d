import 'package:build_mate/core/supabase_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:bcrypt/bcrypt.dart';

final branchAuthProvider = Provider<BranchAuthServices>((ref) {
  final supabase = ref.watch(supabaseProvider);
  return BranchAuthServices(supabase);
});

class BranchAuthServices {
  final SupabaseClient _supabase;

  BranchAuthServices(this._supabase);

  // ========================
  // 1. CREATE USER METHOD
  // ========================
  Future<Map<String, dynamic>> createUser({
    required String email,
    required int branchId,
    required String fullName,
  }) async {
    try {
      // Validate email format
      if (!_isValidEmail(email)) {
        throw 'Invalid email format';
      }

      final supabase = Supabase.instance.client;

      // Check for existing user
      final existingUser =
          await supabase
              .from('branch_users')
              .select()
              .eq('email', email)
              .maybeSingle();

      if (existingUser != null) {
        throw 'Email already in use';
      }

      // Generate random password
      final password = List.generate(15, (index) {
        final random = DateTime.now().microsecondsSinceEpoch % 3;
        switch (random) {
          case 0:
            return String.fromCharCode(
              65 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Uppercase
          case 1:
            return String.fromCharCode(
              97 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Lowercase
          default:
            return (DateTime.now().microsecondsSinceEpoch % 10)
                .toString(); // Numbers
        }
      }).join('');

      // Hash password with bcrypt
      final hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt());

      // Create user record
      final newUser = {
        "email": email,
        "hash_password": hashedPassword,
        "branch_id": branchId,
        "name": fullName,
      };

      // Insert into database
      final response =
          await supabase.from('branch_users').insert(newUser).select();

      return {'success': true, 'data': response.first};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // ========================
  // 2. LOGIN USER METHOD
  // ========================
  Future<Map<String, dynamic>> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      // Check login attempts
      final canAttempt = await _checkLoginAttempts(email);
      if (!canAttempt) {
        throw 'Too many failed attempts. Try again later.';
      }

      // Get user from database
      final userData =
          await _supabase
              .from('branch_users')
              .select()
              .eq('email', email)
              .maybeSingle();

      if (userData == null) {
        // await _recordFailedAttempt(email);
        throw 'Invalid credentials';
      }

      // Verify account status
      // if (userData['is_active'] != true) {
      //   throw 'Account is disabled';
      // }

      // Verify password
      final isValid = BCrypt.checkpw(password, userData['password_hash']);
      if (!isValid) {
        // await _recordFailedAttempt(email);
        throw 'Invalid credentials';
      }

      // Reset failed attempts on successful login
      // await _supabase
      //     .from('branch_users')
      //     .update({'failed_attempts': 0})
      //     .eq('email', email);

      // Update last login
      // await _supabase
      //     .from('branch_users')
      //     .update({'last_login': DateTime.now().toIso8601String()})
      //     .eq('email', email);

      // Get full user data with branch info
      final fullUserData = await _getUserWithBranch(email);

      return {'success': true, 'data': fullUserData};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // ========================
  // HELPER METHODS
  // ========================
  Future<Map<String, dynamic>> _getUserWithBranch(String email) async {
    return await _supabase
        .from('branch_users')
        .select('*, branch:branches(*, company:companies(*))')
        .eq('email', email)
        .single();
  }

  Future<bool> _checkLoginAttempts(String email) async {
    final user =
        await _supabase
            .from('branch_users')
            .select('failed_attempts')
            .eq('email', email)
            .maybeSingle();

    return (user?['failed_attempts'] ?? 0) < 5;
  }

  // Future<void> _recordFailedAttempt(String email) async {
  //   await _supabase.rpc(
  //     'increment_failed_attempts',
  //     params: {'user_email': email},
  //   );
  // }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
