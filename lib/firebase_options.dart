// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA1iAeF0YNh9rymvc9ruQR3KqjEgcV8kmE',
    appId: '1:788242194530:web:9eb00fb3609145436c713b',
    messagingSenderId: '788242194530',
    projectId: 'buildpal-18ea2',
    authDomain: 'buildpal-18ea2.firebaseapp.com',
    storageBucket: 'buildpal-18ea2.firebasestorage.app',
    measurementId: 'G-H3CKG81G2X',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC8ttpcnXgty1OlBsCcmUpmTNeLA6y7GdY',
    appId: '1:788242194530:android:be87ab452e1396d86c713b',
    messagingSenderId: '788242194530',
    projectId: 'buildpal-18ea2',
    storageBucket: 'buildpal-18ea2.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDSMCxAeGFSlN5sXb0vQPD7Mxxmtlq8rKE',
    appId: '1:788242194530:ios:891b09c15e94d0fd6c713b',
    messagingSenderId: '788242194530',
    projectId: 'buildpal-18ea2',
    storageBucket: 'buildpal-18ea2.firebasestorage.app',
    iosBundleId: 'com.buildmate.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDSMCxAeGFSlN5sXb0vQPD7Mxxmtlq8rKE',
    appId: '1:788242194530:ios:d9345347edc439b46c713b',
    messagingSenderId: '788242194530',
    projectId: 'buildpal-18ea2',
    storageBucket: 'buildpal-18ea2.firebasestorage.app',
    iosBundleId: 'com.example.buildMate',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyA1iAeF0YNh9rymvc9ruQR3KqjEgcV8kmE',
    appId: '1:788242194530:web:32e3c65f49b077d16c713b',
    messagingSenderId: '788242194530',
    projectId: 'buildpal-18ea2',
    authDomain: 'buildpal-18ea2.firebaseapp.com',
    storageBucket: 'buildpal-18ea2.firebasestorage.app',
    measurementId: 'G-2H2J2F9SWB',
  );
}
