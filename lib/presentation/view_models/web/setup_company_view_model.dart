import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/requests_dto/hardware_profile_dto.dart';
import 'package:build_mate/data/dto/requests_dto/location_suggestions_request.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/data/models/branch_model.dart';
import 'package:build_mate/data/services/geo_location_services.dart';
import 'package:build_mate/presentation/state/web/hardware_shop_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// Class to track upload progress for each image
class ImageUploadProgress {
  final Uint8List imageBytes;
  final String fileName;
  final double progress;
  final bool isUploading;
  final String? error;
  final String? url;

  ImageUploadProgress({
    required this.imageBytes,
    required this.fileName,
    this.progress = 0.0,
    this.isUploading = false,
    this.error,
    this.url,
  });

  ImageUploadProgress copyWith({
    Uint8List? imageBytes,
    String? fileName,
    double? progress,
    bool? isUploading,
    String? error,
    String? url,
  }) {
    return ImageUploadProgress(
      imageBytes: imageBytes ?? this.imageBytes,
      fileName: fileName ?? this.fileName,
      progress: progress ?? this.progress,
      isUploading: isUploading ?? this.isUploading,
      error: error,
      url: url ?? this.url,
    );
  }
}

// Provider
final setupCompanyViewModelProvider =
    StateNotifierProvider<SetupCompanyViewModel, HardwareShopState>((ref) {
      return SetupCompanyViewModel(ref.watch(geoLocationServiceProvider));
    });

class SetupCompanyViewModel extends StateNotifier<HardwareShopState> {
  final supabase = Supabase.instance.client;

  SetupCompanyViewModel(this._geoLocationServiceImpl)
    : super(
        HardwareShopState(
          addressLocationResult: AsyncValue.data(SuggestionsResponse()),
          locationByIdResponse: AsyncValue.data(LocationResponse()),
        ),
      );
  final GeoLocationService _geoLocationServiceImpl;
  // Update shop details
  void updateShopDetails({
    required String name,
    required String contactEmail,
    required String phoneNumber,
    required String owner,
    required String mainAddress,
  }) {
    state = state.copyWith(
      name: name,
      contactEmail: contactEmail,
      phoneNumber: phoneNumber,
      owner: owner,
      mainAddress: mainAddress,
    );
  }

  // Add shop image
  // void addShopImage(Uint8List image) {
  //   final updatedImages = List<Uint8List>.from(state.images)..add(image);
  //   state = state.copyWith(images: updatedImages);
  // }

  // // Remove shop image
  // void removeShopImage(int index) {
  //   if (index >= 0 && index < state.images.length) {
  //     final updatedImages = List<Uint8List>.from(state.images);
  //     updatedImages.removeAt(index);
  //     state = state.copyWith(images: updatedImages);
  //   }
  // }

  // // Set shop images
  // void setShopImages(List<Uint8List> images) {
  //   state = state.copyWith(images: images);
  // }

  // Add branch
  void addBranch(Map<String, dynamic> branch) {
    final updatedBranches = List<Map<String, dynamic>>.from(state.branches)
      ..add(branch);
    state = state.copyWith(branches: updatedBranches);
  }

  // Update branch
  void updateBranch(int index, Map<String, dynamic> updatedBranch) {
    if (index >= 0 && index < state.branches.length) {
      final updatedBranches = List<Map<String, dynamic>>.from(state.branches);
      updatedBranches[index] = updatedBranch;
      state = state.copyWith(branches: updatedBranches);
    }
  }

  // Remove branch
  void removeBranch(int index) {
    if (index >= 0 && index < state.branches.length) {
      final updatedBranches = List<Map<String, dynamic>>.from(state.branches);
      updatedBranches.removeAt(index);
      state = state.copyWith(branches: updatedBranches);
    }
  }

  // Add shop image URL to state
  void addShopImageUrl(String imageUrl) {
    final updatedImageUrls = List<String>.from(state.imageUrls)..add(imageUrl);
    state = state.copyWith(imageUrls: updatedImageUrls);

    if (kDebugMode) {
      print('Added image URL: $imageUrl');
      print('Total image URLs: ${updatedImageUrls.length}');
    }
  }

  // Remove shop image URL
  void removeShopImageUrl(int index) {
    if (index >= 0 && index < state.imageUrls.length) {
      final updatedImageUrls = List<String>.from(state.imageUrls);
      updatedImageUrls.removeAt(index);
      state = state.copyWith(imageUrls: updatedImageUrls);
    }
  }

  // Get all image URLs
  List<String> getShopImageUrls() {
    return state.imageUrls;
  }

  // Clear image URLs
  void clearShopImageUrls() {
    state = state.copyWith(imageUrls: []);
  }

  // Add these properties to track image upload progress
  Map<String, ImageUploadProgress> imageUploadProgress = {};

  // Upload image with progress tracking
  Future<void> uploadShopImageWithProgress(
    Uint8List imageBytes,
    String fileName,
  ) async {
    final uploadId = DateTime.now().millisecondsSinceEpoch.toString();

    try {
      // Initialize progress tracking
      imageUploadProgress[uploadId] = ImageUploadProgress(
        imageBytes: imageBytes,
        fileName: fileName,
        isUploading: true,
        progress: 0.0,
      );

      // Notify UI of upload start
      state = state.copyWith();

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final path = 'hardware-shops/$timestamp-$fileName';

      // Upload the file
      await supabase.storage
          .from('hardware-shops')
          .uploadBinary(
            path,
            imageBytes,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress updates (since Supabase Flutter SDK doesn't have built-in progress)
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
          progress: i / 10,
        );

        // Notify UI of progress update
        state = state.copyWith();
      }

      // Get the public URL
      final imageUrl = supabase.storage
          .from('hardware-shops')
          .getPublicUrl(path);

      // Add the URL to state
      addShopImageUrl(imageUrl);

      // Update progress with completed status
      imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
        isUploading: false,
        progress: 1.0,
        url: imageUrl,
      );

      // Notify UI of completion
      state = state.copyWith();

      if (kDebugMode) {
        print('Image uploaded successfully: $imageUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // Update progress with error
      imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
        isUploading: false,
        error: e.toString(),
      );

      // Notify UI of error
      state = state.copyWith();

      rethrow;
    }
  }

  // Check if any images are currently uploading
  bool isAnyImageUploading() {
    return imageUploadProgress.values.any((progress) => progress.isUploading);
  }

  // Get all currently uploading images
  List<ImageUploadProgress> getUploadingImages() {
    return imageUploadProgress.values
        .where((progress) => progress.isUploading)
        .toList();
  }

  // Get overall upload progress (average of all uploading images)
  double getOverallUploadProgress() {
    final uploadingImages = getUploadingImages();
    if (uploadingImages.isEmpty) return 0.0;

    final totalProgress = uploadingImages.fold(
      0.0,
      (sum, progress) => sum + progress.progress,
    );
    return totalProgress / uploadingImages.length;
  }

  void setBranchName(String text) {
    state = state.copyWith(branchName: text);
  }

  void setIsSearchingAddress(bool status) {
    state = state.copyWith(isLoading: status);
  }

  void setShopOwner(String text) {
    state = state.copyWith(shopOwner: text);
  }

  void setContactEmail(String text) {
    state = state.copyWith(contactEmail: text);
  }

  void setPhoneNumber(String text) {
    state = state.copyWith(phoneNumber: text);
  }

  void setMainAddress(String text) {
    state = state.copyWith(mainAddress: text);
  }

  void setShopname(String text) {
    state = state.copyWith(shopName: text);
  }

  void setBranchAddress(String text) {
    state = state.copyWith(branchAddress: text);
  }

  void setBranchCity(String text) {
    state = state.copyWith(branchCity: text);
  }

  void setBranchPhonenumberOne(String text) {
    state = state.copyWith(branchPhonenumberOne: text);
  }

  void setBranchPhonenumberTwo(String text) {
    state = state.copyWith(branchPhonenumberTwo: text);
  }

  void setBranchEmailOne(String text) {
    state = state.copyWith(branchEmailOne: text);
  }

  void setBranchEmailTwo(String text) {
    state = state.copyWith(branchEmailTwo: text);
  }

  // Add this method to clear address suggestions
  void clearAddressSuggestions() {
    state = state.copyWith(
      addressLocationResult: AsyncValue.data(SuggestionsResponse()),
      addressPlacesList: [],
    );
  }

  // Handle address suggestions
  Future<void> searchDropOffAddress({required String place}) async {
    if (place.length < 3) {
      state = state.copyWith(
        addressLocationResult: AsyncValue.data(SuggestionsResponse()),
        addressPlacesList: [],
        isLoading: false,
      );
      return;
    }

    setIsSearchingAddress(true);
    state = state.copyWith(addressLocationResult: const AsyncValue.loading());

    final result = await _geoLocationServiceImpl.searchLocationSuggestions(
      LocationSuggestionsRequest(
        input: place,
        includedRegionCodes: ['zw'],
        locationBias: LocationBias(
          circle: CircleModel(
            center: CenterModel(
              latitude: -17.875274658203118,
              longitude: 30.6788601875305,
            ),
            radius: 800,
          ),
        ),
      ),
    );

    result.when(
      (success) {
        setIsSearchingAddress(false);
        state = state.copyWith(
          addressLocationResult: AsyncValue.data(success),
          addressPlacesList: success.suggestions ?? [],
        );
        if (kDebugMode) {
          print('Got ${success.suggestions?.length ?? 0} address suggestions');
        }
      },
      (error) {
        setIsSearchingAddress(false);
        state = state.copyWith(
          addressLocationResult: AsyncValue.error(error, StackTrace.current),
          addressPlacesList: [],
        );
        if (kDebugMode) {
          print('Error getting address suggestions: $error');
        }
      },
    );
  }

  void setIsGetlocationByPlaceId(bool status) {
    state = state.copyWith(isGettingLocationByPlaceId: status);
  }

  Future<void> getLocationByPlaceId({required String placeId}) async {
    setIsGetlocationByPlaceId(true);
    state = state.copyWith(locationByIdResponse: const AsyncValue.loading());

    final result = await _geoLocationServiceImpl.getLocationByPlaceId(
      placeId: placeId,
    );
    result.when(
      (success) {
        setIsGetlocationByPlaceId(false);
        state = state.copyWith(locationByIdResponse: AsyncValue.data(success));

        final curlocation = LatLng(
          success.location?.latitude ?? 0.0,
          success.location?.longitude ?? 0.0,
        );
        state = state.copyWith(currentLocation: curlocation);
        if (kDebugMode) {
          print(success);
          print(
            'LOCATION_BY_ID: ${success.location?.latitude ?? ''}, ${success.location?.longitude ?? ''}',
          );
        }
      },
      (error) {
        setIsGetlocationByPlaceId(false);
      },
    );
  }

  // Select address suggestion and get coordinates
  Future<void> selectAddressSuggestion(
    String suggestion,
    String placeId,
  ) async {
    // Here you would typically call a geocoding API to get coordinates
    // For now, we'll use default coordinates

    state = state.copyWith(selectedAddress: suggestion);

    // In a real implementation, you would get coordinates from the placeId
    // For example:
    // final coordinates = await _geoLocationServiceImpl.getCoordinatesForPlaceId(placeId);
    // state = state.copyWith(
    //   selectedLocation: LatLng(coordinates.latitude, coordinates.longitude)
    // );
  }

  // Save hardware shop profile to Supabase
  Future<Map<String, dynamic>> saveHardwareShopProfile() async {
    try {
      state = state.copyWith(isLoading: true);

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        return {'success': false, 'message': 'User not authenticated'};
      }

      // Validate required fields
      if (state.shopName.isEmpty ||
          state.contactEmail.isEmpty ||
          state.phoneNumber.isEmpty ||
          state.shopOwner.isEmpty ||
          state.mainAddress.isEmpty) {
        return {
          'success': false,
          'message': 'Please fill in all required fields',
        };
      }

      final hardwareShopDetails = HardwareShopDetailsDto(
        supabaseUUID: userId,
        name: state.shopName,
        contactEmail: state.contactEmail,
        phoneNumber: state.phoneNumber,
        ownerName: state.shopOwner,
        mainAddress: state.mainAddress,
      );

      final List<HardwareShopImageDto> shopImages =
          state.imageUrls
              .map((url) => HardwareShopImageDto(imageUrl: url))
              .toList();

      final List<BranchDto> branches =
          state.branchModels
              .map(
                (branch) => BranchDto(
                  branchName: branch.branchName,
                  address: branch.address,
                  city: branch.city,
                  latitude: branch.latitude,
                  longitude: branch.longitude,
                  phonenumberOne: branch.phonenumberOne,
                  phonenumberTwo: branch.phonenumberTwo ?? '',
                  email: branch.emailOne,
                  emailTwo: branch.emailTwo ?? '',
                ),
              )
              .toList();

      final profileData = HardwareProfileDto(
        hardwareShopDetails: hardwareShopDetails,
        hardwareShopimages: shopImages,
        branches: branches,
      );

      if (kDebugMode) {
        print('HARDWARE_SHOP_DATA: ${profileData.toJson()}');
      }

      final response = await supabase.rpc(
        'save_hardware_shop',
        params: {'data': profileData.toJson()},
      );

      if (response == null) {
        return {
          'success': false,
          'message': 'Failed to save hardware shop profile',
        };
      }

      if (response['success'] == true) {
        state = state.copyWith(isLoading: false, errorMessage: null);
        return {
          'success': true,
          'message': response['message'] ?? 'Profile saved successfully',
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to save profile',
        };
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      if (kDebugMode) {
        print('Error saving hardware shop profile: $e');
      }
      return {'success': false, 'message': e.toString()};
    }
  }

  // Load hardware shop profile from Supabase
  Future<void> loadHardwareShopProfile() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Fetch hardware shop data
      final shopResponse =
          await supabase
              .from('hardware_shops')
              .select('''
            *,
            hardware_shops_images(*),
            branches(
              *,
              branch_phonenumbers(*),
              branch_emails(*)
            )
          ''')
              .eq('supabase_id', userId)
              .maybeSingle();

      if (shopResponse == null) {
        // No profile found
        state = state.copyWith(isLoading: false);
        return;
      }

      // Process shop data
      final shopId = shopResponse['id'];
      final shopName = shopResponse['name'];
      final contactEmail = shopResponse['contact_email'];
      final phoneNumber = shopResponse['phone_number'];

      // Process branches
      final List<Map<String, dynamic>> branches = [];
      for (final branchData in shopResponse['branches']) {
        // Extract coordinates from point_coordinates
        final String pointStr = branchData['point_coordinates'] ?? '';
        LatLng location = LatLng(-17.824858, 31.053028); // Default

        if (pointStr.isNotEmpty) {
          try {
            // Format is typically 'POINT(longitude latitude)'
            final coordsStr = pointStr
                .replaceAll('POINT(', '')
                .replaceAll(')', '');
            final coords = coordsStr.split(' ');
            if (coords.length >= 2) {
              final longitude = double.parse(coords[0]);
              final latitude = double.parse(coords[1]);
              location = LatLng(latitude, longitude);
            }
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing coordinates: $e');
            }
          }
        }

        // Process phone numbers
        final List<String> phones = [];
        for (final phoneData in branchData['branch_phonenumbers']) {
          phones.add(phoneData['phonenumber']);
        }

        // Process emails
        final List<String> emails = [];
        for (final emailData in branchData['branch_emails']) {
          emails.add(emailData['email']);
        }

        // Create branch object
        final branch = {
          'name': branchData['branch_name'],
          'address': branchData['address'],
          'city': branchData['city'],
          'location': location,
          'phones': phones,
          'emails': emails,
        };

        branches.add(branch);
      }

      // Update state with loaded data
      state = state.copyWith(
        id: shopId,
        name: shopName,
        contactEmail: contactEmail,
        phoneNumber: phoneNumber,
        branches: branches,
        isLoading: false,
      );

      if (kDebugMode) {
        print('Hardware shop profile loaded successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading hardware shop profile: $e');
      }
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error loading profile: ${e.toString()}',
      );
    }
  }

  // Add this method to create a BranchModel and add it to the list
  void addBranchModel() {
    // Create a new BranchModel using the current state values
    final newBranch = BranchModel(
      branchName: state.branchName,
      address: state.branchAddress,
      city: state.branchCity,
      latitude: state.currentLocation?.latitude ?? 0.0,
      longitude: state.currentLocation?.longitude ?? 0.0,
      phonenumberOne: state.branchPhonenumberOne,
      phonenumberTwo:
          state.branchPhonenumberTwo.isEmpty
              ? null
              : state.branchPhonenumberTwo,
      emailOne: state.branchEmailOne,
      emailTwo: state.branchEmailTwo.isEmpty ? null : state.branchEmailTwo,
    );

    // Create a new list with the existing branches plus the new one
    final updatedBranchModels = List<BranchModel>.from(state.branchModels)
      ..add(newBranch);

    // Update the state with the new list
    state = state.copyWith(branchModels: updatedBranchModels);

    // Clear the branch form fields
    state = state.copyWith(
      branchName: '',
      branchAddress: '',
      branchCity: '',
      branchPhonenumberOne: '',
      branchPhonenumberTwo: '',
      branchEmailOne: '',
      branchEmailTwo: '',
      currentLocation: const LatLng(0.0, 0.0),
    );

    if (kDebugMode) {
      print('Branch added. Total branches: ${updatedBranchModels.length}');
    }
  }

  // Update an existing branch in the list
  void updateBranchModel(int index) {
    if (index >= 0 && index < state.branchModels.length) {
      // Create an updated branch with current form values
      final updatedBranch = BranchModel(
        branchName: state.branchName,
        address: state.branchAddress,
        city: state.branchCity,
        latitude: state.currentLocation?.latitude ?? 0.0,
        longitude: state.currentLocation?.longitude ?? 0.0,
        phonenumberOne: state.branchPhonenumberOne,
        phonenumberTwo:
            state.branchPhonenumberTwo.isEmpty
                ? null
                : state.branchPhonenumberTwo,
        emailOne: state.branchEmailOne,
        emailTwo: state.branchEmailTwo.isEmpty ? null : state.branchEmailTwo,
      );

      // Create a new list with the updated branch
      final updatedBranchModels = List<BranchModel>.from(state.branchModels);
      updatedBranchModels[index] = updatedBranch;

      // Update the state with the new list
      state = state.copyWith(branchModels: updatedBranchModels);

      // Clear the branch form fields
      state = state.copyWith(
        branchName: '',
        branchAddress: '',
        branchCity: '',
        branchPhonenumberOne: '',
        branchPhonenumberTwo: '',
        branchEmailOne: '',
        branchEmailTwo: '',
        currentLocation: const LatLng(0.0, 0.0),
      );

      if (kDebugMode) {
        print('Branch updated at index: $index');
      }
    }
  }

  // Remove a branch from the list
  void removeBranchModel(int index) {
    if (index >= 0 && index < state.branchModels.length) {
      // Create a new list without the branch at the specified index
      final updatedBranchModels = List<BranchModel>.from(state.branchModels);
      updatedBranchModels.removeAt(index);

      // Update the state with the new list
      state = state.copyWith(branchModels: updatedBranchModels);

      if (kDebugMode) {
        print(
          'Branch removed at index: $index. Remaining branches: ${updatedBranchModels.length}',
        );
      }
    }
  }

  // Load branch data into form fields for editing
  void loadBranchForEditing(int index) {
    if (index >= 0 && index < state.branchModels.length) {
      final branch = state.branchModels[index];

      // Update state with branch data
      state = state.copyWith(
        branchName: branch.branchName,
        branchAddress: branch.address,
        branchCity: branch.city,
        branchPhonenumberOne: branch.phonenumberOne,
        branchPhonenumberTwo: branch.phonenumberTwo ?? '',
        branchEmailOne: branch.emailOne,
        branchEmailTwo: branch.emailTwo ?? '',
        currentLocation: LatLng(branch.latitude, branch.longitude),
      );

      if (kDebugMode) {
        print('Loaded branch at index: $index for editing');
      }
    }
  }

  // Add this method to manually set the current location
  void setCurrentLocation(LatLng location) {
    state = state.copyWith(currentLocation: location);
  }
}
