import 'package:bcrypt/bcrypt.dart';
import 'package:build_mate/data/dto/branch_staff_response.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/data/services/branch_auth_services.dart';
import 'package:build_mate/presentation/state/web/users_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final usersViewModelProvider =
    StateNotifierProvider<UsersViewModel, UsersState>(
      (ref) => UsersViewModel(UsersState(), ref.watch(branchAuthProvider)),
    );

class UsersViewModel extends StateNotifier<UsersState> {
  UsersViewModel(super.state, this._authService) {
    fetchBranches();
    selectAllBranchesUsers();
    // getUsersByHardwareShop();
    getShopUsers();
    setAllBranchesSelectedState(true);
  }

  final BranchAuthServices _authService;

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void setError(String error) {
    state = state.copyWith(errorMessage: error);
  }

  // void setUsers(List<UserResponse> users) {
  //   state = state.copyWith(users: users);
  // }
  // void setSelectedUser(UserResponse? user) {
  //   state = state.copyWith(selectedUser: user);
  // }

  void setSelectedUserId(int? userId) {
    state = state.copyWith(selectedUserId: userId);
  }

  void setSaving(bool isSaving) {
    state = state.copyWith(isSaving: isSaving);
  }

  void setSaveSuccess(bool success) {
    state = state.copyWith(saveSuccess: success);
  }

  void reset() {
    state = UsersState();
  }

  void updateFullName(String name) {
    state = state.copyWith(fullName: name);
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email);
  }

  void selectBranch({required int index, required int? branchId}) {
    if (kDebugMode) {
      print('Selecting branch at index: $index with branchId: $branchId');
    }

    if (index >= 0 && index < state.branches.length) {
      state = state.copyWith(selectedBranchIndex: index);
    }
    state = state.copyWith(isAllSelected: false);

    setLoadingUserBranchesState(true);
    //get all users from branch_users from Supabase table where branch_id = branchId
    final supabase = Supabase.instance.client;
    if (branchId != null && branchId > 0) {
      state = state.copyWith(selectedBranchId: branchId);
      supabase
          .from('branch_users')
          .select('*, branch:branches(id, branch_name, address)')
          .eq('branch_id', branchId)
          .then((response) {
            if (kDebugMode) {
              print('Selected Branch users response: $response');
            }
            final users =
                (response as List)
                    .map(
                      (user) => BranchStaffResponse.fromJson(
                        user as Map<String, dynamic>,
                      ),
                    )
                    .toList();
            state = state.copyWith(branchUsers: users);
            setLoadingUserBranchesState(false);
          });
    }
    if (kDebugMode) {
      print('Selected branch index: $index, branchId: $branchId');
      setLoadingUserBranchesState(false);
    }
  }

  void toggleSelectAllBranches() {
    final isAllSelected = state.isAllSelected;
    state = state.copyWith(isAllSelected: !isAllSelected);
  }

  void deselectAllBranchSelection() {
    state = state.copyWith(isAllSelected: false);
  }

  void setSelectedBranch(int id) {
    state = state.copyWith(selectedBranchId: id);
  }

  void setIsSavingLoading(bool isSaving) {
    state = state.copyWith(isSavingUser: isSaving);
  }

  void setAllBranchesSelectedState(bool selectedState) {
    state = state.copyWith(isAllSelected: selectedState);
  }

  Future<void> fetchBranches() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final client = Supabase.instance.client;
      // Get current user's ID
      final userId = client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // First get the hardware shop ID for the current user
      final shopResponse =
          await client
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', userId)
              .single();

      final hardwareShopId = shopResponse['id'];

      // Now fetch all branches with their related phone numbers and emails
      final branchesResponse = await client
          .from('branches')
          .select('''
      id, 
      branch_name,
      address,
      city,
      created_at,
      branch_phonenumbers(id, phonenumber),
      branch_emails(id, email),
      branch_users(count)
    ''')
          .eq('hardware_shop_id', hardwareShopId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('Branches response: $branchesResponse');
      }

      // Map the response to List<BranchResponse>
      final branches =
          (branchesResponse as List)
              .map(
                (branch) =>
                    BranchResponse.fromJson(branch as Map<String, dynamic>),
              )
              .toList();

      // Update state with the fetched branches
      state = state.copyWith(branches: branches, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching branches: $e');
      }
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load branches: ${e.toString()}',
      );
    }
  }

  Future<void> getBranches() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final client = Supabase.instance.client;
      // Get current user's ID
      final userId = client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // First get the hardware shop ID for the current user
      final shopResponse =
          await client
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', userId)
              .single();

      final hardwareShopId = shopResponse['id'];

      // Now fetch all branches with their related phone numbers and emails
      final branchesResponse = await client
          .from('branches')
          .select('''
            id, 
            branch_name, 
            address, 
            city,
            created_at,
            branch_phonenumbers(id, phonenumber),
            branch_emails(id, email)
          ''')
          .eq('hardware_shop_id', hardwareShopId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('Branches response: $branchesResponse');
      }

      // Map the response to List<BranchResponse>
      final branches =
          (branchesResponse as List)
              .map(
                (branch) =>
                    BranchResponse.fromJson(branch as Map<String, dynamic>),
              )
              .toList();

      // Update state with the fetched branches
      state = state.copyWith(branches: branches, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching branches: $e');
      }
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load branches: ${e.toString()}',
      );
    }
  }

  Future<List<Map<String, dynamic>>> selectAllBranchesUsers() async {
    if (kDebugMode) {
      print('ALL_BRANCHES_FETHC_METHOD_CALLED');
    }
    final client = Supabase.instance.client;
    final userId = client.auth.currentUser?.id;

    if (userId == null) {
      throw Exception('User not authenticated');
    }

    final shopOwner =
        await client
            .from('hardware_shops')
            .select('id')
            .eq('supabase_id', userId)
            .single();

    final int shopOwnerId = shopOwner['id'];

    final response = await client
        .from('branch_users')
        .select('''
        *, 
        branch:branches!inner(branch_name, hardware_shop_id)
      ''')
        .eq('branch.hardware_shop_id', shopOwnerId);
    if (kDebugMode) {
      print('Select all branches users response: $response');
    }

    return response;
  }

  Future<Map<String, dynamic>> createUser() async {
    try {
      // Validate email format
      // if (!_isValidEmail(email)) {
      //   throw 'Invalid email format';
      // }
      if (state.email.isEmpty || state.fullName.isEmpty) {
        throw 'Email and Full Name cannot be empty';
      }
      if (state.selectedBranchId <= 0) {
        throw 'Please select a branch';
      }

      setIsSavingLoading(true);

      final supabase = Supabase.instance.client;

      // Check for existing user
      final existingUser =
          await supabase
              .from('branch_users')
              .select()
              .eq('email', state.email)
              .maybeSingle();

      if (existingUser != null) {
        setIsSavingLoading(false);
        throw 'Email already in use';
      }

      // Generate random password
      final password = List.generate(15, (index) {
        final random = DateTime.now().microsecondsSinceEpoch % 3;
        switch (random) {
          case 0:
            return String.fromCharCode(
              65 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Uppercase
          case 1:
            return String.fromCharCode(
              97 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Lowercase
          default:
            return (DateTime.now().microsecondsSinceEpoch % 10)
                .toString(); // Numbers
        }
      }).join('');

      // Hash password with bcrypt
      final hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt());

      // Create user record
      final newUser = {
        "email": state.email,
        "hash_password": hashedPassword,
        "branch_id": state.selectedBranchId,
        "name": state.fullName,
      };

      // Insert into database
      final response =
          await supabase.from('branch_users').insert(newUser).select();
      setIsSavingLoading(false);
      _getUserWithBranch(state.email);
      setSaveSuccess(true);
      return {'success': true, 'data': response.first};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _getUserWithBranch(String email) async {
    final supabase = Supabase.instance.client;
    return await supabase
        .from('branch_users')
        .select('*, branch:branches(*, company:hardware_shops(*))')
        .eq('email', email)
        .single()
        .then((response) {
          if (kDebugMode) {
            print('USER_BY_EMAIL {$response}');
          }
          return response;
        });
  }

  Future<List<Map<String, dynamic>>> getUsersByHardwareShop() async {
    try {
      final supabase = Supabase.instance.client;

      //get current user id
      final supabaseId = supabase.auth.currentUser?.id;
      if (supabaseId == null) {
        throw Exception('User not authenticated');
      }
      // Check if supabaseId is provided

      // First get the shop ID from supabase_id
      final shopResponse =
          await supabase
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', supabaseId)
              .single();

      final shopId = shopResponse['id'] as int;

      // Then get all users associated with branches of this shop
      final usersResponse = await supabase
          .from('branch_users')
          .select('''
          *,
          user:branch_id (*),
          branch:branch_id (
            *,
            hardware_shop:hardware_shop_id (*)
          )
        ''')
          .eq('branch.hardware_shop_id', shopId);

      if (kDebugMode) {
        print('Users response: $usersResponse');
      }

      return usersResponse;
    } on PostgrestException catch (e) {
      if (kDebugMode) {
        print('Error fetching users: $e');
      }
      return [];
    }
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final response = await _authService.loginUser(
      email: email,
      password: password,
    );

    if (response['success']) {
      state = response['data'];
    }

    return response;
  }

  void logout() {
    // state = null;
  }

  void setLoadingUserBranchesState(bool loadingState) {
    state = state.copyWith(isLoadingBranchUsers: loadingState);
  }

  Future<void> getShopUsers() async {
    try {
      state = state.copyWith(isAllSelected: true, selectedBranchIndex: -1);
      setLoadingUserBranchesState(true);
      final supabase = Supabase.instance.client;
      final supabaseId = supabase.auth.currentUser?.id;
      if (supabaseId == null) {
        throw Exception('User not authenticated');
      }

      final response = await supabase.rpc(
        'get_shop_users',
        params: {'shop_supabase_id': supabaseId},
      );

      if (kDebugMode) {
        print('Raw shop users response: $response');
      }

      if (response == null) {
        if (kDebugMode) {
          print('No users found for the shop');
        }
      }

      final List<BranchStaffResponse> shopUsers =
          (response as List)
              .map(
                (user) =>
                    BranchStaffResponse.fromJson(user as Map<String, dynamic>),
              )
              .toList();
      state = state.copyWith(
        branchUsers: shopUsers,
        allUsersCount: shopUsers.length,
      );
      setLoadingUserBranchesState(false);
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error fetching shop users: $e');
        print('Stack trace: $stackTrace');
      }
    }
  }

  Future<void> resetUserPassword(int userId) async {
    try {
      setIsResettingPassword(true);
      setPasswordResetProgress(25); // Started

      final supabase = Supabase.instance.client;
      setPasswordResetProgress(50); // Connected to Supabase

      // Generate random password
      final password = List.generate(15, (index) {
        final random = DateTime.now().microsecondsSinceEpoch % 3;
        switch (random) {
          case 0:
            return String.fromCharCode(
              65 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Uppercase
          case 1:
            return String.fromCharCode(
              97 + (DateTime.now().microsecondsSinceEpoch % 26),
            ); // Lowercase
          default:
            return (DateTime.now().microsecondsSinceEpoch % 10)
                .toString(); // Numbers
        }
      }).join('');
      setPasswordResetProgress(75); // Password generated

      // Hash password with bcrypt
      final hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt());

      // Update user password
      await supabase
          .from('branch_users')
          .update({'hash_password': hashedPassword})
          .eq('id', userId);

      setPasswordResetProgress(100); // Password updated
      setIsResettingPassword(false);
      setPasswordResetProgress(0); // Reset progress for next operation
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
      setIsResettingPassword(false);
      setPasswordResetProgress(0); // Reset progress on error
    }
  }

  void setPasswordResetProgress(int progress) {
    state = state.copyWith(updateProgress: progress);
  }

  void setUpdateUserProgress(int progress) {
    state = state.copyWith(passwordResetProgress: progress);
  }

  Future<bool> updateUser({
    required int userId,
    required String name,
    required String email,
  }) async {
    try {
      setIsUpdatingUser(true);
      setUpdateUserProgress(25); // Started

      final supabase = Supabase.instance.client;
      setUpdateUserProgress(50); // Connected to Supabase
      setUpdateUserProgress(75);

      final updatedData = {
        "name": name,
        'email': email,
        'branch_id': state.selectedBranchId,
      };

      if (kDebugMode) {
        print('Updating user with ID: $userId');
        print('Updated data: $updatedData');
      }

      // Update user data
      await supabase.from('branch_users').update(updatedData).eq('id', userId);

      setUpdateUserProgress(100);
      setIsUpdatingUser(false);
      setUpdateUserProgress(0);

      return true; // Successfully updated
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user: $e');
      }
      setIsUpdatingUser(false);
      setUpdateUserProgress(0);
      return false; // Failed to update
    }
  }

  Future<bool> deleteUser(BranchStaffResponse user) async {
    try {
      setIsUpdatingUser(true);
      setUpdateUserProgress(25); // Started

      final supabase = Supabase.instance.client;
      setUpdateUserProgress(50); // Connected to Supabase

      // Delete user
      await supabase.from('branch_users').delete().eq('id', user.userId ?? 0);

      setUpdateUserProgress(100); // User deleted
      setIsUpdatingUser(false);
      setUpdateUserProgress(0); // Reset progress for next operation
      return true; // Successfully deleted
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting user: $e');
      }
      setIsUpdatingUser(false);
      setUpdateUserProgress(0); // Reset progress on error
      return false; // Failed to delete
    }
  }

  void setIsResettingPassword(bool loadingStatus) {
    state = state.copyWith(isResettingPassword: loadingStatus);
  }

  void setIsUpdatingUser(bool loadingStatus) {
    state = state.copyWith(isUpdatingUser: loadingStatus);
  }
}
