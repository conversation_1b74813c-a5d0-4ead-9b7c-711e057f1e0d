import 'package:build_mate/data/dto/hardware_items_response.dart';
import 'package:build_mate/data/dto/hardware_sub_category_response.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/data/models/category_model_dto.dart';
import 'package:build_mate/presentation/state/web/inventory_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';

final inventoryViewModelProvider =
    StateNotifierProvider<InventoryViewModel, InventoryState>((ref) {
      return InventoryViewModel(InventoryState());
    });

class InventoryViewModel extends StateNotifier<InventoryState> {
  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();

  InventoryViewModel(super.state) {
    getCategories();
    selectAllBranchProducts();
    fetchBranches();
  }

  void setIsLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setSelectedCategoryId(int id) {
    state = state.copyWith(selectedCategoryId: id);
  }

  void setSelectedSubCategoryObj(HardwareSubCategoryResponse obj) {
    state = state.copyWith(selectedSubCategoryObject: obj);
  }

  void setProductName(String name) {
    state = state.copyWith(productName: name);
  }

  void setProductDescription(String description) {
    state = state.copyWith(productDescription: description);
  }

  void setProductPrice(String price) {
    state = state.copyWith(productPrice: price);
  }

  void setFilteredCategory(int categoryId) {
    state = state.copyWith(filterCategoryId: categoryId);
  }

  Future<void> filterProductByBranchCategoryId(int id) async {
    try {
      state = state.copyWith(isLoadingProducts: true);

      if (kDebugMode) {
        print('Selected branch ID: ${state.selectedBranchId}');
         print('Category id: $id');
      }

      final response = await Supabase.instance.client.rpc(
        'get_hardware_products_by_category',
        params: {
          'p_branch_id': state.selectedBranchId,
          'p_hardware_category_id': id,
        },
      );

      if (kDebugMode) {
        print('Filter response: $response');
      }

      state = state.copyWith(isLoadingProducts: false);

      final products =
          (response as List)
              .map((item) => HardwareItem.fromJson(item))
              .toList();
      state = state.copyWith(
        products: products,
        isLoadingProducts: false,
        filterCategoryId: id,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error filtering products: $e');
      }
      state = state.copyWith(isLoadingProducts: false);
    }
  }

  //Add fuction to get categories from supabase table
  Future<void> getCategories() async {
    try {
      state = state.copyWith(isLoadingCategories: true);
      final supabase = Supabase.instance.client;
      await supabase.from('hardware_categories').select().order('name').then((
        response,
      ) {
        if (kDebugMode) {
          print('CATEGORY_RESPONSE: ${response.toString()}');
        }
        final categories =
            response
                .map((category) => CategoryResponse.fromJson(category))
                .toList();

        state = state.copyWith(categories: categories);
      });

      //Update state with categories
      state = state.copyWith(isLoadingCategories: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories: $e');
      }
      state = state.copyWith(isLoadingCategories: false);
    }
  }

  Future<List<Map<String, dynamic>>> selectAllBranchProducts() async {
    if (kDebugMode) {
      print('ALL_PRODUCTS_FETCH_METHOD_CALLED');
    }
    state = state.copyWith(isLoadingProducts: true);
    final client = Supabase.instance.client;
    final userId = client.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    state = state.copyWith(selectedBranchIndex: -1, isAllSelected: true);

    final shopOwner =
        await client
            .from('hardware_shops')
            .select('id')
            .eq('supabase_id', userId)
            .single();

    final int shopOwnerId = shopOwner['id'];

    final response = await client
        .from('branch_products')
        .select('''
    *, 
    branch:branches!inner(branch_name, hardware_shop_id),
    product_images(id, image_url),
     hardware_sub_category:hardware_sub_categories(
      name,
      hardware_category:hardware_categories(name)
    )
    ''')
        .eq('branch.hardware_shop_id', shopOwnerId);

    if (kDebugMode) {
      print('Select all products users response: $response');
    }

    final items = response.map((item) => HardwareItem.fromJson(item)).toList();
    state = state.copyWith(isLoadingProducts: false);
    state = state.copyWith(products: items, allProductsCount: items.length);

    return response;
  }

  Future<void> getSubCategoryById({required int categoryId}) async {
    try {
      state = state.copyWith(isLoadingSubCategories: true);
      final supabase = Supabase.instance.client;
      await supabase
          .from('hardware_sub_categories')
          .select()
          .eq('hardware_category_id', categoryId)
          .order('name')
          .then((response) {
            if (kDebugMode) {
              print('SUB_CATEGORY_RESPONSE: ${response.toString()}');
            }
            final subCategories =
                response
                    .map(
                      (subCategory) =>
                          HardwareSubCategoryResponse.fromJson(subCategory),
                    )
                    .toList();

            state = state.copyWith(subCategories: subCategories);
          });

      //Update state with categories
      state = state.copyWith(isLoadingSubCategories: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting subcategories: $e');
      }
      state = state.copyWith(isLoadingSubCategories: false);
    }
  }

  // Set selected category
  void setSelectedCategory(int? categoryId) {
    state = state.copyWith(
      selectedCategoryId: categoryId,
      selectedSubCategoryId: null, // Reset subcategory when category changes
      selectedSubCategoryObject:
          null, // Reset subcategory object when category changes
      subCategories: [], // Clear subcategories
    );

    // Fetch subcategories if category is selected
    if (categoryId != null) {
      getSubCategoryById(categoryId: categoryId);
    }
  }

  void setSelectedFilterCategory(int? categoryId) {
    state = state.copyWith(filterCategoryId: categoryId ?? 0);
    if (kDebugMode) {
      print('Filter category set to: ${state.filterCategoryId}');
    }
  }

  // Set selected subcategory
  void setSelectedSubCategory(int? subCategoryId) {
    state = state.copyWith(selectedSubCategoryId: subCategoryId);
  }

  // Clear selections
  void clearSelections() {
    state = state.copyWith(
      selectedCategoryId: null,
      selectedSubCategoryId: null,
      selectedSubCategoryObject: null,
      subCategories: [],
      imageUrls: [],
      imageUploadingStates: [false, false, false, false],
    );
  }

  // Image upload methods
  Future<void> uploadProductImage(int index) async {
    try {
      // Set uploading state for this index
      final updatedUploadingStates = List<bool>.from(
        state.imageUploadingStates,
      );
      updatedUploadingStates[index] = true;
      state = state.copyWith(imageUploadingStates: updatedUploadingStates);

      // Pick image file using ImagePicker (following setup company pattern)
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        // Read image as bytes
        final Uint8List imageBytes = await image.readAsBytes();
        final fileName =
            'product_${DateTime.now().millisecondsSinceEpoch}_${image.name}';

        if (kDebugMode) {
          print('Uploading image: $fileName');
        }

        // Upload to Supabase storage
        final supabase = Supabase.instance.client;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final path = 'hardware-shops/product-images/$timestamp-${image.name}';

        await supabase.storage
            .from('hardware-shops')
            .uploadBinary(
              path,
              imageBytes,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: true,
              ),
            );

        if (kDebugMode) {
          print('Upload completed for: $fileName');
        }

        // Get public URL
        final publicUrl = supabase.storage
            .from('hardware-shops')
            .getPublicUrl(path);

        if (kDebugMode) {
          print('Public URL: $publicUrl');
        }

        // Update image URLs list
        final updatedImageUrls = List<String>.from(state.imageUrls);

        // Ensure the list has enough slots
        while (updatedImageUrls.length <= index) {
          updatedImageUrls.add('');
        }

        updatedImageUrls[index] = publicUrl;

        // Update state with new URL
        state = state.copyWith(imageUrls: updatedImageUrls);

        if (kDebugMode) {
          print('Image uploaded successfully: $publicUrl');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
    } finally {
      // Clear uploading state for this index
      final updatedUploadingStates = List<bool>.from(
        state.imageUploadingStates,
      );
      updatedUploadingStates[index] = false;
      state = state.copyWith(imageUploadingStates: updatedUploadingStates);
    }
  }

  // Remove image
  void removeProductImage(int index) {
    final updatedImageUrls = List<String>.from(state.imageUrls);

    if (index < updatedImageUrls.length) {
      updatedImageUrls[index] = '';
      state = state.copyWith(imageUrls: updatedImageUrls);
    }
  }

  // Update product form fields
  void updateProductName(String name) {
    state = state.copyWith(productName: name);
  }

  void updateProductDescription(String description) {
    state = state.copyWith(productDescription: description);
  }

  void updateProductPrice(String price) {
    state = state.copyWith(productPrice: price);
  }

  void setSelectedBranch(int? branchId) {
    state = state.copyWith(selectedBranchId: branchId);
  }

  Future<void> getBranches() async {
    try {
      state = state.copyWith(isLoadingBranches: true);
      final supabase = Supabase.instance.client;
      await supabase.from('branches').select().order('branch_name').then((
        response,
      ) {
        if (kDebugMode) {
          print('BRANCHES_RESPONSE: ${response.toString()}');
        }
        final branches =
            response.map((branch) => BranchResponse.fromJson(branch)).toList();
        state = state.copyWith(branches: branches);
      });

      state = state.copyWith(isLoadingBranches: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting branches: $e');
      }
      state = state.copyWith(isLoadingBranches: false);
    }
  }

  Future<void> saveProduct() async {
    try {
      state = state.copyWith(isSaving: true);

      final productData = {
        'p_branch_id': state.selectedBranchId,
        'p_category_id': state.selectedCategoryId,
        'p_subcategory_id': state.selectedSubCategoryObject?.id,
        'p_name': state.productName,
        'p_description': state.productDescription,
        'p_price': double.tryParse(state.productPrice) ?? 0.0,
        'p_image_urls': state.imageUrls,
      };

      if (kDebugMode) {
        print('Sending product data: $productData');
      }

      final response = await Supabase.instance.client.rpc(
        'save_product',
        params: productData,
      );

      if (kDebugMode) {
        print('Raw response: $response');
      }
      // Parse response to check status
      Map<String, dynamic> responseMap = response as Map<String, dynamic>;
      bool status = responseMap['status'] as bool;
      String message = responseMap['message'] as String;

      if (status) {
        state = state.copyWith(
          isSaving: false,
          saveSuccess: true,
          errorMessage: null,
        );

        if (kDebugMode) {
          print('Product saved successfully');
        }
      } else {
        throw Exception(message);
      }
      // Check if response is successful
      if (responseMap.isNotEmpty) {
        state = state.copyWith(
          isSaving: false,
          saveSuccess: true,
          errorMessage: null,
        );

        if (kDebugMode) {
          print('Product saved successfully');
        }
      } else {
        throw Exception('Failed to save product');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving product: $e');
      }
      state = state.copyWith(
        isSaving: false,
        saveSuccess: false,
        errorMessage: e.toString(),
      );
    }
  }

  Future<void> fetchBranches() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final client = Supabase.instance.client;
      // Get current user's ID
      final userId = client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // First get the hardware shop ID for the current user
      final shopResponse =
          await client
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', userId)
              .single();

      final hardwareShopId = shopResponse['id'];

      // Now fetch all branches with their related phone numbers and emails
      final branchesResponse = await client
          .from('branches')
          .select('''
      id, 
      branch_name,
      address,
      city,
      created_at,
      branch_phonenumbers(id, phonenumber),
      branch_emails(id, email),
      branch_users(count)
    ''')
          .eq('hardware_shop_id', hardwareShopId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('Branches response: $branchesResponse');
      }

      // Map the response to List<BranchResponse>
      final branches =
          (branchesResponse as List)
              .map(
                (branch) =>
                    BranchResponse.fromJson(branch as Map<String, dynamic>),
              )
              .toList();

      // Update state with the fetched branches
      state = state.copyWith(branches: branches, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching branches: $e');
      }
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load branches: ${e.toString()}',
      );
    }
  }

  Future<void> selectBranch({
    required int index,
    required int? branchId,
  }) async {
    try {
      if (kDebugMode) {
        print('Selecting branch at index: $index with branchId: $branchId');
      }

      // Reset state and set loading
      state = state.copyWith(
        products: [], // Clear existing products
        errorMessage: null,
        isLoading: true,
      );

      if (index >= 0 && index < state.branches.length) {
        state = state.copyWith(selectedBranchIndex: index);
      }

      if (branchId == null || branchId <= 0) {
        state = state.copyWith(isLoading: false);
        return;
      }

      state = state.copyWith(selectedBranchId: branchId, isAllSelected: false);
      final supabase = Supabase.instance.client;

      final response = await supabase
          .from('branch_products')
          .select('''
            *, 
            branch:branches!inner(branch_name, hardware_shop_id),
            product_images(id, image_url),
            hardware_sub_category:hardware_sub_categories(
              name,
              hardware_category:hardware_categories(name)
            )
          ''')
          .eq('branch_id', branchId);

      if (kDebugMode) {
        print('Branch products response: $response');
      }

      if (response.isEmpty) {
        state = state.copyWith(
          products: [],
          errorMessage: 'No products found for this branch',
          isLoading: false,
        );
        return;
      }

      final products =
          (response as List)
              .map(
                (item) => HardwareItem.fromJson(item as Map<String, dynamic>),
              )
              .toList();

      state = state.copyWith(products: products, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error in selectBranch: $e');
      }
      state = state.copyWith(
        errorMessage: 'Failed to load branch products: ${e.toString()}',
        products: [],
        isLoading: false,
      );
    }
  }

  void setLoadingUserBranchesState(bool loadingState) {
    state = state.copyWith(isLoadingBranchUsers: loadingState);
  }

  Future<void> editProduct(int productId) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);

      final productData = {
        'p_product_id': productId,
        'p_branch_id': state.selectedBranchId,
        'p_category_id': state.selectedCategoryId,
        'p_subcategory_id': state.selectedSubCategoryObject?.id,
        'p_name': state.productName,
        'p_description': state.productDescription,
        'p_price': double.tryParse(state.productPrice) ?? 0.0,
        'p_image_urls': state.imageUrls,
      };

      if (kDebugMode) {
        print('Sending edit product data: $productData');
      }

      final response = await Supabase.instance.client.rpc(
        'edit_product',
        params: productData,
      );

      if (kDebugMode) {
        print('Edit response: $response');
      }

      Map<String, dynamic> responseMap = response as Map<String, dynamic>;
      bool status = responseMap['status'] as bool;
      String message = responseMap['message'] as String;

      if (status) {
        // Refresh product list after successful edit
        if (state.isAllSelected) {
          await selectAllBranchProducts();
        } else if (state.selectedBranchId != null) {
          await selectBranch(
            index: state.selectedBranchIndex,
            branchId: state.selectedBranchId,
          );
        }

        state = state.copyWith(
          isSaving: false,
          saveSuccess: true,
          errorMessage: null,
        );
      } else {
        throw Exception(message);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error editing product: $e');
      }
      state = state.copyWith(
        isSaving: false,
        saveSuccess: false,
        errorMessage: 'Failed to edit product: ${e.toString()}',
      );
    }
  }

  void populateFormWithProduct(HardwareItem product) {
    // Set branch and category
    setSelectedBranch(product.branchId);

    // Set category first to trigger subcategories load
    if (product.hardwareSubCategory?.hardwareCategory != null) {
      setSelectedCategory(product.hardwareSubCategory!.hardwareCategory!.id);
    }

    // Set product details
    updateProductName(product.name ?? '');
    updateProductPrice('${product.price ?? 0}');
    updateProductDescription(product.description ?? '');

    // Set images
    final imageUrls =
        product.productImages?.map((img) => img.imageUrl ?? '').toList() ?? [];
    setImageUrls(imageUrls);
  }

  void setImageUrls(List<String> urls) {
    state = state.copyWith(imageUrls: urls);
  }

  Future<void> deleteProduct(int productId) async {
    try {
      state = state.copyWith(isDeleting: true); // Set deleting state to true

      // Delete the product from Supabase
      await Supabase.instance.client
          .from('branch_products')
          .delete()
          .eq('id', productId);

      // Refresh the products list based on current selection
      if (state.isAllSelected) {
        await selectAllBranchProducts();
      } else if (state.selectedBranchId != null) {
        await selectBranch(
          index: state.selectedBranchIndex,
          branchId: state.selectedBranchId,
        );
      }

      state = state.copyWith(
        isDeleting: false,
        saveSuccess: true,
        errorMessage: null,
      );

      if (kDebugMode) {
        print('Product deleted successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting product: $e');
      }
      state = state.copyWith(
        isDeleting: false,
        saveSuccess: false,
        errorMessage: 'Failed to delete product: ${e.toString()}',
      );
      rethrow;
    }
  }

  void setFilterCategory(int? categoryId) {
    state = state.copyWith(filterCategoryId: categoryId ?? 0);
  }

  Future<void> applyFilters() async {
    try {
      state = state.copyWith(isLoadingProducts: true);

      // final filteredProducts =
      //     state.filterCategoryId != null
      //         ? state.products
      //             .where(
      //               (product) =>
      //                   product.hardwareSubCategory?.hardwareCategory?.id ==
      //                   state.filterCategoryId,
      //             )
      //             .toList()
      //         : state.products;

      // state = state.copyWith(
      //   filteredProducts: filteredProducts,
      //   isLoadingProducts: false,
      // );
    } catch (e) {
      if (kDebugMode) {
        print('Error applying filters: $e');
      }
      state = state.copyWith(isLoadingProducts: false);
    }
  }
}
