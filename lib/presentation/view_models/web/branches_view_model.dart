import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/requests_dto/hardware_profile_dto.dart';
import 'package:build_mate/data/dto/requests_dto/location_suggestions_request.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/data/models/branch_model.dart';
import 'package:build_mate/data/services/geo_location_services.dart';
import 'package:build_mate/presentation/state/web/branches_state.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class ImageUploadProgress {
  final Uint8List imageBytes;
  final String fileName;
  final double progress;
  final bool isUploading;
  final String? error;
  final String? url;

  ImageUploadProgress({
    required this.imageBytes,
    required this.fileName,
    this.progress = 0.0,
    this.isUploading = false,
    this.error,
    this.url,
  });

  ImageUploadProgress copyWith({
    Uint8List? imageBytes,
    String? fileName,
    double? progress,
    bool? isUploading,
    String? error,
    String? url,
  }) {
    return ImageUploadProgress(
      imageBytes: imageBytes ?? this.imageBytes,
      fileName: fileName ?? this.fileName,
      progress: progress ?? this.progress,
      isUploading: isUploading ?? this.isUploading,
      error: error,
      url: url ?? this.url,
    );
  }
}

final branchViewModelProivder =
    StateNotifierProvider<BranchesViewModel, BranchesState>((ref) {
      return BranchesViewModel(
        BranchesState(
          addressLocationResult: AsyncValue.data(SuggestionsResponse()),
          locationByIdResponse: AsyncValue.data(LocationResponse()),
        ),
        ref.watch(geoLocationServiceProvider),
      );
    });

class BranchesViewModel extends StateNotifier<BranchesState> {
  BranchesViewModel(super.state, this._geoLocationServiceImpl) {
    fetchBranches();
  }

  final GeoLocationService _geoLocationServiceImpl;

  final supabase = Supabase.instance.client;

  Future<void> fetchBranches() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      // Get current user's ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // First get the hardware shop ID for the current user
      final shopResponse =
          await supabase
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', userId)
              .single();

      final hardwareShopId = shopResponse['id'];

      // Now fetch all branches with their related phone numbers and emails
      final branchesResponse = await supabase
          .from('branches')
          .select('''
            id, 
            branch_name, 
            address, 
            city,
            created_at,
            branch_phonenumbers(id, phonenumber),
            branch_emails(id, email)
          ''')
          .eq('hardware_shop_id', hardwareShopId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('Branches response: $branchesResponse');
      }

      // Map the response to List<BranchResponse>
      final branches =
          (branchesResponse as List)
              .map(
                (branch) =>
                    BranchResponse.fromJson(branch as Map<String, dynamic>),
              )
              .toList();

      // Update state with the fetched branches
      state = state.copyWith(branches: branches, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching branches: $e');
      }
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load branches: ${e.toString()}',
      );
    }
  }

  Future<void> addBranch() async {
    state = state.copyWith(isLoading: true);
    final branchJson =
        BranchModel(
          branchName: state.branchName,
          address: state.branchAddress,
          city: state.branchCity,
          latitude: state.currentLocation?.latitude ?? 0.0,
          longitude: state.currentLocation?.longitude ?? 0.0,
          phonenumberOne: state.phoneNumber,
          phonenumberTwo: state.branchPhonenumberTwo,
          emailOne: state.branchEmailOne,
          emailTwo: state.branchEmailTwo,
        ).toJson();

    if (kDebugMode) {
      print('BRANCH_JSON: ${branchJson.toString()}');
    }

    // final branchJson = {
    //   'branchName': 'Branch Name', // REQUIRED
    //   'address': '123 Main St', // REQUIRED
    //   'city': 'City', // Optional
    //   'longitude': 30.123,
    //   'latitude': -17.456,
    //   'emailOne': '<EMAIL>',
    //   'emailTwo': '<EMAIL>',
    //   'phonenumberOne': '1234567890',
    //   'phonenumberTwo': '0987654321',
    // };

    try {
      // Get current user's ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get hardware shop ID for the current user
      final shopResponse =
          await supabase
              .from('hardware_shops')
              .select('id')
              .eq('supabase_id', userId)
              .single();

      final int hardwareShopId = shopResponse['id'];

      final response = await supabase.rpc(
        'save_branch_data',
        params: {'data': branchJson, 'hardware_shop_id': hardwareShopId},
      );

      if (response['success'] == true) {
        state = state.copyWith(isLoading: false);
        if (kDebugMode) {
          print('Success: ${response['message']}');
        }
        // Show success message to user
      } else {
        if (kDebugMode) {
          print('Error: ${response['message']}');
        }
        // Show error message to user
      }
    } catch (e) {
      state = state.copyWith(isLoading: false);
      if (kDebugMode) {
        print('Exception occurred: $e');
      }
      // Show exception message to user
    }
  }

  Future<void> updateBranch(Map<String, dynamic> branchData) async {
    try {
      state = state.copyWith(isUpdatingDetails: true, errorMessage: null);

      final branchId = branchData['id'];
      if (branchId == null) {
        throw Exception('Branch ID is required for update');
      }

      // Get current user's ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Update branch basic info
      await supabase
          .from('branches')
          .update({
            'branch_name': branchData['branch_name'],
            'address': branchData['address'],
            'city': branchData['city'],
          })
          .eq('id', branchId);

      // Handle primary phone number
      if (branchData['phone'] != null &&
          branchData['phone'].toString().isNotEmpty) {
        // First check if phone number exists
        final phoneResponse = await supabase
            .from('branch_phonenumbers')
            .select('id')
            .eq('branch_id', branchId);

        if (phoneResponse.isNotEmpty) {
          // Update existing phone number
          await supabase
              .from('branch_phonenumbers')
              .update({'phonenumber': branchData['phone']})
              .eq('branch_id', branchId)
              .eq('id', phoneResponse[0]['id']);
        } else {
          // Insert new phone number
          await supabase.from('branch_phonenumbers').insert({
            'branch_id': branchId,
            'phonenumber': branchData['phone'],
          });
        }
      }

      // Handle secondary phone number
      if (branchData['second_phone'] != null &&
          branchData['second_phone'].toString().isNotEmpty) {
        // Check if there's already a second phone number
        final secondPhoneResponse = await supabase
            .from('branch_phonenumbers')
            .select('id')
            .eq('branch_id', branchId)
            .order('id')
            .limit(2);

        if (secondPhoneResponse.length > 1) {
          // Update existing second phone number
          await supabase
              .from('branch_phonenumbers')
              .update({'phonenumber': branchData['second_phone']})
              .eq('branch_id', branchId)
              .eq('id', secondPhoneResponse[1]['id']);
        } else {
          // Insert new second phone number
          await supabase.from('branch_phonenumbers').insert({
            'branch_id': branchId,
            'phonenumber': branchData['second_phone'],
          });
        }
      }

      // Handle primary email
      if (branchData['email'] != null &&
          branchData['email'].toString().isNotEmpty) {
        // First check if email exists
        final emailResponse = await supabase
            .from('branch_emails')
            .select('id')
            .eq('branch_id', branchId);

        if (emailResponse.isNotEmpty) {
          // Update existing email
          await supabase
              .from('branch_emails')
              .update({'email': branchData['email']})
              .eq('branch_id', branchId)
              .eq('id', emailResponse[0]['id']);
        } else {
          // Insert new email
          await supabase.from('branch_emails').insert({
            'branch_id': branchId,
            'email': branchData['email'],
          });
        }
      }

      // Handle secondary email
      if (branchData['second_email'] != null &&
          branchData['second_email'].toString().isNotEmpty) {
        // Check if there's already a second email
        final secondEmailResponse = await supabase
            .from('branch_emails')
            .select('id')
            .eq('branch_id', branchId)
            .order('id')
            .limit(2);

        if (secondEmailResponse.length > 1) {
          // Update existing second email
          await supabase
              .from('branch_emails')
              .update({'email': branchData['second_email']})
              .eq('branch_id', branchId)
              .eq('id', secondEmailResponse[1]['id']);
        } else {
          // Insert new second email
          await supabase.from('branch_emails').insert({
            'branch_id': branchId,
            'email': branchData['second_email'],
          });
        }
      }

      // Refresh branches list
      await fetchBranches();

      // Reset updating state
      state = state.copyWith(isUpdatingDetails: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating branch: $e');
      }
      state = state.copyWith(
        isUpdatingDetails: false,
        errorMessage: 'Failed to update branch: ${e.toString()}',
      );
      rethrow; // Rethrow to handle in UI
    }
  }

  void addShopImageUrl(String imageUrl) {
    final updatedImageUrls = List<String>.from(state.imageUrls)..add(imageUrl);
    state = state.copyWith(imageUrls: updatedImageUrls);

    if (kDebugMode) {
      print('Added image URL: $imageUrl');
      print('Total image URLs: ${updatedImageUrls.length}');
    }
  }

  void setBranchName(String text) {
    state = state.copyWith(branchName: text);
  }

  void setIsSearchingAddress(bool status) {
    state = state.copyWith(isLoading: status);
  }

  void setShopOwner(String text) {
    state = state.copyWith(shopOwner: text);
  }

  void setContactEmail(String text) {
    state = state.copyWith(contactEmail: text);
  }

  void setPhoneNumber(String text) {
    state = state.copyWith(phoneNumber: text);
  }

  void setMainAddress(String text) {
    state = state.copyWith(mainAddress: text);
  }

  void setShopname(String text) {
    state = state.copyWith(shopName: text);
  }

  void setBranchAddress(String text) {
    state = state.copyWith(branchAddress: text);
  }

  void setBranchCity(String text) {
    state = state.copyWith(branchCity: text);
  }

  void setBranchPhonenumberOne(String text) {
    state = state.copyWith(branchPhonenumberOne: text);
  }

  void setBranchPhonenumberTwo(String text) {
    state = state.copyWith(branchPhonenumberTwo: text);
  }

  void setBranchEmailOne(String text) {
    state = state.copyWith(branchEmailOne: text);
  }

  void setBranchEmailTwo(String text) {
    state = state.copyWith(branchEmailTwo: text);
  }

  List<String> getShopImageUrls() {
    return state.imageUrls;
  }

  void setCurrentLocation(LatLng location) {
    state = state.copyWith(currentLocation: location);
  }

  // Clear image URLs
  void clearShopImageUrls() {
    state = state.copyWith(imageUrls: []);
  }

  Map<String, ImageUploadProgress> imageUploadProgress = {};

  // Upload image with progress tracking
  Future<void> uploadShopImageWithProgress(
    Uint8List imageBytes,
    String fileName,
  ) async {
    final uploadId = DateTime.now().millisecondsSinceEpoch.toString();

    try {
      // Initialize progress tracking
      imageUploadProgress[uploadId] = ImageUploadProgress(
        imageBytes: imageBytes,
        fileName: fileName,
        isUploading: true,
        progress: 0.0,
      );

      // Notify UI of upload start
      state = state.copyWith();

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final path = 'hardware-shops/$timestamp-$fileName';

      // Upload the file
      await supabase.storage
          .from('hardware-shops')
          .uploadBinary(
            path,
            imageBytes,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress updates (since Supabase Flutter SDK doesn't have built-in progress)
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
          progress: i / 10,
        );

        // Notify UI of progress update
        state = state.copyWith();
      }

      // Get the public URL
      final imageUrl = supabase.storage
          .from('hardware-shops')
          .getPublicUrl(path);

      // Add the URL to state
      addShopImageUrl(imageUrl);

      // Update progress with completed status
      imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
        isUploading: false,
        progress: 1.0,
        url: imageUrl,
      );

      // Notify UI of completion
      state = state.copyWith();

      if (kDebugMode) {
        print('Image uploaded successfully: $imageUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // Update progress with error
      imageUploadProgress[uploadId] = imageUploadProgress[uploadId]!.copyWith(
        isUploading: false,
        error: e.toString(),
      );

      // Notify UI of error
      state = state.copyWith();

      rethrow;
    }
  }

  // Check if any images are currently uploading
  bool isAnyImageUploading() {
    return imageUploadProgress.values.any((progress) => progress.isUploading);
  }

  // Get all currently uploading images
  List<ImageUploadProgress> getUploadingImages() {
    return imageUploadProgress.values
        .where((progress) => progress.isUploading)
        .toList();
  }

  // Get overall upload progress (average of all uploading images)
  double getOverallUploadProgress() {
    final uploadingImages = getUploadingImages();
    if (uploadingImages.isEmpty) return 0.0;

    final totalProgress = uploadingImages.fold(
      0.0,
      (sum, progress) => sum + progress.progress,
    );
    return totalProgress / uploadingImages.length;
  }

  void clearAddressSuggestions() {
    state = state.copyWith(
      addressLocationResult: AsyncValue.data(SuggestionsResponse()),
      addressPlacesList: [],
    );
  }

  Future<void> searchDropOffAddress({required String place}) async {
    if (place.length < 3) {
      state = state.copyWith(
        addressLocationResult: AsyncValue.data(SuggestionsResponse()),
        addressPlacesList: [],
        isLoading: false,
      );
      return;
    }

    setIsSearchingAddress(true);
    state = state.copyWith(addressLocationResult: const AsyncValue.loading());

    final result = await _geoLocationServiceImpl.searchLocationSuggestions(
      LocationSuggestionsRequest(
        input: place,
        includedRegionCodes: ['zw'],
        locationBias: LocationBias(
          circle: CircleModel(
            center: CenterModel(
              latitude: -17.875274658203118,
              longitude: 30.6788601875305,
            ),
            radius: 800,
          ),
        ),
      ),
    );

    result.when(
      (success) {
        setIsSearchingAddress(false);
        state = state.copyWith(
          addressLocationResult: AsyncValue.data(success),
          addressPlacesList: success.suggestions ?? [],
        );
        if (kDebugMode) {
          print('Got ${success.suggestions?.length ?? 0} address suggestions');
        }
      },
      (error) {
        setIsSearchingAddress(false);
        state = state.copyWith(
          addressLocationResult: AsyncValue.error(error, StackTrace.current),
          addressPlacesList: [],
        );
        if (kDebugMode) {
          print('Error getting address suggestions: $error');
        }
      },
    );
  }

  void setIsGetlocationByPlaceId(bool status) {
    state = state.copyWith(isGettingLocationByPlaceId: status);
  }

  Future<void> getLocationByPlaceId({required String placeId}) async {
    setIsGetlocationByPlaceId(true);
    state = state.copyWith(locationByIdResponse: const AsyncValue.loading());

    final result = await _geoLocationServiceImpl.getLocationByPlaceId(
      placeId: placeId,
    );
    result.when(
      (success) {
        setIsGetlocationByPlaceId(false);
        state = state.copyWith(locationByIdResponse: AsyncValue.data(success));

        final curlocation = LatLng(
          success.location?.latitude ?? 0.0,
          success.location?.longitude ?? 0.0,
        );
        state = state.copyWith(currentLocation: curlocation);
        if (kDebugMode) {
          print(success);
          print(
            'LOCATION_BY_ID: ${success.location?.latitude ?? ''}, ${success.location?.longitude ?? ''}',
          );
        }
      },
      (error) {
        setIsGetlocationByPlaceId(false);
      },
    );
  }

  Future<void> selectAddressSuggestion(
    String suggestion,
    String placeId,
  ) async {
    state = state.copyWith(selectedAddress: suggestion);
  }

  Future<void> saveHardwareShopProfile() async {
    try {
      state = state.copyWith(isLoading: true);

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final hardwareShopDetails = HardwareShopDetailsDto(
        supabaseUUID: userId,
        name: state.shopName,
        contactEmail: state.contactEmail,
        phoneNumber: state.phoneNumber,
        ownerName: state.shopOwner,
        mainAddress: state.mainAddress,
      );

      final List<HardwareShopImageDto> shopImages =
          state.imageUrls
              .map((url) => HardwareShopImageDto(imageUrl: url))
              .toList();

      final List<BranchDto> branches =
          state.branchModels
              .map(
                (branch) => BranchDto(
                  branchName: branch.branchName,
                  address: branch.address,
                  city: branch.city,
                  latitude: branch.latitude,
                  longitude: branch.longitude,
                  phonenumberOne: branch.phonenumberOne,
                  phonenumberTwo: branch.phonenumberTwo ?? '',
                  email: branch.emailOne,
                  emailTwo: branch.emailTwo ?? '',
                ),
              )
              .toList();

      final profileData = HardwareProfileDto(
        hardwareShopDetails: hardwareShopDetails,
        hardwareShopimages: shopImages,
        branches: branches,
      );

      try {
        final response = await supabase.rpc(
          'save_hardware_shop',
          params: {'data': profileData.toJson()},
        );

        if (response['success'] == true) {
          state = state.copyWith(isLoading: false);
          if (kDebugMode) {
            print('Success: ${response['message']}');
          }
          // Show success message to user
        } else {
          if (kDebugMode) {
            print('Error: ${response['message']}');
          }
          // Show error message to user
        }
      } catch (e) {
        state = state.copyWith(isLoading: false);
        if (kDebugMode) {
          print('Exception occurred: $e');
        }
        // Show exception message to user
      }

      if (kDebugMode) {
        print('Profile data: $profileData');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false);
      if (kDebugMode) {
        print('Error saving hardware shop profile: $e');
      }
      rethrow;
    }
  }

  // Method to validate branch data
  bool validateBranchData(Map<String, dynamic> branchData) {
    // Check required fields
    if (branchData['branch_name'] == null ||
        branchData['branch_name'].toString().isEmpty) {
      return false;
    }
    if (branchData['address'] == null ||
        branchData['address'].toString().isEmpty) {
      return false;
    }
    if (branchData['city'] == null || branchData['city'].toString().isEmpty) {
      return false;
    }
    return true;
  }

  // Method to get branch by ID
  BranchResponse? getBranchById(int id) {
    try {
      return state.branches.firstWhere((branch) => branch.id == id);
    } catch (e) {
      return null;
    }
  }

  void setIsAddingBranch(bool value) {
    state = state.copyWith(isAddingBranch: value);
  }

  Future<bool> deleteBranch(int branchId) async {
    try {
      state = state.copyWith(isDeletingBranch: true, errorMessage: null);

      // Get current user's ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Delete the branch and its related data
      await supabase.from('branches').delete().eq('id', branchId).then((
        _,
      ) async {
        state = state.copyWith(isDeletingBranch: false);
        await fetchBranches();
      });

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting branch: $e');
      }
      state = state.copyWith(
        isDeletingBranch: false,
        errorMessage: 'Failed to delete branch: ${e.toString()}',
      );
      return false;
    }
  }
}
