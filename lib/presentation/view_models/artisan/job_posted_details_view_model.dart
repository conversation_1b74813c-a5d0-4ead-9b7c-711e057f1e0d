import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:build_mate/presentation/state/client_jobs_details_flow_state.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final jobPostedDetailsViewModelProvider = StateNotifierProvider<
  JobPostedDetailsViewModel,
  ClientJobsDetailsFlowState
>((ref) {
  // Get the existing state from jobPostedViewModelProvider
  final jobPostedViewModel = ref.watch(jobPostedViewModelProvider.notifier);
  final jobPostedState = ref.watch(jobPostedViewModelProvider);

  // Create initial state with the selected job or a default job
  final initialJob = jobPostedState.selectedJob;

  return JobPostedDetailsViewModel(
    jobPostedViewModel,
    initialJob ??
        PostedJobModel(
          id: 0,
          title: '',
          clientName: '',
          description: '',
          budget: 0.0,
          categories: [],
          images: [],
          status: '',
          serviceDate: DateTime.now(),
          postDate: DateTime.now(),
          bids: [],
          hasMyBid: false,
        ),
  );
});

class JobPostedDetailsViewModel
    extends StateNotifier<ClientJobsDetailsFlowState> {
  final supabase = Supabase.instance.client;
  final JobPostedViewModel jobPostedViewModel;

  JobPostedDetailsViewModel(this.jobPostedViewModel, PostedJobModel initialJob)
    : super(ClientJobsDetailsFlowState(job: initialJob));

  // Place a bid on the selected job
  Future<void> placeBid() async {
    state = state.copyWith(isLoading: true);

    try {
      final client = Supabase.instance.client;
      final currentUserUUID = client.auth.currentUser?.id;

      // Get artisan id from artisans table
      final int artisanId = await client
          .from('artisans')
          .select('id')
          .eq('supabase_id', currentUserUUID ?? '')
          .single()
          .then((value) => value['id']);

      // Create a new bid
      final response =
          await client
              .from('bids')
              .insert({'job_id': state.job?.id, 'artisan_id': artisanId})
              .select()
              .single();

      if (kDebugMode) {
        print('BID RESPONSE: $response');
      }

      // Update the job to reflect the new bid
      final updatedJob = PostedJobModel(
        id: state.job?.id ?? 0,
        title: state.job?.title ?? '',
        clientName: state.job?.clientName ?? '',
        clientAvatar: state.job?.clientAvatar ?? '',
        description: state.job?.description ?? '',
        budget: state.job?.budget ?? 0.0,
        categories: state.job?.categories ?? [],
        images: state.job?.images ?? [],
        status: state.job?.status ?? '',
        serviceDate: state.job?.serviceDate ?? DateTime.now(),
        postDate: state.job?.postDate ?? DateTime.now(),
        bids: state.job?.bids ?? [],
        hasMyBid: true,
        myBidId: response['id'],
      );

      // Update the state with the updated job
      state = state.copyWith(job: updatedJob, isLoading: false);

      // Also update the parent view model's state if the job is selected
      jobPostedViewModel.updateJobAfterBid(updatedJob);
    } catch (e) {
      if (kDebugMode) {
        print('Error placing bid: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  // Cancel a bid on the job
  Future<void> cancelBid() async {
    if (state.job?.myBidId == null) {
      if (kDebugMode) {
        print('No bid to cancel');
      }
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      final client = Supabase.instance.client;

      if (kDebugMode) {
        print('JOB_ID: ${state.job?.id}');
      }

      // Delete the bid
      await client.from('bids').delete().eq('job_id', state.job?.id ?? 0);

      // Update the job to reflect the canceled bid
      final updatedJob = PostedJobModel(
        id: state.job?.id ?? 0,
        title: state.job?.title ?? '',
        clientName: state.job?.clientName ?? '',
        clientAvatar: state.job?.clientAvatar ?? '',
        description: state.job?.description ?? '',
        budget: state.job?.budget ?? 0.0,
        categories: state.job?.categories ?? [],
        images: state.job?.images ?? [],
        status: state.job?.status ?? '',
        serviceDate: state.job?.serviceDate ?? DateTime.now(),
        postDate: state.job?.postDate ?? DateTime.now(),
        bids: state.job?.bids ?? [],
        hasMyBid: false,
        myBidId: null,
      );

      // Update the state with the updated job
      state = state.copyWith(job: updatedJob, isLoading: false);

      // Also update the parent view model's state
      jobPostedViewModel.updateJobAfterBid(updatedJob);
    } catch (e) {
      if (kDebugMode) {
        print('Error canceling bid: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }
}
