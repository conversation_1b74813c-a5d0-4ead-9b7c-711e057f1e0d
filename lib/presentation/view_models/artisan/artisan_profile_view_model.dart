import 'dart:convert';
import 'dart:math';
import 'package:build_mate/data/dto/job_details_response.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:build_mate/presentation/view_models/job/client_jobs_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/state/artisan_profile_view_state.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final artisanProfileViewModelProvider =
    StateNotifierProvider<ArtisanProfileViewModel, ArtisanProfileViewState>((
      ref,
    ) {
      return ArtisanProfileViewModel(ref);
    });

class ArtisanProfileViewModel extends StateNotifier<ArtisanProfileViewState> {
  final Ref _ref;

  ArtisanProfileViewModel(this._ref)
    : super(
        ArtisanProfileViewState(
          name: 'Tatenda Kabike',
          profession: 'Electrician',
          location: 'Harare',
          distance: 3.8,
          rating: 4.0,
          totalReviews: 5,
          certifications: ['Connection', 'Fixing'],
          serviceType: 'Electrician',
          isActive: true,
          specializations: ['Connection', 'Fixing'],
          about:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco.',
          nextAvailableTime: '2.00 am',
          workImages: [
            'assets/images/grid_1.jpeg',
            'assets/images/grid_2.jpeg',
            'assets/images/grid_3.jpg',
            'assets/images/grid_4.jpeg',
            'assets/images/grid_1.jpeg',
            'assets/images/grid_2.jpeg',
            'assets/images/grid_3.jpg',
            'assets/images/grid_4.jpeg',
            'assets/images/grid_1.jpeg',
          ],
          reviews: [
            {
              'name': 'John Smith',
              'avatar': 'assets/images/profile_pic.png',
              'rating': 5,
              'date': '2 weeks ago',
              'comment':
                  'Excellent work! Very professional and completed the job quickly.',
            },
            {
              'name': 'Sarah Johnson',
              'avatar': 'assets/images/profile_pic.png',
              'rating': 4,
              'date': '1 month ago',
              'comment':
                  'Good service, fixed my electrical issues efficiently. Would recommend.',
            },
            {
              'name': 'Michael Brown',
              'avatar': 'assets/images/profile_pic.png',
              'rating': 5,
              'date': '2 months ago',
              'comment':
                  'Very knowledgeable and helpful. Explained everything clearly.',
            },
          ],
        ),
      ) {
    setupArtisanData();
    loadRandomServiceType();
    getArtisanProfileData();
  }

  final supabase = Supabase.instance.client;

  //Get selected job from clientJobsViewModelProvider state
  JobDetailsResponse? get selectedJob => _ref.watch(
    clientJobsViewModelProvider.select((state) => state.selectedJob),
  );

  void setupArtisanData() {
    JobDetailsResponse? selectedJob = _ref.watch(
      clientJobsViewModelProvider.select((state) => state.selectedJob),
    );
    state = state.copyWith(selectedJob: selectedJob);
  }

  Future<void> getArtisanProfileData() async {
    final userId = supabase.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    // Set loading state to true
    state = state.copyWith(isLoading: true);

    try {
      //Get artisan id from artisans table
      await supabase
          .from('jobs')
          .select('artisan_id')
          .eq('id', state.selectedJob?.id ?? 0)
          .single()
          .then((value) async {
            if (kDebugMode) {
              print('GET_ARTISAN_DATA: ${value.toString()}');
            }
            try {
              await supabase
                  .from('artisans')
                  .select('''
              id, name, email, avatar, address, supabase_id,
              whatsapp_number, is_company, cover_photo, about,
              artisan_locations (
                location, address
              ),
              artisan_numbers (
                phonenumber, second_phonenumber
              ),
              specializations (
                service_id,services(id, name),
                specialization_tags (
                  sub_category_id,
                  sub_categories (id, name)
                )
              ),
              artisan_images (
                id, image_url
              ),
              artisan_ratings (
                id, rating, comments, created_at,
                client:client_id(name, avatar)
              )
            ''')
                  .eq('id', value['artisan_id'])
                  .single()
                  .then((data) {
                    final artisanProfileData =
                        ArtisanProfileDataResponse.fromJson(data);
                    if (kDebugMode) {
                      print(
                        'ARTISAN_PROFILE_DATA RESPONSE: ${data.toString()}',
                      );
                      print('COVER_IMAGE: ${artisanProfileData.coverPhoto}');
                    }
                    state = state.copyWith(
                      artisanProfileDataResponse: artisanProfileData,
                      isLoading:
                          false, // Set loading to false when data is loaded
                    );
                  });
            } catch (e) {
              if (kDebugMode) {
                print('GET_ARTISAN_DATA_ERROR: ${e.toString()}');
              }
              state = state.copyWith(
                isLoading: false,
              ); // Set loading to false on error
            }
          });
    } catch (e) {
      if (kDebugMode) {
        print('GET_ARTISAN_DATA_ERROR: ${e.toString()}');
      }
      state = state.copyWith(isLoading: false); // Set loading to false on error
    }
  }

  void setSelectedTabIndex(int index) {
    state = state.copyWith(selectedTabIndex: index);
  }

  Future<void> loadRandomServiceType() async {
    try {
      state = state.copyWith(isLoading: true);

      final String response = await rootBundle.loadString(
        'assets/json/categories.json',
      );
      final data = await json.decode(response);
      final categories = List<Map<String, dynamic>>.from(data['categories']);

      if (categories.isNotEmpty) {
        // Generate a random index
        final random = Random();
        final randomIndex = random.nextInt(categories.length);

        // Get a random category
        final randomCategory = categories[randomIndex];

        // Update the service type
        String serviceType = randomCategory['name'];
        List<String> specializations = [];

        // If the category has subcategories, update specializations
        if (randomCategory['subcategories'] != null &&
            randomCategory['subcategories'].isNotEmpty) {
          // Get up to 3 random subcategories
          final subcategories = List<String>.from(
            randomCategory['subcategories'],
          );
          subcategories.shuffle();
          final selectedSubcategories =
              subcategories.take(min(3, subcategories.length)).toList();

          // Update specializations
          specializations = selectedSubcategories;
        }

        state = state.copyWith(
          serviceType: serviceType,
          specializations: specializations,
          isLoading: false,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  void updateProfile({
    required String name,
    required String profession,
    required String location,
    required String about,
    required List<String> specializations,
  }) {
    state = state.copyWith(
      name: name,
      profession: profession,
      location: location,
      about: about,
      specializations: specializations,
    );
  }

  void addWorkImage(String imagePath) {
    final updatedImages = [...state.workImages, imagePath];
    state = state.copyWith(workImages: updatedImages);
  }

  void removeWorkImage(int index) {
    if (index >= 0 && index < state.workImages.length) {
      final updatedImages = [...state.workImages];
      updatedImages.removeAt(index);
      state = state.copyWith(workImages: updatedImages);
    }
  }

  void reorderWorkImages(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final updatedImages = [...state.workImages];
    final item = updatedImages.removeAt(oldIndex);
    updatedImages.insert(newIndex, item);
    state = state.copyWith(workImages: updatedImages);
  }

  // Store categories and subcategories locally in the view model
  List<Map<String, dynamic>> _categories = [];
  List<String> _availableSubcategories = [];

  // Load categories from JSON file
  Future<void> loadCategories() async {
    try {
      state = state.copyWith(isLoading: true);

      // Add a small delay to ensure the widget is built
      await Future.delayed(const Duration(milliseconds: 100));

      final String response = await rootBundle.loadString(
        'assets/json/categories.json',
      );
      final data = await json.decode(response);
      _categories = List<Map<String, dynamic>>.from(data['categories']);

      if (kDebugMode) {
        print('Loaded ${_categories.length} categories from JSON');
      }

      // Find the category that matches the profession or use the first one
      final categoryMatch = _categories.firstWhere(
        (category) => category['name'] == state.profession,
        orElse: () => _categories.isNotEmpty ? _categories.first : {'name': 'Other', 'subcategories': []},
      );

      // Get subcategories for the selected category
      _availableSubcategories = categoryMatch['subcategories'] != null
          ? List<String>.from(categoryMatch['subcategories'])
          : [];

      // If profession is empty, set it to the first category
      if (state.profession.isEmpty && _categories.isNotEmpty) {
        state = state.copyWith(
          profession: _categories.first['name'],
        );
      }

      state = state.copyWith(isLoading: false);
      
      // Notify listeners that data has changed
    
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  // Get categories (for UI)
  List<Map<String, dynamic>> get categories => _categories;
  
  // Get available subcategories (for UI)
  List<String> get availableSubcategories => _availableSubcategories;

  // Update available subcategories when category changes
  void updateAvailableSubcategories(String selectedCategory) {
    // if (selectedCategory == null) return;
    
    // Find the category that matches the selected category
    final categoryMatch = _categories.firstWhere(
      (category) => category['name'] == selectedCategory,
      orElse: () => {'subcategories': []},
    );
    
    // Update available subcategories
    _availableSubcategories = categoryMatch['subcategories'] != null
        ? List<String>.from(categoryMatch['subcategories'])
        : [];
    
    // Filter selected specializations to only include valid ones
    final filteredSpecializations = state.specializations
        .where((spec) => _availableSubcategories.contains(spec))
        .toList();
    
    state = state.copyWith(
      profession: selectedCategory,
      specializations: filteredSpecializations,
    );
    
    // Notify listeners that data has changed

  }

  // Add or remove specialization
  void toggleSpecialization(String specialization) {
    final currentSpecializations = List<String>.from(state.specializations);
    
    if (currentSpecializations.contains(specialization)) {
      currentSpecializations.remove(specialization);
    } else {
      if (currentSpecializations.length < 5) {
        currentSpecializations.add(specialization);
      }
    }
    
    state = state.copyWith(specializations: currentSpecializations);
  }

  // Load profile data
  Future<void> loadProfileData() async {
    try {
      state = state.copyWith(isLoading: true);
      
      // We'll use the existing state data instead of fetching from the database
      // This is a simpler approach for now
      
      // Just load the categories to populate the dropdown
      await loadCategories();
      
      state = state.copyWith(isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading profile data: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  // Save profile changes
  Future<void> saveProfileChanges({
    required String name,
    required String profession,
    required String location,
    required String about,
    required List<String> specializations,
    required String profileImageUrl,
    required String coverImageUrl,
  }) async {
    state = state.copyWith(isLoading: true);
    
    try {
      // Update the state with the new values
      state = state.copyWith(
        name: name,
        profession: profession,
        location: location,
        about: about,
        specializations: specializations,
        profileImageUrl: profileImageUrl,
        coverImageUrl: coverImageUrl,
        isLoading: false,
      );
      
      if (kDebugMode) {
        print('Profile updated successfully');
        print('Name: $name');
        print('Profession: $profession');
        print('Location: $location');
        print('About: $about');
        print('Specializations: $specializations');
        print('Profile Image: $profileImageUrl');
        print('Cover Image: $coverImageUrl');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Error saving profile changes: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  // Load profile data
  
}
