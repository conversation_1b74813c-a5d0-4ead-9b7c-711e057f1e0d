import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:build_mate/presentation/state/job_posted_flow_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';

final jobPostedViewModelProvider =
    StateNotifierProvider<JobPostedViewModel, JobPostedFlowState>((ref) {
      return JobPostedViewModel();
    });

class JobPostedViewModel extends StateNotifier<JobPostedFlowState> {
  final supabase = Supabase.instance.client;
  List<PostedJobModel> jobsList = [];
  // Track loading state per job ID
  Map<int, bool> loadingJobs = {};
  RealtimeChannel? _realtimeChannel;

  // Stream controller for jobs
  final StreamController<List<PostedJobModel>> _jobsStreamController =
      StreamController<List<PostedJobModel>>.broadcast();

  // Expose the stream
  Stream<List<PostedJobModel>> get jobsStream => _jobsStreamController.stream;

  // Add this flag to track if the notifier is mounted/active
  bool _isDisposed = false;

  JobPostedViewModel()
    : super(
        JobPostedFlowState(
          selectedJob: PostedJobModel(
            id: 0,
            title: '',
            clientName: '',
            description: '',
            budget: 0.0,
            categories: [],
            images: [],
            status: '',
            serviceDate: DateTime.now(),
            postDate: DateTime.now(),
            bids: [],
            hasMyBid: false,
          ),
        ),
      ) {
    getCurrentUserId();
    setupRealtimeSubscription();
    fetchAllJobCounts();
  }

  void setupRealtimeSubscription() {
    // Initial fetch to populate the stream
    fetchJobsByTab();

    // Set up realtime subscription for jobs table
    _realtimeChannel =
        supabase
            .channel('public:jobs')
            .onPostgresChanges(
              event: PostgresChangeEvent.all,
              schema: 'public',
              table: 'jobs',
              callback: (payload) {
                // When any change happens, refetch the jobs based on current tab
                fetchJobsByTab();
              },
            )
            .subscribe();

    // Set up realtime subscription for bids table
    supabase
        .channel('public:bids')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bids',
          callback: (payload) {
            // When any change happens to bids, refetch the jobs to update bid status
            fetchJobsByTab();
          },
        )
        .subscribe();
  }

  void setSelectedTabIndex(int index) {
    state = state.copyWith(selectedTabIndex: index);
    fetchJobsByTab();
  }

  Future<void> fetchJobsByTab() async {
    switch (state.selectedTabIndex) {
      case 0:
        // New Jobs tab - fetch open jobs
        await fetchAvailableJobsForArtisan();
        break;
      case 1:
        // My Jobs tab - fetch in-progress jobs for current artisan
        await fetchMyInProgressJobs();
        break;
      case 2:
        // Completed tab - fetch completed jobs for current artisan
        await _fetchMyCompletedJobs();
        break;
    }
  }

  Future<void> fetchAvailableJobs() async {
    // fetchJobsByTab();
  }

  void setSelectedJob(PostedJobModel job) {
    state = state.copyWith(selectedJob: job);
  }

  // Method to fetch all job counts for tabs
  Future<void> fetchAllJobCounts() async {
    try {
      final client = Supabase.instance.client;

      // Fetch new jobs count
      final newJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('status', 'open')
          .count(CountOption.exact);

      // Fetch my in-progress jobs count
      final myJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('status', 'in_progress')
          .eq('artisan_id', state.clientId)
          .count(CountOption.exact);

      // Fetch completed jobs count
      final completedJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('status', 'completed')
          .eq('artisan_id', state.clientId)
          .count(CountOption.exact);

      if (!_isDisposed) {
        state = state.copyWith(
          newJobsCount: newJobsResponse.count,
          myJobsCount: myJobsResponse.count,
          completedJobsCount: completedJobsResponse.count,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching job counts: $e');
      }
    }
  }

  Future<void> getCurrentUserId() async {
    final client = Supabase.instance.client;
    final currentUserUUID = client.auth.currentUser?.id;
    //get artisan id from artisans table in supabase where supabase_id = userId
    await client
        .from('artisans')
        .select('id')
        .eq('supabase_id', currentUserUUID ?? '')
        .single()
        .then((value) async {
          state = state.copyWith(clientId: value['id']);
          await updateOneSignalId();
        });
  }

  Future<void> updateOneSignalId() async {
    try {
      final client = Supabase.instance.client;
      final currentUserUUID = client.auth.currentUser?.id;

      final response =
          await client
              .from('artisans')
              .select('id')
              .eq('supabase_id', currentUserUUID ?? '')
              .single();

      state = state.copyWith(clientId: response['id']);
      // Get the OneSignal User ID (this is the subscription ID)
      final String? onesignalId = await OneSignal.User.getOnesignalId();

      final deviceStateSubscriptionId =
          OneSignal.User.pushSubscription.id ?? '';

      if (kDebugMode) {
        print('CLIENT_ONESIGNAL_ID_RETRIEVED_ON_LOAD: $onesignalId');
      }

      // Only update if we have a valid ID
      if (onesignalId != null && onesignalId.isNotEmpty) {
        await supabase
            .from('artisans')
            .update({'one_signal_id': deviceStateSubscriptionId})
            .eq('id', state.clientId);

        if (kDebugMode) {
          print(
            'OneSignal ID updated successfully for client: ${state.clientId}',
          );
        }
      } else {
        // Handle the case where OneSignal ID is not available yet
        if (kDebugMode) {
          print('OneSignal ID not available yet, will retry later');
        }

        // Set up a listener for when the subscription changes
        OneSignal.User.pushSubscription.addObserver((state) async {
          final deviceStateSubscriptionId =
              OneSignal.User.pushSubscription.id ?? '';
          final newId = await OneSignal.User.getOnesignalId();
          if (kDebugMode) {
            print('OneSignal subscription changed, new ID: $newId');
          }

          if (newId != null && newId.isNotEmpty) {
            await supabase
                .from('clients')
                .update({'one_signal_id': deviceStateSubscriptionId})
                .eq('id', this.state.clientId);

            if (kDebugMode) {
              print(
                'OneSignal ID updated on subscription change for client: ${this.state.clientId}',
              );
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating OneSignal ID: $e');
      }
    }
  }

  Future<List<PostedJobModel>> fetchAvailableJobsForArtisan() async {
    if (_isDisposed) return jobsList; // Return current list if disposed

    state = state.copyWith(isLoading: true);

    try {
      final client = Supabase.instance.client;

      final response = await client
          .from('jobs')
          .select('''
    *,
    client:client_id(id, name,avatar),
    service:service_id(name),
    job_tags:job_tags(
      sub_category_id,
      sub_category:sub_category_id(name)
    ),
    job_images:job_images(image_url),
    bids:bids!bids_job_id_fkey( 
      id,
      artisan_id,
      amount,
      status,
      created_at,
      is_selected,
      artisan:artisan_id(name, avatar)
    )
  ''')
          .eq('status', 'open')
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('NEW_JOBS RESPONSE: $response');
      }

      final jobs =
          response.map((item) {
            // Process categories
            final categories =
                (item['job_tags'] as List)
                    .map(
                      (tag) => {
                        'id': tag['sub_category_id'],
                        'name': tag['sub_category']?['name'] ?? 'Uncategorized',
                      },
                    )
                    .toList();

            // Process images
            final images =
                (item['job_images'] as List)
                    .map((image) => image['image_url'] as String)
                    .toList();

            // Process bids (will only contain this artisan's bids)
            final bids =
                (item['bids'] as List)
                    .map(
                      (bid) => {
                        'id': bid['id'],
                        'amount': bid['amount'],
                        'status': bid['status'],
                        'is_selected': bid['is_selected'],
                        'created_at': bid['created_at'],
                      },
                    )
                    .toList();

            return PostedJobModel(
              id: item['id'],
              title: item['service']?['name'] ?? 'Unknown Service',
              clientName: item['client']?['name'] ?? 'Unknown Client',
              clientAvatar: item['client']?['avatar'],
              description: item['job_description'] ?? '',
              budget: (item['budget'] ?? 0.0).toDouble(),
              categories:
                  categories.map((cat) => cat['name'].toString()).toList(),
              images: images,
              status: item['status'],
              serviceDate: DateTime.parse(item['service_date']),
              postDate: DateTime.parse(item['created_at']),
              bids: bids,
              hasMyBid: bids.isNotEmpty, // Helper flag
              myBidId: bids.isNotEmpty ? bids.first['id'] : null,
            );
          }).toList();

      // Check if disposed before updating state
      if (!_isDisposed) {
        // Update the state with the fetched jobs and set isLoading to false
        state = state.copyWith(
          isLoading: false,
          jobs: jobs,
          newJobsCount: jobs.length,
        );

        // Update the jobsList field
        jobsList = jobs;

        // Add to stream if not closed
        if (!_jobsStreamController.isClosed) {
          _jobsStreamController.add(jobs);
        }
      }

      return jobs;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching jobs: $e');
      }

      // Check if disposed before updating state
      if (!_isDisposed) {
        state = state.copyWith(isLoading: false);
      }

      return jobsList; // Return current list on error
    }
  }

  Future<List<PostedJobModel>> fetchMyInProgressJobs() async {
    if (_isDisposed) return jobsList; // Return current list if disposed

    state = state.copyWith(isLoading: true);

    try {
      final client = Supabase.instance.client;

      final response = await client
          .from('jobs')
          .select('''
    *,
    client:client_id(id, name,avatar),
    service:service_id(name),
    job_tags:job_tags(
      sub_category_id,
      sub_category:sub_category_id(name)
    ),
    job_images:job_images(image_url),
    bids:bids!bids_job_id_fkey( 
      id,
      artisan_id,
      amount,
      status,
      created_at,
      is_selected,
      artisan:artisan_id(name, avatar)
    )
  ''')
          .eq('status', 'in_progress')
          .eq('bids.artisan_id', state.clientId)
          .eq('bids.is_selected', true)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('IN_PROGRESS_JOBS RESPONSE: $response');
      }

      final jobs =
          response.map((item) {
            // Process categories
            final categories =
                (item['job_tags'] as List)
                    .map(
                      (tag) => {
                        'id': tag['sub_category_id'],
                        'name': tag['sub_category']?['name'] ?? 'Uncategorized',
                      },
                    )
                    .toList();

            // Process images
            final images =
                (item['job_images'] as List)
                    .map((image) => image['image_url'] as String)
                    .toList();

            // Process bids
            final bids =
                (item['bids'] as List)
                    .map(
                      (bid) => {
                        'id': bid['id'],
                        'amount': bid['amount'],
                        'status': bid['status'],
                        'is_selected': bid['is_selected'],
                        'created_at': bid['created_at'],
                      },
                    )
                    .toList();

            return PostedJobModel(
              id: item['id'],
              title: item['service']?['name'] ?? 'Unknown Service',
              clientName: item['client']?['name'] ?? 'Unknown Client',
              clientAvatar: item['client']?['avatar'],
              description: item['job_description'] ?? '',
              budget: (item['budget'] ?? 0.0).toDouble(),
              categories:
                  categories.map((cat) => cat['name'].toString()).toList(),
              images: images,
              status: item['status'],
              serviceDate: DateTime.parse(item['service_date']),
              postDate: DateTime.parse(item['created_at']),
              bids: bids,
              hasMyBid: true, // Always true for in-progress jobs
              myBidId: bids.isNotEmpty ? bids.first['id'] : null,
            );
          }).toList();

      // Check if disposed before updating state
      if (!_isDisposed) {
        // Update the state with the fetched jobs and set isLoading to false
        state = state.copyWith(
          isLoading: false,
          jobs: jobs,
          myJobsCount: jobs.length,
        );

        // Update the jobsList field
        jobsList = jobs;

        // Add to stream if not closed
        if (!_jobsStreamController.isClosed) {
          _jobsStreamController.add(jobs);
        }
      }

      return jobs;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching in-progress jobs: $e');
      }

      // Check if disposed before updating state
      if (!_isDisposed) {
        state = state.copyWith(isLoading: false);
      }

      return jobsList; // Return current list on error
    }
  }

  Future<List<PostedJobModel>> _fetchMyCompletedJobs() async {
    if (_isDisposed) return jobsList;

    state = state.copyWith(isLoading: true);

    try {
      final client = Supabase.instance.client;

      final response = await client
          .from('jobs')
          .select('''
    *,
    client:client_id(id, name,avatar),
    service:service_id(name),
    job_tags:job_tags(
      sub_category_id,
      sub_category:sub_category_id(name)
    ),
    job_images:job_images(image_url),
    bids:bids!bids_job_id_fkey( 
      id,
      artisan_id,
      amount,
      status,
      created_at,
      is_selected,
      artisan:artisan_id(name, avatar)
    )
  ''')
          .eq('status', 'completed')
          .eq('bids.artisan_id', state.clientId)
          .eq('bids.is_selected', true)
          .order('created_at', ascending: false);

      // Process response similar to fetchAvailableJobsForArtisan
      final jobs = _processJobsResponse(response);

      if (!_isDisposed) {
        state = state.copyWith(
          isLoading: false,
          jobs: jobs,
          completedJobsCount: jobs.length,
        );
        jobsList = jobs;

        if (!_jobsStreamController.isClosed) {
          _jobsStreamController.add(jobs);
        }
      }

      return jobs;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching completed jobs: $e');
      }

      if (!_isDisposed) {
        state = state.copyWith(isLoading: false);
      }

      return jobsList;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _realtimeChannel?.unsubscribe();
    _jobsStreamController.close();
    super.dispose();
  }

  Future<void> bid(PostedJobModel job) async {
    // Set loading for specific job
    loadingJobs[job.id] = true;
    state = state.copyWith(); // Trigger UI update

    try {
      final client = Supabase.instance.client;
      final currentUserUUID = client.auth.currentUser?.id;

      if (currentUserUUID == null) {
        throw Exception('User not authenticated');
      }

      if (kDebugMode) {
        print('Artisan ID: ${state.clientId}, Job ID: ${job.id}');
      }

      if (kDebugMode) {
        print('BID FUNCTION CALLED:');
      }

      // Create a new bid
      final response =
          await client
              .from('bids')
              .insert({
                'job_id': job.id,
                'artisan_id': state.clientId,
                'status': 'pending',
              })
              .select()
              .single();

      // Get the bid ID from the response
      final int bidId = response['id'];
      final int jobId = response['job_id'];

      // Update the job in our local state to reflect the new bid
      final updatedJob = PostedJobModel(
        id: job.id,
        title: job.title,
        clientName: job.clientName,
        clientAvatar: job.clientAvatar,
        description: job.description,
        budget: job.budget,
        categories: job.categories,
        images: job.images,
        status: job.status,
        serviceDate: job.serviceDate,
        postDate: job.postDate,
        bids: job.bids,
        hasMyBid: true,
        myBidId: bidId,
      );

      // Update the job in the list
      updateJobAfterBid(updatedJob);

      // Send notification to client
      try {
        final res = await supabase.functions.invoke(
          'send-bid-notification-to-client',
          body: {'jobId': jobId},
        );
        final data = res.data;
        if (kDebugMode) {
          print('Notification sent: ${data.toString()}');
        }
      } catch (notificationError) {
        if (kDebugMode) {
          print('Error sending notification: $notificationError');
        }
        // Continue execution even if notification fails
      }

      // Clear loading state
      loadingJobs[job.id] = false;
      state = state.copyWith(); // Trigger UI update
    } catch (e) {
      if (kDebugMode) {
        print('Error placing bid: $e');
      }
      loadingJobs[job.id] = false;
      state = state.copyWith(); // Trigger UI update
      rethrow; // Rethrow to allow the UI to handle the error
    }
  }

  Future<void> cancelBid() async {
    state = state.copyWith(isCancellingLoading: true);

    try {
      final client = Supabase.instance.client;

      // Get the selected job
      final selectedJob = state.selectedJob;
      if (selectedJob == null ||
          !selectedJob.hasMyBid ||
          selectedJob.myBidId == null) {
        throw Exception('No bid to cancel');
      }

      // Delete the bid
      await client.from('bids').delete().eq('id', selectedJob.id);

      // Update the job to reflect the canceled bid
      final updatedJob = PostedJobModel(
        id: selectedJob.id,
        title: selectedJob.title,
        clientName: selectedJob.clientName,
        clientAvatar: selectedJob.clientAvatar,
        description: selectedJob.description,
        budget: selectedJob.budget,
        categories: selectedJob.categories,
        images: selectedJob.images,
        status: selectedJob.status,
        serviceDate: selectedJob.serviceDate,
        postDate: selectedJob.postDate,
        bids: selectedJob.bids,
        hasMyBid: false,
        myBidId: null,
      );

      // Update the state with the updated job
      updateJobAfterBid(updatedJob);
      state = state.copyWith(isCancellingLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error canceling bid: $e');
      }
      state = state.copyWith(isCancellingLoading: false);
      rethrow; // Rethrow to allow the UI to handle the error
    }
  }

  // Update a job after placing or canceling a bid
  void updateJobAfterBid(PostedJobModel updatedJob) {
    final updatedJobs = List<PostedJobModel>.from(state.jobs);
    final jobIndex = updatedJobs.indexWhere((job) => job.id == updatedJob.id);

    if (jobIndex != -1) {
      updatedJobs[jobIndex] = updatedJob;
      state = state.copyWith(jobs: updatedJobs, selectedJob: updatedJob);
    }
  }

  // Add this helper method to check if a specific job is loading
  bool isJobLoading(int jobId) {
    return loadingJobs[jobId] == true;
  }

  // Handle a new job being inserted
  // Future<void> _handleJobInserted(Map<String, dynamic> newRecord) async {
  //   if (newRecord['status'] != 'open') return;

  //   // Fetch the complete job data with all relations
  //   final jobData = await _fetchSingleJobWithRelations(newRecord['id']);
  //   if (jobData == null) return;

  //   // Create a new job model
  //   final newJob = _createJobModelFromData(jobData);

  //   // Add to the current list without reloading everything
  //   final updatedJobs = [...state.jobs, newJob];
  //   // Sort by post date descending
  //   updatedJobs.sort((a, b) => b.postDate.compareTo(a.postDate));

  //   // Update state
  //   state = state.copyWith(jobs: updatedJobs);
  //   jobsList = updatedJobs;
  // }

  // Handle a job being updated
  // Future<void> _handleJobUpdated(
  //   Map<String, dynamic> oldRecord,
  //   Map<String, dynamic> newRecord,
  // ) async {
  //   // Get the job ID for easier reference
  //   final int jobId = newRecord['id'];

  //   // Check if this job exists in our current list
  //   final existingJobIndex = state.jobs.indexWhere((job) => job.id == jobId);
  //   final bool jobExistsInList = existingJobIndex != -1;

  //   // Case 1: Status changed from 'open' to something else - remove from list
  //   if (oldRecord['status'] == 'open' && newRecord['status'] != 'open') {
  //     if (jobExistsInList) {
  //       final updatedJobs = [...state.jobs];
  //       updatedJobs.removeAt(existingJobIndex);
  //       state = state.copyWith(jobs: updatedJobs);
  //       jobsList = updatedJobs;
  //     }
  //     return;
  //   }

  //   // Case 2: Status changed to 'open' - add to list if not already there
  //   if (oldRecord['status'] != 'open' && newRecord['status'] == 'open') {
  //     if (!jobExistsInList) {
  //       final jobData = await _fetchSingleJobWithRelations(jobId);
  //       if (jobData == null) return;

  //       final newJob = _createJobModelFromData(jobData);
  //       final updatedJobs = [...state.jobs, newJob];
  //       updatedJobs.sort((a, b) => b.postDate.compareTo(a.postDate));

  //       state = state.copyWith(jobs: updatedJobs);
  //       jobsList = updatedJobs;
  //     } else {
  //       // Job exists but status changed to open, update it
  //       final jobData = await _fetchSingleJobWithRelations(jobId);
  //       if (jobData == null) return;

  //       final updatedJob = _createJobModelFromData(jobData);
  //       final updatedJobs = [...state.jobs];
  //       updatedJobs[existingJobIndex] = updatedJob;

  //       state = state.copyWith(jobs: updatedJobs);
  //       jobsList = updatedJobs;
  //     }
  //     return;
  //   }

  //   // Case 3: Job is already 'open' and remains 'open' - just update it
  //   if (newRecord['status'] == 'open' && jobExistsInList) {
  //     final jobData = await _fetchSingleJobWithRelations(jobId);
  //     if (jobData == null) return;

  //     final updatedJob = _createJobModelFromData(jobData);
  //     final updatedJobs = [...state.jobs];
  //     updatedJobs[existingJobIndex] = updatedJob;

  //     state = state.copyWith(jobs: updatedJobs);
  //     jobsList = updatedJobs;
  //   }
  // }

  // Handle a job being deleted
  // void _handleJobDeleted(Map<String, dynamic> oldRecord) {
  //   final updatedJobs =
  //       state.jobs.where((job) => job.id != oldRecord['id']).toList();
  //   state = state.copyWith(jobs: updatedJobs);
  //   jobsList = updatedJobs;
  // }

  // Helper method to fetch a single job with all its relations
  // Future<Map<String, dynamic>?> _fetchSingleJobWithRelations(int jobId) async {
  //   try {
  //     final response =
  //         await supabase
  //             .from('jobs')
  //             .select('''
  //           *,
  //           client:client_id(id, name, avatar),
  //           service:service_id(name),
  //           job_tags:job_tags(
  //             sub_category_id,
  //             sub_category:sub_category_id(name)
  //           ),
  //           job_images:job_images(image_url),
  //           bids:bids!bids_job_id_fkey(
  //             id,
  //             artisan_id,
  //             amount,
  //             status,
  //             created_at,
  //             is_selected,
  //             artisan:artisan_id(name, avatar)
  //           )
  //         ''')
  //             .eq('id', jobId)
  //             .single();

  //     return response;
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('Error fetching single job: $e');
  //     }
  //     return null;
  //   }
  // }

  // Helper method to create a PostedJobModel from job data
  // PostedJobModel _createJobModelFromData(Map<String, dynamic> item) {
  //   // Process categories
  //   final categories =
  //       (item['job_tags'] as List)
  //           .map(
  //             (tag) => {
  //               'id': tag['sub_category_id'],
  //               'name': tag['sub_category']?['name'] ?? 'Uncategorized',
  //             },
  //           )
  //           .toList();

  //   // Process images
  //   final images =
  //       (item['job_images'] as List)
  //           .map((image) => image['image_url'] as String)
  //           .toList();

  //   // Process bids
  //   final bids =
  //       (item['bids'] as List)
  //           .map(
  //             (bid) => {
  //               'id': bid['id'],
  //               'amount': bid['amount'],
  //               'status': bid['status'],
  //               'is_selected': bid['is_selected'],
  //               'created_at': bid['created_at'],
  //             },
  //           )
  //           .toList();

  //   return PostedJobModel(
  //     id: item['id'],
  //     title: item['service']?['name'] ?? 'Unknown Service',
  //     clientName: item['client']?['name'] ?? 'Unknown Client',
  //     clientAvatar: item['client']?['avatar'],
  //     description: item['job_description'] ?? '',
  //     budget: (item['budget'] ?? 0.0).toDouble(),
  //     categories: categories.map((cat) => cat['name'].toString()).toList(),
  //     images: images,
  //     status: item['status'],
  //     serviceDate: DateTime.parse(item['service_date']),
  //     postDate: DateTime.parse(item['created_at']),
  //     bids: bids,
  //     hasMyBid: bids.isNotEmpty, // Helper flag
  //     myBidId: bids.isNotEmpty ? bids.first['id'] : null,
  //   );
  // }

  // Add this method to refresh the stream with current data
  void refreshJobsStream() {
    // Don't proceed if disposed
    if (_isDisposed || _jobsStreamController.isClosed) return;

    // Check if we already have jobs in the list
    if (jobsList.isNotEmpty) {
      // Immediately emit the current jobs list to the stream
      _jobsStreamController.add(jobsList);
    }

    // Also fetch fresh data from the server
    fetchAvailableJobsForArtisan();
  }

  // Helper method to process jobs response
  List<PostedJobModel> _processJobsResponse(List<dynamic> response) {
    return response.map((item) {
      // Process categories
      final categories =
          (item['job_tags'] as List)
              .map(
                (tag) => {
                  'id': tag['sub_category_id'],
                  'name': tag['sub_category']?['name'] ?? 'Uncategorized',
                },
              )
              .toList();

      // Process images
      final images =
          (item['job_images'] as List)
              .map((image) => image['image_url'] as String)
              .toList();

      // Process bids
      final bids =
          (item['bids'] as List)
              .map(
                (bid) => {
                  'id': bid['id'],
                  'amount': bid['amount'],
                  'status': bid['status'],
                  'is_selected': bid['is_selected'],
                  'created_at': bid['created_at'],
                },
              )
              .toList();

      return PostedJobModel(
        id: item['id'],
        title: item['service']?['name'] ?? 'Unknown Service',
        clientName: item['client']?['name'] ?? 'Unknown Client',
        clientAvatar: item['client']?['avatar'],
        description: item['job_description'] ?? '',
        budget: (item['budget'] ?? 0.0).toDouble(),
        categories: categories.map((cat) => cat['name'].toString()).toList(),
        images: images,
        status: item['status'],
        serviceDate: DateTime.parse(item['service_date']),
        postDate: DateTime.parse(item['created_at']),
        bids: bids,
        hasMyBid: true, // Always true for completed jobs with selected bids
        myBidId: bids.isNotEmpty ? bids.first['id'] : null,
      );
    }).toList();
  }
}
