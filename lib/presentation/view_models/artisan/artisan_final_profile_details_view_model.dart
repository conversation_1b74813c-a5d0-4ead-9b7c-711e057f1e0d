import 'dart:io';
import 'dart:convert';
import 'package:build_mate/data/dto/requests_dto/artsian_profile_request.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:build_mate/presentation/state/artisan_final_profile_details_state.dart';
import 'package:build_mate/presentation/state/artisan_profile_state.dart';
import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:build_mate/presentation/view_models/user/onboarding/artisan_profile_details_view_model.dart';

// Provider to access the artisan profile state from the previous screen
final artisanProfileStateProvider = Provider<ArtisanProfileState>((ref) {
  return ref.watch(artisanProfileDetailsViewModelProvider);
});

final artisanFinalProfileDetailsViewModelProvider = StateNotifierProvider<
  ArtisanFinalProfileDetailsViewModel,
  ArtisanFinalProfileDetailsState
>((ref) {
  return ArtisanFinalProfileDetailsViewModel(
    ArtisanFinalProfileDetailsState(),
    ref.read(goRouterProvider),
    ref.watch(artisanProfileStateProvider),
  );
});

class ArtisanFinalProfileDetailsViewModel
    extends StateNotifier<ArtisanFinalProfileDetailsState> {
  final GoRouter _router;
  final supabase = Supabase.instance.client;
  final aboutController = TextEditingController();
  File? nationalIdImageFile;
  final ImagePicker _picker = ImagePicker();
  final ArtisanProfileState artisanProfileState;

  // Add upload progress tracking
  double nationalIdUploadProgress = 0.0;
  bool isUploadingNationalId = false;

  ArtisanFinalProfileDetailsViewModel(
    super.state,
    this._router,
    this.artisanProfileState,
  ) {
    _loadCategories();
    _logArtisanProfileState();
  }

  void _logArtisanProfileState() {
    if (kDebugMode) {
      print('Artisan Profile State:');
    }
    if (kDebugMode) {
      print('Supabase UUID: ${artisanProfileState.supabaseUUID}');
    }
    if (kDebugMode) {
      print('Full Name: ${artisanProfileState.fullname}');
    }
    if (kDebugMode) {
      print('Email: ${artisanProfileState.email}');
    }
    if (kDebugMode) {
      print('Phone Number: ${artisanProfileState.phoneNumber}');
    }

    if (kDebugMode) {
      print('Second Phone Number: ${artisanProfileState.secondPhoneNumber}');
    }
    if (kDebugMode) {
      print('WhatsApp Number: ${artisanProfileState.whatsappNumber}');
    }
    if (kDebugMode) {
      print('Address: ${artisanProfileState.address}');
    }
    if (kDebugMode) {
      print('National ID: ${artisanProfileState.nationalId}');
    }
    if (kDebugMode) {
      print('Avatar URL: ${artisanProfileState.avatarUrl}');
    }
    if (kDebugMode) {
      print('Cover Image URL: ${artisanProfileState.coverImageUrl}');
    }
    if (kDebugMode) {
      print(
        'Location: ${artisanProfileState.location.latitude}, ${artisanProfileState.location.longitude}',
      );
    }
  }

  Future<void> _loadCategories() async {
    try {
      state = state.copyWith(isLoading: true);

      // Try to get categories from storage first
      final categories = await _getCategoriesFromStorage();

      // If we have categories, we're done
      if (categories.isNotEmpty) {
        if (kDebugMode) {
          print('Loaded ${categories.length} categories from storage');
        }
        state = state.copyWith(isLoading: false);
        return;
      }

      // If no categories in storage, fetch them from the database
      await _fetchServicesAndSubcategories();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<List<Map<String, dynamic>>> _getCategoriesFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);
        return List<Map<String, dynamic>>.from(categoriesData['categories']);
      }

      // If no categories in storage, fetch them
      await _fetchServicesAndSubcategories();

      // Try again after fetching
      final newCategoriesJson = prefs.getString('categories');
      if (newCategoriesJson != null) {
        final categoriesData = jsonDecode(newCategoriesJson);
        return List<Map<String, dynamic>>.from(categoriesData['categories']);
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories from storage: $e');
      }
      return [];
    }
  }

  Future<void> _fetchServicesAndSubcategories() async {
    try {
      // Fetch all services
      final servicesResponse = await supabase
          .from('services')
          .select('id, name')
          .order('name');

      if (servicesResponse.isEmpty) {
        if (kDebugMode) {
          print('No services found');
        }
        return;
      }

      final List<Map<String, dynamic>> services =
          List<Map<String, dynamic>>.from(servicesResponse);

      if (kDebugMode) {
        print('Fetched ${services.length} services from database');
      }
      for (var service in services) {
        if (kDebugMode) {
          print('Service: ${service['name']}, ID: ${service['id']}');
        }
      }

      // Create maps to store services and their subcategories
      final Map<String, List<String>> categoriesMap = {};
      final Map<String, int> serviceIdMap = {}; // Map service name to ID (int)
      final Map<String, Map<String, int>> subcategoryIdMap =
          {}; // Map subcategory names to IDs (int) for each service

      // For each service, fetch its subcategories
      for (final service in services) {
        final int serviceId = service['id']; // Keep as int
        final String serviceName = service['name'];

        // Store service ID mapping
        serviceIdMap[serviceName] = serviceId;

        // Initialize subcategory ID map for this service
        subcategoryIdMap[serviceName] = {};

        // Fetch subcategories for this service
        final subcategoriesResponse = await supabase
            .from('sub_categories')
            .select('id, name')
            .eq('service_id', serviceId)
            .order('name');

        if (subcategoriesResponse.isNotEmpty) {
          final List<dynamic> subcategories = List<dynamic>.from(
            subcategoriesResponse,
          );
          final List<String> subcategoryNames = [];

          // Store subcategory names and their IDs
          for (final subcategory in subcategories) {
            final String subcategoryName = subcategory['name'];
            final int subcategoryId = subcategory['id']; // Keep as int

            subcategoryNames.add(subcategoryName);
            subcategoryIdMap[serviceName]![subcategoryName] = subcategoryId;
          }

          // Add to our categories map
          categoriesMap[serviceName] = subcategoryNames;
        }
      }

      // Now we have maps of service names to IDs and subcategory names to IDs
      if (kDebugMode) {
        print('Service ID Map: $serviceIdMap');
      }
      if (kDebugMode) {
        print('Subcategory ID Map: $subcategoryIdMap');
      }

      // Store the categories in a format that can be used by the ServiceCategoriesScreen
      final List<Map<String, dynamic>> formattedCategories = [];

      categoriesMap.forEach((serviceName, subcategories) {
        formattedCategories.add({
          'name': serviceName,
          'id': serviceIdMap[serviceName],
          'subcategories': subcategories,
        });
      });

      // Store the formatted categories and ID mappings in shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'categories',
        jsonEncode({
          'categories': formattedCategories,
          'serviceIdMap': serviceIdMap,
          'subcategoryIdMap': subcategoryIdMap,
        }),
      );

      if (kDebugMode) {
        print('Categories and ID mappings saved to shared preferences');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching services and subcategories: $e');
      }
      rethrow;
    }
  }

  Future<void> onCategorySelected(
    String mainCategory,
    String subCategory,
    List<String> subCategories,
  ) async {
    try {
      if (kDebugMode) {
        print(
          'onCategorySelected called with: mainCategory=$mainCategory, subCategory=$subCategory',
        );
      }

      // Get the service ID and subcategory ID from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');
      int serviceId = 0; // Default to 0 for int
      Map<String, int> subcategoryIdMap = {}; // Use int for IDs

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);
        if (kDebugMode) {
          print(
            'Categories data from shared preferences: ${categoriesData.keys}',
          );
        }

        // Safely extract the service ID map
        if (categoriesData.containsKey('serviceIdMap')) {
          final serviceIdMap = Map<String, dynamic>.from(
            categoriesData['serviceIdMap'],
          );
          if (kDebugMode) {
            print('Service ID Map from shared preferences: $serviceIdMap');
          }

          if (serviceIdMap.containsKey(mainCategory)) {
            serviceId = serviceIdMap[mainCategory]; // Keep as int
            if (kDebugMode) {
              print('Found service ID for $mainCategory: $serviceId');
            }
          } else {
            if (kDebugMode) {
              print('Service $mainCategory not found in serviceIdMap');
            }
            // If not found in shared preferences, fetch from database
            await _fetchServiceIdFromDatabase(mainCategory);
            return; // This will restart the process with updated shared preferences
          }
        } else {
          if (kDebugMode) {
            print('serviceIdMap not found in shared preferences');
          }
          // If serviceIdMap not found, fetch all services and subcategories
          await _fetchServicesAndSubcategories();
          return; // This will restart the process with updated shared preferences
        }

        // Safely extract the subcategory ID map
        if (categoriesData.containsKey('subcategoryIdMap')) {
          final allSubcategoryIdMaps = categoriesData['subcategoryIdMap'];
          if (allSubcategoryIdMaps != null &&
              allSubcategoryIdMaps is Map &&
              allSubcategoryIdMaps.containsKey(mainCategory)) {
            // Convert the subcategory map to the correct type
            final subMap = allSubcategoryIdMaps[mainCategory];
            if (subMap != null && subMap is Map) {
              subcategoryIdMap = Map<String, int>.from(
                subMap.map(
                  (key, value) => MapEntry(key.toString(), value as int),
                ),
              );
              if (kDebugMode) {
                print(
                  'Found subcategory ID map for $mainCategory: $subcategoryIdMap',
                );
              }
            } else {
              if (kDebugMode) {
                print('subMap is null or not a Map');
              }
            }
          } else {
            if (kDebugMode) {
              print(
                'allSubcategoryIdMaps is null, not a Map, or does not contain $mainCategory',
              );
            }
          }
        } else {
          if (kDebugMode) {
            print('subcategoryIdMap not found in shared preferences');
          }
        }
      } else {
        if (kDebugMode) {
          print(
            'No categories found in shared preferences, fetching from database',
          );
        }
        await _fetchServicesAndSubcategories();
        return; // This will restart the process with updated shared preferences
      }

      if (kDebugMode) {
        print(
          'Setting state with: mainCategory=$mainCategory, mainCategoryId=$serviceId',
        );
      }
      if (kDebugMode) {
        print('subcategoryIdMap=$subcategoryIdMap');
      }

      // First, set the state with the selected category and subcategory
      state = state.copyWith(
        selectedMainCategory: mainCategory,
        selectedMainCategoryId: serviceId, // Use int directly
        selectedSubCategories: [subCategory], // Initialize with first selection
        relatedSubCategories: subCategories,
        subcategoryIdMap: subcategoryIdMap,
      );

      if (kDebugMode) {
        print(
          'Updated state: ${state.selectedMainCategory}, ${state.selectedMainCategoryId}',
        );
      }

      // If the subcategories list is empty or service ID is 0, try to fetch them from the database
      if (subCategories.isEmpty || serviceId == 0) {
        await _fetchServiceAndSubcategoriesFromDatabase(
          mainCategory,
          subCategory,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in onCategorySelected: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _fetchServiceIdFromDatabase(String mainCategory) async {
    try {
      state = state.copyWith(isLoading: true);

      // Find the service ID for the selected main category
      final serviceResponse =
          await supabase
              .from('services')
              .select('id')
              .eq('name', mainCategory)
              .maybeSingle();

      if (serviceResponse != null) {
        final int fetchedServiceId = serviceResponse['id']; // Keep as int
        if (kDebugMode) {
          print(
            'Fetched service ID from database: $fetchedServiceId for $mainCategory',
          );
        }

        // Update shared preferences with this new service
        final prefs = await SharedPreferences.getInstance();
        final categoriesJson = prefs.getString('categories');

        if (categoriesJson != null) {
          final categoriesData = jsonDecode(categoriesJson);

          // Update the service ID map
          if (categoriesData.containsKey('serviceIdMap')) {
            final serviceIdMap = Map<String, dynamic>.from(
              categoriesData['serviceIdMap'],
            );
            serviceIdMap[mainCategory] = fetchedServiceId;
            categoriesData['serviceIdMap'] = serviceIdMap;

            // Save updated data back to shared preferences
            await prefs.setString('categories', jsonEncode(categoriesData));
            if (kDebugMode) {
              print('Updated serviceIdMap in shared preferences');
            }
          }
        }

        state = state.copyWith(isLoading: false);
      } else {
        if (kDebugMode) {
          print('Service not found in database: $mainCategory');
        }
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching service ID from database: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _fetchServiceAndSubcategoriesFromDatabase(
    String mainCategory,
    String subCategory,
  ) async {
    try {
      state = state.copyWith(isLoading: true);

      // Find the service ID for the selected main category
      final serviceResponse =
          await supabase
              .from('services')
              .select('id')
              .eq('name', mainCategory)
              .maybeSingle();

      if (serviceResponse != null) {
        final int fetchedServiceId = serviceResponse['id']; // Keep as int
        if (kDebugMode) {
          print('Fetched service ID from database: $fetchedServiceId');
        }

        // Update the state with the fetched service ID
        state = state.copyWith(selectedMainCategoryId: fetchedServiceId);

        // Fetch subcategories for this service
        final subcategoriesResponse = await supabase
            .from('sub_categories')
            .select('id, name')
            .eq('service_id', fetchedServiceId)
            .order('name');

        if (subcategoriesResponse.isNotEmpty) {
          final List<dynamic> fetchedSubcategories = List<dynamic>.from(
            subcategoriesResponse,
          );
          final List<String> subcategoryNames =
              fetchedSubcategories
                  .map((subcategory) => subcategory['name'] as String)
                  .toList();

          // Create subcategory ID map
          final Map<String, int> newSubcategoryIdMap = {};
          for (final subcategory in fetchedSubcategories) {
            newSubcategoryIdMap[subcategory['name']] =
                subcategory['id']; // Keep as int
          }

          if (kDebugMode) {
            print(
              'Fetched subcategory ID map from database: $newSubcategoryIdMap',
            );
          }

          // Update the state with the fetched subcategories and ID map
          state = state.copyWith(
            relatedSubCategories: subcategoryNames,
            subcategoryIdMap: newSubcategoryIdMap,
            isLoading: false,
          );

          if (kDebugMode) {
            print(
              'Updated state after fetch: ${state.selectedMainCategoryId}, ${state.subcategoryIdMap}',
            );
          }

          // Update shared preferences with this new data
          _updateSharedPreferencesWithNewData(
            mainCategory,
            fetchedServiceId,
            newSubcategoryIdMap,
          );
        }
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching service and subcategories from database: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _updateSharedPreferencesWithNewData(
    String mainCategory,
    int serviceId, // Use int
    Map<String, int> subcategoryIdMap, // Use int
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);

        // Update the service ID map
        if (categoriesData.containsKey('serviceIdMap')) {
          final serviceIdMap = Map<String, dynamic>.from(
            categoriesData['serviceIdMap'],
          );
          serviceIdMap[mainCategory] = serviceId;
          categoriesData['serviceIdMap'] = serviceIdMap;
        }

        // Update the subcategory ID map
        if (categoriesData.containsKey('subcategoryIdMap')) {
          final allSubcategoryIdMaps = Map<String, dynamic>.from(
            categoriesData['subcategoryIdMap'],
          );
          allSubcategoryIdMaps[mainCategory] = subcategoryIdMap;
          categoriesData['subcategoryIdMap'] = allSubcategoryIdMaps;
        }

        // Save updated data back to shared preferences
        await prefs.setString('categories', jsonEncode(categoriesData));
        if (kDebugMode) {
          print('Updated shared preferences with new data for $mainCategory');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating shared preferences with new data: $e');
      }
    }
  }

  void setSelectedMainCategory(String mainCategory) {
    state = state.copyWith(selectedMainCategory: mainCategory);
  }

  void addSubCategory(String subCategory) {
    final updatedSubCategories = [...state.selectedSubCategories, subCategory];
    state = state.copyWith(selectedSubCategories: updatedSubCategories);
  }

  void removeSubCategory(String subCategory) {
    final updatedSubCategories = [...state.selectedSubCategories];
    updatedSubCategories.remove(subCategory);
    state = state.copyWith(selectedSubCategories: updatedSubCategories);
  }

  void setRelatedSubCategories(List<String> subCategories) {
    state = state.copyWith(relatedSubCategories: subCategories);
  }

  void updateAbout(String about) {
    state = state.copyWith(about: about);
  }

  Future<void> pickNationalIdImage(BuildContext context) async {
    // Request permission
    final status = await Permission.photos.request();

    if (status.isGranted) {
      try {
        // Set uploading state
        isUploadingNationalId = true;
        nationalIdUploadProgress = 0.0;
        // Don't set isLoading to true to avoid fullscreen loader

        final XFile? pickedFile = await _picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          nationalIdImageFile = File(pickedFile.path);

          // Upload to Supabase and get URL with progress
          final imageUrl = await _uploadNationalIdImage(nationalIdImageFile!, (
            progress,
          ) {
            nationalIdUploadProgress = progress;
            // Trigger UI update without changing isLoading
            state = state.copyWith();
          });

          state = state.copyWith(nationalIdImage: imageUrl);
          isUploadingNationalId = false;
        } else {
          // User canceled picking
          isUploadingNationalId = false;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error picking image: $e');
        }
        if (context.mounted) {
          _showMessage(
            message: 'Failed to pick image',
            isError: true,
            context: context,
          );
          // ScaffoldMessenger.of(
          //   context,
          // ).showSnackBar(const SnackBar(content: Text('Failed to pick image')));
        }

        isUploadingNationalId = false;
      }
    } else {
      if (context.mounted) {
        _showMessage(
          message: 'Permission to access gallery denied',
          isError: true,
          context: context,
        );
      }
    }
  }

  void _showMessage({
    required String message,
    required bool isError,
    required BuildContext context,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isError ? Colors.green.shade600 : Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Future<String> _uploadNationalIdImage(
    File imageFile,
    Function(double) onProgress,
  ) async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-national-id-$timestamp.$fileExtension';
      final filePath = 'national-id/$fileName';

      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress for demo purposes
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      if (kDebugMode) {
        print('National ID image uploaded successfully: $imageUrl');
      }

      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading national ID image: $e');
      }
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }
      rethrow;
    }
  }

  bool validateInputs(BuildContext context) {
    if (state.selectedMainCategory.isEmpty ||
        state.selectedSubCategories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select your main service and specializations'),
        ),
      );
      return false;
    }

    if (aboutController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please provide information about your services'),
        ),
      );
      return false;
    }

    if (state.nationalIdImage.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please upload your national ID document'),
        ),
      );
      return false;
    }

    return true;
  }

  Future<void> saveArtisanFinalDetails(BuildContext context) async {
    if (!validateInputs(context)) {
      return;
    }

    try {
      state = state.copyWith(isLoading: true);

      // Update about from controller
      updateAbout(aboutController.text.trim());

      // Save to Supabase
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Debug: Print subcategory map types
      if (kDebugMode) {
        print('Subcategory ID Map: ${state.subcategoryIdMap}');
      }
      for (final entry in state.subcategoryIdMap.entries) {
        if (kDebugMode) {
          print(
            'Key: ${entry.key}, Value: ${entry.value}, Type: ${entry.value.runtimeType}',
          );
        }
      }

      // Get subcategory IDs for selected subcategories
      final List<int> selectedSubCategoryIds = [];
      for (final subCategory in state.selectedSubCategories) {
        if (kDebugMode) {
          print('Processing subcategory: $subCategory');
        }
        final subCategoryId = state.subcategoryIdMap[subCategory];
        if (subCategoryId != null) {
          if (kDebugMode) {
            print(
              'Found ID: $subCategoryId, Type: ${subCategoryId.runtimeType}',
            );
          }

          final int intId =
              // ignore: unnecessary_type_check
              subCategoryId is int
                  ? subCategoryId
                  : int.parse(subCategoryId.toString());
          selectedSubCategoryIds.add(intId);
        }
      }

      if (kDebugMode) {
        print('Selected subcategory IDs: $selectedSubCategoryIds');
      }

      // Create the ArtisanProfileRequest object
      final artisanData = ArtisanProfileRequest(
        artisanDetails: ArtisanDetails(
          supabaseId:
              artisanProfileState.supabaseUUID.isNotEmpty
                  ? artisanProfileState.supabaseUUID
                  : userId,
          fullname: artisanProfileState.fullname,
          email: artisanProfileState.email,
          whatsappNumber: artisanProfileState.whatsappNumber,
          address: artisanProfileState.address,
          nationalId: artisanProfileState.nationalId,
          avatarUrl: artisanProfileState.avatarUrl,
          coverImageUrl: artisanProfileState.coverImageUrl,
          isCompany: 0,
          about: state.about,
          nationalIdDocument: state.nationalIdImage,
        ),
        artisanSpecialization: ArtisanSpecialization(
          serviceId: state.selectedMainCategoryId,
        ),
        specializationTags:
            selectedSubCategoryIds.map((id) {
              if (kDebugMode) {
                print(
                  'Creating SpecializationTags with ID: $id, Type: ${id.runtimeType}',
                );
              }
              return SpecializationTags(subCategoryId: id);
            }).toList(),
        phonenumbers: [
          Phonenumbers(
            phonenumber: artisanProfileState.phoneNumber,
            secondPhonenumber:
                artisanProfileState.secondPhoneNumber.isEmpty
                    ? ''
                    : artisanProfileState.secondPhoneNumber,
          ),
        ],
        artisanLocation: ArtisanLocation(
          latitude: artisanProfileState.location.latitude,
          longitude: artisanProfileState.location.longitude,
          address: artisanProfileState.address,
        ),
      );

      // Debug: Print the final request
      final requestJson = artisanData.toJson();
      if (kDebugMode) {
        print('Request JSON: $requestJson');
      }

      BuildContext dialogContext = context;
      // Show progress dialog with loading indicator
      if (dialogContext.mounted) {
        showDialog(
          context: dialogContext,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const Text(
                    'Saving your profile...',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            );
          },
        );
      }

      // Invoke the Supabase function
      try {
        final response = await supabase.functions.invoke(
          'save-artisan-details',
          body: requestJson,
        );
        if (kDebugMode) {
          print('Edge function response: ${response.data}');
        }
      } catch (functionError) {
        if (kDebugMode) {
          print('Edge function error: $functionError');
        }
        // Even if the function call fails, we'll continue since the data is saved
      }

      // Save role preference
      final prefs = await SharedPreferences.getInstance();
      prefs.setBool('is_client', false);

      state = state.copyWith(isLoading: false);

      // Close the progress dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile saved successfully!')),
        );
      }

      // Navigate to welcome screen
      _router.goNamed(RouteConstants.WELCOME_SCREEN);
    } catch (e) {
      state = state.copyWith(isLoading: false);
      if (kDebugMode) {
        print('Error saving artisan final details: $e');
      }

      // Close the progress dialog if it's open
      if (context.mounted) {
        Navigator.maybeOf(context)?.pop();
      }

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    }
  }

  @override
  void dispose() {
    aboutController.dispose();
    super.dispose();
  }
}
