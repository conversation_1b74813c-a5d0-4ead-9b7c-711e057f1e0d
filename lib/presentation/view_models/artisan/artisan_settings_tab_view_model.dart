import 'package:build_mate/presentation/state/artisan_settings_tab_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final artisanSettingsTabViewModelProvider =
    StateNotifierProvider<ArtisanSettingsTabViewModel, ArtisanSettingsTabState>((
      ref,
    ) {
      return ArtisanSettingsTabViewModel(ArtisanSettingsTabState());
    });

class ArtisanSettingsTabViewModel extends StateNotifier<ArtisanSettingsTabState> {
  ArtisanSettingsTabViewModel(super.state) {
    getArtisanProfile();
  }

  void setLoadingState(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getArtisanProfile() async {
    setLoadingState(true);
    final user = Supabase.instance.client.auth.currentUser;

    if (user == null) {
      setLoadingState(false);
      return;
    }

    try {
      await Supabase.instance.client
          .from('artisans')
          .select('name, avatar, is_available')
          .eq('supabase_id', user.id)
          .single()
          .then((value) {
            if (kDebugMode) {
              print('ARTISAN_PROFILE_DATA: ${value.toString()}');
            }
            state = state.copyWith(
              profileUrl: value['avatar'] ?? '',
              username: value['name'] ?? '',
              isAvailable: value['is_available'] ?? false,
            );
            setLoadingState(false);
          });
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching artisan profile: $e');
      }
      setLoadingState(false);
    }
  }

  Future<void> updateAvailability(bool isAvailable) async {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) return;

    try {
      // Update in database
      await Supabase.instance.client
          .from('artisans')
          .update({'is_available': isAvailable})
          .eq('supabase_id', user.id);

      // Update local state
      state = state.copyWith(isAvailable: isAvailable);
      
      if (kDebugMode) {
        print('Availability updated to: $isAvailable');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating availability: $e');
      }
    }
  }

  void updateDarkMode(bool darkMode) {
    state = state.copyWith(darkMode: darkMode);
    // TODO: Implement dark mode persistence
  }

  void updateNotifications(bool notifications) {
    state = state.copyWith(notifications: notifications);
    // TODO: Implement notification settings persistence
  }

  void updatePlayInBackground(bool playInBackground) {
    state = state.copyWith(playInBackground: playInBackground);
    // TODO: Implement play in background persistence
  }

  Future<void> logout() async {
    await Supabase.instance.client.auth.signOut(scope: SignOutScope.global);
  }
}
