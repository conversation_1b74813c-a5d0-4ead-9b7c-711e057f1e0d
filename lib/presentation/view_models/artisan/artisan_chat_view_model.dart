import 'package:build_mate/presentation/state/chat_state.dart';
import 'package:build_mate/data/services/chat_service.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ArtisanChatViewModel extends StateNotifier<ChatState> {
  final ChatService _chatService;

  ArtisanChatViewModel(super.state, this._chatService) {
    // subscribeToChannel();
    listenToGameChanges(8);
  }

  void sendMessage(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getConverstations() async {
    try {
      if (kDebugMode) {
        print('Starting to fetch artisan conversations');
      }

      // Get the current user's Supabase ID
      final supabase = Supabase.instance.client;
      final supabaseId = supabase.auth.currentUser?.id;

      if (kDebugMode) {
        print('Current Supabase user ID: $supabaseId');
      }

      if (supabaseId == null) {
        if (kDebugMode) {
          print('No authenticated user found');
        }
        return;
      }

      getConverstations();

      // Get the artisan ID from the database using the Supabase ID
      try {
        final artisanResponse =
            await supabase
                .from('artisans')
                .select('id')
                .eq('supabase_id', supabaseId)
                .maybeSingle();

        if (kDebugMode) {
          print('Artisan response: $artisanResponse');
        }

        if (artisanResponse == null) {
          if (kDebugMode) {
            print('No artisan found for Supabase ID: $supabaseId');
          }
          return;
        }

        final artisanId = artisanResponse['id'];
        if (kDebugMode) {
          print('Fetching conversations for artisan ID: $artisanId');
        }

        // Get conversations for this artisan
        try {
          final conversations = await _chatService.getConversations(
            false,
            artisanId,
          );

          if (kDebugMode) {
            print(
              'Found ${conversations.length} conversations for artisan ID: $artisanId',
            );
            for (final conversation in conversations) {
              print(
                'Conversation ID: ${conversation.id}, Client: ${conversation.client?.name}',
              );
            }
          }

          state = state.copyWith(conversations: AsyncValue.data(conversations));
        } catch (e) {
          if (kDebugMode) {
            print('Error in chatService.getConversations: $e');
          }
          rethrow;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error querying artisans table: $e');
        }
        rethrow;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Top-level error in artisanConversationsProvider: $e');
      }
      rethrow;
    }
  }

  // void _handleInsert(Map<String, dynamic> payload) {
  //   final message = payload['new'] as Map<String, dynamic>;
  //   state = state.copyWith(
  //     messages: [...state.messages, message],
  //   );
  // }

  // void _handleUpdate(Map<String, dynamic> payload) {
  //   final message = payload['new'] as Map<String, dynamic>;
  //   state = state.copyWith(
  //     messages: state.messages.map((m) {
  //       if (m['id'] == message['id']) {
  //         return message;
  //       }
  //       return m;
  //     }).toList(),
  //   );
  // }

  // void _handleDelete(Map<String, dynamic> payload) {
  //   final messageId = payload['old']['id'] as int;
  //   state = state.copyWith(
  //     messages: state.messages.where((m) => m['id'] != messageId).toList(),
  //   );
  // }

  RealtimeChannel? _channel;
  Future<void> listenToGameChanges(int conversationId) async {
    try {
      final supabase = Supabase.instance.client;
      await supabase.auth.refreshSession();

      // Remove existing subscription if any
      await _channel?.unsubscribe();

      // Create private channel - equivalent to { config: { private: true } }
      final channelName = 'conversation:$conversationId';
      _channel = supabase.channel(
        channelName,
        opts: const RealtimeChannelConfig(
          private: true, // This makes it a private channel
        ),
      );

      // Listen to INSERT events
      _channel!
          .onBroadcast(
            event: 'INSERT',
            callback: (payload) {
              debugPrint('INSERT event received: $payload');
              _handleInsert(payload);
            },
          )
          .onBroadcast(
            event: 'UPDATE',
            callback: (payload) {
              debugPrint('UPDATE event received: $payload');
              _handleUpdate(payload);
            },
          )
          .onBroadcast(
            event: 'DELETE',
            callback: (payload) {
              debugPrint('DELETE event received: $payload');
              _handleDelete(payload);
            },
          );
      // Subscribe to the channel
      _channel!.subscribe((status, error) {
        switch (status) {
          case RealtimeSubscribeStatus.subscribed:
            debugPrint('Successfully subscribed to $channelName');
            break;
          case RealtimeSubscribeStatus.timedOut:
            debugPrint('Subscription timed out for $channelName');
            break;
          case RealtimeSubscribeStatus.channelError:
            debugPrint('Channel error for $channelName: ${error?.toString()}');
            break;
          case RealtimeSubscribeStatus.closed:
            debugPrint('Channel closed for $channelName');
            break;
        }
      });
    } catch (e) {
      debugPrint('Error setting up broadcast listener: $e');
      rethrow;
    }
  }

  void _handleInsert(Map<String, dynamic> payload) {
    // Handle INSERT event
  }

  void _handleUpdate(Map<String, dynamic> payload) {
    // Handle UPDATE event
    debugPrint('UPDATED!!!');
  }

  void _handleDelete(Map<String, dynamic> payload) {
    // Handle DELETE event
  }
}

final chatViewModelProvider =
    StateNotifierProvider<ArtisanChatViewModel, ChatState>((ref) {
      return ArtisanChatViewModel(
        ChatState(conversations: AsyncValue.data([])),
        ChatService(),
      );
    });
