import 'dart:io';

import 'package:build_mate/data/dto/artisan_rating_response.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:build_mate/presentation/state/artisan_portfolio_flow_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

//Return portfolio Provider
final artisanPortfolioViewModelProvider =
    StateNotifierProvider<ArtisanPortfolioViewModel, ArtisanPortfolioFlowState>(
      (ref) {
        return ArtisanPortfolioViewModel(ArtisanPortfolioFlowState());
      },
    );

class ArtisanPortfolioViewModel
    extends StateNotifier<ArtisanPortfolioFlowState> {
  ArtisanPortfolioViewModel(super.state) {
    getCurrentUserId().then((_) {
      getArtisanProfileData();
      fetchArtisanRatings();
    });
  }

  // Add upload progress tracking
  double artisanImageUploadProgress = 0.0;
  bool isUploadingArtisanImage = false;

  // Add deletion tracking
  Set<int> deletingImageIds = {};

  // Add this property to track loading state for ratings
  bool isLoadingRatings = false;

  // Add upload progress tracking for cover image
  double coverImageUploadProgress = 0.0;
  bool isUploadingCoverImage = false;
  File? coverImageFile;

  // Add upload progress tracking for avatar image
  double avatarImageUploadProgress = 0.0;
  bool isUploadingAvatarImage = false;
  File? avatarImageFile;

  final supabase = Supabase.instance.client;

  Future<void> getCurrentUserId() {
    final userId = supabase.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }
    //get id from artisans table
    return supabase
        .from('artisans')
        .select('id')
        .eq('supabase_id', userId)
        .single()
        .then((value) {
          state = state.copyWith(currentUserId: value['id']);
        });
  }

  Future<void> getArtisanProfileData() async {
    //Get current user

    try {
      state = state.copyWith(isLoading: true);
      await supabase
          .from('artisans')
          .select('''
            id, name, email, avatar, address, supabase_id, 
            whatsapp_number, is_company, cover_photo, about,
            artisan_locations (
              location, address
            ),
            artisan_numbers (
              phonenumber, second_phonenumber
            ),
            specializations (
              service_id,services(id, name),
              specialization_tags (
                sub_category_id,
                sub_categories (id, name)
              )
            ),
            artisan_images (
              id, image_url
            )
          ''')
          .eq('id', state.currentUserId)
          .single()
          .then((data) {
            final artisanProfileData = ArtisanProfileDataResponse.fromJson(
              data,
            );
            if (kDebugMode) {
              print('ARTISAN_PROFILE_DATA RESPONSE: ${data.toString()}');
              print('COVER_IMAGE: ${artisanProfileData.coverPhoto}');
            }
            state = state.copyWith(
              artisanProfileData: artisanProfileData,
              isLoading: false,
              artisanImages: artisanProfileData.artisanImages ?? [],
            );
          });
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching artisan with relationships: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<String> uploadArtianImage(
    File imageFile,
    Function(double) onProgress,
  ) async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-artisan-work-$timestamp.$fileExtension';
      final filePath = 'artisan-work-images/$fileName';

      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress for demo purposes
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      // Get artisan_id from artisans table
      

      // Save image URL to artisan_images table
      await supabase.from('artisan_images').insert({
        'artisan_id': state.currentUserId,
        'image_url': imageUrl,
      });

      // Refresh artisan profile data to get updated images
      await supabase
          .from('artisan_images')
          .select()
          .eq('artisan_id', state.currentUserId)
          .then((value) {
            //map to Artisan list of images
            final artisanImages =
                value.map((image) => ArtisanImage.fromJson(image)).toList();
            state = state.copyWith(artisanImages: artisanImages);
          });

      if (kDebugMode) {
        print('Artisan image uploaded successfully: $imageUrl');
      }

      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading artisan image: $e');
      }
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }
      rethrow;
    }
  }

  Future<void> pickAndUploadProjectImage(BuildContext context) async {
    try {
      // Set uploading state
      isUploadingArtisanImage = true;
      artisanImageUploadProgress = 0.0;
      // Notify listeners that state has changed
      state = state.copyWith();

      // Request permission
      final status = await Permission.photos.request();

      if (status.isGranted) {
        final ImagePicker picker = ImagePicker();
        final XFile? pickedFile = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          final File imageFile = File(pickedFile.path);

          // Upload the image and get URL
          await uploadArtianImage(imageFile, (progress) {
            artisanImageUploadProgress = progress;
            // Notify listeners that progress has changed
            state = state.copyWith();
          });

          // Success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Image uploaded successfully')),
            );
          }
        }
      } else {
        if (kDebugMode) {
          print('Permission not granted: $status');
        }
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Gallery permission not granted')),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking and uploading image: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to upload image: ${e.toString()}')),
        );
      }
    } finally {
      // Reset uploading state
      isUploadingArtisanImage = false;
      artisanImageUploadProgress = 0.0;
      // Notify listeners that state has changed
      state = state.copyWith();
    }
  }

  Future<void> deleteArtisanImage(int imageId, String imageUrl) async {
    try {
      // Add to deleting set
      deletingImageIds.add(imageId);
      // Notify listeners that state has changed
      state = state.copyWith();

      // Delete from database first
      await supabase.from('artisan_images').delete().eq('id', imageId);

      // Try to delete from storage if possible
      try {
        // Extract the file path from the URL
        final String bucketName = "avatars";
        String filePath = imageUrl.split('/public/$bucketName/')[1];

        // Delete from storage
        await supabase.storage.from(bucketName).remove([
          filePath,
        ]); // Takes a list of file paths

        if (kDebugMode) {
          print('Image deleted successfully from storage: $filePath');
        }
      } catch (storageError) {
        // If storage deletion fails, just log it but continue
        if (kDebugMode) {
          print('Error deleting file from storage: $storageError');
        }
      }

      // Update the state by removing the deleted image
      final updatedImages =
          state.artisanImages.where((image) => image.id != imageId).toList();

      state = state.copyWith(artisanImages: updatedImages);

      if (kDebugMode) {
        print('Successfully deleted image with ID: $imageId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting artisan image: $e');
      }
      rethrow;
    } finally {
      // Remove from deleting set regardless of success/failure
      deletingImageIds.remove(imageId);
      // Notify listeners that state has changed
      state = state.copyWith();
    }
  }

  // Helper method to check if an image is being deleted
  bool isImageDeleting(int imageId) {
    return deletingImageIds.contains(imageId);
  }

  Future<void> fetchArtisanRatings() async {
    try {
      isLoadingRatings = true;
      state = state.copyWith();

      if (state.currentUserId == 0) {
        if (kDebugMode) {
          print('Cannot fetch ratings: currentUserId is 0');
        }
        return;
      }

      // Fetch ratings with related client data
      final response = await supabase
          .from('artisan_ratings')
          .select('''
            id, 
            rating, 
            comments, 
            created_at,
            client:client_id (
              id, 
              name, 
              avatar
            )
          ''')
          .eq('artisan_id', state.currentUserId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('Fetched ${response.length} ratings');
      }

      // Process the response
      final ratings = List<Map<String, dynamic>>.from(response);

      //User ArtisanRatingResponse to passs json to ratingsList
      final ratingsList =
          ratings.map((rating) {
            return ArtisanRatingResponse.fromJson(rating);
          }).toList();

      state = state.copyWith(ratingsList: ratingsList);
      if (kDebugMode) {
        print('RATING STATE DATA: ${state.ratingsList.toString()}');
      }

      // Calculate average rating
      double totalRating = 0;
      if (ratings.isNotEmpty) {
        for (var rating in ratings) {
          totalRating += rating['rating'] as int? ?? 0;
        }

        final averageRating = totalRating / ratings.length;

        // Update state with ratings data
        state = state.copyWith(
          averageRating: averageRating,
          totalReviews: ratings.length,
          ratings: ratings,
        );
      } else {
        // No ratings found
        state = state.copyWith(
          averageRating: 0.0,
          totalReviews: 0,
          ratings: [],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching artisan ratings: $e');
      }
    } finally {
      isLoadingRatings = false;
      state = state.copyWith();
    }
  }

  // Helper method to format the date for display
  String formatRatingDate(String dateString) {
    final date = DateTime.parse(dateString);
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      if (difference.inHours < 1) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }

  // Save profile changes
  Future<bool> saveProfile({
    required String name,
    required String about,
    required String location,
  }) async {
    try {
      state = state.copyWith(isSavingProfile: true);

      // Get the current user ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get the artisan ID from state
      final artisanId = state.currentUserId;
      if (artisanId == 0) {
        throw Exception('Artisan ID not found');
      }

      if (kDebugMode) {
        print('Saving profile for artisan ID: $artisanId');
        print('Name: $name');
        print('About: $about');
        print('Location: $location');
      }

      // Update the artisan record in the database
      await supabase
          .from('artisans')
          .update({'name': name, 'about': about, 'address': location})
          .eq('id', artisanId);

      // Update the state with the new values
      if (state.artisanProfileData != null) {
        final updatedProfileData = state.artisanProfileData!.copyWith(
          name: name,
          about: about,
          address: location,
        );

        state = state.copyWith(
          artisanProfileData: updatedProfileData,
          isSavingProfile: false,
        );
      }

      if (kDebugMode) {
        print('Profile updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving profile: $e');
      }
      state = state.copyWith(isSavingProfile: false);
      return false;
    }
  }

  Future<void> selectAndUploadCoverImage(BuildContext context) async {
    try {
      // Request permission
      final status = await Permission.photos.request();

      if (status.isGranted) {
        // Set uploading state
        isUploadingCoverImage = true;
        coverImageUploadProgress = 0.0;
        // Notify listeners that state has changed
        state = state.copyWith();

        final ImagePicker picker = ImagePicker();
        final XFile? pickedFile = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          coverImageFile = File(pickedFile.path);

          // Upload the image and get URL
          final imageUrl = await uploadCoverImage(coverImageFile!, (progress) {
            coverImageUploadProgress = progress;
            // Notify listeners that progress has changed
            state = state.copyWith();
          });

          // Get the current cover photo URL to delete it later
          final currentCoverPhotoUrl = state.artisanProfileData?.coverPhoto;

          // Update artisan record with new cover photo URL
          await supabase
              .from('artisans')
              .update({'cover_photo': imageUrl})
              .eq('id', state.currentUserId);

          // Update state with new cover photo URL
          if (state.artisanProfileData != null) {
            final updatedProfileData = state.artisanProfileData!.copyWith(
              coverPhoto: imageUrl,
            );

            state = state.copyWith(artisanProfileData: updatedProfileData);
          }

          // Try to delete the old image if it exists
          if (currentCoverPhotoUrl != null && currentCoverPhotoUrl.isNotEmpty) {
            try {
              // Extract the file path from the URL
              final String bucketName = "avatars";
              if (currentCoverPhotoUrl.contains('/public/$bucketName/')) {
                String filePath = currentCoverPhotoUrl.split('/public/$bucketName/')[1];
                
                // Delete from storage
                await supabase.storage.from(bucketName).remove([filePath]);
                
                if (kDebugMode) {
                  print('Old cover image deleted successfully: $filePath');
                }
              }
            } catch (deleteError) {
              // If deletion fails, just log it but continue
              if (kDebugMode) {
                print('Error deleting old cover image: $deleteError');
              }
            }
          }

          // Success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Cover image updated successfully')),
            );
          }
        }
      } else {
        if (kDebugMode) {
          print('Permission not granted: $status');
        }
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Gallery permission not granted')),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking and uploading cover image: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload cover image: ${e.toString()}'),
          ),
        );
      }
    } finally {
      // Reset uploading state
      isUploadingCoverImage = false;
      coverImageUploadProgress = 0.0;
      // Notify listeners that state has changed
      state = state.copyWith();
    }
  }

  Future<String> uploadCoverImage(
    File imageFile,
    Function(double) onProgress,
  ) async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-cover-$timestamp.$fileExtension';
      final filePath = 'cover/$fileName';

      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );
          

      // Simulate progress for demo purposes
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      if (kDebugMode) {
        print('Cover image uploaded successfully: $imageUrl');
      }

      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading cover image: $e');
      }
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }
      rethrow;
    }
  }

  Future<void> selectAndUploadAvatarImage(BuildContext context) async {
    try {
      // Request permission
      final status = await Permission.photos.request();

      if (status.isGranted) {
        // Set uploading state
        isUploadingAvatarImage = true;
        avatarImageUploadProgress = 0.0;
        // Notify listeners that state has changed
        state = state.copyWith();

        final ImagePicker picker = ImagePicker();
        final XFile? pickedFile = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          avatarImageFile = File(pickedFile.path);

          // Upload the image and get URL
          final imageUrl = await uploadAvatarImage(avatarImageFile!, (
            progress,
          ) {
            avatarImageUploadProgress = progress;
            // Notify listeners that progress has changed
            state = state.copyWith();
          });

          // Get the current avatar URL to delete it later
          final currentAvatarUrl = state.artisanProfileData?.avatar;

          // Update artisan record with new avatar URL
          await supabase
              .from('artisans')
              .update({'avatar': imageUrl})
              .eq('id', state.currentUserId);

          // Update state with new avatar URL
          if (state.artisanProfileData != null) {
            final updatedProfileData = state.artisanProfileData!.copyWith(
              avatar: imageUrl,
            );

            state = state.copyWith(artisanProfileData: updatedProfileData);
          }

          // Try to delete the old image if it exists
          if (currentAvatarUrl != null && currentAvatarUrl.isNotEmpty) {
            try {
              // Extract the file path from the URL
              final String bucketName = "avatars";
              if (currentAvatarUrl.contains('/public/$bucketName/')) {
                String filePath = currentAvatarUrl.split('/public/$bucketName/')[1];
                
                // Delete from storage
                await supabase.storage.from(bucketName).remove([filePath]);
                
                if (kDebugMode) {
                  print('Old avatar image deleted successfully: $filePath');
                }
              }
            } catch (deleteError) {
              // If deletion fails, just log it but continue
              if (kDebugMode) {
                print('Error deleting old avatar image: $deleteError');
              }
            }
          }

          // Success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile image updated successfully'),
              ),
            );
          }
        }
      } else {
        if (kDebugMode) {
          print('Permission not granted: $status');
        }
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Gallery permission not granted')),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking and uploading avatar image: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload profile image: ${e.toString()}'),
          ),
        );
      }
    } finally {
      // Reset uploading state
      isUploadingAvatarImage = false;
      avatarImageUploadProgress = 0.0;
      // Notify listeners that state has changed
      state = state.copyWith();
    }
  }


  Future<String> uploadAvatarImage(
    File imageFile,
    Function(double) onProgress,
  ) async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-avatar-$timestamp.$fileExtension';
      final filePath = 'avatars-profile/$fileName';

      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress for demo purposes
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      if (kDebugMode) {
        print('Avatar image uploaded successfully: $imageUrl');
      }

      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading avatar image: $e');
      }
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }
      rethrow;
    }
  }
}
