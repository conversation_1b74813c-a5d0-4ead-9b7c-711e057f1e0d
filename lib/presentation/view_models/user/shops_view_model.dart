import 'dart:async';
import 'dart:ui' as ui;
import 'package:build_mate/utils/custom_markers.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/data/dto/branch_distance_response.dart';
import 'package:image/image.dart' as img;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:build_mate/data/models/client_model.dart';
import 'package:build_mate/presentation/state/shops_state.dart';

final shopsViewModelProvider =
    StateNotifierProvider<ShopsViewModel, ShopsState>(
      (ref) => ShopsViewModel(ShopsState()),
    );

class ShopsViewModel extends StateNotifier<ShopsState> {
  ShopsViewModel(super.state) {
    getCurrentLocation();
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void setSearchingState(bool isSearching) {
    state = state.copyWith(isSearchingProduct: isSearching);
  }

  Future<void> updateSearchQueryString(String query) async {
    state = state.copyWith(searchQuery: query);
    if (state.searchQuery.isEmpty) return;
    await searchProducts(
      latitude: state.currentLocation.latitude,
      longitude: state.currentLocation.longitude,
      searchQuery: query,
    );
  }

  void setCameraPosition(double latitude, double longitude, double zoom) {
    final CameraPosition initialPosition = CameraPosition(
      target: LatLng(latitude, longitude),
      zoom: 15.0,
    );

    state = state.copyWith(initialPosition: initialPosition);
  }

  void reset() {
    state = ShopsState();
  }

  void setShowdetailsVisibility(bool isVisible) {
    state = state.copyWith(ishopDetailsSheetVisible: isVisible);
  }

  void setRadius(double radius) {
    state = state.copyWith(radius: radius);
  }

  void setAdjustingRadiusState(bool isAdjust) {
    state = state.copyWith(isAdjusting: isAdjust);
  }

  Future<void> getNearbyShops({
    required double latitude,
    required double longitude,
    double distance = 10,
  }) async {
    try {
      setLoading(true);
      final response = await Supabase.instance.client.functions.invoke(
        'get-branches-by-distance',
        body: {
          'latitude': latitude,
          'longitude': longitude,
          'distance': distance,
        },
      );

      final branchResponse = BranchDistanceResponse.fromJson(response.data);
      if (kDebugMode) {
        print('Shops Response: ${branchResponse.toJson()}');
      }

      if (branchResponse.success == true) {
        final shops = branchResponse.data ?? [];
        // Debug prints for shop images
        for (var shop in shops) {
          debugPrint('Shop ${shop.id} images: ${shop.hardwareShop?.images}');
        }
        state = state.copyWith(nearbyShops: shops, error: null);
        state = state.copyWith(branchCount: branchResponse.data?.length ?? 0);

        if (shops.isEmpty) {
          // Keep only user marker when no shops found
          state = state.copyWith(
            markers: state.userMarker != null ? {state.userMarker!} : {},
          );
        } else {
          await loadShops();
        }
      } else {
        throw Exception('Failed to get nearby shops');
      }
    } catch (e) {
      debugPrint('Error fetching nearby shops: $e');
      state = state.copyWith(
        error: e.toString(),
        nearbyShops: [],
        markers: state.userMarker != null ? {state.userMarker!} : {},
      );
    } finally {
      setLoading(false);
    }
  }

  Future<void> searchProducts({
    required double latitude,
    required double longitude,
    required String searchQuery,
  }) async {
    try {
      setSearchingState(true);
      final response = await Supabase.instance.client.functions.invoke(
        'search_product_in_branches',
        body: {
          'productName': searchQuery,
          'latitude': latitude,
          'longitude': longitude,
        },
      );

      final branchResponse = BranchDistanceResponse.fromJson(response.data);
      if (kDebugMode) {
        print('Search Shops Response: ${branchResponse.toJson()}');
      }

      if (branchResponse.success == true) {
        setSearchingState(false);
        final shops = branchResponse.data ?? [];
        // Debug prints for shop images
        for (var shop in shops) {
          debugPrint('Shop ${shop.id} images: ${shop.hardwareShop?.images}');
        }
        state = state.copyWith(nearbyShops: shops, error: null);
        state = state.copyWith(branchCount: branchResponse.data?.length ?? 0);

        if (shops.isEmpty) {
          // Keep only user marker when no shops found
          setSearchingState(false);
          state = state.copyWith(
            markers: state.userMarker != null ? {state.userMarker!} : {},
          );
        } else {
          setSearchingState(false);
          await loadShops();
        }
      } else {
        setSearchingState(false);
        throw Exception('Failed to get nearby shops');
      }
    } catch (e) {
      setSearchingState(false);
      debugPrint('Error fetching nearby shops: $e');
      state = state.copyWith(
        error: e.toString(),
        nearbyShops: [],
        markers: state.userMarker != null ? {state.userMarker!} : {},
      );
    } finally {
      setLoading(false);
    }
  }

  Future<void> getAllNearbyShops({
    required double latitude,
    required double longitude,
    double distance = 10,
  }) async {
    try {
      setLoading(true);
      final response = await Supabase.instance.client.functions.invoke(
        'get-branches-by-distance',
        body: {
          'latitude': latitude,
          'longitude': longitude,
          'distance': distance,
        },
      );

      final branchResponse = BranchDistanceResponse.fromJson(response.data);
      if (kDebugMode) {
        print('ALL_SHOPS_RESPONSE: $branchResponse');
      }

      if (branchResponse.success == true) {
        final shops = branchResponse.data ?? [];
        state = state.copyWith(nearbyShops: shops, error: null);

        if (shops.isEmpty) {
          // Keep only user marker when no shops found
          state = state.copyWith(
            markers: state.userMarker != null ? {state.userMarker!} : {},
          );
        } else {
          await loadShops();
        }
      } else {
        throw Exception('Failed to get nearby shops');
      }
    } catch (e) {
      debugPrint('Error fetching nearby shops: $e');
      state = state.copyWith(
        error: e.toString(),
        nearbyShops: [],
        markers: state.userMarker != null ? {state.userMarker!} : {},
      );
    } finally {
      setLoading(false);
    }
  }

  Future<void> loadShops() async {
    debugPrint('Loading shops for markers. Count: ${state.nearbyShops.length}');
    if (state.nearbyShops.isEmpty) return;

    state = state.copyWith(isLoading: true);
    try {
      await _createMarkersForShops();
    } catch (e) {
      debugPrint('Error loading shop markers: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _createMarkersForShops() async {
    final Set<Marker> newMarkers = {};

    // Preserve user marker if it exists
    if (state.userMarker != null) {
      newMarkers.add(state.userMarker!);
    }

    for (final shop in state.nearbyShops) {
      try {
        debugPrint('Creating marker for shop: ${shop.id}');
        final marker = await _createMarkerForShop(shop);
        if (marker != null) {
          newMarkers.add(marker);
          debugPrint('Added marker for shop: ${shop.id}');
        }
      } catch (e) {
        debugPrint('Error creating marker for shop ${shop.id}: $e');
      }
    }

    debugPrint('Created ${newMarkers.length} markers');
    state = state.copyWith(markers: newMarkers);
  }

  Future<Marker?> _createMarkerForShop(BranchDistance shop) async {
    final coords = shop.pointCoordinates
        ?.replaceAll('(', '')
        .replaceAll(')', '')
        .split(',');

    if (coords == null || coords.length != 2) return null;

    final lat = double.tryParse(coords[1].trim());
    final lng = double.tryParse(coords[0].trim());

    if (lat == null || lng == null) return null;

    BitmapDescriptor icon;
    // icon = await _createShopCustomMarkerIconWithOptions(width: 40, height: 40);
    icon = await getCustomMarker();

    return Marker(
      markerId: MarkerId(shop.id.toString()),
      position: LatLng(lat, lng),
      icon: icon,
      infoWindow: InfoWindow(
        title: shop.name ?? 'Shop',
        snippet: shop.hardwareShop?.name,
      ),
      onTap: () {
        selectShop(shop);
        state = state.copyWith(selectedMarkerId: shop.id.toString());
        setShowdetailsVisibility(true);
      },
    );
  }

  void selectShop(BranchDistance? shop) {
    state = state.copyWith(
      selectedShop: shop,
      selectedMarkerId: shop?.id.toString(),
      selectedBranchId: shop?.id ?? 0,
      selectedShopname: shop?.hardwareShop?.name ?? '',
      selectedBranchName: shop?.name ?? '',
    );
  }

  // Future<BitmapDescriptor> _createCustomMarkerIcon(
  //   String imageUrl, {
  //   bool isUser = false,
  // }) async {
  //   final Size size = const Size(40, 40); // Reduced from 100,100
  //   final pictureRecorder = ui.PictureRecorder();
  //   final canvas = Canvas(pictureRecorder);
  //   final center = Offset(size.width / 2, size.height / 2);

  //   try {
  //     // Draw ripple effects first
  //     final rippleColor = isUser ? Colors.blue : Colors.orange;
  //     for (int i = 0; i < 3; i++) {
  //       final ripplePaint =
  //           Paint()
  //             ..color = rippleColor.withAlpha((0.15 - (i * 0.05) * 255).round())
  //             ..style = PaintingStyle.stroke
  //             ..strokeWidth = 2;
  //       canvas.drawCircle(center, (size.width / 2) + (i * 6), ripplePaint);
  //     }

  //     // Draw white background circle
  //     final bgPaint =
  //         Paint()
  //           ..color = Colors.white
  //           ..style = PaintingStyle.fill;
  //     canvas.drawCircle(center, size.width / 2, bgPaint);

  //     // Draw image with circle clip
  //     final completer = Completer<void>();
  //     final imageProvider = NetworkImage(imageUrl);
  //     final imageStream = imageProvider.resolve(ImageConfiguration.empty);

  //     late ImageStreamListener listener;
  //     listener = ImageStreamListener(
  //       (ImageInfo info, bool _) {
  //         try {
  //           final image = info.image;
  //           final imageSize = Size(
  //             image.width.toDouble(),
  //             image.height.toDouble(),
  //           );

  //           // Create circular clip path
  //           final clipPath =
  //               Path()..addOval(
  //                 Rect.fromCircle(
  //                   center: center,
  //                   radius: size.width / 2 - 4, // Slightly smaller for border
  //                 ),
  //               );

  //           canvas.save();
  //           canvas.clipPath(clipPath);

  //           // Calculate image drawing rect maintaining aspect ratio
  //           final scale =
  //               size.width / math.min(imageSize.width, imageSize.height);
  //           final w = imageSize.width * scale;
  //           final h = imageSize.height * scale;
  //           final src = Rect.fromLTWH(0, 0, imageSize.width, imageSize.height);
  //           final dst = Rect.fromCenter(center: center, width: w, height: h);

  //           canvas.drawImageRect(image, src, dst, Paint());
  //           canvas.restore();

  //           // Draw stronger border last
  //           final borderPaint =
  //               Paint()
  //                 ..color = isUser ? Colors.blue : Colors.orange
  //                 ..style = PaintingStyle.stroke
  //                 ..strokeWidth = 3;
  //           canvas.drawCircle(center, size.width / 2 - 1, borderPaint);

  //           completer.complete();
  //         } catch (e) {
  //           completer.completeError(e);
  //         } finally {
  //           imageStream.removeListener(listener);
  //         }
  //       },
  //       onError: (error, stackTrace) {
  //         completer.completeError(error);
  //         imageStream.removeListener(listener);
  //       },
  //     );

  //     imageStream.addListener(listener);
  //     await completer.future;

  //     final picture = pictureRecorder.endRecording();
  //     final image = await picture.toImage(
  //       size.width.toInt(),
  //       size.height.toInt(),
  //     );
  //     final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

  //     if (byteData == null) throw Exception('Failed to generate marker icon');

  //     return BitmapDescriptor.bytes(byteData.buffer.asUint8List());
  //   } catch (e) {
  //     debugPrint('Error creating marker icon: $e');
  //     return BitmapDescriptor.defaultMarkerWithHue(
  //       isUser ? BitmapDescriptor.hueBlue : BitmapDescriptor.hueOrange,
  //     );
  //   }
  // }

  // Future<BitmapDescriptor> _createCustomMarkerIcon(
  //   String imageUrl, {
  //   bool isUser = false,
  // }) async {
  //   // Increased size for better quality
  //   final Size size = const Size(40, 40);
  //   final pictureRecorder = ui.PictureRecorder();
  //   final canvas = Canvas(pictureRecorder);
  //   final center = Offset(size.width / 2, size.height / 2);

  //   try {
  //     // Draw ripple effects first
  //     final rippleColor = isUser ? Colors.blue : Colors.orange;
  //     for (int i = 0; i < 3; i++) {
  //       final ripplePaint =
  //           Paint()
  //             ..color = rippleColor.withAlpha((0.15 - (i * 0.05) * 255).round())
  //             ..style = PaintingStyle.stroke
  //             ..strokeWidth = 2;
  //       canvas.drawCircle(center, (size.width / 2) + (i * 6), ripplePaint);
  //     }

  //     // Draw white background circle
  //     final bgPaint =
  //         Paint()
  //           ..color = Colors.white
  //           ..style = PaintingStyle.fill;
  //     canvas.drawCircle(center, size.width / 2, bgPaint);

  //     // Draw image with circle clip
  //     final completer = Completer<void>();
  //     final imageProvider = NetworkImage(imageUrl);
  //     final imageStream = imageProvider.resolve(ImageConfiguration.empty);

  //     late ImageStreamListener listener;
  //     listener = ImageStreamListener(
  //       (ImageInfo info, bool _) {
  //         try {
  //           final image = info.image;
  //           final imageSize = Size(
  //             image.width.toDouble(),
  //             image.height.toDouble(),
  //           );

  //           // Create circular clip path
  //           final clipPath =
  //               Path()..addOval(
  //                 Rect.fromCircle(
  //                   center: center,
  //                   radius: size.width / 2 - 4, // Slightly smaller for border
  //                 ),
  //               );

  //           canvas.save();
  //           canvas.clipPath(clipPath);

  //           // Calculate image drawing rect maintaining aspect ratio
  //           final scale =
  //               size.width / math.min(imageSize.width, imageSize.height);
  //           final w = imageSize.width * scale;
  //           final h = imageSize.height * scale;
  //           final src = Rect.fromLTWH(0, 0, imageSize.width, imageSize.height);
  //           final dst = Rect.fromCenter(center: center, width: w, height: h);

  //           // Use high quality filtering
  //           final paint = Paint()..filterQuality = FilterQuality.high;
  //           canvas.drawImageRect(image, src, dst, paint);
  //           canvas.restore();

  //           // Draw stronger border last
  //           final borderPaint =
  //               Paint()
  //                 ..color = isUser ? Colors.blue : Colors.orange
  //                 ..style = PaintingStyle.stroke
  //                 ..strokeWidth = 3;
  //           canvas.drawCircle(center, size.width / 2 - 1, borderPaint);

  //           completer.complete();
  //         } catch (e) {
  //           completer.completeError(e);
  //         } finally {
  //           imageStream.removeListener(listener);
  //         }
  //       },
  //       onError: (error, stackTrace) {
  //         completer.completeError(error);
  //         imageStream.removeListener(listener);
  //       },
  //     );

  //     imageStream.addListener(listener);
  //     await completer.future;

  //     final picture = pictureRecorder.endRecording();
  //     final image = await picture.toImage(
  //       size.width.toInt(),
  //       size.height.toInt(),
  //     );
  //     final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

  //     if (byteData == null) throw Exception('Failed to generate marker icon');

  //     return BitmapDescriptor.bytes(byteData.buffer.asUint8List());
  //   } catch (e) {
  //     debugPrint('Error creating marker icon: $e');
  //     return BitmapDescriptor.defaultMarkerWithHue(
  //       isUser ? BitmapDescriptor.hueBlue : BitmapDescriptor.hueOrange,
  //     );
  //   }
  // }

  Future<BitmapDescriptor> setSimpleCustomMapPin() async {
    try {
      return await _createShopCustomMarkerIconWithOptions(
        assetPath: 'assets/svg/hardware_shop.svg',
        width: 40, // Optimized size for good visibility on the map
        height: 40, // Square proportion for consistent appearance
      );
    } catch (e) {
      debugPrint('Error creating custom pin: $e');
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    }
  }

  Future<BitmapDescriptor> getCustomMarker() async {
    return await BitmapDescriptor.asset(
      const ImageConfiguration(size: Size(48, 48)), // size can be adjusted
      'assets/images/store_marker.png',
    );
  }

  Future<BitmapDescriptor> getCurrentUserMarker() async {
    return await BitmapDescriptor.asset(
      const ImageConfiguration(size: Size(48, 48)), // size can be adjusted
      'assets/images/store_marker.png',
    );
  }

  Future<BitmapDescriptor> getCircularMarkerFromUrl(String url) async {
    try {
      // 1. Download image using Dio
      final response = await Dio().get<List<int>>(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      // 2. Decode image
      final original = img.decodeImage(Uint8List.fromList(response.data!));
      if (original == null) throw Exception('Failed to decode image');

      // 3. Resize image to desired size (e.g., 100x100)
      final size = 100;
      final resized = img.copyResizeCropSquare(original, size: size);

      // 4. Create circular mask
      final circle = img.Image(width: size, height: size);
      final center = size ~/ 2;
      final radius = size ~/ 2;

      for (int y = 0; y < size; y++) {
        for (int x = 0; x < size; x++) {
          final dx = x - center;
          final dy = y - center;
          if (dx * dx + dy * dy <= radius * radius) {
            circle.setPixel(x, y, resized.getPixel(x, y));
          } else {
            circle.setPixel(x, y, img.ColorFloat16.rgb(0, 0, 0)); // transparent
          }
        }
      }

      // 5. Add orange border
      final borderColor = img.ColorFloat16.rgb(255, 165, 0); // Orange
      const borderWidth = 4;

      for (int y = 0; y < size; y++) {
        for (int x = 0; x < size; x++) {
          final dx = x - center;
          final dy = y - center;
          final distSq = dx * dx + dy * dy;

          if (distSq >= (radius - borderWidth) * (radius - borderWidth) &&
              distSq <= radius * radius) {
            circle.setPixel(x, y, borderColor);
          }
        }
      }

      // 6. Encode to PNG
      final pngBytes = Uint8List.fromList(img.encodePng(circle));

      // 7. Convert to BitmapDescriptor
      return BitmapDescriptor.bytes(pngBytes);
    } catch (e) {
      debugPrint('Error generating circular marker: $e');
      rethrow;
    }
  }

  Future<BitmapDescriptor> _createShopCustomMarkerIconWithOptions({
    String assetPath = 'assets/images/store_marker.png',
    double? width,
    double? height,
    Color? tintColor,
  }) async {
    try {
      // Load PNG from assets
      final ByteData byteData = await rootBundle.load(assetPath);
      final Uint8List uint8List = byteData.buffer.asUint8List();

      // If no resizing or tinting needed, return directly
      if (width == null && height == null && tintColor == null) {
        return BitmapDescriptor.bytes(uint8List);
      }

      // Decode the image for processing
      final ui.Codec codec = await ui.instantiateImageCodec(uint8List);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      // Use original dimensions if not specified
      final double targetWidth = width ?? originalImage.width.toDouble();
      final double targetHeight = height ?? originalImage.height.toDouble();

      // Create a new canvas for processing
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Apply tint color if provided
      Paint? paint;
      if (tintColor != null) {
        paint =
            Paint()..colorFilter = ColorFilter.mode(tintColor, BlendMode.srcIn);
      }

      // Draw the resized image
      canvas.drawImageRect(
        originalImage,
        Rect.fromLTWH(
          0,
          0,
          originalImage.width.toDouble(),
          originalImage.height.toDouble(),
        ),
        Rect.fromLTWH(0, 0, targetWidth, targetHeight),
        paint ?? Paint(),
      );

      // Convert back to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image processedImage = await picture.toImage(
        targetWidth.toInt(),
        targetHeight.toInt(),
      );

      // Convert to bytes
      final ByteData? processedByteData = await processedImage.toByteData(
        format: ui.ImageByteFormat.png,
      );
      final Uint8List processedBytes = processedByteData!.buffer.asUint8List();

      // Dispose of images to free memory
      originalImage.dispose();
      processedImage.dispose();

      return BitmapDescriptor.bytes(processedBytes);
    } catch (e) {
      debugPrint('Error creating custom marker: $e');
      return BitmapDescriptor.defaultMarker;
    }
  }

  //   Future<BitmapDescriptor> _createShopCustomMarkerIcon() async {
  //   final Size size = const Size(80, 80);
  //   final pictureRecorder = ui.PictureRecorder();
  //   final canvas = Canvas(pictureRecorder);
  //   final center = Offset(size.width / 2, size.height / 2);

  //   try {
  //     // Draw shadow
  //     final shadowPaint = Paint()
  //       ..color = Colors.black.withOpacity(0.2)
  //       ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
  //     canvas.drawCircle(center, size.width / 2 - 4, shadowPaint);

  //     // Draw white background circle
  //     final bgPaint = Paint()
  //       ..color = Colors.white
  //       ..style = PaintingStyle.fill;
  //     canvas.drawCircle(center, size.width / 2 - 4, bgPaint);

  //     // Load and draw SVG
  //     final svgString = await rootBundle.loadString('assets/svg/hardware_shop.svg');
  //     final SvgPicture svgPicture = SvgPicture.string(svgString);
  // final DrawableRoot svgRoot = svgPicture.pictureProvider.picture!.root;

  //     // Create circular clip path
  //     final clipPath = Path()
  //       ..addOval(
  //         Rect.fromCircle(
  //           center: center,
  //           radius: size.width / 2 - 8, // Smaller for border
  //         ),
  //       );

  //     canvas.save();
  //     canvas.clipPath(clipPath);

  //     // Calculate SVG drawing size
  //     final viewBox = svgRoot.viewport;
  //     final double scale = (size.width * 0.6) / viewBox.width; // 60% of marker size

  //     canvas.translate(
  //       center.dx - (viewBox.width * scale / 2),
  //       center.dy - (viewBox.height * scale / 2),
  //     );
  //     canvas.scale(scale);

  //     svgRoot.draw(canvas, Rect.fromLTWH(0, 0, viewBox.width, viewBox.height));
  //     canvas.restore();

  //     // Draw border
  //     final borderPaint = Paint()
  //       ..color = const Color(0xFFFF6B00)
  //       ..style = PaintingStyle.stroke
  //       ..strokeWidth = 3;
  //     canvas.drawCircle(center, size.width / 2 - 5, borderPaint);

  //     final picture = pictureRecorder.endRecording();
  //     final image = await picture.toImage(
  //       size.width.toInt(),
  //       size.height.toInt(),
  //     );
  //     final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

  //     if (byteData == null) throw Exception('Failed to generate marker icon');

  //     return BitmapDescriptor.bytes(byteData.buffer.asUint8List());
  //   } catch (e) {
  //     debugPrint('Error creating shop marker icon: $e');
  //     return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
  //   }
  // }

  Future<ClientModel?> _getClientInfo() async {
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) return null;

      final response =
          await Supabase.instance.client
              .from('clients')
              .select()
              .eq('supabase_id', currentUser.id)
              .single();

      return ClientModel.fromJson(response);
    } catch (e) {
      debugPrint('Error fetching client info: $e');
      return null;
    }
  }

  Future<void> getCurrentLocation() async {
    try {
      setLoading(true);

      final clientInfo = await _getClientInfo();
      state = state.copyWith(clientInfo: clientInfo);

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permission denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      final LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        // distanceFilter: 100,
      );

      // Get current position
      final Position position = await Geolocator.getCurrentPosition(
            locationSettings: locationSettings,
          )
          .then((position) {
            setCameraPosition(position.latitude, position.longitude, 12);
            state = state.copyWith(
              currentLocation: LatLng(position.latitude, position.longitude),
            );
            return position;
          })
          .catchError((e) {
            debugPrint('Error getting current position: $e');
            throw Exception('Error getting current position');
          });

      // Create user marker
      BitmapDescriptor userIcon;
      if (clientInfo?.avatar != null && clientInfo!.avatar!.isNotEmpty) {
        // userIcon = await _createCustomMarkerIcon(
        //   clientInfo.avatar!,
        //   isUser: true,
        // );

        final dio =
            Dio()
              ..options.connectTimeout = Duration(seconds: 5)
              ..options.receiveTimeout = Duration(seconds: 5);

        // Get a cached marker using Dio
        userIcon = await CustomMapMarkers.getCachedMarker(
          clientInfo.avatar!,
          label: '',
          size: 44,
          backgroundColor: Colors.transparent,
          addBorder: true,
          borderColor: Colors.white,
          dioInstance: dio, // optional - pass your configured Dio instance
        );
      } else {
        userIcon = BitmapDescriptor.defaultMarkerWithHue(
          BitmapDescriptor.hueBlue,
        );
      }

      final userMarker = Marker(
        markerId: const MarkerId('current_user'),
        position: LatLng(position.latitude, position.longitude),
        icon: userIcon,
        infoWindow: InfoWindow(
          title: clientInfo?.name ?? 'You',
          snippet: 'Current Location',
        ),
        onTap: () {
          // selectShop(null);
          debugPrint('User marker tapped');
          setShowdetailsVisibility(false);
        },
      );

      // Update state with user location and marker
      state = state.copyWith(
        userLocation: position,
        userMarker: userMarker,
        markers: {userMarker}, // Initialize markers with user marker
      );

      //Get nearby shops for current location
      await getNearbyShops(
        latitude: position.latitude,
        longitude: position.longitude,
        distance: 10,
      );
    } catch (e) {
      debugPrint('Error getting location: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      setLoading(false);
    }
  }
}
