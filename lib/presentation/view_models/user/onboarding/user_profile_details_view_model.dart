import 'dart:io';
import 'package:build_mate/data/dto/client_profile_data_request.dart';
import 'package:build_mate/main.dart';
import 'package:build_mate/presentation/state/user_profile_flow_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:build_mate/presentation/components/custom_toast.dart';

class UserProfileDetailsViewModel extends StateNotifier<UserProfileFlowState> {
  UserProfileDetailsViewModel(super.state) {
    getCurrentUserId();
    prefillUserData();
  }

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final fullNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final secondPhoneNumberController = TextEditingController();
  final addressController = TextEditingController();
  final nationalIdController = TextEditingController();
  final whatsappNumberController = TextEditingController();

  File? imageFile;

  /// Gets the current authenticated user ID from Supabase
  /// Returns empty string if no user is authenticated
  String getCurrentUserId() {
    // Get the current session
    final session = supabase.auth.currentSession;

    // Check if session exists and has a user ID
    if (session != null) {
      updateSupabaseUUID(session.user.id);
      return session.user.id;
    }

    // If no session, try to get from current user
    final user = supabase.auth.currentUser;
    if (user != null) {
      return user.id;
    }

    return '';
  }

  /// Gets the current authenticated user's basic profile information
  /// Returns a map with name, email, and avatar URL
  Map<String, String> getCurrentUserProfile() {
    final result = <String, String>{'name': '', 'email': '', 'avatarUrl': ''};

    // Try to get user from current session or direct user object
    final user = supabase.auth.currentUser;

    if (user == null) {
      return result;
    }

    // Get email from user object
    if (user.email != null) {
      result['email'] = user.email!;
    }

    // Get user metadata which might contain name and avatar
    final metadata = user.userMetadata;

    if (metadata != null) {
      // Try different possible keys for name
      if (metadata['full_name'] != null) {
        result['name'] = metadata['full_name'] as String;
      } else if (metadata['name'] != null) {
        result['name'] = metadata['name'] as String;
      }

      // Try different possible keys for avatar
      if (metadata['avatar_url'] != null) {
        result['avatarUrl'] = metadata['avatar_url'] as String;
      } else if (metadata['picture'] != null) {
        result['avatarUrl'] = metadata['picture'] as String;
      }
    }

    return result;
  }

  /// Pre-fills the form with user data from Supabase if available
  void prefillUserData() {
    final userProfile = getCurrentUserProfile();

    // Set email if available
    if (userProfile['email']!.isNotEmpty) {
      emailController.text = userProfile['email']!;
      updateEmail(userProfile['email']!);
    }

    // Set name if available
    if (userProfile['name']!.isNotEmpty) {
      fullNameController.text = userProfile['name']!;
      updateFullName(userProfile['name']!);
    }

    // Set avatar URL if available
    if (userProfile['avatarUrl']!.isNotEmpty) {
      updateAvatarUrl(userProfile['avatarUrl']!);
    }
  }

  @override
  void dispose() {
    fullNameController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    secondPhoneNumberController.dispose();
    whatsappNumberController.dispose();
    addressController.dispose();
    nationalIdController.dispose();
    super.dispose();
  }

  void updateFullName(String fullname) {
    state = state.copyWith(fullname: fullname);
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email);
  }

  void updatePhoneNumber(String phonenumber) {
    state = state.copyWith(phonenumber: phonenumber);
  }

  void updateSecondPhoneNumber(String secondPhonenumber) {
    state = state.copyWith(secondPhonenumber: secondPhonenumber);
  }

  void updateAddress(String address) {
    state = state.copyWith(address: address);
  }

  void updateNationalId(String nationalId) {
    state = state.copyWith(nationalId: nationalId);
  }

  void updateAvatarUrl(String avatarUrl) {
    state = state.copyWith(avatarUrl: avatarUrl);
  }

  void updateIsUpdatingProfile(bool isUpdatingProfile) {
    state = state.copyWith(isUpdatingProfile: isUpdatingProfile);
  }

  void updateSupabaseUUID(String supabaseUUID) {
    state = state.copyWith(supabaseUUID: supabaseUUID);
  }

  void updateLocation(LatLng location) {
    state = state.copyWith(location: location);
  }

  void updateWhatsappNumber(String whatsappNumber) {
    state = state.copyWith(whatsappNumber: whatsappNumber);
  }

  /// Uploads a file to Supabase storage and returns the public URL
  Future<String> uploadProfileImage(File file) async {
    try {
      // Get user ID for creating a unique file path
      final userId = getCurrentUserId();
      if (userId.isEmpty) {
        throw Exception('User not authenticated');
      }

      // Create a unique file name with timestamp to avoid conflicts
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = file.path.split('.').last;
      final fileName = 'profile_$userId\_$timestamp.$fileExtension';

      // Upload file to the 'avatars' bucket
      // Using a public folder to avoid RLS issues
      final filePath = 'public/$fileName';

      if (kDebugMode) {
        print('Uploading to path: $filePath');
      }

      // Upload the file
      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            file,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Get the public URL for the uploaded file
      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);
      updateAvatarUrl(imageUrl);

      if (kDebugMode) {
        print('Image uploaded successfully: $imageUrl');
      }
      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // More detailed error logging
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }

      throw Exception('Failed to upload image: $e');
    }
  }

  void setImageFile(File? file) {
    imageFile = file;
    if (file != null) {
      // Clear avatar URL when local image is selected
      updateAvatarUrl('');
    }
  }

  /// Checks if the user is properly authenticated with Supabase
  Future<bool> isAuthenticated() async {
    final session = supabase.auth.currentSession;

    if (session == null) {
      if (kDebugMode) {
        print('No active session found');
      }
      return false;
    }

    // Check if token is expired
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();

    if (expiresAt.isBefore(now)) {
      if (kDebugMode) {
        print('Session has expired. Expires at: $expiresAt, Now: $now');
      }
      return false;
    }

    if (kDebugMode) {
      print('User is authenticated. Session expires at: $expiresAt');
    }
    return true;
  }

  Future<void> selectImage() async {
    final ImagePicker picker = ImagePicker();
    try {
      // First check if user is authenticated
      final authenticated = await isAuthenticated();
      if (!authenticated) {
        throw Exception('User is not authenticated. Please log in again.');
      }

      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        final File imageFile = File(image.path);
        setImageFile(imageFile);

        // Upload the image to Supabase storage
        updateIsUpdatingProfile(true); // Show loading indicator

        final imageUrl = await uploadProfileImage(imageFile);

        // Update the avatar URL in the state
        updateAvatarUrl(imageUrl);

        updateIsUpdatingProfile(false); // Hide loading indicator
      }
    } catch (e) {
      updateIsUpdatingProfile(false); // Hide loading indicator in case of error
      throw Exception('Error selecting image: $e');
    }
  }

  Future<bool> saveProfile(BuildContext context) async {
    if (!formKey.currentState!.validate()) return false;

    updateIsUpdatingProfile(true);

    try {
      // Get current user ID
      final userId = getCurrentUserId();
      if (userId.isNotEmpty) {
        updateSupabaseUUID(userId);
      }

      // Update state with form values
      updateFullName(fullNameController.text);
      updateEmail(emailController.text);
      updatePhoneNumber(phoneNumberController.text);
      updateSecondPhoneNumber(secondPhoneNumberController.text);
      updateWhatsappNumber(whatsappNumberController.text);
      updateAddress(addressController.text);
      updateNationalId(nationalIdController.text);

      // Print the complete profile data including location
      if (kDebugMode) {
        print('Profile saved with data:');
      }
      if (kDebugMode) {
        print('Name: ${state.fullname}');
      }
      if (kDebugMode) {
        print('Email: ${state.email}');
      }
      if (kDebugMode) {
        print('Phone: ${state.phonenumber}');
      }
      if (kDebugMode) {
        print('Second Phone: ${state.secondPhonenumber}');
      }
      if (kDebugMode) {
        print('WhatsApp: ${state.whatsappNumber}');
      }
      if (kDebugMode) {
        print('Address: ${state.address}');
      }
      if (kDebugMode) {
        print(
          'Location: ${state.location.latitude}, ${state.location.longitude}',
        );
      }
      if (kDebugMode) {
        print('User ID: ${state.supabaseUUID}');
      }
      if (kDebugMode) {
        print('Avatar URL: ${state.avatarUrl}');
      }

      final clientRequestBody = ClientProfileDataRequest(
        client: ClientModel(
          name: state.fullname,
          email: state.email,
          avatar: state.avatarUrl,
          address: state.address,
          nationalId: state.nationalId,
          supabaseId: state.supabaseUUID,
          whatsappNumber: state.whatsappNumber,
        ),
        phonenumbers: [
          PhoneNumberModel(phonenumber: state.phonenumber),
          // Add second phone number if it's not empty
          if (state.secondPhonenumber.isNotEmpty)
            PhoneNumberModel(phonenumber: state.secondPhonenumber),
        ],
        location: LocationModel(
          latitude: state.location.latitude,
          longitude: state.location.longitude,
          address: state.address,
        ),
      );

      if (kDebugMode) {
        print('Request body: ${clientRequestBody.toJson()}');
      }

      final success = await supabase.functions
          .invoke('save-client-data', body: clientRequestBody.toJson())
          .then((response) async {
            final responseData = response.data;
            if (kDebugMode) {
              print('Response from edge function: $responseData');
            }

            if (responseData != null &&
                responseData['message'] == 'Data saved successfully') {
              await OneSignal.User.getOnesignalId().then((value) {
                if (kDebugMode) {
                  print('ONESIGNAL ID RETRIEVED ON LOAD: $value');
                }
                supabase
                    .from('clients')
                    .update({'onesignal_id': value})
                    .eq('id', responseData['id']);
              });

              if (context.mounted) {
                CustomToast.show(
                  context: context,
                  message: "Profile saved successfully!",
                  isError: false,
                );
              }
              // Show success toast

              // Profile saved successfully
              if (kDebugMode) {
                print('Profile saved successfully');
              }
              return true; // Return success status
            } else {
              // Handle unexpected response format
              if (kDebugMode) {
                print('Unexpected response format: $responseData');
              }
              throw Exception('Failed to save profile: Unexpected response');
            }
          });

      return success;
    } catch (e) {
      // Show error toast
      if (context.mounted) {
        CustomToast.show(
          context: context,
          message: "Error: ${e.toString()}",
          isError: true,
          duration: const Duration(seconds: 3),
        );
      }

      throw Exception('Error saving profile: $e');
    } finally {
      updateIsUpdatingProfile(false);
    }
  }

  Future<PermissionStatus> checkLocationPermission() async {
    // Check current permission status
    final status = await Permission.location.status;

    if (status.isDenied || status.isRestricted || status.isLimited) {
      // Explicitly request permission
      return await Permission.location.request();
    }

    return status;
  }

  Future<void> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition();

      // Create LatLng object and update state
      final location = LatLng(position.latitude, position.longitude);
      updateLocation(location);

      if (kDebugMode) {
        print('Location: ${location.latitude}, ${location.longitude}');
      }

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address =
            '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}, ${place.country}';

        // Update address field
        addressController.text = address;
        updateAddress(address);
      }
    } catch (e) {
      throw Exception('Error getting location: $e');
    }
  }
}

final userProfileDetailsViewModelProvider =
    StateNotifierProvider<UserProfileDetailsViewModel, UserProfileFlowState>((
      ref,
    ) {
      return UserProfileDetailsViewModel(UserProfileFlowState());
    });
