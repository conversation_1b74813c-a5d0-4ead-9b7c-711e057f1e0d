import 'dart:io';
import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:build_mate/data/shared_preferences/preferences_provider.dart';
import 'package:build_mate/presentation/state/artisan_profile_state.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

final artisanProfileDetailsViewModelProvider =
    StateNotifierProvider<ArtisanProfileDetailsViewModel, ArtisanProfileState>((
      ref,
    ) {
      return ArtisanProfileDetailsViewModel(
        ArtisanProfileState(),
        ref.read(preferencesProvider),
        ref.read(goRouterProvider),
      );
    });

class ArtisanProfileDetailsViewModel
    extends StateNotifier<ArtisanProfileState> {
  final PreferencesService _preferencesService;
  final GoRouter _router;
  final supabase = Supabase.instance.client;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final fullNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final secondPhoneNumberController = TextEditingController();
  final whatsappNumberController = TextEditingController();
  final addressController = TextEditingController();
  final nationalIdController = TextEditingController();

  // Image files
  File? profileImageFile;
  File? coverImageFile;

  // Upload progress
  double profileUploadProgress = 0.0;
  double coverUploadProgress = 0.0;
  bool isUploadingProfile = false;
  bool isUploadingCover = false;

  ArtisanProfileDetailsViewModel(
    super.state,
    this._preferencesService,
    this._router,
  ) {
    _getCurrentUserId();
    _prefillUserData();
    _checkLocationPermission();
  }

  void _getCurrentUserId() {
    final userId = supabase.auth.currentUser?.id;
    if (userId != null) {
      state = state.copyWith(supabaseUUID: userId);
    }
  }

  void _prefillUserData() {
    final user = supabase.auth.currentUser;
    if (user != null) {
      // Set email if available
      if (user.email != null) {
        emailController.text = user.email!;
        state = state.copyWith(email: user.email!);
      }

      // Get user metadata which might contain name and avatar
      final metadata = user.userMetadata;
      if (metadata != null) {
        // Try different possible keys for name
        if (metadata['full_name'] != null) {
          fullNameController.text = metadata['full_name'] as String;
          state = state.copyWith(fullname: metadata['full_name'] as String);
        } else if (metadata['name'] != null) {
          fullNameController.text = metadata['name'] as String;
          state = state.copyWith(fullname: metadata['name'] as String);
        }

        // Try different possible keys for avatar
        if (metadata['avatar_url'] != null) {
          state = state.copyWith(avatarUrl: metadata['avatar_url'] as String);
        } else if (metadata['picture'] != null) {
          state = state.copyWith(avatarUrl: metadata['picture'] as String);
        }
      }
    }
  }

  Future<PermissionStatus> checkLocationPermission() async {
    // Check current permission status
    final status = await Permission.location.status;

    if (status.isDenied || status.isRestricted || status.isLimited) {
      // Explicitly request permission
      return await Permission.location.request();
    }

    return status;
  }  

  Future<void> _checkLocationPermission() async {
    try {
      final status = await checkLocationPermission();

      if (status.isGranted) {
        // Permission granted, get current location
        await getCurrentLocation();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location permission: $e');
      }
    }
  }

  Future<void> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition();

      // Create LatLng object and update state
      final location = LatLng(position.latitude, position.longitude);
      state = state.copyWith(location: location);

      if (kDebugMode) {
        print('Location: ${location.latitude}, ${location.longitude}');
      }

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address =
            '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}, ${place.country}';

        // Update address field
        addressController.text = address;
        state = state.copyWith(address: address);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location: $e');
      }
    }
  }

  Future<void> selectProfileImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        // Set loading state
        isUploadingProfile = true;
        profileUploadProgress = 0.0;
        state = state.copyWith(isUpdatingProfile: true);

        // Set the file locally
        profileImageFile = File(image.path);

        // Upload to Supabase immediately
        final imageUrl = await _uploadImage(profileImageFile!, 'profile', (
          progress,
        ) {
          profileUploadProgress = progress;
          // Trigger UI update
          state = state.copyWith(isUpdatingProfile: state.isUpdatingProfile);
        });

        // Update state with the new avatar URL
        state = state.copyWith(avatarUrl: imageUrl, isUpdatingProfile: false);
        isUploadingProfile = false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error selecting and uploading profile image: $e');
      }
      state = state.copyWith(isUpdatingProfile: false);
      isUploadingProfile = false;
      rethrow;
    }
  }

  Future<void> selectCoverImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        // Set loading state
        isUploadingCover = true;
        coverUploadProgress = 0.0;
        state = state.copyWith(isUpdatingProfile: true);

        // Set the file locally
        coverImageFile = File(image.path);

        // Upload to Supabase immediately
        final imageUrl = await _uploadCoverImage(coverImageFile!, 'cover', (
          progress,
        ) {
          coverUploadProgress = progress;
          // Trigger UI update
          state = state.copyWith(isUpdatingProfile: state.isUpdatingProfile);
        });

        // Update state with the new cover image URL
        state = state.copyWith(
          coverImageUrl: imageUrl,
          isUpdatingProfile: false,
        );
        isUploadingCover = false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error selecting and uploading cover image: $e');
      }
      state = state.copyWith(isUpdatingProfile: false);
      isUploadingCover = false;
      rethrow;
    }
  }

  Future<String> _uploadImage(
    File imageFile,
    String folder,
    Function(double) onProgress,
  ) async {
    try {
      final userId = state.supabaseUUID;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-$timestamp.$fileExtension';
      final filePath = '$folder/$fileName';

      // Create upload options
      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );
      // Simulate progress for demo purposes (since Supabase Flutter SDK doesn't have built-in progress)
      // In a real implementation, you'd use the actual progress from the upload response
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);
      if (kDebugMode) {
        print('Image uploaded successfully: $imageUrl');
      }
      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // More detailed error logging
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }

      rethrow;
    }
  }

  Future<String> _uploadCoverImage(
    File imageFile,
    String folder,
    Function(double) onProgress,
  ) async {
    try {
      final userId = state.supabaseUUID;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-$timestamp.$fileExtension';
      final filePath = '$folder/$fileName';

      // Get file size for progress calculation
   

      // Create upload options
      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );
      // Simulate progress for demo purposes (since Supabase Flutter SDK doesn't have built-in progress)
      // In a real implementation, you'd use the actual progress from the upload response
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress(i / 10);
      }

      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);
      if (kDebugMode) {
        print('Cover Image uploaded successfully: $imageUrl');
      }
      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // More detailed error logging
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }

      rethrow;
    }
  }

  Map<String, dynamic> collectProfileData() {
    // Update state with form values
    state = state.copyWith(
      fullname: fullNameController.text,
      email: emailController.text,
      phoneNumber: phoneNumberController.text,
      secondPhoneNumber: secondPhoneNumberController.text,
      whatsappNumber: whatsappNumberController.text,
      address: addressController.text,
      nationalId: nationalIdController.text,
    );

    // Prepare data to pass to next screen
    final artisanData = {
      'supabase_id': state.supabaseUUID,
      'fullname': state.fullname,
      'email': state.email,
      'phone_number': state.phoneNumber,
      'second_phone_number': state.secondPhoneNumber,
      'whatsapp_number': state.whatsappNumber,
      'address': state.address,
      'national_id': state.nationalId,
      'avatar_url': state.avatarUrl,
      'cover_image_url': state.coverImageUrl,
      'created_at': DateTime.now().toIso8601String(),
      'latitude': state.location.latitude,
      'longitude': state.location.longitude,
    };

    return artisanData;
  }

  @override
  void dispose() {
    super.dispose();
    // Dispose controllers
    fullNameController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    secondPhoneNumberController.dispose();
    whatsappNumberController.dispose();
    addressController.dispose();
    nationalIdController.dispose();
  }

  // Add these methods to update the state when the user fills out the form
  void updateFormState() {
    state = state.copyWith(
      fullname: fullNameController.text,
      email: emailController.text,
      phoneNumber: phoneNumberController.text,
      secondPhoneNumber: secondPhoneNumberController.text,
      whatsappNumber: whatsappNumberController.text,
      address: addressController.text,
      nationalId: nationalIdController.text,
    );
  }

  // Call this method when navigating to the next screen
  void prepareForNextScreen() {
    updateFormState();
  }
}
