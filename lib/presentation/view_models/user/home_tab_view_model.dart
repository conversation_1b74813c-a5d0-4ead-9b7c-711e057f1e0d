import 'package:build_mate/presentation/state/home_tab_state.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final homeTabViewModelProvider =
    StateNotifierProvider<HomeTabViewModel, HomeTabState>((ref) {
      return HomeTabViewModel(HomeTabState());
    });

class HomeTabViewModel extends StateNotifier<HomeTabState> {
  HomeTabViewModel(super.state) {
    getUserProfile();
    _checkLocationPermission();
  }

  void setLoadingState(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getUserProfile() async {
    setLoadingState(true);
    final user = Supabase.instance.client.auth.currentUser;

    if (user == null) return;
    await Supabase.instance.client
        .from('clients')
        .select('name, avatar')
        .eq('supabase_id', user.id)
        .single()
        .then((value) {
          state = state.copyWith(
            profileUrl: value['avatar'],
            username: value['name'],
          );
          setLoadingState(false);
        });
  }

  Future<PermissionStatus> checkLocationPermission() async {
    // Check current permission status
    final status = await Permission.location.status;

    if (status.isDenied || status.isRestricted || status.isLimited) {
      // Explicitly request permission
      return await Permission.location.request();
    }

    return status;
  }

  Future<void> _checkLocationPermission() async {
    try {
      final status = await checkLocationPermission();

      if (status.isGranted) {
        // Permission granted, get current location
        await getCurrentLocation();
      }
    } catch (e) {
      debugPrint('Error checking location permission: $e');
    }
  }

  Future<void> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition();

      // Create LatLng object and update state
      final location = LatLng(position.latitude, position.longitude);
      debugPrint('Location: ${location.latitude}, ${location.longitude}');

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '${place.locality}';

        state = state.copyWith(currentLocation: address);
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }
}
