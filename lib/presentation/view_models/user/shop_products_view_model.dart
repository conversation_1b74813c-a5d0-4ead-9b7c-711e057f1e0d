import 'package:build_mate/presentation/state/shop_products_state.dart';
import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/data/dto/shop_product_response.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final shopProductsViewModelProvider =
    StateNotifierProvider<ShopProductsViewModel, ShopProductsState>((ref) {
      return ShopProductsViewModel(ShopProductsState(), ref);
    });

class ShopProductsViewModel extends StateNotifier<ShopProductsState> {
  ShopProductsViewModel(super.state, this._ref) {
    setShopname();
    getBranchProducts();
    loadSubcategories();
  }
  final Ref _ref;

  Future<void> getBranchProducts() async {
    int? selectedBranchId = _ref.watch(
      shopsViewModelProvider.select((state) => state.selectedBranchId),
    );

    state = state.copyWith(isLoadingProducts: true);
    try {
      final response = await Supabase.instance.client
          .from('branch_products')
          .select(
            '*, hardware_sub_categories(name), product_images(id, image_url)',
          )
          .eq('branch_id', selectedBranchId ?? 0)
          .order('name');

      final products =
          (response as List)
              .map(
                (item) =>
                    ShopProductResponse.fromJson(item as Map<String, dynamic>),
              )
              .toList();

      state = state.copyWith(products: products, isLoadingProducts: false);
    } catch (e) {
      state = state.copyWith(isLoadingProducts: false);

      debugPrint('Error fetching branch products: $e');
    }
  }

  Future<void> loadSubcategories() async {
    try {
      final subcategories = await Supabase.instance.client
          .from('hardware_sub_categories')
          .select('id, name')
          .order('name');
      state = state.copyWith(
        subcategories: subcategories,
        filteredSubcategories: subcategories, // maintain a filtered list
      );
    } catch (e) {
      debugPrint('Error loading subcategories: $e');
    }
  }

  void setShopname() {
    String? shopName = _ref.watch(
      shopsViewModelProvider.select((state) => state.selectedShopname),
    );
    String? branchName = _ref.watch(
      shopsViewModelProvider.select((state) => state.selectedBranchName),
    );

    int? selectedBranchId = _ref.watch(
      shopsViewModelProvider.select((state) => state.selectedBranchId),
    );

    state = state.copyWith(
      shopname: shopName ?? '',
      branchName: branchName ?? '',
      branchId: selectedBranchId ?? 0,
    );
  }

  Future<void> onSearchProduct(String value) async {
    state = state.copyWith(isLoadingProducts: true);
    try {
      final response = await Supabase.instance.client
          .from('branch_products')
          .select(
            '*, hardware_sub_categories(name), product_images(id, image_url)',
          )
          .eq('branch_id', state.branchId)
          .ilike('name', '%${value.trim()}%')
          .order('name');

      final products =
          (response as List)
              .map(
                (item) =>
                    ShopProductResponse.fromJson(item as Map<String, dynamic>),
              )
              .toList();

      state = state.copyWith(products: products);

      // if (kDebugMode) {
      //   print('Branch products response: $products');
      // }
      state = state.copyWith(isLoadingProducts: false);
    } catch (e) {
      state = state.copyWith(isLoadingProducts: false);

      debugPrint('Error fetching branch products: $e');
    }
  }

  Future<void> getProductsByFilter(int subCategoryId) async {
    state = state.copyWith(isLoadingProducts: true);
    try {
      final response = await Supabase.instance.client
          .from('branch_products')
          .select(
            '*, hardware_sub_categories(name), product_images(id, image_url)',
          )
          .eq('branch_id', state.branchId)
          .eq('hardware_sub_category_id', subCategoryId)
          .order('name');

      final products =
          (response as List)
              .map(
                (item) =>
                    ShopProductResponse.fromJson(item as Map<String, dynamic>),
              )
              .toList();

      state = state.copyWith(products: products);

      // if (kDebugMode) {
      //   print('Branch products response: $products');
      // }
      state = state.copyWith(isLoadingProducts: false);
    } catch (e) {
      state = state.copyWith(isLoadingProducts: false);

      debugPrint('Error fetching branch products: $e');
    }
  }

  void selectSubcategory(int? subcategoryId) {
    debugPrint('Selected subcategory: $subcategoryId');
    state = state.copyWith(selectedSubcategoryId: subcategoryId);
    // Optionally trigger filtering logic here
  }

  void filterSubCategory(String val) {
    // Always filter from the full subcategories list, not the filtered one
    final filtered =
        val.isEmpty
            ? state.subcategories
            : state.subcategories
                .where(
                  (s) => (s['name'] ?? '').toString().toLowerCase().contains(
                    val.toLowerCase(),
                  ),
                )
                .toList();
    state = state.copyWith(search: val, filteredSubcategories: filtered);
  }

  Future<void> resetFilter() async {
    selectSubcategory(null);
    setSelectedSubCatagoryName(null);
    await getBranchProducts();
  }

  void setSelectedSubCatagoryName(String? name) {
    state = state.copyWith(selectedSubcategoryName: name);
  }
}
