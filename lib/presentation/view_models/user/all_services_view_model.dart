import 'package:build_mate/data/dto/service_icons_response.dart';
import 'package:build_mate/presentation/state/all_services_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final allServicesViewModelProvider =
    StateNotifierProvider<AllServicesViewModel, AllServicesState>((ref) {
      return AllServicesViewModel(AllServicesState());
    });

class AllServicesViewModel extends StateNotifier<AllServicesState> {
  AllServicesViewModel(super.state) {
    loadCategories();
    fetchServices();
  }

  Map<String, int> serviceIdMap = {};
  Map<String, Map<String, int>> subcategoryIdMap = {};
  Map<String, List<String>> categoriesMap = {};

  void setServiceId(int id) {
    state = state.copyWith(selectedServiceId: id);
  }

  void setSelectedService(int serviceId, String serviceName) {
    state = state.copyWith(
      selectedServiceId: serviceId,
      selectedServiceName: serviceName,
    );

    if (kDebugMode) {
      print('Selected service: $serviceName (ID: $serviceId)');
    }
  }

  Future<void> fetchServices() async {
    try {
      state = state.copyWith(isLoading: true);

      final supabase = Supabase.instance.client;
      // Fetch all services
      final servicesResponse = await supabase
          .from('services')
          .select('id, name, icon')
          .lt('id', 26)
          .order('name');

      if (kDebugMode) {
        print('SERVICES_ICONS_RESPONSE: ${servicesResponse.toString()}');
      }

      // Map the response to a list of ServiceIconsResponse
      final List<ServiceIconsResponse> services =
          (servicesResponse as List)
              .map((service) => ServiceIconsResponse.fromJson(service))
              .toList();

      // Update the state with the services
      state = state.copyWith(services: services, isLoading: false);

      // Create a map of service names to IDs for easier lookup
      for (final service in services) {
        if (service.name != null) {
          serviceIdMap[service.name!] = service.id ?? 0;
        }
      }

      if (kDebugMode) {
        print('Loaded ${services.length} services');
        print('Service ID Map: $serviceIdMap');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching services: $e');
      }
      state = state.copyWith(isLoading: false);
      rethrow;
    }
  }

  void loadCategories() {
    try {
      final List<Map<String, dynamic>> categories = [
        {'title': 'Bathroom', 'icon': 'assets/svg/bathroom.svg'},
        {'title': 'Builder', 'icon': 'assets/svg/builder.svg'},
        {'title': 'Carpenter', 'icon': 'assets/svg/carpenter.svg'},
        {'title': 'Security', 'icon': 'assets/svg/security.svg'},
        {'title': 'Flooring', 'icon': 'assets/svg/flooring.svg'},
        {'title': 'Cleaner', 'icon': 'assets/svg/cleaner.svg'},
        {'title': 'Drainage', 'icon': 'assets/svg/drainage.svg'},
        {'title': 'Driveway', 'icon': 'assets/svg/driveway.svg'},
        {'title': 'Electrical', 'icon': 'assets/svg/electrical.svg'},
        {'title': 'Flooring', 'icon': 'assets/svg/flooring.svg'},
        {'title': 'Landscaping', 'icon': 'assets/svg/landscaping.svg'},
        {
          'title': 'Interior Designer',
          'icon': 'assets/svg/interior_design.svg',
        },
        {'title': 'Kitchen Specialist', 'icon': 'assets/svg/kitchen.svg'},
        {'title': 'Locksmith', 'icon': 'assets/svg/locksmith.svg'},
        {'title': 'Painter', 'icon': 'assets/svg/painter.svg'},
        {'title': 'Pest Control', 'icon': 'assets/svg/pest_control.svg'},
        {'title': 'Plumbing', 'icon': 'assets/svg/plumber.svg'},
        {'title': 'Solar', 'icon': 'assets/svg/solar_power.svg'},
        {'title': 'Roofer', 'icon': 'assets/svg/roofing.svg'},
        {'title': 'Swimming Pool', 'icon': 'assets/svg/swimming_pool.svg'},
        {'title': 'Tiler', 'icon': 'assets/svg/tiles.svg'},
        {'title': 'Window fitter', 'icon': 'assets/svg/window_fitting.svg'},
        {'title': 'Tree Cutting', 'icon': 'assets/svg/tree_cutting.svg'},
        {'title': 'Borehole', 'icon': 'assets/svg/borehole.svg'},
      ];

      state = state.copyWith(categories: categories);

      if (kDebugMode) {
        print('Loaded ${categories.length} service categories');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
    }
  }
}
