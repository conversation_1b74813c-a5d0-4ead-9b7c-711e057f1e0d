import 'package:build_mate/data/services/chat_service.dart';
import 'package:build_mate/presentation/state/user_chat_state.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final userChatViewModelProvider =
    StateNotifierProvider<UserChatViewModel, UserChatState>((ref) {
      return UserChatViewModel(
        UserChatState(conversations: AsyncValue.data([])),
        ChatService(),
        ref,
      );
    });

class UserChatViewModel extends StateNotifier<UserChatState> {
  UserChatViewModel(super.state, this._chatService, this.ref) {
    getConversations();
  }

  final ChatService _chatService;
  final Ref ref;

  void updateLoadingState(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getConversations() async {
    try {
      if (kDebugMode) {
        print('Starting to fetch client conversations');
      }

      // Get the current user's Supabase ID
      final supabase = Supabase.instance.client;
      final supabaseId = supabase.auth.currentUser?.id;

      if (kDebugMode) {
        print('Current Supabase user ID: $supabaseId');
      }

      if (supabaseId == null) {
        if (kDebugMode) {
          print('No authenticated user found');
        }
        return;
      }

      // Get the client ID from the database using the Supabase ID
      try {
        final clientResponse =
            await supabase
                .from('clients')
                .select('id')
                .eq('supabase_id', supabaseId)
                .maybeSingle();

        if (kDebugMode) {
          print('Client response: $clientResponse');
        }

        if (clientResponse == null) {
          if (kDebugMode) {
            print('No client found for Supabase ID: $supabaseId');
          }
          return;
        }

        final clientId = clientResponse['id'];
        if (kDebugMode) {
          print('Fetching conversations for client ID: $clientId');
        }

        // Set up realtime subscription for conversations table
        supabase
            .channel('public:conversations')
            .onPostgresChanges(
              event: PostgresChangeEvent.all,
              schema: 'public',
              table: 'conversations',
              filter: PostgresChangeFilter(
                type: PostgresChangeFilterType.eq,
                column: 'client_id',
                value: clientId,
              ),
              callback: (payload) {
                if (kDebugMode) {
                  print('Conversation change detected: ${payload.eventType}');
                }
                // Refresh the provider when a conversation changes
                ref.invalidateSelf();
              },
            )
            .subscribe();

        // Set up realtime subscription for messages table to update when new messages arrive
        supabase
            .channel('public:messages')
            .onPostgresChanges(
              event: PostgresChangeEvent.all,
              schema: 'public',
              table: 'messages',
              callback: (payload) {
                if (kDebugMode) {
                  print('Message change detected: ${payload.eventType}');
                }
                // Refresh the provider when a message changes
                ref.invalidateSelf();
              },
            )
            .subscribe();

        // Get conversations for this client
        try {
          final conversations = await _chatService.getConversations(
            true,
            clientId,
          );

          if (kDebugMode) {
            print(
              'Found ${conversations.length} conversations for client ID: $clientId',
            );
            for (final conversation in conversations) {
              print(
                'Conversation ID: ${conversation.id}, Artisan: ${conversation.artisan?.name}',
              );
            }
          }

          state = state.copyWith(conversations: AsyncValue.data(conversations));
        } catch (e) {
          if (kDebugMode) {
            print('Error in chatService.getConversations: $e');
          }
          rethrow;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error querying clients table: $e');
        }
        rethrow;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Top-level error in conversationsProvider: $e');
      }
      rethrow;
    }
  }

  RealtimeChannel? _channel;
  Future<void> listenToGameChanges(int conversationId) async {
    try {
      final supabase = Supabase.instance.client;
      await supabase.auth.refreshSession();

      // Remove existing subscription if any
      await _channel?.unsubscribe();

      // Create private channel - equivalent to { config: { private: true } }
      final channelName = 'conversation:$conversationId';
      _channel = supabase.channel(
        channelName,
        opts: const RealtimeChannelConfig(
          private: true, // This makes it a private channel
        ),
      );

      // Listen to INSERT events
      _channel!
          .onBroadcast(
            event: 'INSERT',
            callback: (payload) {
              debugPrint('INSERT event received: $payload');
              _handleInsert(payload);
            },
          )
          .onBroadcast(
            event: 'UPDATE',
            callback: (payload) {
              debugPrint('UPDATE event received: $payload');
              _handleUpdate(payload);
            },
          )
          .onBroadcast(
            event: 'DELETE',
            callback: (payload) {
              debugPrint('DELETE event received: $payload');
              _handleDelete(payload);
            },
          );
      // Subscribe to the channel
      _channel!.subscribe((status, error) {
        switch (status) {
          case RealtimeSubscribeStatus.subscribed:
            debugPrint('Successfully subscribed to $channelName');
            break;
          case RealtimeSubscribeStatus.timedOut:
            debugPrint('Subscription timed out for $channelName');
            break;
          case RealtimeSubscribeStatus.channelError:
            debugPrint('Channel error for $channelName: ${error?.toString()}');
            break;
          case RealtimeSubscribeStatus.closed:
            debugPrint('Channel closed for $channelName');
            break;
        }
      });
    } catch (e) {
      debugPrint('Error setting up broadcast listener: $e');
      rethrow;
    }
  }

  void _handleInsert(Map<String, dynamic> payload) {
    // Handle INSERT event
  }

  void _handleUpdate(Map<String, dynamic> payload) {
    // Handle UPDATE event
    debugPrint('UPDATED!!!');
  }

  void _handleDelete(Map<String, dynamic> payload) {
    // Handle DELETE event
  }
}
