import 'package:build_mate/presentation/state/client_profile_tab_state.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final clientProfileTabViewModelProvider =
    StateNotifierProvider<ClientProfileTabViewModel, ClientProfileTabState>((
      ref,
    ) {
      return ClientProfileTabViewModel(ClientProfileTabState());
    });

class ClientProfileTabViewModel extends StateNotifier<ClientProfileTabState> {
  ClientProfileTabViewModel(super.state) {
    getUserProfile();
  }

  void setLoadingState(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getUserProfile() async {
    setLoadingState(true);
    final user = Supabase.instance.client.auth.currentUser;

    if (user == null) return;
    await Supabase.instance.client
        .from('clients')
        .select('name, avatar')
        .eq('supabase_id', user.id)
        .single()
        .then((value) {
          state = state.copyWith(
            profileUrl: value['avatar'],
            username: value['name'],
          );
          setLoadingState(false);
        });
  }

  Future<void> logout() async {
    await Supabase.instance.client.auth.signOut(scope: SignOutScope.global);
  }
}
