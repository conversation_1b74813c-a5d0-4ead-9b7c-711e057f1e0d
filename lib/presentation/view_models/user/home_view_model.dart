import 'package:build_mate/data/dto/service_icons_response.dart';
import 'package:build_mate/presentation/state/home_flow_state.dart';
import 'package:build_mate/presentation/view_models/user/all_services_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final homeViewModelProvider =
    StateNotifierProvider<HomeViewModel, HomeFlowState>((ref) {
      return HomeViewModel(HomeFlowState(), ref);
    });

class HomeViewModel extends StateNotifier<HomeFlowState> {
  HomeViewModel(super.state, this._ref) {
    fetchServices();
  }

  final Ref _ref;

  void setServiceId(int serviceId) {
    _ref.read(allServicesViewModelProvider.notifier).setServiceId(serviceId);
  }

  Future<void> fetchServices() async {
    try {
      state = state.copyWith(isLoadingServices: true);

      final supabase = Supabase.instance.client;
      // Fetch all services
      final servicesResponse = await supabase
          .from('services')
          .select('id, name, icon')
          .limit(6)
          .order('name');

      if (kDebugMode) {
        print('SERVICES_ICONS_RESPONSE: ${servicesResponse.toString()}');
      }

      // Map the response to a list of ServiceIconsResponse
      final List<ServiceIconsResponse> services =
          (servicesResponse as List)
              .map((service) => ServiceIconsResponse.fromJson(service))
              .toList();

      // Update the state with the services
      state = state.copyWith(services: services, isLoadingServices: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching services: $e');
      }
      state = state.copyWith(isLoadingServices: false);
      rethrow;
    }
  }
}
