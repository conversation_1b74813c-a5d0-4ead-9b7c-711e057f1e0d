import 'package:build_mate/presentation/state/profile_settings_state.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

final profileSettingsViewModelProvider =
    StateNotifierProvider<ProfileSettingsViewModel, ProfileSettingsState>((
      ref,
    ) {
      return ProfileSettingsViewModel(ProfileSettingsState());
    });

class ProfileSettingsViewModel extends StateNotifier<ProfileSettingsState> {
  ProfileSettingsViewModel(super.state) {
    getUserProfile();
  }

  final supabase = Supabase.instance.client;

  void setLoadingState(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  Future<void> getUserProfile() async {
    setLoadingState(true);
    final user = Supabase.instance.client.auth.currentUser;

    if (user == null) return;
    await Supabase.instance.client
        .from('clients')
        .select('name, avatar')
        .eq('supabase_id', user.id)
        .single()
        .then((value) {
          state = state.copyWith(
            profileUrl: value['avatar'],
            username: value['name'],
          );
          setLoadingState(false);
        });
  }

  Future<void> updateUserName(String newName) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) throw Exception('User not logged in');
      await supabase
          .from('clients')
          .update({'name': newName})
          .eq('supabase_id', userId);

      state = state.copyWith(username: newName, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  Future<void> pickAndUploadAvatar() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      final picker = ImagePicker();
      final picked = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      if (picked == null) {
        state = state.copyWith(isLoading: false);
        return;
      }
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) throw Exception('User not logged in');
      final fileExt = picked.path.split('.').last;
      final filePath = 'avatars/$userId/avatar.$fileExt';
      final file = File(picked.path);
      await supabase.storage
          .from('avatars')
          .upload(filePath, file, fileOptions: FileOptions(upsert: true));
      // if (storageResponse.error != null) throw storageResponse.error!;

      final publicUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      // Update the state immediately with the new profile URL and increment image version
      state = state.copyWith(
        profileUrl: publicUrl,
        imageVersion: state.imageVersion + 1,
      );

      await supabase
          .from('clients')
          .update({'avatar': publicUrl})
          .eq('supabase_id', userId);

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }
}
