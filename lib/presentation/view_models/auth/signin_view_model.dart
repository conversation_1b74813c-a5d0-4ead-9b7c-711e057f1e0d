import 'package:build_mate/data/shared_preferences/preferences_provider.dart';
import 'package:build_mate/main.dart';
import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/state/signin_flow_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SigninViewModel extends StateNotifier<SigninFlowState> {
  SigninViewModel(super.state, this.ref);

  final Ref ref;
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool obscurePassword = true;
  bool rememberMe = false;

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email);
  }

  void updatePassword(String password) {
    state = state.copyWith(password: password);
  }

  void updateIsLoggingIn(bool isLoggingIn) {
    state = state.copyWith(isLoggingIn: isLoggingIn);
  }

  void resetState() {
    state = SigninFlowState();
  }

  void toggleObscurePassword() {
    obscurePassword = !obscurePassword;
    // We need to notify listeners since this isn't part of the state
    ref.notifyListeners();
  }

  void toggleRememberMe(bool? value) {
    rememberMe = value ?? false;
    // We need to notify listeners since this isn't part of the state
    ref.notifyListeners();
  }

  Future<void> handleSignIn({required BuildContext context}) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    updateIsLoggingIn(true);

    try {
      // Use the email and password from the controllers
      final email = emailController.text.trim();
      final password = passwordController.text;

      if (kDebugMode) {
        print('Attempting to sign in with email: $email');
      }

      // Sign in with Supabase
      final AuthResponse response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        if (kDebugMode) {
          print('Sign in successful for user: ${response.user!.id}');
        }

        // Store user ID in preferences
        ref.read(preferencesProvider).setSupabaseId(id: response.user!.id);

        // Navigate to appropriate screen based on user profile
        if (context.mounted) {
          await checkUserProfile();
        }
      } else {
        throw 'Sign in failed: No user returned';
      }
    } catch (error) {
      if (kDebugMode) {
        print('Sign in error: $error');
      }

      if (context.mounted) {
        String errorMessage = 'Sign in failed';

        // Handle specific Supabase auth errors
        if (error.toString().contains('Invalid login credentials')) {
          errorMessage = 'Invalid email or password';
        } else if (error.toString().contains('Email not confirmed')) {
          errorMessage = 'Please verify your email address';
        } else if (error.toString().contains('Too many requests')) {
          errorMessage = 'Too many attempts. Please try again later';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    } finally {
      updateIsLoggingIn(false);
    }
  }

  Future<void> checkUserProfile() async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) return;

      // First, check if user exists in clients table
      final clientResponse =
          await supabase
              .from('clients')
              .select()
              .eq('supabase_id', userId)
              .maybeSingle();

      // If found in clients table, navigate to client home
      if (clientResponse != null) {
        if (kDebugMode) {
          print('User profile found in clients table: $clientResponse');
        }
        ref.read(goRouterProvider).goNamed(RouteConstants.CLIENT_HOME_SCREEN);
        return;
      }

      // If not found in clients, check artisans table
      final artisanResponse =
          await supabase
              .from('artisans')
              .select()
              .eq('supabase_id', userId)
              .maybeSingle();

      // If found in artisans table, navigate to service provider home
      if (artisanResponse != null) {
        if (kDebugMode) {
          print('User profile found in artisans table: $artisanResponse');
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.SERVICE_PROVIDER_HOME_SCREEN);
        return;
      }

      // If not found in either table, check preferences for role selection
      final isClient = ref.read(preferencesProvider).getIsClient();

      if (isClient) {
        // Navigate to client profile creation
        if (kDebugMode) {
          print(
            'User not found in any table, navigating to client profile creation',
          );
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.USER_PROFILE_DETAILS_SCREEN);
      } else {
        // Navigate to artisan profile creation
        if (kDebugMode) {
          print(
            'User not found in any table, navigating to artisan profile creation',
          );
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.ARTISAN_PROFILE_DETAILS_SCREEN);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking user profile: $e');
      }

      // If there's an error, check preferences for role selection
      final isClient = ref.read(preferencesProvider).getIsClient();

      if (isClient) {
        // Default to client profile creation
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.USER_PROFILE_DETAILS_SCREEN);
      } else {
        // Default to artisan profile creation
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.ARTISAN_PROFILE_DETAILS_SCREEN);
      }
    }
  }

  Future<void> handleGoogleSignIn({required BuildContext context}) async {
    updateIsLoggingIn(true);

    try {
      /// Web Client ID that you registered with Google Cloud.
      const webClientId =
          '612682401448-21v9j45thn5a77j1mkbu785pafc3ck6g.apps.googleusercontent.com';

      /// iOS Client ID that you registered with Google Cloud.
      const iosClientId =
          '612682401448-78n6p4oulj9g05ng08f99qi3bgec6s6m.apps.googleusercontent.com';

      if (kIsWeb) {
        // Web implementation with proper configuration
        if (kDebugMode) {
          print('Starting Google Sign-In for Web');
        }

        // Use Supabase's OAuth sign-in with Google for web
        await supabase.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo:
              'https://zdphpgvpgyswcumwnvhp.supabase.co/auth/v1/callback',
        );

        if (kDebugMode) {
          // print('Supabase OAuth initiated: ${res.url != null}');
        }

        // For web, we need to listen for auth state changes to handle the redirect back
        supabase.auth.onAuthStateChange.listen((data) async {
          final AuthChangeEvent event = data.event;
          final Session? session = data.session;

          if (event == AuthChangeEvent.signedIn && session != null) {
            if (kDebugMode) {
              print('User signed in: ${session.user.id}');
            }

            // Store user ID in preferences
            ref.read(preferencesProvider).setSupabaseId(id: session.user.id);

            // Check user profile and navigate to appropriate screen
            try {
              await checkUserProfile();
            } catch (e) {
              if (kDebugMode) {
                print('Error navigating after web auth: $e');
              }
              // Fallback navigation if profile check fails
              ref.read(goRouterProvider).go('/');
            }
          }
        });
      } else {
        // Mobile implementation can use serverClientId
        final GoogleSignIn googleSignIn = GoogleSignIn(
          clientId: iosClientId,
          serverClientId: webClientId,
          scopes: ['email', 'profile'],
        );

        final googleUser = await googleSignIn.signIn();
        if (googleUser == null) {
          updateIsLoggingIn(false);
          return;
        }

        final googleAuth = await googleUser.authentication;
        final accessToken = googleAuth.accessToken;
        final idToken = googleAuth.idToken;

        if (accessToken == null) {
          throw 'No Access Token found.';
        }
        if (idToken == null) {
          throw 'No ID Token found.';
        }

        final AuthResponse res = await supabase.auth.signInWithIdToken(
          provider: OAuthProvider.google,
          idToken: idToken,
          accessToken: accessToken,
        );

        if (res.user != null) {
          // Store user ID in preferences if needed
          ref.read(preferencesProvider).setSupabaseId(id: res.user!.id);

          // Navigate to appropriate screen on successful sign in
          if (context.mounted) {
            await checkUserProfile();
          }
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign-In Error: $error');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      updateIsLoggingIn(false);
    }
  }

  Future<void> handleGoogleSignInWithTokens({
    required BuildContext context,
    required String? idToken,
    required String? accessToken,
  }) async {
    updateIsLoggingIn(true);

    try {
      if (kDebugMode) {
        print('Signing in with Google tokens');
        print(
          'ID Token: ${idToken != null ? '${idToken.substring(0, 10)}...' : 'null'}',
        );
        print(
          'Access Token: ${accessToken != null ? '${accessToken.substring(0, 10)}...' : 'null'}',
        );
      }

      if (kIsWeb && accessToken != null) {
        if (kDebugMode) {
          print('Web sign-in with access token');
        }

        // For web, initiate a direct OAuth flow instead of trying to use the token
        // This is more reliable for web authentication
        final redirectUrl = '${Uri.base.origin}/auth/callback';
        if (kDebugMode) {
          print('Redirect URL: $redirectUrl');
        }

        await supabase.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo: redirectUrl,
        );

        if (kDebugMode) {
          print('OAuth flow initiated, page will redirect');
        }

        // The page will redirect to Google and then back to the callback URL
        return;
      } else if (idToken != null && accessToken != null) {
        // For mobile, use ID token sign-in
        final AuthResponse res = await supabase.auth.signInWithIdToken(
          provider: OAuthProvider.google,
          idToken: idToken,
          accessToken: accessToken,
        );

        if (res.user != null) {
          // Store user ID in preferences
          ref.read(preferencesProvider).setSupabaseId(id: res.user!.id);

          // Check user profile and navigate to appropriate screen
          if (context.mounted) {
            await checkUserProfile();
          }
        }
      } else {
        throw 'Missing required tokens for authentication';
      }
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign-In Error: $error');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      updateIsLoggingIn(false);
    }
  }

  Future<void> handleAppleSignIn({required BuildContext context}) async {
    updateIsLoggingIn(true);

    try {
      await Future.delayed(const Duration(seconds: 1));

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Apple Sign In not implemented yet')),
        );
      }
    } catch (error) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      updateIsLoggingIn(false);
    }
  }

  Future<void> handleFacebookSignIn({required BuildContext context}) async {
    updateIsLoggingIn(true);

    try {
      await Future.delayed(const Duration(seconds: 1));

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Facebook Sign In not implemented yet')),
        );
      }
    } catch (error) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      updateIsLoggingIn(false);
    }
  }

  void navigateToSignUp({required BuildContext context}) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sign Up not implemented yet')),
    );
  }

  void handleResetPassword({required BuildContext context}) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reset Password not implemented yet')),
    );
  }
}

final signinViewModelProvider =
    StateNotifierProvider<SigninViewModel, SigninFlowState>((ref) {
      return SigninViewModel(SigninFlowState(), ref);
    });
