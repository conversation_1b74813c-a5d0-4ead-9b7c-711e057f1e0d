import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AnimatedRippleMarker extends StatefulWidget {
  final LatLng position;
  final String imageUrl;
  final bool isUser;
  final VoidCallback? onTap;
  final GoogleMapController? mapController;

  const AnimatedRippleMarker({
    super.key,
    required this.position,
    required this.imageUrl,
    required this.mapController,
    this.isUser = false,
    this.onTap,
  });

  @override
  State<AnimatedRippleMarker> createState() => _AnimatedRippleMarkerState();
}

class _AnimatedRippleMarkerState extends State<AnimatedRippleMarker>
    with TickerProviderStateMixin {
  late AnimationController _rippleController;
  Offset? _screenPosition;

  @override
  void initState() {
    super.initState();
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
    _updatePosition();
  }

  @override
  void dispose() {
    _rippleController.dispose();
    super.dispose();
  }

  Future<void> _updatePosition() async {
    if (widget.mapController == null) return;
    final screenPoint = await widget.mapController!.getScreenCoordinate(widget.position);
    if (mounted) {
      setState(() {
        _screenPosition = Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_screenPosition == null) return const SizedBox();

    return Positioned(
      left: _screenPosition!.dx - 50,
      top: _screenPosition!.dy - 50,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _rippleController,
          builder: (context, child) {
            return CustomPaint(
              size: const Size(100, 100),
              painter: RipplePainter(
                progress: _rippleController.value,
                isUser: widget.isUser,
              ),
            );
          },
        ),
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final double progress;
  final bool isUser;

  RipplePainter({
    required this.progress,
    required this.isUser,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseColor = isUser ? Colors.blue : Colors.orange;
    
    for (int i = 0; i < 3; i++) {
      final phase = (progress - (i * 0.3)) % 1.0;
      if (phase > 0) {
        final radius = phase * (size.width / 2);
        final opacity = (1.0 - phase) * 0.4;
        
        final paint = Paint()
          ..color = baseColor.withAlpha((opacity*255).round())
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
        
        canvas.drawCircle(center, radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) => true;
}
