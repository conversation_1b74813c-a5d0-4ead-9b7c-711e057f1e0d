import 'package:flutter/material.dart';

class RippleMarker extends StatefulWidget {
  final Widget child;
  final Color rippleColor;
  final double size;

  const RippleMarker({
    super.key,
    required this.child,
    this.rippleColor = Colors.orange,
    this.size = 50,
  });

  @override
  State<RippleMarker> createState() => _RippleMarkerState();
}

class _RippleMarkerState extends State<RippleMarker> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: RipplePainter(
        color: widget.rippleColor,
        animationValue: _controller,
      ),
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: Center(child: widget.child),
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final Color color;
  final Animation<double> animationValue;

  RipplePainter({
    required this.color,
    required this.animationValue,
  }) : super(repaint: animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color.withAlpha((((1 - animationValue.value) * 0.4)*255).round())
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      (size.width / 2) * animationValue.value,
      paint,
    );

    if (animationValue.value < 0.5) {
      canvas.drawCircle(
        Offset(size.width / 2, size.height / 2),
        (size.width / 2) * (animationValue.value + 0.5),
        paint..color = color.withAlpha((((0.5 - animationValue.value) * 0.4)*255).round()),
      );
    }
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) => true;
}
