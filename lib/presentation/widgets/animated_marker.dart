import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AnimatedMapMarker extends StatefulWidget {
  final LatLng position;
  final GoogleMapController? mapController;
  final VoidCallback? onTap;

  const AnimatedMapMarker({
    super.key,
    required this.position,
    required this.mapController,
    this.onTap,
  });

  @override
  State<AnimatedMapMarker> createState() => _AnimatedMapMarkerState();
}

class _AnimatedMapMarkerState extends State<AnimatedMapMarker>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Offset? _screenPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _updatePosition();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updatePosition() {
    if (widget.mapController == null) return;
    widget.mapController!.getScreenCoordinate(widget.position).then((point) {
      if (mounted) {
        setState(() {
          _screenPosition = Offset(point.x.toDouble(), point.y.toDouble());
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_screenPosition == null) return const SizedBox();

    return Positioned(
      left: _screenPosition!.dx - 20,
      top: _screenPosition!.dy - 20,
      child: RepaintBoundary(
        child: SizedBox(
          width: 40,
          height: 40,
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  // Multiple ripple layers
                  for (var i = 0; i < 3; i++)
                    Positioned.fill(
                      child: CustomPaint(
                        painter: _RipplePainter(
                          progress: (_controller.value - (i * 0.33)) % 1.0,
                          color: Colors.orange,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

class _RipplePainter extends CustomPainter {
  final double progress;
  final Color color;

  _RipplePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withAlpha(((1 - progress) * 0.4 * 255).round())
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    final radius = (size.width / 2) * progress;
    canvas.drawCircle(Offset(size.width / 2, size.height / 2), radius, paint);
  }

  @override
  bool shouldRepaint(_RipplePainter oldDelegate) =>
      oldDelegate.progress != progress;
}
