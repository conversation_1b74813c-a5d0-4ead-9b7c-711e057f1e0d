import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RadarMarkerOverlay extends StatefulWidget {
  final LatLng position;
  final GoogleMapController? mapController;
  final Color color;

  const RadarMarkerOverlay({
    super.key,
    required this.position,
    required this.mapController,
    this.color = Colors.orange,
  });

  @override
  State<RadarMarkerOverlay> createState() => _RadarMarkerOverlayState();
}

class _RadarMarkerOverlayState extends State<RadarMarkerOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Offset? _screenPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    _updatePosition();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _updatePosition() async {
    if (widget.mapController == null) return;
    final screenPoint = await widget.mapController!.getScreenCoordinate(widget.position);
    if (mounted) {
      setState(() {
        _screenPosition = Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_screenPosition == null) return const SizedBox();

    return Positioned(
      left: _screenPosition!.dx - 50,
      top: _screenPosition!.dy - 50,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            size: const Size(100, 100),
            painter: RadarPainter(
              color: widget.color,
              animation: _controller,
            ),
          );
        },
      ),
    );
  }
}

class RadarPainter extends CustomPainter {
  final Color color;
  final Animation<double> animation;

  RadarPainter({
    required this.color,
    required this.animation,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw multiple ripple circles with different phases
    for (var i = 0; i < 3; i++) {
      final progress = (animation.value - (i * 0.333)) % 1.0;
      final paint = Paint()
        ..color = color.withAlpha((((1.0 - progress) * 0.3)*255).round())
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final radius = size.width * 0.25 * (1.0 + progress);
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant RadarPainter oldDelegate) => true;
}
