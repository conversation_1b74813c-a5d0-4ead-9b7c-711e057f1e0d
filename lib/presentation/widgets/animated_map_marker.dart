import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AnimatedMapMarker extends StatefulWidget {
  final LatLng position;
  final Color color;
  final GoogleMapController? mapController;

  const AnimatedMapMarker({
    super.key,
    required this.position,
    required this.color,
    required this.mapController,
  });

  @override
  State<AnimatedMapMarker> createState() => _AnimatedMapMarkerState();
}

class _AnimatedMapMarkerState extends State<AnimatedMapMarker>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Offset? _screenPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(); // Make it loop continuously
    _updateScreenPosition();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _updateScreenPosition() async {
    if (widget.mapController == null) return;
    final screenPoint = await widget.mapController!.getScreenCoordinate(widget.position);
    if (mounted) {
      setState(() {
        _screenPosition = Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_screenPosition == null) return const SizedBox();

    return Positioned(
      left: _screenPosition!.dx - 50,
      top: _screenPosition!.dy - 50,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Stack(
            children: [
              // Multiple ripple layers
              for (var i = 0; i < 3; i++)
                CustomPaint(
                  size: const Size(100, 100),
                  painter: RipplePainter(
                    color: widget.color,
                    animationValue: (_controller.value + (i * 0.3)) % 1.0,
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final Color color;
  final double animationValue;

  RipplePainter({
    required this.color,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color.withAlpha(((1 - animationValue) * 0.3 * 255).round())
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final radius = (size.width / 2) * (0.5 + animationValue * 0.5);
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      radius,
      paint,
    );
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) => true;
}

class MapMarkerRipple extends StatelessWidget {
  final bool isSelected;

  const MapMarkerRipple({
    super.key,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (!isSelected) return const SizedBox();

    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.orange.withAlpha(((1 - value)*255).round()),
              width: 2,
            ),
          ),
        );
      },
      onEnd: () {
        if (isSelected) {
          (context as Element).markNeedsBuild();
        }
      },
    );
  }
}
