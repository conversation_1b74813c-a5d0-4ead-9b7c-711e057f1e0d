import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MarkerRipple extends StatefulWidget {
  final LatLng position;
  final GoogleMapController? mapController;

  const MarkerRipple({
    super.key,
    required this.position,
    required this.mapController, required bool isSelected,
  });

  @override
  State<MarkerRipple> createState() => _MarkerRippleState();
}

class _MarkerRippleState extends State<MarkerRipple> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Offset? _screenPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(); // Make the animation loop continuously
    _updatePosition();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _updatePosition() async {
    if (widget.mapController == null) return;
    final screenPoint = await widget.mapController!.getScreenCoordinate(widget.position);
    if (mounted) {
      setState(() {
        _screenPosition = Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_screenPosition == null) return const SizedBox();

    return Positioned(
      left: _screenPosition!.dx - 50,
      top: _screenPosition!.dy - 50,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            size: const Size(100, 100),
            painter: RipplePainter(
              animation: _controller,
            ),
          );
        },
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final Animation<double> animation;
  final List<Color> rippleColors = [
    Colors.orange.withAlpha((0.3*255).round()),
    Colors.orange.withAlpha((0.2*255).round()),
    Colors.orange.withAlpha((0.1*255).round()),
  ];

  RipplePainter({required this.animation}) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw multiple ripple layers with different phases
    for (int i = 0; i < 3; i++) {
      final progress = (animation.value + (i * 0.33)) % 1.0;
      final radius = size.width * 0.3 * (1 + progress);
      
      final paint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0
        ..color = rippleColors[i].withAlpha(((1 - progress)*255).round());

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) => true;
}
