import 'package:flutter/material.dart';

class ImageContainer extends StatelessWidget {
  final List<String> images;
  final List<bool> loadingStates;
  final Function(int) onPageChanged;
  final int currentIndex;

  const ImageContainer({
    super.key,
    required this.images,
    required this.loadingStates,
    required this.onPageChanged,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PageView.builder(
          itemCount: images.length,
          onPageChanged: onPageChanged,
          itemBuilder: (context, index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child:
                  loadingStates[index]
                      ? const Center(child: CircularProgressIndicator())
                      : ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(images[index], fit: BoxFit.cover),
                      ),
            );
          },
        ),
        Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children:
                images.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          currentIndex == entry.key
                              ? Theme.of(context).primaryColor
                              : Colors.grey.withAlpha((0.5 * 255).round()),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
