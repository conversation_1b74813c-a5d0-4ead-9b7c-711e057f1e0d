import 'package:build_mate/data/dto/shop_product_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shop_products_state.freezed.dart';

@freezed
abstract class ShopProductsState with _$ShopProductsState {
   factory ShopProductsState({
    @Default(false) bool isLoadingProducts,
    @Default(0) int branchId,
    @Default([]) List<ShopProductResponse> products,
    @Default('') String shopname,
    @Default('') String branchName,
    @Default([]) List<dynamic> subcategories,
    @Default([]) List<dynamic> filteredSubcategories,
    @Default('') String search,
    int? selectedSubcategoryId,
    String? selectedSubcategoryName,
   }) = _ShopProductsState;
}