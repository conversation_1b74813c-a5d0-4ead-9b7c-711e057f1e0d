import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_settings_tab_state.freezed.dart';

@freezed
abstract class ArtisanSettingsTabState with _$ArtisanSettingsTabState {
  factory ArtisanSettingsTabState({
    @Default(false) bool isLoading,
    @Default('') String profileUrl,
    @Default('') String username,
    @Default('') String currentLocation,
    @Default(false) bool isAvailable,
    @Default(false) bool darkMode,
    @Default(true) bool notifications,
    @Default(false) bool playInBackground,
  }) = _ArtisanSettingsTabState;
}
