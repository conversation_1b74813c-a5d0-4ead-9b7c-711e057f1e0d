import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:build_mate/data/dto/branch_distance_response.dart';
import 'package:build_mate/data/models/client_model.dart';

part 'shops_state.freezed.dart';

@freezed
abstract class ShopsState with _$ShopsState {
  factory ShopsState({
    @Default(false) bool isLoading,
    @Default([]) List<BranchDistance> nearbyShops,
    @Default({}) Set<Marker> markers,
    String? error,
    Position? userLocation,
    ClientModel? clientInfo,
    Marker? userMarker,
    BranchDistance? selectedShop,
    @Default(false) bool ishopDetailsSheetVisible,
    @Default(false) bool isAdjusttingDistance,
    String? selectedMarkerId,
    @Default(10) double radius,
    @Default(false) bool isAdjusting,
    @Default(-1) int selectedBranchId,
    @Default('') String selectedShopname,
    @Default('') String selectedBranchName,
    @Default(0) int branchCount,
    @Default(LatLng(0.0, 0.0 )) LatLng currentLocation,
    CameraPosition? initialPosition,
    @Default('') String searchQuery,
    @Default(false) bool isSearchingProduct,
  }) = _ShopsState;
}
