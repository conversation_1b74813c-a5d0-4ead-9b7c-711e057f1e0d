// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_final_profile_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ArtisanFinalProfileDetailsState {

 String get selectedMainCategory; int get selectedMainCategoryId; List<String> get selectedSubCategories; List<String> get relatedSubCategories; Map<String, int> get subcategoryIdMap; String get about; String get nationalIdImage; bool get isLoading;
/// Create a copy of ArtisanFinalProfileDetailsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanFinalProfileDetailsStateCopyWith<ArtisanFinalProfileDetailsState> get copyWith => _$ArtisanFinalProfileDetailsStateCopyWithImpl<ArtisanFinalProfileDetailsState>(this as ArtisanFinalProfileDetailsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanFinalProfileDetailsState&&(identical(other.selectedMainCategory, selectedMainCategory) || other.selectedMainCategory == selectedMainCategory)&&(identical(other.selectedMainCategoryId, selectedMainCategoryId) || other.selectedMainCategoryId == selectedMainCategoryId)&&const DeepCollectionEquality().equals(other.selectedSubCategories, selectedSubCategories)&&const DeepCollectionEquality().equals(other.relatedSubCategories, relatedSubCategories)&&const DeepCollectionEquality().equals(other.subcategoryIdMap, subcategoryIdMap)&&(identical(other.about, about) || other.about == about)&&(identical(other.nationalIdImage, nationalIdImage) || other.nationalIdImage == nationalIdImage)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading));
}


@override
int get hashCode => Object.hash(runtimeType,selectedMainCategory,selectedMainCategoryId,const DeepCollectionEquality().hash(selectedSubCategories),const DeepCollectionEquality().hash(relatedSubCategories),const DeepCollectionEquality().hash(subcategoryIdMap),about,nationalIdImage,isLoading);

@override
String toString() {
  return 'ArtisanFinalProfileDetailsState(selectedMainCategory: $selectedMainCategory, selectedMainCategoryId: $selectedMainCategoryId, selectedSubCategories: $selectedSubCategories, relatedSubCategories: $relatedSubCategories, subcategoryIdMap: $subcategoryIdMap, about: $about, nationalIdImage: $nationalIdImage, isLoading: $isLoading)';
}


}

/// @nodoc
abstract mixin class $ArtisanFinalProfileDetailsStateCopyWith<$Res>  {
  factory $ArtisanFinalProfileDetailsStateCopyWith(ArtisanFinalProfileDetailsState value, $Res Function(ArtisanFinalProfileDetailsState) _then) = _$ArtisanFinalProfileDetailsStateCopyWithImpl;
@useResult
$Res call({
 String selectedMainCategory, int selectedMainCategoryId, List<String> selectedSubCategories, List<String> relatedSubCategories, Map<String, int> subcategoryIdMap, String about, String nationalIdImage, bool isLoading
});




}
/// @nodoc
class _$ArtisanFinalProfileDetailsStateCopyWithImpl<$Res>
    implements $ArtisanFinalProfileDetailsStateCopyWith<$Res> {
  _$ArtisanFinalProfileDetailsStateCopyWithImpl(this._self, this._then);

  final ArtisanFinalProfileDetailsState _self;
  final $Res Function(ArtisanFinalProfileDetailsState) _then;

/// Create a copy of ArtisanFinalProfileDetailsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedMainCategory = null,Object? selectedMainCategoryId = null,Object? selectedSubCategories = null,Object? relatedSubCategories = null,Object? subcategoryIdMap = null,Object? about = null,Object? nationalIdImage = null,Object? isLoading = null,}) {
  return _then(_self.copyWith(
selectedMainCategory: null == selectedMainCategory ? _self.selectedMainCategory : selectedMainCategory // ignore: cast_nullable_to_non_nullable
as String,selectedMainCategoryId: null == selectedMainCategoryId ? _self.selectedMainCategoryId : selectedMainCategoryId // ignore: cast_nullable_to_non_nullable
as int,selectedSubCategories: null == selectedSubCategories ? _self.selectedSubCategories : selectedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,relatedSubCategories: null == relatedSubCategories ? _self.relatedSubCategories : relatedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,subcategoryIdMap: null == subcategoryIdMap ? _self.subcategoryIdMap : subcategoryIdMap // ignore: cast_nullable_to_non_nullable
as Map<String, int>,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nationalIdImage: null == nationalIdImage ? _self.nationalIdImage : nationalIdImage // ignore: cast_nullable_to_non_nullable
as String,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _ArtisanFinalProfileDetailsState implements ArtisanFinalProfileDetailsState {
   _ArtisanFinalProfileDetailsState({this.selectedMainCategory = '', this.selectedMainCategoryId = 0, final  List<String> selectedSubCategories = const [], final  List<String> relatedSubCategories = const [], final  Map<String, int> subcategoryIdMap = const {}, this.about = '', this.nationalIdImage = '', this.isLoading = false}): _selectedSubCategories = selectedSubCategories,_relatedSubCategories = relatedSubCategories,_subcategoryIdMap = subcategoryIdMap;
  

@override@JsonKey() final  String selectedMainCategory;
@override@JsonKey() final  int selectedMainCategoryId;
 final  List<String> _selectedSubCategories;
@override@JsonKey() List<String> get selectedSubCategories {
  if (_selectedSubCategories is EqualUnmodifiableListView) return _selectedSubCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_selectedSubCategories);
}

 final  List<String> _relatedSubCategories;
@override@JsonKey() List<String> get relatedSubCategories {
  if (_relatedSubCategories is EqualUnmodifiableListView) return _relatedSubCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_relatedSubCategories);
}

 final  Map<String, int> _subcategoryIdMap;
@override@JsonKey() Map<String, int> get subcategoryIdMap {
  if (_subcategoryIdMap is EqualUnmodifiableMapView) return _subcategoryIdMap;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_subcategoryIdMap);
}

@override@JsonKey() final  String about;
@override@JsonKey() final  String nationalIdImage;
@override@JsonKey() final  bool isLoading;

/// Create a copy of ArtisanFinalProfileDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanFinalProfileDetailsStateCopyWith<_ArtisanFinalProfileDetailsState> get copyWith => __$ArtisanFinalProfileDetailsStateCopyWithImpl<_ArtisanFinalProfileDetailsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanFinalProfileDetailsState&&(identical(other.selectedMainCategory, selectedMainCategory) || other.selectedMainCategory == selectedMainCategory)&&(identical(other.selectedMainCategoryId, selectedMainCategoryId) || other.selectedMainCategoryId == selectedMainCategoryId)&&const DeepCollectionEquality().equals(other._selectedSubCategories, _selectedSubCategories)&&const DeepCollectionEquality().equals(other._relatedSubCategories, _relatedSubCategories)&&const DeepCollectionEquality().equals(other._subcategoryIdMap, _subcategoryIdMap)&&(identical(other.about, about) || other.about == about)&&(identical(other.nationalIdImage, nationalIdImage) || other.nationalIdImage == nationalIdImage)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading));
}


@override
int get hashCode => Object.hash(runtimeType,selectedMainCategory,selectedMainCategoryId,const DeepCollectionEquality().hash(_selectedSubCategories),const DeepCollectionEquality().hash(_relatedSubCategories),const DeepCollectionEquality().hash(_subcategoryIdMap),about,nationalIdImage,isLoading);

@override
String toString() {
  return 'ArtisanFinalProfileDetailsState(selectedMainCategory: $selectedMainCategory, selectedMainCategoryId: $selectedMainCategoryId, selectedSubCategories: $selectedSubCategories, relatedSubCategories: $relatedSubCategories, subcategoryIdMap: $subcategoryIdMap, about: $about, nationalIdImage: $nationalIdImage, isLoading: $isLoading)';
}


}

/// @nodoc
abstract mixin class _$ArtisanFinalProfileDetailsStateCopyWith<$Res> implements $ArtisanFinalProfileDetailsStateCopyWith<$Res> {
  factory _$ArtisanFinalProfileDetailsStateCopyWith(_ArtisanFinalProfileDetailsState value, $Res Function(_ArtisanFinalProfileDetailsState) _then) = __$ArtisanFinalProfileDetailsStateCopyWithImpl;
@override @useResult
$Res call({
 String selectedMainCategory, int selectedMainCategoryId, List<String> selectedSubCategories, List<String> relatedSubCategories, Map<String, int> subcategoryIdMap, String about, String nationalIdImage, bool isLoading
});




}
/// @nodoc
class __$ArtisanFinalProfileDetailsStateCopyWithImpl<$Res>
    implements _$ArtisanFinalProfileDetailsStateCopyWith<$Res> {
  __$ArtisanFinalProfileDetailsStateCopyWithImpl(this._self, this._then);

  final _ArtisanFinalProfileDetailsState _self;
  final $Res Function(_ArtisanFinalProfileDetailsState) _then;

/// Create a copy of ArtisanFinalProfileDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedMainCategory = null,Object? selectedMainCategoryId = null,Object? selectedSubCategories = null,Object? relatedSubCategories = null,Object? subcategoryIdMap = null,Object? about = null,Object? nationalIdImage = null,Object? isLoading = null,}) {
  return _then(_ArtisanFinalProfileDetailsState(
selectedMainCategory: null == selectedMainCategory ? _self.selectedMainCategory : selectedMainCategory // ignore: cast_nullable_to_non_nullable
as String,selectedMainCategoryId: null == selectedMainCategoryId ? _self.selectedMainCategoryId : selectedMainCategoryId // ignore: cast_nullable_to_non_nullable
as int,selectedSubCategories: null == selectedSubCategories ? _self._selectedSubCategories : selectedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,relatedSubCategories: null == relatedSubCategories ? _self._relatedSubCategories : relatedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,subcategoryIdMap: null == subcategoryIdMap ? _self._subcategoryIdMap : subcategoryIdMap // ignore: cast_nullable_to_non_nullable
as Map<String, int>,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nationalIdImage: null == nationalIdImage ? _self.nationalIdImage : nationalIdImage // ignore: cast_nullable_to_non_nullable
as String,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
