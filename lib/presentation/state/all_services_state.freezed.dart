// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'all_services_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AllServicesState {

 List<Map<String, dynamic>> get categories; List<ServiceIconsResponse>? get services; bool get isLoading; int get selectedServiceId; String get selectedServiceName;
/// Create a copy of AllServicesState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AllServicesStateCopyWith<AllServicesState> get copyWith => _$AllServicesStateCopyWithImpl<AllServicesState>(this as AllServicesState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AllServicesState&&const DeepCollectionEquality().equals(other.categories, categories)&&const DeepCollectionEquality().equals(other.services, services)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.selectedServiceId, selectedServiceId) || other.selectedServiceId == selectedServiceId)&&(identical(other.selectedServiceName, selectedServiceName) || other.selectedServiceName == selectedServiceName));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(categories),const DeepCollectionEquality().hash(services),isLoading,selectedServiceId,selectedServiceName);

@override
String toString() {
  return 'AllServicesState(categories: $categories, services: $services, isLoading: $isLoading, selectedServiceId: $selectedServiceId, selectedServiceName: $selectedServiceName)';
}


}

/// @nodoc
abstract mixin class $AllServicesStateCopyWith<$Res>  {
  factory $AllServicesStateCopyWith(AllServicesState value, $Res Function(AllServicesState) _then) = _$AllServicesStateCopyWithImpl;
@useResult
$Res call({
 List<Map<String, dynamic>> categories, List<ServiceIconsResponse>? services, bool isLoading, int selectedServiceId, String selectedServiceName
});




}
/// @nodoc
class _$AllServicesStateCopyWithImpl<$Res>
    implements $AllServicesStateCopyWith<$Res> {
  _$AllServicesStateCopyWithImpl(this._self, this._then);

  final AllServicesState _self;
  final $Res Function(AllServicesState) _then;

/// Create a copy of AllServicesState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categories = null,Object? services = freezed,Object? isLoading = null,Object? selectedServiceId = null,Object? selectedServiceName = null,}) {
  return _then(_self.copyWith(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,services: freezed == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as List<ServiceIconsResponse>?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedServiceId: null == selectedServiceId ? _self.selectedServiceId : selectedServiceId // ignore: cast_nullable_to_non_nullable
as int,selectedServiceName: null == selectedServiceName ? _self.selectedServiceName : selectedServiceName // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _AllServicesState implements AllServicesState {
   _AllServicesState({final  List<Map<String, dynamic>> categories = const [], final  List<ServiceIconsResponse>? services = const [], this.isLoading = false, this.selectedServiceId = 0, this.selectedServiceName = ''}): _categories = categories,_services = services;
  

 final  List<Map<String, dynamic>> _categories;
@override@JsonKey() List<Map<String, dynamic>> get categories {
  if (_categories is EqualUnmodifiableListView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_categories);
}

 final  List<ServiceIconsResponse>? _services;
@override@JsonKey() List<ServiceIconsResponse>? get services {
  final value = _services;
  if (value == null) return null;
  if (_services is EqualUnmodifiableListView) return _services;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  int selectedServiceId;
@override@JsonKey() final  String selectedServiceName;

/// Create a copy of AllServicesState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AllServicesStateCopyWith<_AllServicesState> get copyWith => __$AllServicesStateCopyWithImpl<_AllServicesState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AllServicesState&&const DeepCollectionEquality().equals(other._categories, _categories)&&const DeepCollectionEquality().equals(other._services, _services)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.selectedServiceId, selectedServiceId) || other.selectedServiceId == selectedServiceId)&&(identical(other.selectedServiceName, selectedServiceName) || other.selectedServiceName == selectedServiceName));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_categories),const DeepCollectionEquality().hash(_services),isLoading,selectedServiceId,selectedServiceName);

@override
String toString() {
  return 'AllServicesState(categories: $categories, services: $services, isLoading: $isLoading, selectedServiceId: $selectedServiceId, selectedServiceName: $selectedServiceName)';
}


}

/// @nodoc
abstract mixin class _$AllServicesStateCopyWith<$Res> implements $AllServicesStateCopyWith<$Res> {
  factory _$AllServicesStateCopyWith(_AllServicesState value, $Res Function(_AllServicesState) _then) = __$AllServicesStateCopyWithImpl;
@override @useResult
$Res call({
 List<Map<String, dynamic>> categories, List<ServiceIconsResponse>? services, bool isLoading, int selectedServiceId, String selectedServiceName
});




}
/// @nodoc
class __$AllServicesStateCopyWithImpl<$Res>
    implements _$AllServicesStateCopyWith<$Res> {
  __$AllServicesStateCopyWithImpl(this._self, this._then);

  final _AllServicesState _self;
  final $Res Function(_AllServicesState) _then;

/// Create a copy of AllServicesState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categories = null,Object? services = freezed,Object? isLoading = null,Object? selectedServiceId = null,Object? selectedServiceName = null,}) {
  return _then(_AllServicesState(
categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,services: freezed == services ? _self._services : services // ignore: cast_nullable_to_non_nullable
as List<ServiceIconsResponse>?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedServiceId: null == selectedServiceId ? _self.selectedServiceId : selectedServiceId // ignore: cast_nullable_to_non_nullable
as int,selectedServiceName: null == selectedServiceName ? _self.selectedServiceName : selectedServiceName // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
