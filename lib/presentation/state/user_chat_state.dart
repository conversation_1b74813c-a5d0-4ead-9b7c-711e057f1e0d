import 'package:build_mate/data/models/chat_models.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'user_chat_state.freezed.dart';

@freezed
abstract class UserChatState with _$UserChatState {
   factory UserChatState({
    @Default(false) bool isLoading,
     required AsyncValue<List<Conversation>> conversations,
}) = _UserChatState;
}