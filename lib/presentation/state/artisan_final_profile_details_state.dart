import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_final_profile_details_state.freezed.dart';

@freezed
abstract class ArtisanFinalProfileDetailsState with _$ArtisanFinalProfileDetailsState {
  factory ArtisanFinalProfileDetailsState({
    @Default('') String selectedMainCategory,
    @Default(0) int selectedMainCategoryId,
    @Default([]) List<String> selectedSubCategories,
    @Default([]) List<String> relatedSubCategories,
    @Default({}) Map<String, int> subcategoryIdMap,
    @Default('') String about,
    @Default('') String nationalIdImage,
    @Default(false) bool isLoading,
  }) = _ArtisanFinalProfileDetailsState;
}
