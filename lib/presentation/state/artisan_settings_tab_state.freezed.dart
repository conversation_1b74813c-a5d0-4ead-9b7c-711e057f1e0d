// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_settings_tab_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ArtisanSettingsTabState {

 bool get isLoading; String get profileUrl; String get username; String get currentLocation; bool get isAvailable; bool get darkMode; bool get notifications; bool get playInBackground;
/// Create a copy of ArtisanSettingsTabState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanSettingsTabStateCopyWith<ArtisanSettingsTabState> get copyWith => _$ArtisanSettingsTabStateCopyWithImpl<ArtisanSettingsTabState>(this as ArtisanSettingsTabState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanSettingsTabState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.darkMode, darkMode) || other.darkMode == darkMode)&&(identical(other.notifications, notifications) || other.notifications == notifications)&&(identical(other.playInBackground, playInBackground) || other.playInBackground == playInBackground));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,currentLocation,isAvailable,darkMode,notifications,playInBackground);

@override
String toString() {
  return 'ArtisanSettingsTabState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, currentLocation: $currentLocation, isAvailable: $isAvailable, darkMode: $darkMode, notifications: $notifications, playInBackground: $playInBackground)';
}


}

/// @nodoc
abstract mixin class $ArtisanSettingsTabStateCopyWith<$Res>  {
  factory $ArtisanSettingsTabStateCopyWith(ArtisanSettingsTabState value, $Res Function(ArtisanSettingsTabState) _then) = _$ArtisanSettingsTabStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String profileUrl, String username, String currentLocation, bool isAvailable, bool darkMode, bool notifications, bool playInBackground
});




}
/// @nodoc
class _$ArtisanSettingsTabStateCopyWithImpl<$Res>
    implements $ArtisanSettingsTabStateCopyWith<$Res> {
  _$ArtisanSettingsTabStateCopyWithImpl(this._self, this._then);

  final ArtisanSettingsTabState _self;
  final $Res Function(ArtisanSettingsTabState) _then;

/// Create a copy of ArtisanSettingsTabState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? currentLocation = null,Object? isAvailable = null,Object? darkMode = null,Object? notifications = null,Object? playInBackground = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,currentLocation: null == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as String,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,darkMode: null == darkMode ? _self.darkMode : darkMode // ignore: cast_nullable_to_non_nullable
as bool,notifications: null == notifications ? _self.notifications : notifications // ignore: cast_nullable_to_non_nullable
as bool,playInBackground: null == playInBackground ? _self.playInBackground : playInBackground // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _ArtisanSettingsTabState implements ArtisanSettingsTabState {
   _ArtisanSettingsTabState({this.isLoading = false, this.profileUrl = '', this.username = '', this.currentLocation = '', this.isAvailable = false, this.darkMode = false, this.notifications = true, this.playInBackground = false});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  String profileUrl;
@override@JsonKey() final  String username;
@override@JsonKey() final  String currentLocation;
@override@JsonKey() final  bool isAvailable;
@override@JsonKey() final  bool darkMode;
@override@JsonKey() final  bool notifications;
@override@JsonKey() final  bool playInBackground;

/// Create a copy of ArtisanSettingsTabState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanSettingsTabStateCopyWith<_ArtisanSettingsTabState> get copyWith => __$ArtisanSettingsTabStateCopyWithImpl<_ArtisanSettingsTabState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanSettingsTabState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.darkMode, darkMode) || other.darkMode == darkMode)&&(identical(other.notifications, notifications) || other.notifications == notifications)&&(identical(other.playInBackground, playInBackground) || other.playInBackground == playInBackground));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,currentLocation,isAvailable,darkMode,notifications,playInBackground);

@override
String toString() {
  return 'ArtisanSettingsTabState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, currentLocation: $currentLocation, isAvailable: $isAvailable, darkMode: $darkMode, notifications: $notifications, playInBackground: $playInBackground)';
}


}

/// @nodoc
abstract mixin class _$ArtisanSettingsTabStateCopyWith<$Res> implements $ArtisanSettingsTabStateCopyWith<$Res> {
  factory _$ArtisanSettingsTabStateCopyWith(_ArtisanSettingsTabState value, $Res Function(_ArtisanSettingsTabState) _then) = __$ArtisanSettingsTabStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String profileUrl, String username, String currentLocation, bool isAvailable, bool darkMode, bool notifications, bool playInBackground
});




}
/// @nodoc
class __$ArtisanSettingsTabStateCopyWithImpl<$Res>
    implements _$ArtisanSettingsTabStateCopyWith<$Res> {
  __$ArtisanSettingsTabStateCopyWithImpl(this._self, this._then);

  final _ArtisanSettingsTabState _self;
  final $Res Function(_ArtisanSettingsTabState) _then;

/// Create a copy of ArtisanSettingsTabState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? currentLocation = null,Object? isAvailable = null,Object? darkMode = null,Object? notifications = null,Object? playInBackground = null,}) {
  return _then(_ArtisanSettingsTabState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,currentLocation: null == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as String,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,darkMode: null == darkMode ? _self.darkMode : darkMode // ignore: cast_nullable_to_non_nullable
as bool,notifications: null == notifications ? _self.notifications : notifications // ignore: cast_nullable_to_non_nullable
as bool,playInBackground: null == playInBackground ? _self.playInBackground : playInBackground // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
