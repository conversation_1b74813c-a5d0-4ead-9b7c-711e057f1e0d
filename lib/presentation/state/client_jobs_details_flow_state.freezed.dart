// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'client_jobs_details_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ClientJobsDetailsFlowState {

 dynamic get isLoading; PostedJobModel? get job;
/// Create a copy of ClientJobsDetailsFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientJobsDetailsFlowStateCopyWith<ClientJobsDetailsFlowState> get copyWith => _$ClientJobsDetailsFlowStateCopyWithImpl<ClientJobsDetailsFlowState>(this as ClientJobsDetailsFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientJobsDetailsFlowState&&const DeepCollectionEquality().equals(other.isLoading, isLoading)&&(identical(other.job, job) || other.job == job));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(isLoading),job);

@override
String toString() {
  return 'ClientJobsDetailsFlowState(isLoading: $isLoading, job: $job)';
}


}

/// @nodoc
abstract mixin class $ClientJobsDetailsFlowStateCopyWith<$Res>  {
  factory $ClientJobsDetailsFlowStateCopyWith(ClientJobsDetailsFlowState value, $Res Function(ClientJobsDetailsFlowState) _then) = _$ClientJobsDetailsFlowStateCopyWithImpl;
@useResult
$Res call({
 dynamic isLoading, PostedJobModel? job
});




}
/// @nodoc
class _$ClientJobsDetailsFlowStateCopyWithImpl<$Res>
    implements $ClientJobsDetailsFlowStateCopyWith<$Res> {
  _$ClientJobsDetailsFlowStateCopyWithImpl(this._self, this._then);

  final ClientJobsDetailsFlowState _self;
  final $Res Function(ClientJobsDetailsFlowState) _then;

/// Create a copy of ClientJobsDetailsFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = freezed,Object? job = freezed,}) {
  return _then(_self.copyWith(
isLoading: freezed == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as dynamic,job: freezed == job ? _self.job : job // ignore: cast_nullable_to_non_nullable
as PostedJobModel?,
  ));
}

}


/// @nodoc


class _ClientJobsDetailsFlowState implements ClientJobsDetailsFlowState {
   _ClientJobsDetailsFlowState({this.isLoading = false, this.job});
  

@override@JsonKey() final  dynamic isLoading;
@override final  PostedJobModel? job;

/// Create a copy of ClientJobsDetailsFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientJobsDetailsFlowStateCopyWith<_ClientJobsDetailsFlowState> get copyWith => __$ClientJobsDetailsFlowStateCopyWithImpl<_ClientJobsDetailsFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientJobsDetailsFlowState&&const DeepCollectionEquality().equals(other.isLoading, isLoading)&&(identical(other.job, job) || other.job == job));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(isLoading),job);

@override
String toString() {
  return 'ClientJobsDetailsFlowState(isLoading: $isLoading, job: $job)';
}


}

/// @nodoc
abstract mixin class _$ClientJobsDetailsFlowStateCopyWith<$Res> implements $ClientJobsDetailsFlowStateCopyWith<$Res> {
  factory _$ClientJobsDetailsFlowStateCopyWith(_ClientJobsDetailsFlowState value, $Res Function(_ClientJobsDetailsFlowState) _then) = __$ClientJobsDetailsFlowStateCopyWithImpl;
@override @useResult
$Res call({
 dynamic isLoading, PostedJobModel? job
});




}
/// @nodoc
class __$ClientJobsDetailsFlowStateCopyWithImpl<$Res>
    implements _$ClientJobsDetailsFlowStateCopyWith<$Res> {
  __$ClientJobsDetailsFlowStateCopyWithImpl(this._self, this._then);

  final _ClientJobsDetailsFlowState _self;
  final $Res Function(_ClientJobsDetailsFlowState) _then;

/// Create a copy of ClientJobsDetailsFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = freezed,Object? job = freezed,}) {
  return _then(_ClientJobsDetailsFlowState(
isLoading: freezed == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as dynamic,job: freezed == job ? _self.job : job // ignore: cast_nullable_to_non_nullable
as PostedJobModel?,
  ));
}


}

// dart format on
