// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post_job_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PostJobFlowState {

 String get selectedMainCategory; String get selectedSubCategory; List<String> get selectedSubCategories; List<String> get relatedSubCategories; int get selectedMainCategoryId; String get description; String? get budget; List<File> get jobImageFiles; DateTime? get serviceDate; bool get isLoading; bool get isSubmitting; Map<String, int> get subcategoryIdMap; int get selectedServiceId;
/// Create a copy of PostJobFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PostJobFlowStateCopyWith<PostJobFlowState> get copyWith => _$PostJobFlowStateCopyWithImpl<PostJobFlowState>(this as PostJobFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PostJobFlowState&&(identical(other.selectedMainCategory, selectedMainCategory) || other.selectedMainCategory == selectedMainCategory)&&(identical(other.selectedSubCategory, selectedSubCategory) || other.selectedSubCategory == selectedSubCategory)&&const DeepCollectionEquality().equals(other.selectedSubCategories, selectedSubCategories)&&const DeepCollectionEquality().equals(other.relatedSubCategories, relatedSubCategories)&&(identical(other.selectedMainCategoryId, selectedMainCategoryId) || other.selectedMainCategoryId == selectedMainCategoryId)&&(identical(other.description, description) || other.description == description)&&(identical(other.budget, budget) || other.budget == budget)&&const DeepCollectionEquality().equals(other.jobImageFiles, jobImageFiles)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSubmitting, isSubmitting) || other.isSubmitting == isSubmitting)&&const DeepCollectionEquality().equals(other.subcategoryIdMap, subcategoryIdMap)&&(identical(other.selectedServiceId, selectedServiceId) || other.selectedServiceId == selectedServiceId));
}


@override
int get hashCode => Object.hash(runtimeType,selectedMainCategory,selectedSubCategory,const DeepCollectionEquality().hash(selectedSubCategories),const DeepCollectionEquality().hash(relatedSubCategories),selectedMainCategoryId,description,budget,const DeepCollectionEquality().hash(jobImageFiles),serviceDate,isLoading,isSubmitting,const DeepCollectionEquality().hash(subcategoryIdMap),selectedServiceId);

@override
String toString() {
  return 'PostJobFlowState(selectedMainCategory: $selectedMainCategory, selectedSubCategory: $selectedSubCategory, selectedSubCategories: $selectedSubCategories, relatedSubCategories: $relatedSubCategories, selectedMainCategoryId: $selectedMainCategoryId, description: $description, budget: $budget, jobImageFiles: $jobImageFiles, serviceDate: $serviceDate, isLoading: $isLoading, isSubmitting: $isSubmitting, subcategoryIdMap: $subcategoryIdMap, selectedServiceId: $selectedServiceId)';
}


}

/// @nodoc
abstract mixin class $PostJobFlowStateCopyWith<$Res>  {
  factory $PostJobFlowStateCopyWith(PostJobFlowState value, $Res Function(PostJobFlowState) _then) = _$PostJobFlowStateCopyWithImpl;
@useResult
$Res call({
 String selectedMainCategory, String selectedSubCategory, List<String> selectedSubCategories, List<String> relatedSubCategories, int selectedMainCategoryId, String description, String? budget, List<File> jobImageFiles, DateTime? serviceDate, bool isLoading, bool isSubmitting, Map<String, int> subcategoryIdMap, int selectedServiceId
});




}
/// @nodoc
class _$PostJobFlowStateCopyWithImpl<$Res>
    implements $PostJobFlowStateCopyWith<$Res> {
  _$PostJobFlowStateCopyWithImpl(this._self, this._then);

  final PostJobFlowState _self;
  final $Res Function(PostJobFlowState) _then;

/// Create a copy of PostJobFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedMainCategory = null,Object? selectedSubCategory = null,Object? selectedSubCategories = null,Object? relatedSubCategories = null,Object? selectedMainCategoryId = null,Object? description = null,Object? budget = freezed,Object? jobImageFiles = null,Object? serviceDate = freezed,Object? isLoading = null,Object? isSubmitting = null,Object? subcategoryIdMap = null,Object? selectedServiceId = null,}) {
  return _then(_self.copyWith(
selectedMainCategory: null == selectedMainCategory ? _self.selectedMainCategory : selectedMainCategory // ignore: cast_nullable_to_non_nullable
as String,selectedSubCategory: null == selectedSubCategory ? _self.selectedSubCategory : selectedSubCategory // ignore: cast_nullable_to_non_nullable
as String,selectedSubCategories: null == selectedSubCategories ? _self.selectedSubCategories : selectedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,relatedSubCategories: null == relatedSubCategories ? _self.relatedSubCategories : relatedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,selectedMainCategoryId: null == selectedMainCategoryId ? _self.selectedMainCategoryId : selectedMainCategoryId // ignore: cast_nullable_to_non_nullable
as int,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as String?,jobImageFiles: null == jobImageFiles ? _self.jobImageFiles : jobImageFiles // ignore: cast_nullable_to_non_nullable
as List<File>,serviceDate: freezed == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSubmitting: null == isSubmitting ? _self.isSubmitting : isSubmitting // ignore: cast_nullable_to_non_nullable
as bool,subcategoryIdMap: null == subcategoryIdMap ? _self.subcategoryIdMap : subcategoryIdMap // ignore: cast_nullable_to_non_nullable
as Map<String, int>,selectedServiceId: null == selectedServiceId ? _self.selectedServiceId : selectedServiceId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _PostJobFlowState implements PostJobFlowState {
   _PostJobFlowState({this.selectedMainCategory = '', this.selectedSubCategory = '', final  List<String> selectedSubCategories = const [], final  List<String> relatedSubCategories = const [], this.selectedMainCategoryId = 0, this.description = '', this.budget, final  List<File> jobImageFiles = const [], this.serviceDate, this.isLoading = false, this.isSubmitting = false, final  Map<String, int> subcategoryIdMap = const {}, this.selectedServiceId = 0}): _selectedSubCategories = selectedSubCategories,_relatedSubCategories = relatedSubCategories,_jobImageFiles = jobImageFiles,_subcategoryIdMap = subcategoryIdMap;
  

@override@JsonKey() final  String selectedMainCategory;
@override@JsonKey() final  String selectedSubCategory;
 final  List<String> _selectedSubCategories;
@override@JsonKey() List<String> get selectedSubCategories {
  if (_selectedSubCategories is EqualUnmodifiableListView) return _selectedSubCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_selectedSubCategories);
}

 final  List<String> _relatedSubCategories;
@override@JsonKey() List<String> get relatedSubCategories {
  if (_relatedSubCategories is EqualUnmodifiableListView) return _relatedSubCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_relatedSubCategories);
}

@override@JsonKey() final  int selectedMainCategoryId;
@override@JsonKey() final  String description;
@override final  String? budget;
 final  List<File> _jobImageFiles;
@override@JsonKey() List<File> get jobImageFiles {
  if (_jobImageFiles is EqualUnmodifiableListView) return _jobImageFiles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_jobImageFiles);
}

@override final  DateTime? serviceDate;
@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isSubmitting;
 final  Map<String, int> _subcategoryIdMap;
@override@JsonKey() Map<String, int> get subcategoryIdMap {
  if (_subcategoryIdMap is EqualUnmodifiableMapView) return _subcategoryIdMap;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_subcategoryIdMap);
}

@override@JsonKey() final  int selectedServiceId;

/// Create a copy of PostJobFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PostJobFlowStateCopyWith<_PostJobFlowState> get copyWith => __$PostJobFlowStateCopyWithImpl<_PostJobFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PostJobFlowState&&(identical(other.selectedMainCategory, selectedMainCategory) || other.selectedMainCategory == selectedMainCategory)&&(identical(other.selectedSubCategory, selectedSubCategory) || other.selectedSubCategory == selectedSubCategory)&&const DeepCollectionEquality().equals(other._selectedSubCategories, _selectedSubCategories)&&const DeepCollectionEquality().equals(other._relatedSubCategories, _relatedSubCategories)&&(identical(other.selectedMainCategoryId, selectedMainCategoryId) || other.selectedMainCategoryId == selectedMainCategoryId)&&(identical(other.description, description) || other.description == description)&&(identical(other.budget, budget) || other.budget == budget)&&const DeepCollectionEquality().equals(other._jobImageFiles, _jobImageFiles)&&(identical(other.serviceDate, serviceDate) || other.serviceDate == serviceDate)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSubmitting, isSubmitting) || other.isSubmitting == isSubmitting)&&const DeepCollectionEquality().equals(other._subcategoryIdMap, _subcategoryIdMap)&&(identical(other.selectedServiceId, selectedServiceId) || other.selectedServiceId == selectedServiceId));
}


@override
int get hashCode => Object.hash(runtimeType,selectedMainCategory,selectedSubCategory,const DeepCollectionEquality().hash(_selectedSubCategories),const DeepCollectionEquality().hash(_relatedSubCategories),selectedMainCategoryId,description,budget,const DeepCollectionEquality().hash(_jobImageFiles),serviceDate,isLoading,isSubmitting,const DeepCollectionEquality().hash(_subcategoryIdMap),selectedServiceId);

@override
String toString() {
  return 'PostJobFlowState(selectedMainCategory: $selectedMainCategory, selectedSubCategory: $selectedSubCategory, selectedSubCategories: $selectedSubCategories, relatedSubCategories: $relatedSubCategories, selectedMainCategoryId: $selectedMainCategoryId, description: $description, budget: $budget, jobImageFiles: $jobImageFiles, serviceDate: $serviceDate, isLoading: $isLoading, isSubmitting: $isSubmitting, subcategoryIdMap: $subcategoryIdMap, selectedServiceId: $selectedServiceId)';
}


}

/// @nodoc
abstract mixin class _$PostJobFlowStateCopyWith<$Res> implements $PostJobFlowStateCopyWith<$Res> {
  factory _$PostJobFlowStateCopyWith(_PostJobFlowState value, $Res Function(_PostJobFlowState) _then) = __$PostJobFlowStateCopyWithImpl;
@override @useResult
$Res call({
 String selectedMainCategory, String selectedSubCategory, List<String> selectedSubCategories, List<String> relatedSubCategories, int selectedMainCategoryId, String description, String? budget, List<File> jobImageFiles, DateTime? serviceDate, bool isLoading, bool isSubmitting, Map<String, int> subcategoryIdMap, int selectedServiceId
});




}
/// @nodoc
class __$PostJobFlowStateCopyWithImpl<$Res>
    implements _$PostJobFlowStateCopyWith<$Res> {
  __$PostJobFlowStateCopyWithImpl(this._self, this._then);

  final _PostJobFlowState _self;
  final $Res Function(_PostJobFlowState) _then;

/// Create a copy of PostJobFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedMainCategory = null,Object? selectedSubCategory = null,Object? selectedSubCategories = null,Object? relatedSubCategories = null,Object? selectedMainCategoryId = null,Object? description = null,Object? budget = freezed,Object? jobImageFiles = null,Object? serviceDate = freezed,Object? isLoading = null,Object? isSubmitting = null,Object? subcategoryIdMap = null,Object? selectedServiceId = null,}) {
  return _then(_PostJobFlowState(
selectedMainCategory: null == selectedMainCategory ? _self.selectedMainCategory : selectedMainCategory // ignore: cast_nullable_to_non_nullable
as String,selectedSubCategory: null == selectedSubCategory ? _self.selectedSubCategory : selectedSubCategory // ignore: cast_nullable_to_non_nullable
as String,selectedSubCategories: null == selectedSubCategories ? _self._selectedSubCategories : selectedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,relatedSubCategories: null == relatedSubCategories ? _self._relatedSubCategories : relatedSubCategories // ignore: cast_nullable_to_non_nullable
as List<String>,selectedMainCategoryId: null == selectedMainCategoryId ? _self.selectedMainCategoryId : selectedMainCategoryId // ignore: cast_nullable_to_non_nullable
as int,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as String?,jobImageFiles: null == jobImageFiles ? _self._jobImageFiles : jobImageFiles // ignore: cast_nullable_to_non_nullable
as List<File>,serviceDate: freezed == serviceDate ? _self.serviceDate : serviceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSubmitting: null == isSubmitting ? _self.isSubmitting : isSubmitting // ignore: cast_nullable_to_non_nullable
as bool,subcategoryIdMap: null == subcategoryIdMap ? _self._subcategoryIdMap : subcategoryIdMap // ignore: cast_nullable_to_non_nullable
as Map<String, int>,selectedServiceId: null == selectedServiceId ? _self.selectedServiceId : selectedServiceId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
