// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shop_products_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ShopProductsState {

 bool get isLoadingProducts; int get branchId; List<ShopProductResponse> get products; String get shopname; String get branchName; List<dynamic> get subcategories; List<dynamic> get filteredSubcategories; String get search; int? get selectedSubcategoryId; String? get selectedSubcategoryName;
/// Create a copy of ShopProductsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShopProductsStateCopyWith<ShopProductsState> get copyWith => _$ShopProductsStateCopyWithImpl<ShopProductsState>(this as ShopProductsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShopProductsState&&(identical(other.isLoadingProducts, isLoadingProducts) || other.isLoadingProducts == isLoadingProducts)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&const DeepCollectionEquality().equals(other.products, products)&&(identical(other.shopname, shopname) || other.shopname == shopname)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&const DeepCollectionEquality().equals(other.subcategories, subcategories)&&const DeepCollectionEquality().equals(other.filteredSubcategories, filteredSubcategories)&&(identical(other.search, search) || other.search == search)&&(identical(other.selectedSubcategoryId, selectedSubcategoryId) || other.selectedSubcategoryId == selectedSubcategoryId)&&(identical(other.selectedSubcategoryName, selectedSubcategoryName) || other.selectedSubcategoryName == selectedSubcategoryName));
}


@override
int get hashCode => Object.hash(runtimeType,isLoadingProducts,branchId,const DeepCollectionEquality().hash(products),shopname,branchName,const DeepCollectionEquality().hash(subcategories),const DeepCollectionEquality().hash(filteredSubcategories),search,selectedSubcategoryId,selectedSubcategoryName);

@override
String toString() {
  return 'ShopProductsState(isLoadingProducts: $isLoadingProducts, branchId: $branchId, products: $products, shopname: $shopname, branchName: $branchName, subcategories: $subcategories, filteredSubcategories: $filteredSubcategories, search: $search, selectedSubcategoryId: $selectedSubcategoryId, selectedSubcategoryName: $selectedSubcategoryName)';
}


}

/// @nodoc
abstract mixin class $ShopProductsStateCopyWith<$Res>  {
  factory $ShopProductsStateCopyWith(ShopProductsState value, $Res Function(ShopProductsState) _then) = _$ShopProductsStateCopyWithImpl;
@useResult
$Res call({
 bool isLoadingProducts, int branchId, List<ShopProductResponse> products, String shopname, String branchName, List<dynamic> subcategories, List<dynamic> filteredSubcategories, String search, int? selectedSubcategoryId, String? selectedSubcategoryName
});




}
/// @nodoc
class _$ShopProductsStateCopyWithImpl<$Res>
    implements $ShopProductsStateCopyWith<$Res> {
  _$ShopProductsStateCopyWithImpl(this._self, this._then);

  final ShopProductsState _self;
  final $Res Function(ShopProductsState) _then;

/// Create a copy of ShopProductsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoadingProducts = null,Object? branchId = null,Object? products = null,Object? shopname = null,Object? branchName = null,Object? subcategories = null,Object? filteredSubcategories = null,Object? search = null,Object? selectedSubcategoryId = freezed,Object? selectedSubcategoryName = freezed,}) {
  return _then(_self.copyWith(
isLoadingProducts: null == isLoadingProducts ? _self.isLoadingProducts : isLoadingProducts // ignore: cast_nullable_to_non_nullable
as bool,branchId: null == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int,products: null == products ? _self.products : products // ignore: cast_nullable_to_non_nullable
as List<ShopProductResponse>,shopname: null == shopname ? _self.shopname : shopname // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,subcategories: null == subcategories ? _self.subcategories : subcategories // ignore: cast_nullable_to_non_nullable
as List<dynamic>,filteredSubcategories: null == filteredSubcategories ? _self.filteredSubcategories : filteredSubcategories // ignore: cast_nullable_to_non_nullable
as List<dynamic>,search: null == search ? _self.search : search // ignore: cast_nullable_to_non_nullable
as String,selectedSubcategoryId: freezed == selectedSubcategoryId ? _self.selectedSubcategoryId : selectedSubcategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubcategoryName: freezed == selectedSubcategoryName ? _self.selectedSubcategoryName : selectedSubcategoryName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _ShopProductsState implements ShopProductsState {
   _ShopProductsState({this.isLoadingProducts = false, this.branchId = 0, final  List<ShopProductResponse> products = const [], this.shopname = '', this.branchName = '', final  List<dynamic> subcategories = const [], final  List<dynamic> filteredSubcategories = const [], this.search = '', this.selectedSubcategoryId, this.selectedSubcategoryName}): _products = products,_subcategories = subcategories,_filteredSubcategories = filteredSubcategories;
  

@override@JsonKey() final  bool isLoadingProducts;
@override@JsonKey() final  int branchId;
 final  List<ShopProductResponse> _products;
@override@JsonKey() List<ShopProductResponse> get products {
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_products);
}

@override@JsonKey() final  String shopname;
@override@JsonKey() final  String branchName;
 final  List<dynamic> _subcategories;
@override@JsonKey() List<dynamic> get subcategories {
  if (_subcategories is EqualUnmodifiableListView) return _subcategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_subcategories);
}

 final  List<dynamic> _filteredSubcategories;
@override@JsonKey() List<dynamic> get filteredSubcategories {
  if (_filteredSubcategories is EqualUnmodifiableListView) return _filteredSubcategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_filteredSubcategories);
}

@override@JsonKey() final  String search;
@override final  int? selectedSubcategoryId;
@override final  String? selectedSubcategoryName;

/// Create a copy of ShopProductsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShopProductsStateCopyWith<_ShopProductsState> get copyWith => __$ShopProductsStateCopyWithImpl<_ShopProductsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShopProductsState&&(identical(other.isLoadingProducts, isLoadingProducts) || other.isLoadingProducts == isLoadingProducts)&&(identical(other.branchId, branchId) || other.branchId == branchId)&&const DeepCollectionEquality().equals(other._products, _products)&&(identical(other.shopname, shopname) || other.shopname == shopname)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&const DeepCollectionEquality().equals(other._subcategories, _subcategories)&&const DeepCollectionEquality().equals(other._filteredSubcategories, _filteredSubcategories)&&(identical(other.search, search) || other.search == search)&&(identical(other.selectedSubcategoryId, selectedSubcategoryId) || other.selectedSubcategoryId == selectedSubcategoryId)&&(identical(other.selectedSubcategoryName, selectedSubcategoryName) || other.selectedSubcategoryName == selectedSubcategoryName));
}


@override
int get hashCode => Object.hash(runtimeType,isLoadingProducts,branchId,const DeepCollectionEquality().hash(_products),shopname,branchName,const DeepCollectionEquality().hash(_subcategories),const DeepCollectionEquality().hash(_filteredSubcategories),search,selectedSubcategoryId,selectedSubcategoryName);

@override
String toString() {
  return 'ShopProductsState(isLoadingProducts: $isLoadingProducts, branchId: $branchId, products: $products, shopname: $shopname, branchName: $branchName, subcategories: $subcategories, filteredSubcategories: $filteredSubcategories, search: $search, selectedSubcategoryId: $selectedSubcategoryId, selectedSubcategoryName: $selectedSubcategoryName)';
}


}

/// @nodoc
abstract mixin class _$ShopProductsStateCopyWith<$Res> implements $ShopProductsStateCopyWith<$Res> {
  factory _$ShopProductsStateCopyWith(_ShopProductsState value, $Res Function(_ShopProductsState) _then) = __$ShopProductsStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoadingProducts, int branchId, List<ShopProductResponse> products, String shopname, String branchName, List<dynamic> subcategories, List<dynamic> filteredSubcategories, String search, int? selectedSubcategoryId, String? selectedSubcategoryName
});




}
/// @nodoc
class __$ShopProductsStateCopyWithImpl<$Res>
    implements _$ShopProductsStateCopyWith<$Res> {
  __$ShopProductsStateCopyWithImpl(this._self, this._then);

  final _ShopProductsState _self;
  final $Res Function(_ShopProductsState) _then;

/// Create a copy of ShopProductsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoadingProducts = null,Object? branchId = null,Object? products = null,Object? shopname = null,Object? branchName = null,Object? subcategories = null,Object? filteredSubcategories = null,Object? search = null,Object? selectedSubcategoryId = freezed,Object? selectedSubcategoryName = freezed,}) {
  return _then(_ShopProductsState(
isLoadingProducts: null == isLoadingProducts ? _self.isLoadingProducts : isLoadingProducts // ignore: cast_nullable_to_non_nullable
as bool,branchId: null == branchId ? _self.branchId : branchId // ignore: cast_nullable_to_non_nullable
as int,products: null == products ? _self._products : products // ignore: cast_nullable_to_non_nullable
as List<ShopProductResponse>,shopname: null == shopname ? _self.shopname : shopname // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,subcategories: null == subcategories ? _self._subcategories : subcategories // ignore: cast_nullable_to_non_nullable
as List<dynamic>,filteredSubcategories: null == filteredSubcategories ? _self._filteredSubcategories : filteredSubcategories // ignore: cast_nullable_to_non_nullable
as List<dynamic>,search: null == search ? _self.search : search // ignore: cast_nullable_to_non_nullable
as String,selectedSubcategoryId: freezed == selectedSubcategoryId ? _self.selectedSubcategoryId : selectedSubcategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubcategoryName: freezed == selectedSubcategoryName ? _self.selectedSubcategoryName : selectedSubcategoryName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
