// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signin_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SigninFlowState {

 String get email; String get password; bool get isLoggingIn;
/// Create a copy of SigninFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SigninFlowStateCopyWith<SigninFlowState> get copyWith => _$SigninFlowStateCopyWithImpl<SigninFlowState>(this as SigninFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SigninFlowState&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.isLoggingIn, isLoggingIn) || other.isLoggingIn == isLoggingIn));
}


@override
int get hashCode => Object.hash(runtimeType,email,password,isLoggingIn);

@override
String toString() {
  return 'SigninFlowState(email: $email, password: $password, isLoggingIn: $isLoggingIn)';
}


}

/// @nodoc
abstract mixin class $SigninFlowStateCopyWith<$Res>  {
  factory $SigninFlowStateCopyWith(SigninFlowState value, $Res Function(SigninFlowState) _then) = _$SigninFlowStateCopyWithImpl;
@useResult
$Res call({
 String email, String password, bool isLoggingIn
});




}
/// @nodoc
class _$SigninFlowStateCopyWithImpl<$Res>
    implements $SigninFlowStateCopyWith<$Res> {
  _$SigninFlowStateCopyWithImpl(this._self, this._then);

  final SigninFlowState _self;
  final $Res Function(SigninFlowState) _then;

/// Create a copy of SigninFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? password = null,Object? isLoggingIn = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,isLoggingIn: null == isLoggingIn ? _self.isLoggingIn : isLoggingIn // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _SigninFlowState implements SigninFlowState {
   _SigninFlowState({this.email = '', this.password = '', this.isLoggingIn = false});
  

@override@JsonKey() final  String email;
@override@JsonKey() final  String password;
@override@JsonKey() final  bool isLoggingIn;

/// Create a copy of SigninFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SigninFlowStateCopyWith<_SigninFlowState> get copyWith => __$SigninFlowStateCopyWithImpl<_SigninFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SigninFlowState&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.isLoggingIn, isLoggingIn) || other.isLoggingIn == isLoggingIn));
}


@override
int get hashCode => Object.hash(runtimeType,email,password,isLoggingIn);

@override
String toString() {
  return 'SigninFlowState(email: $email, password: $password, isLoggingIn: $isLoggingIn)';
}


}

/// @nodoc
abstract mixin class _$SigninFlowStateCopyWith<$Res> implements $SigninFlowStateCopyWith<$Res> {
  factory _$SigninFlowStateCopyWith(_SigninFlowState value, $Res Function(_SigninFlowState) _then) = __$SigninFlowStateCopyWithImpl;
@override @useResult
$Res call({
 String email, String password, bool isLoggingIn
});




}
/// @nodoc
class __$SigninFlowStateCopyWithImpl<$Res>
    implements _$SigninFlowStateCopyWith<$Res> {
  __$SigninFlowStateCopyWithImpl(this._self, this._then);

  final _SigninFlowState _self;
  final $Res Function(_SigninFlowState) _then;

/// Create a copy of SigninFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? password = null,Object? isLoggingIn = null,}) {
  return _then(_SigninFlowState(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,isLoggingIn: null == isLoggingIn ? _self.isLoggingIn : isLoggingIn // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
