// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_chat_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UserChatState {

 bool get isLoading; AsyncValue<List<Conversation>> get conversations;
/// Create a copy of UserChatState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserChatStateCopyWith<UserChatState> get copyWith => _$UserChatStateCopyWithImpl<UserChatState>(this as UserChatState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserChatState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.conversations, conversations) || other.conversations == conversations));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,conversations);

@override
String toString() {
  return 'UserChatState(isLoading: $isLoading, conversations: $conversations)';
}


}

/// @nodoc
abstract mixin class $UserChatStateCopyWith<$Res>  {
  factory $UserChatStateCopyWith(UserChatState value, $Res Function(UserChatState) _then) = _$UserChatStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, AsyncValue<List<Conversation>> conversations
});




}
/// @nodoc
class _$UserChatStateCopyWithImpl<$Res>
    implements $UserChatStateCopyWith<$Res> {
  _$UserChatStateCopyWithImpl(this._self, this._then);

  final UserChatState _self;
  final $Res Function(UserChatState) _then;

/// Create a copy of UserChatState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? conversations = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,conversations: null == conversations ? _self.conversations : conversations // ignore: cast_nullable_to_non_nullable
as AsyncValue<List<Conversation>>,
  ));
}

}


/// @nodoc


class _UserChatState implements UserChatState {
   _UserChatState({this.isLoading = false, required this.conversations});
  

@override@JsonKey() final  bool isLoading;
@override final  AsyncValue<List<Conversation>> conversations;

/// Create a copy of UserChatState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserChatStateCopyWith<_UserChatState> get copyWith => __$UserChatStateCopyWithImpl<_UserChatState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserChatState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.conversations, conversations) || other.conversations == conversations));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,conversations);

@override
String toString() {
  return 'UserChatState(isLoading: $isLoading, conversations: $conversations)';
}


}

/// @nodoc
abstract mixin class _$UserChatStateCopyWith<$Res> implements $UserChatStateCopyWith<$Res> {
  factory _$UserChatStateCopyWith(_UserChatState value, $Res Function(_UserChatState) _then) = __$UserChatStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, AsyncValue<List<Conversation>> conversations
});




}
/// @nodoc
class __$UserChatStateCopyWithImpl<$Res>
    implements _$UserChatStateCopyWith<$Res> {
  __$UserChatStateCopyWithImpl(this._self, this._then);

  final _UserChatState _self;
  final $Res Function(_UserChatState) _then;

/// Create a copy of UserChatState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? conversations = null,}) {
  return _then(_UserChatState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,conversations: null == conversations ? _self.conversations : conversations // ignore: cast_nullable_to_non_nullable
as AsyncValue<List<Conversation>>,
  ));
}


}

// dart format on
