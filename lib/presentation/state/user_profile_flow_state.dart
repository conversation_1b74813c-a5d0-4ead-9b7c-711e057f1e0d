import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'user_profile_flow_state.freezed.dart';

@freezed
abstract class UserProfileFlowState with _$UserProfileFlowState {
  factory UserProfileFlowState({
    @Default('') String fullname,
    @Default('') String email,
    @Default('') String phonenumber,
    @Default('') String secondPhonenumber,
    @Default('') String whatsappNumber,
    @Default('') String address,
    @Default('') String nationalId,
    @Default('') String avatarUrl,
    @Default(false) bool isUpdatingProfile,
    @Default('') String supabaseUUID,
    @Default(LatLng(0.0, 0.0)) LatLng location,
  }) = _UserProfileFlowState;
}
