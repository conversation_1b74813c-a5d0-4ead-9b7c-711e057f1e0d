// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_profile_view_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ArtisanProfileViewState {

 String get name; String get profession; String get location; double get distance; double get rating; int get totalReviews; List<String> get certifications; String get serviceType; bool get isActive; List<String> get specializations; String get about; String get nextAvailableTime; String get coverImageUrl; String get profileImageUrl; List<String> get workImages; List<Map<String, dynamic>> get reviews; int get selectedTabIndex; ArtisanProfileDataResponse? get artisanProfileDataResponse; bool get isLoading; JobDetailsResponse? get selectedJob; List<Map<String, dynamic>> get categories; List<String> get availableSubcategories;
/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanProfileViewStateCopyWith<ArtisanProfileViewState> get copyWith => _$ArtisanProfileViewStateCopyWithImpl<ArtisanProfileViewState>(this as ArtisanProfileViewState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanProfileViewState&&(identical(other.name, name) || other.name == name)&&(identical(other.profession, profession) || other.profession == profession)&&(identical(other.location, location) || other.location == location)&&(identical(other.distance, distance) || other.distance == distance)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other.certifications, certifications)&&(identical(other.serviceType, serviceType) || other.serviceType == serviceType)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other.specializations, specializations)&&(identical(other.about, about) || other.about == about)&&(identical(other.nextAvailableTime, nextAvailableTime) || other.nextAvailableTime == nextAvailableTime)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&const DeepCollectionEquality().equals(other.workImages, workImages)&&const DeepCollectionEquality().equals(other.reviews, reviews)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.artisanProfileDataResponse, artisanProfileDataResponse) || other.artisanProfileDataResponse == artisanProfileDataResponse)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&const DeepCollectionEquality().equals(other.categories, categories)&&const DeepCollectionEquality().equals(other.availableSubcategories, availableSubcategories));
}


@override
int get hashCode => Object.hashAll([runtimeType,name,profession,location,distance,rating,totalReviews,const DeepCollectionEquality().hash(certifications),serviceType,isActive,const DeepCollectionEquality().hash(specializations),about,nextAvailableTime,coverImageUrl,profileImageUrl,const DeepCollectionEquality().hash(workImages),const DeepCollectionEquality().hash(reviews),selectedTabIndex,artisanProfileDataResponse,isLoading,selectedJob,const DeepCollectionEquality().hash(categories),const DeepCollectionEquality().hash(availableSubcategories)]);

@override
String toString() {
  return 'ArtisanProfileViewState(name: $name, profession: $profession, location: $location, distance: $distance, rating: $rating, totalReviews: $totalReviews, certifications: $certifications, serviceType: $serviceType, isActive: $isActive, specializations: $specializations, about: $about, nextAvailableTime: $nextAvailableTime, coverImageUrl: $coverImageUrl, profileImageUrl: $profileImageUrl, workImages: $workImages, reviews: $reviews, selectedTabIndex: $selectedTabIndex, artisanProfileDataResponse: $artisanProfileDataResponse, isLoading: $isLoading, selectedJob: $selectedJob, categories: $categories, availableSubcategories: $availableSubcategories)';
}


}

/// @nodoc
abstract mixin class $ArtisanProfileViewStateCopyWith<$Res>  {
  factory $ArtisanProfileViewStateCopyWith(ArtisanProfileViewState value, $Res Function(ArtisanProfileViewState) _then) = _$ArtisanProfileViewStateCopyWithImpl;
@useResult
$Res call({
 String name, String profession, String location, double distance, double rating, int totalReviews, List<String> certifications, String serviceType, bool isActive, List<String> specializations, String about, String nextAvailableTime, String coverImageUrl, String profileImageUrl, List<String> workImages, List<Map<String, dynamic>> reviews, int selectedTabIndex, ArtisanProfileDataResponse? artisanProfileDataResponse, bool isLoading, JobDetailsResponse? selectedJob, List<Map<String, dynamic>> categories, List<String> availableSubcategories
});


$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileDataResponse;$JobDetailsResponseCopyWith<$Res>? get selectedJob;

}
/// @nodoc
class _$ArtisanProfileViewStateCopyWithImpl<$Res>
    implements $ArtisanProfileViewStateCopyWith<$Res> {
  _$ArtisanProfileViewStateCopyWithImpl(this._self, this._then);

  final ArtisanProfileViewState _self;
  final $Res Function(ArtisanProfileViewState) _then;

/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? profession = null,Object? location = null,Object? distance = null,Object? rating = null,Object? totalReviews = null,Object? certifications = null,Object? serviceType = null,Object? isActive = null,Object? specializations = null,Object? about = null,Object? nextAvailableTime = null,Object? coverImageUrl = null,Object? profileImageUrl = null,Object? workImages = null,Object? reviews = null,Object? selectedTabIndex = null,Object? artisanProfileDataResponse = freezed,Object? isLoading = null,Object? selectedJob = freezed,Object? categories = null,Object? availableSubcategories = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profession: null == profession ? _self.profession : profession // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,distance: null == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as double,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,certifications: null == certifications ? _self.certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>,serviceType: null == serviceType ? _self.serviceType : serviceType // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,specializations: null == specializations ? _self.specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<String>,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nextAvailableTime: null == nextAvailableTime ? _self.nextAvailableTime : nextAvailableTime // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,profileImageUrl: null == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String,workImages: null == workImages ? _self.workImages : workImages // ignore: cast_nullable_to_non_nullable
as List<String>,reviews: null == reviews ? _self.reviews : reviews // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,artisanProfileDataResponse: freezed == artisanProfileDataResponse ? _self.artisanProfileDataResponse : artisanProfileDataResponse // ignore: cast_nullable_to_non_nullable
as ArtisanProfileDataResponse?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as JobDetailsResponse?,categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,availableSubcategories: null == availableSubcategories ? _self.availableSubcategories : availableSubcategories // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}
/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileDataResponse {
    if (_self.artisanProfileDataResponse == null) {
    return null;
  }

  return $ArtisanProfileDataResponseCopyWith<$Res>(_self.artisanProfileDataResponse!, (value) {
    return _then(_self.copyWith(artisanProfileDataResponse: value));
  });
}/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobDetailsResponseCopyWith<$Res>? get selectedJob {
    if (_self.selectedJob == null) {
    return null;
  }

  return $JobDetailsResponseCopyWith<$Res>(_self.selectedJob!, (value) {
    return _then(_self.copyWith(selectedJob: value));
  });
}
}


/// @nodoc


class _ArtisanProfileViewState implements ArtisanProfileViewState {
   _ArtisanProfileViewState({this.name = '', this.profession = '', this.location = '', this.distance = 0.0, this.rating = 0.0, this.totalReviews = 0, final  List<String> certifications = const [], this.serviceType = '', this.isActive = true, final  List<String> specializations = const [], this.about = '', this.nextAvailableTime = '', this.coverImageUrl = 'assets/images/profile_pic.png', this.profileImageUrl = 'assets/images/profile_pic.png', final  List<String> workImages = const [], final  List<Map<String, dynamic>> reviews = const [], this.selectedTabIndex = 0, this.artisanProfileDataResponse, this.isLoading = true, this.selectedJob, final  List<Map<String, dynamic>> categories = const [], final  List<String> availableSubcategories = const []}): _certifications = certifications,_specializations = specializations,_workImages = workImages,_reviews = reviews,_categories = categories,_availableSubcategories = availableSubcategories;
  

@override@JsonKey() final  String name;
@override@JsonKey() final  String profession;
@override@JsonKey() final  String location;
@override@JsonKey() final  double distance;
@override@JsonKey() final  double rating;
@override@JsonKey() final  int totalReviews;
 final  List<String> _certifications;
@override@JsonKey() List<String> get certifications {
  if (_certifications is EqualUnmodifiableListView) return _certifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_certifications);
}

@override@JsonKey() final  String serviceType;
@override@JsonKey() final  bool isActive;
 final  List<String> _specializations;
@override@JsonKey() List<String> get specializations {
  if (_specializations is EqualUnmodifiableListView) return _specializations;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_specializations);
}

@override@JsonKey() final  String about;
@override@JsonKey() final  String nextAvailableTime;
@override@JsonKey() final  String coverImageUrl;
@override@JsonKey() final  String profileImageUrl;
 final  List<String> _workImages;
@override@JsonKey() List<String> get workImages {
  if (_workImages is EqualUnmodifiableListView) return _workImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_workImages);
}

 final  List<Map<String, dynamic>> _reviews;
@override@JsonKey() List<Map<String, dynamic>> get reviews {
  if (_reviews is EqualUnmodifiableListView) return _reviews;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reviews);
}

@override@JsonKey() final  int selectedTabIndex;
@override final  ArtisanProfileDataResponse? artisanProfileDataResponse;
@override@JsonKey() final  bool isLoading;
@override final  JobDetailsResponse? selectedJob;
 final  List<Map<String, dynamic>> _categories;
@override@JsonKey() List<Map<String, dynamic>> get categories {
  if (_categories is EqualUnmodifiableListView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_categories);
}

 final  List<String> _availableSubcategories;
@override@JsonKey() List<String> get availableSubcategories {
  if (_availableSubcategories is EqualUnmodifiableListView) return _availableSubcategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableSubcategories);
}


/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanProfileViewStateCopyWith<_ArtisanProfileViewState> get copyWith => __$ArtisanProfileViewStateCopyWithImpl<_ArtisanProfileViewState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanProfileViewState&&(identical(other.name, name) || other.name == name)&&(identical(other.profession, profession) || other.profession == profession)&&(identical(other.location, location) || other.location == location)&&(identical(other.distance, distance) || other.distance == distance)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other._certifications, _certifications)&&(identical(other.serviceType, serviceType) || other.serviceType == serviceType)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&const DeepCollectionEquality().equals(other._specializations, _specializations)&&(identical(other.about, about) || other.about == about)&&(identical(other.nextAvailableTime, nextAvailableTime) || other.nextAvailableTime == nextAvailableTime)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&const DeepCollectionEquality().equals(other._workImages, _workImages)&&const DeepCollectionEquality().equals(other._reviews, _reviews)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.artisanProfileDataResponse, artisanProfileDataResponse) || other.artisanProfileDataResponse == artisanProfileDataResponse)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&const DeepCollectionEquality().equals(other._categories, _categories)&&const DeepCollectionEquality().equals(other._availableSubcategories, _availableSubcategories));
}


@override
int get hashCode => Object.hashAll([runtimeType,name,profession,location,distance,rating,totalReviews,const DeepCollectionEquality().hash(_certifications),serviceType,isActive,const DeepCollectionEquality().hash(_specializations),about,nextAvailableTime,coverImageUrl,profileImageUrl,const DeepCollectionEquality().hash(_workImages),const DeepCollectionEquality().hash(_reviews),selectedTabIndex,artisanProfileDataResponse,isLoading,selectedJob,const DeepCollectionEquality().hash(_categories),const DeepCollectionEquality().hash(_availableSubcategories)]);

@override
String toString() {
  return 'ArtisanProfileViewState(name: $name, profession: $profession, location: $location, distance: $distance, rating: $rating, totalReviews: $totalReviews, certifications: $certifications, serviceType: $serviceType, isActive: $isActive, specializations: $specializations, about: $about, nextAvailableTime: $nextAvailableTime, coverImageUrl: $coverImageUrl, profileImageUrl: $profileImageUrl, workImages: $workImages, reviews: $reviews, selectedTabIndex: $selectedTabIndex, artisanProfileDataResponse: $artisanProfileDataResponse, isLoading: $isLoading, selectedJob: $selectedJob, categories: $categories, availableSubcategories: $availableSubcategories)';
}


}

/// @nodoc
abstract mixin class _$ArtisanProfileViewStateCopyWith<$Res> implements $ArtisanProfileViewStateCopyWith<$Res> {
  factory _$ArtisanProfileViewStateCopyWith(_ArtisanProfileViewState value, $Res Function(_ArtisanProfileViewState) _then) = __$ArtisanProfileViewStateCopyWithImpl;
@override @useResult
$Res call({
 String name, String profession, String location, double distance, double rating, int totalReviews, List<String> certifications, String serviceType, bool isActive, List<String> specializations, String about, String nextAvailableTime, String coverImageUrl, String profileImageUrl, List<String> workImages, List<Map<String, dynamic>> reviews, int selectedTabIndex, ArtisanProfileDataResponse? artisanProfileDataResponse, bool isLoading, JobDetailsResponse? selectedJob, List<Map<String, dynamic>> categories, List<String> availableSubcategories
});


@override $ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileDataResponse;@override $JobDetailsResponseCopyWith<$Res>? get selectedJob;

}
/// @nodoc
class __$ArtisanProfileViewStateCopyWithImpl<$Res>
    implements _$ArtisanProfileViewStateCopyWith<$Res> {
  __$ArtisanProfileViewStateCopyWithImpl(this._self, this._then);

  final _ArtisanProfileViewState _self;
  final $Res Function(_ArtisanProfileViewState) _then;

/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? profession = null,Object? location = null,Object? distance = null,Object? rating = null,Object? totalReviews = null,Object? certifications = null,Object? serviceType = null,Object? isActive = null,Object? specializations = null,Object? about = null,Object? nextAvailableTime = null,Object? coverImageUrl = null,Object? profileImageUrl = null,Object? workImages = null,Object? reviews = null,Object? selectedTabIndex = null,Object? artisanProfileDataResponse = freezed,Object? isLoading = null,Object? selectedJob = freezed,Object? categories = null,Object? availableSubcategories = null,}) {
  return _then(_ArtisanProfileViewState(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,profession: null == profession ? _self.profession : profession // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,distance: null == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as double,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,certifications: null == certifications ? _self._certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>,serviceType: null == serviceType ? _self.serviceType : serviceType // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,specializations: null == specializations ? _self._specializations : specializations // ignore: cast_nullable_to_non_nullable
as List<String>,about: null == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String,nextAvailableTime: null == nextAvailableTime ? _self.nextAvailableTime : nextAvailableTime // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,profileImageUrl: null == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String,workImages: null == workImages ? _self._workImages : workImages // ignore: cast_nullable_to_non_nullable
as List<String>,reviews: null == reviews ? _self._reviews : reviews // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,artisanProfileDataResponse: freezed == artisanProfileDataResponse ? _self.artisanProfileDataResponse : artisanProfileDataResponse // ignore: cast_nullable_to_non_nullable
as ArtisanProfileDataResponse?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as JobDetailsResponse?,categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,availableSubcategories: null == availableSubcategories ? _self._availableSubcategories : availableSubcategories // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileDataResponse {
    if (_self.artisanProfileDataResponse == null) {
    return null;
  }

  return $ArtisanProfileDataResponseCopyWith<$Res>(_self.artisanProfileDataResponse!, (value) {
    return _then(_self.copyWith(artisanProfileDataResponse: value));
  });
}/// Create a copy of ArtisanProfileViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobDetailsResponseCopyWith<$Res>? get selectedJob {
    if (_self.selectedJob == null) {
    return null;
  }

  return $JobDetailsResponseCopyWith<$Res>(_self.selectedJob!, (value) {
    return _then(_self.copyWith(selectedJob: value));
  });
}
}

// dart format on
