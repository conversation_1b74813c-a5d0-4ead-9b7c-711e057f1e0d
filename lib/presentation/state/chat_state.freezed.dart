// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChatState {

 bool get isLoading; bool get isSubscribing; bool get isSubscribed; String? get subscriptionError;// @Default([]) List<Conversation> conversations
 AsyncValue<List<Conversation>> get conversations;
/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatStateCopyWith<ChatState> get copyWith => _$ChatStateCopyWithImpl<ChatState>(this as ChatState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSubscribing, isSubscribing) || other.isSubscribing == isSubscribing)&&(identical(other.isSubscribed, isSubscribed) || other.isSubscribed == isSubscribed)&&(identical(other.subscriptionError, subscriptionError) || other.subscriptionError == subscriptionError)&&(identical(other.conversations, conversations) || other.conversations == conversations));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isSubscribing,isSubscribed,subscriptionError,conversations);

@override
String toString() {
  return 'ChatState(isLoading: $isLoading, isSubscribing: $isSubscribing, isSubscribed: $isSubscribed, subscriptionError: $subscriptionError, conversations: $conversations)';
}


}

/// @nodoc
abstract mixin class $ChatStateCopyWith<$Res>  {
  factory $ChatStateCopyWith(ChatState value, $Res Function(ChatState) _then) = _$ChatStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool isSubscribing, bool isSubscribed, String? subscriptionError, AsyncValue<List<Conversation>> conversations
});




}
/// @nodoc
class _$ChatStateCopyWithImpl<$Res>
    implements $ChatStateCopyWith<$Res> {
  _$ChatStateCopyWithImpl(this._self, this._then);

  final ChatState _self;
  final $Res Function(ChatState) _then;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? isSubscribing = null,Object? isSubscribed = null,Object? subscriptionError = freezed,Object? conversations = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSubscribing: null == isSubscribing ? _self.isSubscribing : isSubscribing // ignore: cast_nullable_to_non_nullable
as bool,isSubscribed: null == isSubscribed ? _self.isSubscribed : isSubscribed // ignore: cast_nullable_to_non_nullable
as bool,subscriptionError: freezed == subscriptionError ? _self.subscriptionError : subscriptionError // ignore: cast_nullable_to_non_nullable
as String?,conversations: null == conversations ? _self.conversations : conversations // ignore: cast_nullable_to_non_nullable
as AsyncValue<List<Conversation>>,
  ));
}

}


/// @nodoc


class _ChatState implements ChatState {
   _ChatState({this.isLoading = false, this.isSubscribing = false, this.isSubscribed = false, this.subscriptionError, required this.conversations});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isSubscribing;
@override@JsonKey() final  bool isSubscribed;
@override final  String? subscriptionError;
// @Default([]) List<Conversation> conversations
@override final  AsyncValue<List<Conversation>> conversations;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatStateCopyWith<_ChatState> get copyWith => __$ChatStateCopyWithImpl<_ChatState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSubscribing, isSubscribing) || other.isSubscribing == isSubscribing)&&(identical(other.isSubscribed, isSubscribed) || other.isSubscribed == isSubscribed)&&(identical(other.subscriptionError, subscriptionError) || other.subscriptionError == subscriptionError)&&(identical(other.conversations, conversations) || other.conversations == conversations));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isSubscribing,isSubscribed,subscriptionError,conversations);

@override
String toString() {
  return 'ChatState(isLoading: $isLoading, isSubscribing: $isSubscribing, isSubscribed: $isSubscribed, subscriptionError: $subscriptionError, conversations: $conversations)';
}


}

/// @nodoc
abstract mixin class _$ChatStateCopyWith<$Res> implements $ChatStateCopyWith<$Res> {
  factory _$ChatStateCopyWith(_ChatState value, $Res Function(_ChatState) _then) = __$ChatStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool isSubscribing, bool isSubscribed, String? subscriptionError, AsyncValue<List<Conversation>> conversations
});




}
/// @nodoc
class __$ChatStateCopyWithImpl<$Res>
    implements _$ChatStateCopyWith<$Res> {
  __$ChatStateCopyWithImpl(this._self, this._then);

  final _ChatState _self;
  final $Res Function(_ChatState) _then;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? isSubscribing = null,Object? isSubscribed = null,Object? subscriptionError = freezed,Object? conversations = null,}) {
  return _then(_ChatState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSubscribing: null == isSubscribing ? _self.isSubscribing : isSubscribing // ignore: cast_nullable_to_non_nullable
as bool,isSubscribed: null == isSubscribed ? _self.isSubscribed : isSubscribed // ignore: cast_nullable_to_non_nullable
as bool,subscriptionError: freezed == subscriptionError ? _self.subscriptionError : subscriptionError // ignore: cast_nullable_to_non_nullable
as String?,conversations: null == conversations ? _self.conversations : conversations // ignore: cast_nullable_to_non_nullable
as AsyncValue<List<Conversation>>,
  ));
}


}

// dart format on
