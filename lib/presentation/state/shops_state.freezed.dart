// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shops_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ShopsState {

 bool get isLoading; List<BranchDistance> get nearbyShops; Set<Marker> get markers; String? get error; Position? get userLocation; ClientModel? get clientInfo; Marker? get userMarker; BranchDistance? get selectedShop; bool get ishopDetailsSheetVisible; bool get isAdjusttingDistance; String? get selectedMarkerId; double get radius; bool get isAdjusting; int get selectedBranchId; String get selectedShopname; String get selectedBranchName; int get branchCount; LatLng get currentLocation; CameraPosition? get initialPosition; String get searchQuery; bool get isSearchingProduct;
/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShopsStateCopyWith<ShopsState> get copyWith => _$ShopsStateCopyWithImpl<ShopsState>(this as ShopsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShopsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&const DeepCollectionEquality().equals(other.nearbyShops, nearbyShops)&&const DeepCollectionEquality().equals(other.markers, markers)&&(identical(other.error, error) || other.error == error)&&(identical(other.userLocation, userLocation) || other.userLocation == userLocation)&&(identical(other.clientInfo, clientInfo) || other.clientInfo == clientInfo)&&(identical(other.userMarker, userMarker) || other.userMarker == userMarker)&&(identical(other.selectedShop, selectedShop) || other.selectedShop == selectedShop)&&(identical(other.ishopDetailsSheetVisible, ishopDetailsSheetVisible) || other.ishopDetailsSheetVisible == ishopDetailsSheetVisible)&&(identical(other.isAdjusttingDistance, isAdjusttingDistance) || other.isAdjusttingDistance == isAdjusttingDistance)&&(identical(other.selectedMarkerId, selectedMarkerId) || other.selectedMarkerId == selectedMarkerId)&&(identical(other.radius, radius) || other.radius == radius)&&(identical(other.isAdjusting, isAdjusting) || other.isAdjusting == isAdjusting)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.selectedShopname, selectedShopname) || other.selectedShopname == selectedShopname)&&(identical(other.selectedBranchName, selectedBranchName) || other.selectedBranchName == selectedBranchName)&&(identical(other.branchCount, branchCount) || other.branchCount == branchCount)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.initialPosition, initialPosition) || other.initialPosition == initialPosition)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.isSearchingProduct, isSearchingProduct) || other.isSearchingProduct == isSearchingProduct));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,const DeepCollectionEquality().hash(nearbyShops),const DeepCollectionEquality().hash(markers),error,userLocation,clientInfo,userMarker,selectedShop,ishopDetailsSheetVisible,isAdjusttingDistance,selectedMarkerId,radius,isAdjusting,selectedBranchId,selectedShopname,selectedBranchName,branchCount,currentLocation,initialPosition,searchQuery,isSearchingProduct]);

@override
String toString() {
  return 'ShopsState(isLoading: $isLoading, nearbyShops: $nearbyShops, markers: $markers, error: $error, userLocation: $userLocation, clientInfo: $clientInfo, userMarker: $userMarker, selectedShop: $selectedShop, ishopDetailsSheetVisible: $ishopDetailsSheetVisible, isAdjusttingDistance: $isAdjusttingDistance, selectedMarkerId: $selectedMarkerId, radius: $radius, isAdjusting: $isAdjusting, selectedBranchId: $selectedBranchId, selectedShopname: $selectedShopname, selectedBranchName: $selectedBranchName, branchCount: $branchCount, currentLocation: $currentLocation, initialPosition: $initialPosition, searchQuery: $searchQuery, isSearchingProduct: $isSearchingProduct)';
}


}

/// @nodoc
abstract mixin class $ShopsStateCopyWith<$Res>  {
  factory $ShopsStateCopyWith(ShopsState value, $Res Function(ShopsState) _then) = _$ShopsStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, List<BranchDistance> nearbyShops, Set<Marker> markers, String? error, Position? userLocation, ClientModel? clientInfo, Marker? userMarker, BranchDistance? selectedShop, bool ishopDetailsSheetVisible, bool isAdjusttingDistance, String? selectedMarkerId, double radius, bool isAdjusting, int selectedBranchId, String selectedShopname, String selectedBranchName, int branchCount, LatLng currentLocation, CameraPosition? initialPosition, String searchQuery, bool isSearchingProduct
});


$BranchDistanceCopyWith<$Res>? get selectedShop;

}
/// @nodoc
class _$ShopsStateCopyWithImpl<$Res>
    implements $ShopsStateCopyWith<$Res> {
  _$ShopsStateCopyWithImpl(this._self, this._then);

  final ShopsState _self;
  final $Res Function(ShopsState) _then;

/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? nearbyShops = null,Object? markers = null,Object? error = freezed,Object? userLocation = freezed,Object? clientInfo = freezed,Object? userMarker = freezed,Object? selectedShop = freezed,Object? ishopDetailsSheetVisible = null,Object? isAdjusttingDistance = null,Object? selectedMarkerId = freezed,Object? radius = null,Object? isAdjusting = null,Object? selectedBranchId = null,Object? selectedShopname = null,Object? selectedBranchName = null,Object? branchCount = null,Object? currentLocation = null,Object? initialPosition = freezed,Object? searchQuery = null,Object? isSearchingProduct = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,nearbyShops: null == nearbyShops ? _self.nearbyShops : nearbyShops // ignore: cast_nullable_to_non_nullable
as List<BranchDistance>,markers: null == markers ? _self.markers : markers // ignore: cast_nullable_to_non_nullable
as Set<Marker>,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,userLocation: freezed == userLocation ? _self.userLocation : userLocation // ignore: cast_nullable_to_non_nullable
as Position?,clientInfo: freezed == clientInfo ? _self.clientInfo : clientInfo // ignore: cast_nullable_to_non_nullable
as ClientModel?,userMarker: freezed == userMarker ? _self.userMarker : userMarker // ignore: cast_nullable_to_non_nullable
as Marker?,selectedShop: freezed == selectedShop ? _self.selectedShop : selectedShop // ignore: cast_nullable_to_non_nullable
as BranchDistance?,ishopDetailsSheetVisible: null == ishopDetailsSheetVisible ? _self.ishopDetailsSheetVisible : ishopDetailsSheetVisible // ignore: cast_nullable_to_non_nullable
as bool,isAdjusttingDistance: null == isAdjusttingDistance ? _self.isAdjusttingDistance : isAdjusttingDistance // ignore: cast_nullable_to_non_nullable
as bool,selectedMarkerId: freezed == selectedMarkerId ? _self.selectedMarkerId : selectedMarkerId // ignore: cast_nullable_to_non_nullable
as String?,radius: null == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double,isAdjusting: null == isAdjusting ? _self.isAdjusting : isAdjusting // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchId: null == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int,selectedShopname: null == selectedShopname ? _self.selectedShopname : selectedShopname // ignore: cast_nullable_to_non_nullable
as String,selectedBranchName: null == selectedBranchName ? _self.selectedBranchName : selectedBranchName // ignore: cast_nullable_to_non_nullable
as String,branchCount: null == branchCount ? _self.branchCount : branchCount // ignore: cast_nullable_to_non_nullable
as int,currentLocation: null == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng,initialPosition: freezed == initialPosition ? _self.initialPosition : initialPosition // ignore: cast_nullable_to_non_nullable
as CameraPosition?,searchQuery: null == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String,isSearchingProduct: null == isSearchingProduct ? _self.isSearchingProduct : isSearchingProduct // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BranchDistanceCopyWith<$Res>? get selectedShop {
    if (_self.selectedShop == null) {
    return null;
  }

  return $BranchDistanceCopyWith<$Res>(_self.selectedShop!, (value) {
    return _then(_self.copyWith(selectedShop: value));
  });
}
}


/// @nodoc


class _ShopsState implements ShopsState {
   _ShopsState({this.isLoading = false, final  List<BranchDistance> nearbyShops = const [], final  Set<Marker> markers = const {}, this.error, this.userLocation, this.clientInfo, this.userMarker, this.selectedShop, this.ishopDetailsSheetVisible = false, this.isAdjusttingDistance = false, this.selectedMarkerId, this.radius = 10, this.isAdjusting = false, this.selectedBranchId = -1, this.selectedShopname = '', this.selectedBranchName = '', this.branchCount = 0, this.currentLocation = const LatLng(0.0, 0.0), this.initialPosition, this.searchQuery = '', this.isSearchingProduct = false}): _nearbyShops = nearbyShops,_markers = markers;
  

@override@JsonKey() final  bool isLoading;
 final  List<BranchDistance> _nearbyShops;
@override@JsonKey() List<BranchDistance> get nearbyShops {
  if (_nearbyShops is EqualUnmodifiableListView) return _nearbyShops;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_nearbyShops);
}

 final  Set<Marker> _markers;
@override@JsonKey() Set<Marker> get markers {
  if (_markers is EqualUnmodifiableSetView) return _markers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_markers);
}

@override final  String? error;
@override final  Position? userLocation;
@override final  ClientModel? clientInfo;
@override final  Marker? userMarker;
@override final  BranchDistance? selectedShop;
@override@JsonKey() final  bool ishopDetailsSheetVisible;
@override@JsonKey() final  bool isAdjusttingDistance;
@override final  String? selectedMarkerId;
@override@JsonKey() final  double radius;
@override@JsonKey() final  bool isAdjusting;
@override@JsonKey() final  int selectedBranchId;
@override@JsonKey() final  String selectedShopname;
@override@JsonKey() final  String selectedBranchName;
@override@JsonKey() final  int branchCount;
@override@JsonKey() final  LatLng currentLocation;
@override final  CameraPosition? initialPosition;
@override@JsonKey() final  String searchQuery;
@override@JsonKey() final  bool isSearchingProduct;

/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShopsStateCopyWith<_ShopsState> get copyWith => __$ShopsStateCopyWithImpl<_ShopsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShopsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&const DeepCollectionEquality().equals(other._nearbyShops, _nearbyShops)&&const DeepCollectionEquality().equals(other._markers, _markers)&&(identical(other.error, error) || other.error == error)&&(identical(other.userLocation, userLocation) || other.userLocation == userLocation)&&(identical(other.clientInfo, clientInfo) || other.clientInfo == clientInfo)&&(identical(other.userMarker, userMarker) || other.userMarker == userMarker)&&(identical(other.selectedShop, selectedShop) || other.selectedShop == selectedShop)&&(identical(other.ishopDetailsSheetVisible, ishopDetailsSheetVisible) || other.ishopDetailsSheetVisible == ishopDetailsSheetVisible)&&(identical(other.isAdjusttingDistance, isAdjusttingDistance) || other.isAdjusttingDistance == isAdjusttingDistance)&&(identical(other.selectedMarkerId, selectedMarkerId) || other.selectedMarkerId == selectedMarkerId)&&(identical(other.radius, radius) || other.radius == radius)&&(identical(other.isAdjusting, isAdjusting) || other.isAdjusting == isAdjusting)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.selectedShopname, selectedShopname) || other.selectedShopname == selectedShopname)&&(identical(other.selectedBranchName, selectedBranchName) || other.selectedBranchName == selectedBranchName)&&(identical(other.branchCount, branchCount) || other.branchCount == branchCount)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.initialPosition, initialPosition) || other.initialPosition == initialPosition)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.isSearchingProduct, isSearchingProduct) || other.isSearchingProduct == isSearchingProduct));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,const DeepCollectionEquality().hash(_nearbyShops),const DeepCollectionEquality().hash(_markers),error,userLocation,clientInfo,userMarker,selectedShop,ishopDetailsSheetVisible,isAdjusttingDistance,selectedMarkerId,radius,isAdjusting,selectedBranchId,selectedShopname,selectedBranchName,branchCount,currentLocation,initialPosition,searchQuery,isSearchingProduct]);

@override
String toString() {
  return 'ShopsState(isLoading: $isLoading, nearbyShops: $nearbyShops, markers: $markers, error: $error, userLocation: $userLocation, clientInfo: $clientInfo, userMarker: $userMarker, selectedShop: $selectedShop, ishopDetailsSheetVisible: $ishopDetailsSheetVisible, isAdjusttingDistance: $isAdjusttingDistance, selectedMarkerId: $selectedMarkerId, radius: $radius, isAdjusting: $isAdjusting, selectedBranchId: $selectedBranchId, selectedShopname: $selectedShopname, selectedBranchName: $selectedBranchName, branchCount: $branchCount, currentLocation: $currentLocation, initialPosition: $initialPosition, searchQuery: $searchQuery, isSearchingProduct: $isSearchingProduct)';
}


}

/// @nodoc
abstract mixin class _$ShopsStateCopyWith<$Res> implements $ShopsStateCopyWith<$Res> {
  factory _$ShopsStateCopyWith(_ShopsState value, $Res Function(_ShopsState) _then) = __$ShopsStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, List<BranchDistance> nearbyShops, Set<Marker> markers, String? error, Position? userLocation, ClientModel? clientInfo, Marker? userMarker, BranchDistance? selectedShop, bool ishopDetailsSheetVisible, bool isAdjusttingDistance, String? selectedMarkerId, double radius, bool isAdjusting, int selectedBranchId, String selectedShopname, String selectedBranchName, int branchCount, LatLng currentLocation, CameraPosition? initialPosition, String searchQuery, bool isSearchingProduct
});


@override $BranchDistanceCopyWith<$Res>? get selectedShop;

}
/// @nodoc
class __$ShopsStateCopyWithImpl<$Res>
    implements _$ShopsStateCopyWith<$Res> {
  __$ShopsStateCopyWithImpl(this._self, this._then);

  final _ShopsState _self;
  final $Res Function(_ShopsState) _then;

/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? nearbyShops = null,Object? markers = null,Object? error = freezed,Object? userLocation = freezed,Object? clientInfo = freezed,Object? userMarker = freezed,Object? selectedShop = freezed,Object? ishopDetailsSheetVisible = null,Object? isAdjusttingDistance = null,Object? selectedMarkerId = freezed,Object? radius = null,Object? isAdjusting = null,Object? selectedBranchId = null,Object? selectedShopname = null,Object? selectedBranchName = null,Object? branchCount = null,Object? currentLocation = null,Object? initialPosition = freezed,Object? searchQuery = null,Object? isSearchingProduct = null,}) {
  return _then(_ShopsState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,nearbyShops: null == nearbyShops ? _self._nearbyShops : nearbyShops // ignore: cast_nullable_to_non_nullable
as List<BranchDistance>,markers: null == markers ? _self._markers : markers // ignore: cast_nullable_to_non_nullable
as Set<Marker>,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,userLocation: freezed == userLocation ? _self.userLocation : userLocation // ignore: cast_nullable_to_non_nullable
as Position?,clientInfo: freezed == clientInfo ? _self.clientInfo : clientInfo // ignore: cast_nullable_to_non_nullable
as ClientModel?,userMarker: freezed == userMarker ? _self.userMarker : userMarker // ignore: cast_nullable_to_non_nullable
as Marker?,selectedShop: freezed == selectedShop ? _self.selectedShop : selectedShop // ignore: cast_nullable_to_non_nullable
as BranchDistance?,ishopDetailsSheetVisible: null == ishopDetailsSheetVisible ? _self.ishopDetailsSheetVisible : ishopDetailsSheetVisible // ignore: cast_nullable_to_non_nullable
as bool,isAdjusttingDistance: null == isAdjusttingDistance ? _self.isAdjusttingDistance : isAdjusttingDistance // ignore: cast_nullable_to_non_nullable
as bool,selectedMarkerId: freezed == selectedMarkerId ? _self.selectedMarkerId : selectedMarkerId // ignore: cast_nullable_to_non_nullable
as String?,radius: null == radius ? _self.radius : radius // ignore: cast_nullable_to_non_nullable
as double,isAdjusting: null == isAdjusting ? _self.isAdjusting : isAdjusting // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchId: null == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int,selectedShopname: null == selectedShopname ? _self.selectedShopname : selectedShopname // ignore: cast_nullable_to_non_nullable
as String,selectedBranchName: null == selectedBranchName ? _self.selectedBranchName : selectedBranchName // ignore: cast_nullable_to_non_nullable
as String,branchCount: null == branchCount ? _self.branchCount : branchCount // ignore: cast_nullable_to_non_nullable
as int,currentLocation: null == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng,initialPosition: freezed == initialPosition ? _self.initialPosition : initialPosition // ignore: cast_nullable_to_non_nullable
as CameraPosition?,searchQuery: null == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String,isSearchingProduct: null == isSearchingProduct ? _self.isSearchingProduct : isSearchingProduct // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ShopsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BranchDistanceCopyWith<$Res>? get selectedShop {
    if (_self.selectedShop == null) {
    return null;
  }

  return $BranchDistanceCopyWith<$Res>(_self.selectedShop!, (value) {
    return _then(_self.copyWith(selectedShop: value));
  });
}
}

// dart format on
