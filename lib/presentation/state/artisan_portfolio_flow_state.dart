import 'package:build_mate/data/dto/artisan_rating_response.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_portfolio_flow_state.freezed.dart';
part 'artisan_portfolio_flow_state.g.dart';

@freezed
abstract class ArtisanPortfolioFlowState with _$ArtisanPortfolioFlowState {
   factory ArtisanPortfolioFlowState({
    ArtisanProfileDataResponse? artisanProfileData,
    @Default(false) bool isLoading,
    @Default([]) List<ArtisanImage> artisanImages,
    @Default(0) int currentUserId,
    @Default(0.0) double averageRating,
    @Default(0) int totalReviews,
    @Default([]) List<Map<String, dynamic>> ratings,
    @Default([]) List<ArtisanRatingResponse> ratingsList,
    @Default(false) bool isSavingProfile
   }) = _ArtisanPortfolioFlowState;

   factory ArtisanPortfolioFlowState.fromJson(Map<String, dynamic> json) => _$ArtisanPortfolioFlowStateFromJson(json);
}
