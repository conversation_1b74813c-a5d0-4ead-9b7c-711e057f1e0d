// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_portfolio_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ArtisanPortfolioFlowState {

 ArtisanProfileDataResponse? get artisanProfileData; bool get isLoading; List<ArtisanImage> get artisanImages; int get currentUserId; double get averageRating; int get totalReviews; List<Map<String, dynamic>> get ratings; List<ArtisanRatingResponse> get ratingsList; bool get isSavingProfile;
/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanPortfolioFlowStateCopyWith<ArtisanPortfolioFlowState> get copyWith => _$ArtisanPortfolioFlowStateCopyWithImpl<ArtisanPortfolioFlowState>(this as ArtisanPortfolioFlowState, _$identity);

  /// Serializes this ArtisanPortfolioFlowState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanPortfolioFlowState&&(identical(other.artisanProfileData, artisanProfileData) || other.artisanProfileData == artisanProfileData)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&const DeepCollectionEquality().equals(other.artisanImages, artisanImages)&&(identical(other.currentUserId, currentUserId) || other.currentUserId == currentUserId)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other.ratings, ratings)&&const DeepCollectionEquality().equals(other.ratingsList, ratingsList)&&(identical(other.isSavingProfile, isSavingProfile) || other.isSavingProfile == isSavingProfile));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,artisanProfileData,isLoading,const DeepCollectionEquality().hash(artisanImages),currentUserId,averageRating,totalReviews,const DeepCollectionEquality().hash(ratings),const DeepCollectionEquality().hash(ratingsList),isSavingProfile);

@override
String toString() {
  return 'ArtisanPortfolioFlowState(artisanProfileData: $artisanProfileData, isLoading: $isLoading, artisanImages: $artisanImages, currentUserId: $currentUserId, averageRating: $averageRating, totalReviews: $totalReviews, ratings: $ratings, ratingsList: $ratingsList, isSavingProfile: $isSavingProfile)';
}


}

/// @nodoc
abstract mixin class $ArtisanPortfolioFlowStateCopyWith<$Res>  {
  factory $ArtisanPortfolioFlowStateCopyWith(ArtisanPortfolioFlowState value, $Res Function(ArtisanPortfolioFlowState) _then) = _$ArtisanPortfolioFlowStateCopyWithImpl;
@useResult
$Res call({
 ArtisanProfileDataResponse? artisanProfileData, bool isLoading, List<ArtisanImage> artisanImages, int currentUserId, double averageRating, int totalReviews, List<Map<String, dynamic>> ratings, List<ArtisanRatingResponse> ratingsList, bool isSavingProfile
});


$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileData;

}
/// @nodoc
class _$ArtisanPortfolioFlowStateCopyWithImpl<$Res>
    implements $ArtisanPortfolioFlowStateCopyWith<$Res> {
  _$ArtisanPortfolioFlowStateCopyWithImpl(this._self, this._then);

  final ArtisanPortfolioFlowState _self;
  final $Res Function(ArtisanPortfolioFlowState) _then;

/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? artisanProfileData = freezed,Object? isLoading = null,Object? artisanImages = null,Object? currentUserId = null,Object? averageRating = null,Object? totalReviews = null,Object? ratings = null,Object? ratingsList = null,Object? isSavingProfile = null,}) {
  return _then(_self.copyWith(
artisanProfileData: freezed == artisanProfileData ? _self.artisanProfileData : artisanProfileData // ignore: cast_nullable_to_non_nullable
as ArtisanProfileDataResponse?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,artisanImages: null == artisanImages ? _self.artisanImages : artisanImages // ignore: cast_nullable_to_non_nullable
as List<ArtisanImage>,currentUserId: null == currentUserId ? _self.currentUserId : currentUserId // ignore: cast_nullable_to_non_nullable
as int,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,ratings: null == ratings ? _self.ratings : ratings // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,ratingsList: null == ratingsList ? _self.ratingsList : ratingsList // ignore: cast_nullable_to_non_nullable
as List<ArtisanRatingResponse>,isSavingProfile: null == isSavingProfile ? _self.isSavingProfile : isSavingProfile // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileData {
    if (_self.artisanProfileData == null) {
    return null;
  }

  return $ArtisanProfileDataResponseCopyWith<$Res>(_self.artisanProfileData!, (value) {
    return _then(_self.copyWith(artisanProfileData: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ArtisanPortfolioFlowState implements ArtisanPortfolioFlowState {
   _ArtisanPortfolioFlowState({this.artisanProfileData, this.isLoading = false, final  List<ArtisanImage> artisanImages = const [], this.currentUserId = 0, this.averageRating = 0.0, this.totalReviews = 0, final  List<Map<String, dynamic>> ratings = const [], final  List<ArtisanRatingResponse> ratingsList = const [], this.isSavingProfile = false}): _artisanImages = artisanImages,_ratings = ratings,_ratingsList = ratingsList;
  factory _ArtisanPortfolioFlowState.fromJson(Map<String, dynamic> json) => _$ArtisanPortfolioFlowStateFromJson(json);

@override final  ArtisanProfileDataResponse? artisanProfileData;
@override@JsonKey() final  bool isLoading;
 final  List<ArtisanImage> _artisanImages;
@override@JsonKey() List<ArtisanImage> get artisanImages {
  if (_artisanImages is EqualUnmodifiableListView) return _artisanImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_artisanImages);
}

@override@JsonKey() final  int currentUserId;
@override@JsonKey() final  double averageRating;
@override@JsonKey() final  int totalReviews;
 final  List<Map<String, dynamic>> _ratings;
@override@JsonKey() List<Map<String, dynamic>> get ratings {
  if (_ratings is EqualUnmodifiableListView) return _ratings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_ratings);
}

 final  List<ArtisanRatingResponse> _ratingsList;
@override@JsonKey() List<ArtisanRatingResponse> get ratingsList {
  if (_ratingsList is EqualUnmodifiableListView) return _ratingsList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_ratingsList);
}

@override@JsonKey() final  bool isSavingProfile;

/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanPortfolioFlowStateCopyWith<_ArtisanPortfolioFlowState> get copyWith => __$ArtisanPortfolioFlowStateCopyWithImpl<_ArtisanPortfolioFlowState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ArtisanPortfolioFlowStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanPortfolioFlowState&&(identical(other.artisanProfileData, artisanProfileData) || other.artisanProfileData == artisanProfileData)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&const DeepCollectionEquality().equals(other._artisanImages, _artisanImages)&&(identical(other.currentUserId, currentUserId) || other.currentUserId == currentUserId)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other._ratings, _ratings)&&const DeepCollectionEquality().equals(other._ratingsList, _ratingsList)&&(identical(other.isSavingProfile, isSavingProfile) || other.isSavingProfile == isSavingProfile));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,artisanProfileData,isLoading,const DeepCollectionEquality().hash(_artisanImages),currentUserId,averageRating,totalReviews,const DeepCollectionEquality().hash(_ratings),const DeepCollectionEquality().hash(_ratingsList),isSavingProfile);

@override
String toString() {
  return 'ArtisanPortfolioFlowState(artisanProfileData: $artisanProfileData, isLoading: $isLoading, artisanImages: $artisanImages, currentUserId: $currentUserId, averageRating: $averageRating, totalReviews: $totalReviews, ratings: $ratings, ratingsList: $ratingsList, isSavingProfile: $isSavingProfile)';
}


}

/// @nodoc
abstract mixin class _$ArtisanPortfolioFlowStateCopyWith<$Res> implements $ArtisanPortfolioFlowStateCopyWith<$Res> {
  factory _$ArtisanPortfolioFlowStateCopyWith(_ArtisanPortfolioFlowState value, $Res Function(_ArtisanPortfolioFlowState) _then) = __$ArtisanPortfolioFlowStateCopyWithImpl;
@override @useResult
$Res call({
 ArtisanProfileDataResponse? artisanProfileData, bool isLoading, List<ArtisanImage> artisanImages, int currentUserId, double averageRating, int totalReviews, List<Map<String, dynamic>> ratings, List<ArtisanRatingResponse> ratingsList, bool isSavingProfile
});


@override $ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileData;

}
/// @nodoc
class __$ArtisanPortfolioFlowStateCopyWithImpl<$Res>
    implements _$ArtisanPortfolioFlowStateCopyWith<$Res> {
  __$ArtisanPortfolioFlowStateCopyWithImpl(this._self, this._then);

  final _ArtisanPortfolioFlowState _self;
  final $Res Function(_ArtisanPortfolioFlowState) _then;

/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? artisanProfileData = freezed,Object? isLoading = null,Object? artisanImages = null,Object? currentUserId = null,Object? averageRating = null,Object? totalReviews = null,Object? ratings = null,Object? ratingsList = null,Object? isSavingProfile = null,}) {
  return _then(_ArtisanPortfolioFlowState(
artisanProfileData: freezed == artisanProfileData ? _self.artisanProfileData : artisanProfileData // ignore: cast_nullable_to_non_nullable
as ArtisanProfileDataResponse?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,artisanImages: null == artisanImages ? _self._artisanImages : artisanImages // ignore: cast_nullable_to_non_nullable
as List<ArtisanImage>,currentUserId: null == currentUserId ? _self.currentUserId : currentUserId // ignore: cast_nullable_to_non_nullable
as int,averageRating: null == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,ratings: null == ratings ? _self._ratings : ratings // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,ratingsList: null == ratingsList ? _self._ratingsList : ratingsList // ignore: cast_nullable_to_non_nullable
as List<ArtisanRatingResponse>,isSavingProfile: null == isSavingProfile ? _self.isSavingProfile : isSavingProfile // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ArtisanPortfolioFlowState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ArtisanProfileDataResponseCopyWith<$Res>? get artisanProfileData {
    if (_self.artisanProfileData == null) {
    return null;
  }

  return $ArtisanProfileDataResponseCopyWith<$Res>(_self.artisanProfileData!, (value) {
    return _then(_self.copyWith(artisanProfileData: value));
  });
}
}

// dart format on
