// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'client_profile_tab_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ClientProfileTabState {

 bool get isLoading; String get profileUrl; String get username; String get currentLoaction;
/// Create a copy of ClientProfileTabState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientProfileTabStateCopyWith<ClientProfileTabState> get copyWith => _$ClientProfileTabStateCopyWithImpl<ClientProfileTabState>(this as ClientProfileTabState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientProfileTabState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.currentLoaction, currentLoaction) || other.currentLoaction == currentLoaction));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,currentLoaction);

@override
String toString() {
  return 'ClientProfileTabState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, currentLoaction: $currentLoaction)';
}


}

/// @nodoc
abstract mixin class $ClientProfileTabStateCopyWith<$Res>  {
  factory $ClientProfileTabStateCopyWith(ClientProfileTabState value, $Res Function(ClientProfileTabState) _then) = _$ClientProfileTabStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String profileUrl, String username, String currentLoaction
});




}
/// @nodoc
class _$ClientProfileTabStateCopyWithImpl<$Res>
    implements $ClientProfileTabStateCopyWith<$Res> {
  _$ClientProfileTabStateCopyWithImpl(this._self, this._then);

  final ClientProfileTabState _self;
  final $Res Function(ClientProfileTabState) _then;

/// Create a copy of ClientProfileTabState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? currentLoaction = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,currentLoaction: null == currentLoaction ? _self.currentLoaction : currentLoaction // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _ClientProfileTabState implements ClientProfileTabState {
   _ClientProfileTabState({this.isLoading = false, this.profileUrl = '', this.username = '', this.currentLoaction = ''});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  String profileUrl;
@override@JsonKey() final  String username;
@override@JsonKey() final  String currentLoaction;

/// Create a copy of ClientProfileTabState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientProfileTabStateCopyWith<_ClientProfileTabState> get copyWith => __$ClientProfileTabStateCopyWithImpl<_ClientProfileTabState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientProfileTabState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.currentLoaction, currentLoaction) || other.currentLoaction == currentLoaction));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,currentLoaction);

@override
String toString() {
  return 'ClientProfileTabState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, currentLoaction: $currentLoaction)';
}


}

/// @nodoc
abstract mixin class _$ClientProfileTabStateCopyWith<$Res> implements $ClientProfileTabStateCopyWith<$Res> {
  factory _$ClientProfileTabStateCopyWith(_ClientProfileTabState value, $Res Function(_ClientProfileTabState) _then) = __$ClientProfileTabStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String profileUrl, String username, String currentLoaction
});




}
/// @nodoc
class __$ClientProfileTabStateCopyWithImpl<$Res>
    implements _$ClientProfileTabStateCopyWith<$Res> {
  __$ClientProfileTabStateCopyWithImpl(this._self, this._then);

  final _ClientProfileTabState _self;
  final $Res Function(_ClientProfileTabState) _then;

/// Create a copy of ClientProfileTabState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? currentLoaction = null,}) {
  return _then(_ClientProfileTabState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,currentLoaction: null == currentLoaction ? _self.currentLoaction : currentLoaction // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
