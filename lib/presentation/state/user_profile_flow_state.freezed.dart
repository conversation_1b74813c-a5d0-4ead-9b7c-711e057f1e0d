// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_profile_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UserProfileFlowState {

 String get fullname; String get email; String get phonenumber; String get secondPhonenumber; String get whatsappNumber; String get address; String get nationalId; String get avatarUrl; bool get isUpdatingProfile; String get supabaseUUID; LatLng get location;
/// Create a copy of UserProfileFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserProfileFlowStateCopyWith<UserProfileFlowState> get copyWith => _$UserProfileFlowStateCopyWithImpl<UserProfileFlowState>(this as UserProfileFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserProfileFlowState&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isUpdatingProfile, isUpdatingProfile) || other.isUpdatingProfile == isUpdatingProfile)&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.location, location) || other.location == location));
}


@override
int get hashCode => Object.hash(runtimeType,fullname,email,phonenumber,secondPhonenumber,whatsappNumber,address,nationalId,avatarUrl,isUpdatingProfile,supabaseUUID,location);

@override
String toString() {
  return 'UserProfileFlowState(fullname: $fullname, email: $email, phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, isUpdatingProfile: $isUpdatingProfile, supabaseUUID: $supabaseUUID, location: $location)';
}


}

/// @nodoc
abstract mixin class $UserProfileFlowStateCopyWith<$Res>  {
  factory $UserProfileFlowStateCopyWith(UserProfileFlowState value, $Res Function(UserProfileFlowState) _then) = _$UserProfileFlowStateCopyWithImpl;
@useResult
$Res call({
 String fullname, String email, String phonenumber, String secondPhonenumber, String whatsappNumber, String address, String nationalId, String avatarUrl, bool isUpdatingProfile, String supabaseUUID, LatLng location
});




}
/// @nodoc
class _$UserProfileFlowStateCopyWithImpl<$Res>
    implements $UserProfileFlowStateCopyWith<$Res> {
  _$UserProfileFlowStateCopyWithImpl(this._self, this._then);

  final UserProfileFlowState _self;
  final $Res Function(UserProfileFlowState) _then;

/// Create a copy of UserProfileFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fullname = null,Object? email = null,Object? phonenumber = null,Object? secondPhonenumber = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? isUpdatingProfile = null,Object? supabaseUUID = null,Object? location = null,}) {
  return _then(_self.copyWith(
fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,secondPhonenumber: null == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,isUpdatingProfile: null == isUpdatingProfile ? _self.isUpdatingProfile : isUpdatingProfile // ignore: cast_nullable_to_non_nullable
as bool,supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LatLng,
  ));
}

}


/// @nodoc


class _UserProfileFlowState implements UserProfileFlowState {
   _UserProfileFlowState({this.fullname = '', this.email = '', this.phonenumber = '', this.secondPhonenumber = '', this.whatsappNumber = '', this.address = '', this.nationalId = '', this.avatarUrl = '', this.isUpdatingProfile = false, this.supabaseUUID = '', this.location = const LatLng(0.0, 0.0)});
  

@override@JsonKey() final  String fullname;
@override@JsonKey() final  String email;
@override@JsonKey() final  String phonenumber;
@override@JsonKey() final  String secondPhonenumber;
@override@JsonKey() final  String whatsappNumber;
@override@JsonKey() final  String address;
@override@JsonKey() final  String nationalId;
@override@JsonKey() final  String avatarUrl;
@override@JsonKey() final  bool isUpdatingProfile;
@override@JsonKey() final  String supabaseUUID;
@override@JsonKey() final  LatLng location;

/// Create a copy of UserProfileFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserProfileFlowStateCopyWith<_UserProfileFlowState> get copyWith => __$UserProfileFlowStateCopyWithImpl<_UserProfileFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserProfileFlowState&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.phonenumber, phonenumber) || other.phonenumber == phonenumber)&&(identical(other.secondPhonenumber, secondPhonenumber) || other.secondPhonenumber == secondPhonenumber)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isUpdatingProfile, isUpdatingProfile) || other.isUpdatingProfile == isUpdatingProfile)&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.location, location) || other.location == location));
}


@override
int get hashCode => Object.hash(runtimeType,fullname,email,phonenumber,secondPhonenumber,whatsappNumber,address,nationalId,avatarUrl,isUpdatingProfile,supabaseUUID,location);

@override
String toString() {
  return 'UserProfileFlowState(fullname: $fullname, email: $email, phonenumber: $phonenumber, secondPhonenumber: $secondPhonenumber, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, isUpdatingProfile: $isUpdatingProfile, supabaseUUID: $supabaseUUID, location: $location)';
}


}

/// @nodoc
abstract mixin class _$UserProfileFlowStateCopyWith<$Res> implements $UserProfileFlowStateCopyWith<$Res> {
  factory _$UserProfileFlowStateCopyWith(_UserProfileFlowState value, $Res Function(_UserProfileFlowState) _then) = __$UserProfileFlowStateCopyWithImpl;
@override @useResult
$Res call({
 String fullname, String email, String phonenumber, String secondPhonenumber, String whatsappNumber, String address, String nationalId, String avatarUrl, bool isUpdatingProfile, String supabaseUUID, LatLng location
});




}
/// @nodoc
class __$UserProfileFlowStateCopyWithImpl<$Res>
    implements _$UserProfileFlowStateCopyWith<$Res> {
  __$UserProfileFlowStateCopyWithImpl(this._self, this._then);

  final _UserProfileFlowState _self;
  final $Res Function(_UserProfileFlowState) _then;

/// Create a copy of UserProfileFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fullname = null,Object? email = null,Object? phonenumber = null,Object? secondPhonenumber = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? isUpdatingProfile = null,Object? supabaseUUID = null,Object? location = null,}) {
  return _then(_UserProfileFlowState(
fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phonenumber: null == phonenumber ? _self.phonenumber : phonenumber // ignore: cast_nullable_to_non_nullable
as String,secondPhonenumber: null == secondPhonenumber ? _self.secondPhonenumber : secondPhonenumber // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,isUpdatingProfile: null == isUpdatingProfile ? _self.isUpdatingProfile : isUpdatingProfile // ignore: cast_nullable_to_non_nullable
as bool,supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LatLng,
  ));
}


}

// dart format on
