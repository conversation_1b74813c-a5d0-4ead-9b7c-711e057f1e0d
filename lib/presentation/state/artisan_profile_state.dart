import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'artisan_profile_state.freezed.dart';

@freezed
abstract class ArtisanProfileState with _$ArtisanProfileState {
  factory ArtisanProfileState({
    @Default('') String supabaseUUID,
    @Default('') String fullname,
    @Default('') String email,
    @Default('') String phoneNumber,
    @Default('') String secondPhoneNumber,
    @Default('') String whatsappNumber,
    @Default('') String address,
    @Default('') String nationalId,
    @Default('') String avatarUrl,
    @Default('') String coverImageUrl,
    @Default(LatLng(0, 0)) LatLng location,
    @Default(false) bool isUpdatingProfile,
  }) = _ArtisanProfileState;
}
