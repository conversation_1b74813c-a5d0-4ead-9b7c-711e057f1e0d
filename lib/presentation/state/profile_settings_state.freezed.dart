// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_settings_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProfileSettingsState {

 bool get isLoading; String get profileUrl; String get username; int get imageVersion; String? get errorMessage;
/// Create a copy of ProfileSettingsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileSettingsStateCopyWith<ProfileSettingsState> get copyWith => _$ProfileSettingsStateCopyWithImpl<ProfileSettingsState>(this as ProfileSettingsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileSettingsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.imageVersion, imageVersion) || other.imageVersion == imageVersion)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,imageVersion,errorMessage);

@override
String toString() {
  return 'ProfileSettingsState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, imageVersion: $imageVersion, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $ProfileSettingsStateCopyWith<$Res>  {
  factory $ProfileSettingsStateCopyWith(ProfileSettingsState value, $Res Function(ProfileSettingsState) _then) = _$ProfileSettingsStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String profileUrl, String username, int imageVersion, String? errorMessage
});




}
/// @nodoc
class _$ProfileSettingsStateCopyWithImpl<$Res>
    implements $ProfileSettingsStateCopyWith<$Res> {
  _$ProfileSettingsStateCopyWithImpl(this._self, this._then);

  final ProfileSettingsState _self;
  final $Res Function(ProfileSettingsState) _then;

/// Create a copy of ProfileSettingsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? imageVersion = null,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,imageVersion: null == imageVersion ? _self.imageVersion : imageVersion // ignore: cast_nullable_to_non_nullable
as int,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _ProfileSettingsState implements ProfileSettingsState {
   _ProfileSettingsState({this.isLoading = false, this.profileUrl = '', this.username = '', this.imageVersion = 0, this.errorMessage});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  String profileUrl;
@override@JsonKey() final  String username;
@override@JsonKey() final  int imageVersion;
@override final  String? errorMessage;

/// Create a copy of ProfileSettingsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProfileSettingsStateCopyWith<_ProfileSettingsState> get copyWith => __$ProfileSettingsStateCopyWithImpl<_ProfileSettingsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProfileSettingsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.profileUrl, profileUrl) || other.profileUrl == profileUrl)&&(identical(other.username, username) || other.username == username)&&(identical(other.imageVersion, imageVersion) || other.imageVersion == imageVersion)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,profileUrl,username,imageVersion,errorMessage);

@override
String toString() {
  return 'ProfileSettingsState(isLoading: $isLoading, profileUrl: $profileUrl, username: $username, imageVersion: $imageVersion, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$ProfileSettingsStateCopyWith<$Res> implements $ProfileSettingsStateCopyWith<$Res> {
  factory _$ProfileSettingsStateCopyWith(_ProfileSettingsState value, $Res Function(_ProfileSettingsState) _then) = __$ProfileSettingsStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String profileUrl, String username, int imageVersion, String? errorMessage
});




}
/// @nodoc
class __$ProfileSettingsStateCopyWithImpl<$Res>
    implements _$ProfileSettingsStateCopyWith<$Res> {
  __$ProfileSettingsStateCopyWithImpl(this._self, this._then);

  final _ProfileSettingsState _self;
  final $Res Function(_ProfileSettingsState) _then;

/// Create a copy of ProfileSettingsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? profileUrl = null,Object? username = null,Object? imageVersion = null,Object? errorMessage = freezed,}) {
  return _then(_ProfileSettingsState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,profileUrl: null == profileUrl ? _self.profileUrl : profileUrl // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,imageVersion: null == imageVersion ? _self.imageVersion : imageVersion // ignore: cast_nullable_to_non_nullable
as int,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
