// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'artisan_profile_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ArtisanProfileState {

 String get supabaseUUID; String get fullname; String get email; String get phoneNumber; String get secondPhoneNumber; String get whatsappNumber; String get address; String get nationalId; String get avatarUrl; String get coverImageUrl; LatLng get location; bool get isUpdatingProfile;
/// Create a copy of ArtisanProfileState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ArtisanProfileStateCopyWith<ArtisanProfileState> get copyWith => _$ArtisanProfileStateCopyWithImpl<ArtisanProfileState>(this as ArtisanProfileState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ArtisanProfileState&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.secondPhoneNumber, secondPhoneNumber) || other.secondPhoneNumber == secondPhoneNumber)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.location, location) || other.location == location)&&(identical(other.isUpdatingProfile, isUpdatingProfile) || other.isUpdatingProfile == isUpdatingProfile));
}


@override
int get hashCode => Object.hash(runtimeType,supabaseUUID,fullname,email,phoneNumber,secondPhoneNumber,whatsappNumber,address,nationalId,avatarUrl,coverImageUrl,location,isUpdatingProfile);

@override
String toString() {
  return 'ArtisanProfileState(supabaseUUID: $supabaseUUID, fullname: $fullname, email: $email, phoneNumber: $phoneNumber, secondPhoneNumber: $secondPhoneNumber, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, coverImageUrl: $coverImageUrl, location: $location, isUpdatingProfile: $isUpdatingProfile)';
}


}

/// @nodoc
abstract mixin class $ArtisanProfileStateCopyWith<$Res>  {
  factory $ArtisanProfileStateCopyWith(ArtisanProfileState value, $Res Function(ArtisanProfileState) _then) = _$ArtisanProfileStateCopyWithImpl;
@useResult
$Res call({
 String supabaseUUID, String fullname, String email, String phoneNumber, String secondPhoneNumber, String whatsappNumber, String address, String nationalId, String avatarUrl, String coverImageUrl, LatLng location, bool isUpdatingProfile
});




}
/// @nodoc
class _$ArtisanProfileStateCopyWithImpl<$Res>
    implements $ArtisanProfileStateCopyWith<$Res> {
  _$ArtisanProfileStateCopyWithImpl(this._self, this._then);

  final ArtisanProfileState _self;
  final $Res Function(ArtisanProfileState) _then;

/// Create a copy of ArtisanProfileState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? supabaseUUID = null,Object? fullname = null,Object? email = null,Object? phoneNumber = null,Object? secondPhoneNumber = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? coverImageUrl = null,Object? location = null,Object? isUpdatingProfile = null,}) {
  return _then(_self.copyWith(
supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,secondPhoneNumber: null == secondPhoneNumber ? _self.secondPhoneNumber : secondPhoneNumber // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LatLng,isUpdatingProfile: null == isUpdatingProfile ? _self.isUpdatingProfile : isUpdatingProfile // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _ArtisanProfileState implements ArtisanProfileState {
   _ArtisanProfileState({this.supabaseUUID = '', this.fullname = '', this.email = '', this.phoneNumber = '', this.secondPhoneNumber = '', this.whatsappNumber = '', this.address = '', this.nationalId = '', this.avatarUrl = '', this.coverImageUrl = '', this.location = const LatLng(0, 0), this.isUpdatingProfile = false});
  

@override@JsonKey() final  String supabaseUUID;
@override@JsonKey() final  String fullname;
@override@JsonKey() final  String email;
@override@JsonKey() final  String phoneNumber;
@override@JsonKey() final  String secondPhoneNumber;
@override@JsonKey() final  String whatsappNumber;
@override@JsonKey() final  String address;
@override@JsonKey() final  String nationalId;
@override@JsonKey() final  String avatarUrl;
@override@JsonKey() final  String coverImageUrl;
@override@JsonKey() final  LatLng location;
@override@JsonKey() final  bool isUpdatingProfile;

/// Create a copy of ArtisanProfileState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ArtisanProfileStateCopyWith<_ArtisanProfileState> get copyWith => __$ArtisanProfileStateCopyWithImpl<_ArtisanProfileState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ArtisanProfileState&&(identical(other.supabaseUUID, supabaseUUID) || other.supabaseUUID == supabaseUUID)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.email, email) || other.email == email)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.secondPhoneNumber, secondPhoneNumber) || other.secondPhoneNumber == secondPhoneNumber)&&(identical(other.whatsappNumber, whatsappNumber) || other.whatsappNumber == whatsappNumber)&&(identical(other.address, address) || other.address == address)&&(identical(other.nationalId, nationalId) || other.nationalId == nationalId)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.coverImageUrl, coverImageUrl) || other.coverImageUrl == coverImageUrl)&&(identical(other.location, location) || other.location == location)&&(identical(other.isUpdatingProfile, isUpdatingProfile) || other.isUpdatingProfile == isUpdatingProfile));
}


@override
int get hashCode => Object.hash(runtimeType,supabaseUUID,fullname,email,phoneNumber,secondPhoneNumber,whatsappNumber,address,nationalId,avatarUrl,coverImageUrl,location,isUpdatingProfile);

@override
String toString() {
  return 'ArtisanProfileState(supabaseUUID: $supabaseUUID, fullname: $fullname, email: $email, phoneNumber: $phoneNumber, secondPhoneNumber: $secondPhoneNumber, whatsappNumber: $whatsappNumber, address: $address, nationalId: $nationalId, avatarUrl: $avatarUrl, coverImageUrl: $coverImageUrl, location: $location, isUpdatingProfile: $isUpdatingProfile)';
}


}

/// @nodoc
abstract mixin class _$ArtisanProfileStateCopyWith<$Res> implements $ArtisanProfileStateCopyWith<$Res> {
  factory _$ArtisanProfileStateCopyWith(_ArtisanProfileState value, $Res Function(_ArtisanProfileState) _then) = __$ArtisanProfileStateCopyWithImpl;
@override @useResult
$Res call({
 String supabaseUUID, String fullname, String email, String phoneNumber, String secondPhoneNumber, String whatsappNumber, String address, String nationalId, String avatarUrl, String coverImageUrl, LatLng location, bool isUpdatingProfile
});




}
/// @nodoc
class __$ArtisanProfileStateCopyWithImpl<$Res>
    implements _$ArtisanProfileStateCopyWith<$Res> {
  __$ArtisanProfileStateCopyWithImpl(this._self, this._then);

  final _ArtisanProfileState _self;
  final $Res Function(_ArtisanProfileState) _then;

/// Create a copy of ArtisanProfileState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? supabaseUUID = null,Object? fullname = null,Object? email = null,Object? phoneNumber = null,Object? secondPhoneNumber = null,Object? whatsappNumber = null,Object? address = null,Object? nationalId = null,Object? avatarUrl = null,Object? coverImageUrl = null,Object? location = null,Object? isUpdatingProfile = null,}) {
  return _then(_ArtisanProfileState(
supabaseUUID: null == supabaseUUID ? _self.supabaseUUID : supabaseUUID // ignore: cast_nullable_to_non_nullable
as String,fullname: null == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,secondPhoneNumber: null == secondPhoneNumber ? _self.secondPhoneNumber : secondPhoneNumber // ignore: cast_nullable_to_non_nullable
as String,whatsappNumber: null == whatsappNumber ? _self.whatsappNumber : whatsappNumber // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,nationalId: null == nationalId ? _self.nationalId : nationalId // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,coverImageUrl: null == coverImageUrl ? _self.coverImageUrl : coverImageUrl // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as LatLng,isUpdatingProfile: null == isUpdatingProfile ? _self.isUpdatingProfile : isUpdatingProfile // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
