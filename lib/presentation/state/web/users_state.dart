import 'package:build_mate/data/dto/branch_staff_response.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'users_state.freezed.dart';

@freezed
abstract class UsersState with _$UsersState {
  factory UsersState({
    @Default(false) bool isLoading,
    @Default(false) bool isSaving,
    @Default(false) bool saveSuccess,
    String? errorMessage,
    @Default([])
    List<String> users, // Assuming users are represented as a list of strings
    int? selectedUserId,
    String? selectedUserName,
    @Default([]) List<BranchResponse> branches,
    @Default(-1) int selectedBranchIndex,
    @Default(false) bool isAllSelected,
    @Default(0) int selectedBranchId,
    @Default(false) bool isLoadingBranches,
    @Default('') String fullName,
    @Default('') String email,
    @Default(false) bool isSavingUser,
    @Default([]) List<BranchStaffResponse> branchUsers,
    @Default(false) bool isLoadingBranchUsers,
    @Default(false) bool isUpdatingUser,
    @Default(false) bool isResettingPassword,
    @Default(0) int updateProgress,
    @Default(0) int allUsersCount,
    @Default(0) int passwordResetProgress,
  }) = _UsersState;
}
