// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branches_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BranchesState {

 List<BranchResponse> get branches; bool get isLoading; bool get isUpdatingDetails; bool get isAddingBranch; String? get errorMessage; String get shopName; String get shopOwner; String get contactEmail; String get phoneNumber; String get mainAddress; String get selectedAddress; String get branchName; String get branchAddress; String get branchCity; String get branchPhonenumberOne; String get branchPhonenumberTwo; String get branchEmailOne; String get branchEmailTwo; bool get isUploadingImage; bool get isDeletingBranch; List<String> get imageUrls; AsyncValue<SuggestionsResponse> get addressLocationResult; List<Suggestion> get addressPlacesList; LatLng? get currentLocation; LatLng? get selectedLocation; AsyncValue<LocationResponse> get locationByIdResponse; bool get isGettingLocationByPlaceId; List<BranchModel> get branchModels;
/// Create a copy of BranchesState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BranchesStateCopyWith<BranchesState> get copyWith => _$BranchesStateCopyWithImpl<BranchesState>(this as BranchesState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BranchesState&&const DeepCollectionEquality().equals(other.branches, branches)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isUpdatingDetails, isUpdatingDetails) || other.isUpdatingDetails == isUpdatingDetails)&&(identical(other.isAddingBranch, isAddingBranch) || other.isAddingBranch == isAddingBranch)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.shopName, shopName) || other.shopName == shopName)&&(identical(other.shopOwner, shopOwner) || other.shopOwner == shopOwner)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.selectedAddress, selectedAddress) || other.selectedAddress == selectedAddress)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.branchAddress, branchAddress) || other.branchAddress == branchAddress)&&(identical(other.branchCity, branchCity) || other.branchCity == branchCity)&&(identical(other.branchPhonenumberOne, branchPhonenumberOne) || other.branchPhonenumberOne == branchPhonenumberOne)&&(identical(other.branchPhonenumberTwo, branchPhonenumberTwo) || other.branchPhonenumberTwo == branchPhonenumberTwo)&&(identical(other.branchEmailOne, branchEmailOne) || other.branchEmailOne == branchEmailOne)&&(identical(other.branchEmailTwo, branchEmailTwo) || other.branchEmailTwo == branchEmailTwo)&&(identical(other.isUploadingImage, isUploadingImage) || other.isUploadingImage == isUploadingImage)&&(identical(other.isDeletingBranch, isDeletingBranch) || other.isDeletingBranch == isDeletingBranch)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&(identical(other.addressLocationResult, addressLocationResult) || other.addressLocationResult == addressLocationResult)&&const DeepCollectionEquality().equals(other.addressPlacesList, addressPlacesList)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.selectedLocation, selectedLocation) || other.selectedLocation == selectedLocation)&&(identical(other.locationByIdResponse, locationByIdResponse) || other.locationByIdResponse == locationByIdResponse)&&(identical(other.isGettingLocationByPlaceId, isGettingLocationByPlaceId) || other.isGettingLocationByPlaceId == isGettingLocationByPlaceId)&&const DeepCollectionEquality().equals(other.branchModels, branchModels));
}


@override
int get hashCode => Object.hashAll([runtimeType,const DeepCollectionEquality().hash(branches),isLoading,isUpdatingDetails,isAddingBranch,errorMessage,shopName,shopOwner,contactEmail,phoneNumber,mainAddress,selectedAddress,branchName,branchAddress,branchCity,branchPhonenumberOne,branchPhonenumberTwo,branchEmailOne,branchEmailTwo,isUploadingImage,isDeletingBranch,const DeepCollectionEquality().hash(imageUrls),addressLocationResult,const DeepCollectionEquality().hash(addressPlacesList),currentLocation,selectedLocation,locationByIdResponse,isGettingLocationByPlaceId,const DeepCollectionEquality().hash(branchModels)]);

@override
String toString() {
  return 'BranchesState(branches: $branches, isLoading: $isLoading, isUpdatingDetails: $isUpdatingDetails, isAddingBranch: $isAddingBranch, errorMessage: $errorMessage, shopName: $shopName, shopOwner: $shopOwner, contactEmail: $contactEmail, phoneNumber: $phoneNumber, mainAddress: $mainAddress, selectedAddress: $selectedAddress, branchName: $branchName, branchAddress: $branchAddress, branchCity: $branchCity, branchPhonenumberOne: $branchPhonenumberOne, branchPhonenumberTwo: $branchPhonenumberTwo, branchEmailOne: $branchEmailOne, branchEmailTwo: $branchEmailTwo, isUploadingImage: $isUploadingImage, isDeletingBranch: $isDeletingBranch, imageUrls: $imageUrls, addressLocationResult: $addressLocationResult, addressPlacesList: $addressPlacesList, currentLocation: $currentLocation, selectedLocation: $selectedLocation, locationByIdResponse: $locationByIdResponse, isGettingLocationByPlaceId: $isGettingLocationByPlaceId, branchModels: $branchModels)';
}


}

/// @nodoc
abstract mixin class $BranchesStateCopyWith<$Res>  {
  factory $BranchesStateCopyWith(BranchesState value, $Res Function(BranchesState) _then) = _$BranchesStateCopyWithImpl;
@useResult
$Res call({
 List<BranchResponse> branches, bool isLoading, bool isUpdatingDetails, bool isAddingBranch, String? errorMessage, String shopName, String shopOwner, String contactEmail, String phoneNumber, String mainAddress, String selectedAddress, String branchName, String branchAddress, String branchCity, String branchPhonenumberOne, String branchPhonenumberTwo, String branchEmailOne, String branchEmailTwo, bool isUploadingImage, bool isDeletingBranch, List<String> imageUrls, AsyncValue<SuggestionsResponse> addressLocationResult, List<Suggestion> addressPlacesList, LatLng? currentLocation, LatLng? selectedLocation, AsyncValue<LocationResponse> locationByIdResponse, bool isGettingLocationByPlaceId, List<BranchModel> branchModels
});




}
/// @nodoc
class _$BranchesStateCopyWithImpl<$Res>
    implements $BranchesStateCopyWith<$Res> {
  _$BranchesStateCopyWithImpl(this._self, this._then);

  final BranchesState _self;
  final $Res Function(BranchesState) _then;

/// Create a copy of BranchesState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? branches = null,Object? isLoading = null,Object? isUpdatingDetails = null,Object? isAddingBranch = null,Object? errorMessage = freezed,Object? shopName = null,Object? shopOwner = null,Object? contactEmail = null,Object? phoneNumber = null,Object? mainAddress = null,Object? selectedAddress = null,Object? branchName = null,Object? branchAddress = null,Object? branchCity = null,Object? branchPhonenumberOne = null,Object? branchPhonenumberTwo = null,Object? branchEmailOne = null,Object? branchEmailTwo = null,Object? isUploadingImage = null,Object? isDeletingBranch = null,Object? imageUrls = null,Object? addressLocationResult = null,Object? addressPlacesList = null,Object? currentLocation = freezed,Object? selectedLocation = freezed,Object? locationByIdResponse = null,Object? isGettingLocationByPlaceId = null,Object? branchModels = null,}) {
  return _then(_self.copyWith(
branches: null == branches ? _self.branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isUpdatingDetails: null == isUpdatingDetails ? _self.isUpdatingDetails : isUpdatingDetails // ignore: cast_nullable_to_non_nullable
as bool,isAddingBranch: null == isAddingBranch ? _self.isAddingBranch : isAddingBranch // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,shopName: null == shopName ? _self.shopName : shopName // ignore: cast_nullable_to_non_nullable
as String,shopOwner: null == shopOwner ? _self.shopOwner : shopOwner // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,selectedAddress: null == selectedAddress ? _self.selectedAddress : selectedAddress // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,branchAddress: null == branchAddress ? _self.branchAddress : branchAddress // ignore: cast_nullable_to_non_nullable
as String,branchCity: null == branchCity ? _self.branchCity : branchCity // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberOne: null == branchPhonenumberOne ? _self.branchPhonenumberOne : branchPhonenumberOne // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberTwo: null == branchPhonenumberTwo ? _self.branchPhonenumberTwo : branchPhonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,branchEmailOne: null == branchEmailOne ? _self.branchEmailOne : branchEmailOne // ignore: cast_nullable_to_non_nullable
as String,branchEmailTwo: null == branchEmailTwo ? _self.branchEmailTwo : branchEmailTwo // ignore: cast_nullable_to_non_nullable
as String,isUploadingImage: null == isUploadingImage ? _self.isUploadingImage : isUploadingImage // ignore: cast_nullable_to_non_nullable
as bool,isDeletingBranch: null == isDeletingBranch ? _self.isDeletingBranch : isDeletingBranch // ignore: cast_nullable_to_non_nullable
as bool,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,addressLocationResult: null == addressLocationResult ? _self.addressLocationResult : addressLocationResult // ignore: cast_nullable_to_non_nullable
as AsyncValue<SuggestionsResponse>,addressPlacesList: null == addressPlacesList ? _self.addressPlacesList : addressPlacesList // ignore: cast_nullable_to_non_nullable
as List<Suggestion>,currentLocation: freezed == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,selectedLocation: freezed == selectedLocation ? _self.selectedLocation : selectedLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,locationByIdResponse: null == locationByIdResponse ? _self.locationByIdResponse : locationByIdResponse // ignore: cast_nullable_to_non_nullable
as AsyncValue<LocationResponse>,isGettingLocationByPlaceId: null == isGettingLocationByPlaceId ? _self.isGettingLocationByPlaceId : isGettingLocationByPlaceId // ignore: cast_nullable_to_non_nullable
as bool,branchModels: null == branchModels ? _self.branchModels : branchModels // ignore: cast_nullable_to_non_nullable
as List<BranchModel>,
  ));
}

}


/// @nodoc


class _BranchesState implements BranchesState {
   _BranchesState({final  List<BranchResponse> branches = const [], this.isLoading = false, this.isUpdatingDetails = false, this.isAddingBranch = false, this.errorMessage, this.shopName = '', this.shopOwner = '', this.contactEmail = '', this.phoneNumber = '', this.mainAddress = '', this.selectedAddress = '', this.branchName = '', this.branchAddress = '', this.branchCity = '', this.branchPhonenumberOne = '', this.branchPhonenumberTwo = '', this.branchEmailOne = '', this.branchEmailTwo = '', this.isUploadingImage = false, this.isDeletingBranch = false, final  List<String> imageUrls = const [], required this.addressLocationResult, final  List<Suggestion> addressPlacesList = const [], this.currentLocation = const LatLng(0.0, 0.0), this.selectedLocation, required this.locationByIdResponse, this.isGettingLocationByPlaceId = false, final  List<BranchModel> branchModels = const []}): _branches = branches,_imageUrls = imageUrls,_addressPlacesList = addressPlacesList,_branchModels = branchModels;
  

 final  List<BranchResponse> _branches;
@override@JsonKey() List<BranchResponse> get branches {
  if (_branches is EqualUnmodifiableListView) return _branches;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branches);
}

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isUpdatingDetails;
@override@JsonKey() final  bool isAddingBranch;
@override final  String? errorMessage;
@override@JsonKey() final  String shopName;
@override@JsonKey() final  String shopOwner;
@override@JsonKey() final  String contactEmail;
@override@JsonKey() final  String phoneNumber;
@override@JsonKey() final  String mainAddress;
@override@JsonKey() final  String selectedAddress;
@override@JsonKey() final  String branchName;
@override@JsonKey() final  String branchAddress;
@override@JsonKey() final  String branchCity;
@override@JsonKey() final  String branchPhonenumberOne;
@override@JsonKey() final  String branchPhonenumberTwo;
@override@JsonKey() final  String branchEmailOne;
@override@JsonKey() final  String branchEmailTwo;
@override@JsonKey() final  bool isUploadingImage;
@override@JsonKey() final  bool isDeletingBranch;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

@override final  AsyncValue<SuggestionsResponse> addressLocationResult;
 final  List<Suggestion> _addressPlacesList;
@override@JsonKey() List<Suggestion> get addressPlacesList {
  if (_addressPlacesList is EqualUnmodifiableListView) return _addressPlacesList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_addressPlacesList);
}

@override@JsonKey() final  LatLng? currentLocation;
@override final  LatLng? selectedLocation;
@override final  AsyncValue<LocationResponse> locationByIdResponse;
@override@JsonKey() final  bool isGettingLocationByPlaceId;
 final  List<BranchModel> _branchModels;
@override@JsonKey() List<BranchModel> get branchModels {
  if (_branchModels is EqualUnmodifiableListView) return _branchModels;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branchModels);
}


/// Create a copy of BranchesState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BranchesStateCopyWith<_BranchesState> get copyWith => __$BranchesStateCopyWithImpl<_BranchesState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BranchesState&&const DeepCollectionEquality().equals(other._branches, _branches)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isUpdatingDetails, isUpdatingDetails) || other.isUpdatingDetails == isUpdatingDetails)&&(identical(other.isAddingBranch, isAddingBranch) || other.isAddingBranch == isAddingBranch)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.shopName, shopName) || other.shopName == shopName)&&(identical(other.shopOwner, shopOwner) || other.shopOwner == shopOwner)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.selectedAddress, selectedAddress) || other.selectedAddress == selectedAddress)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.branchAddress, branchAddress) || other.branchAddress == branchAddress)&&(identical(other.branchCity, branchCity) || other.branchCity == branchCity)&&(identical(other.branchPhonenumberOne, branchPhonenumberOne) || other.branchPhonenumberOne == branchPhonenumberOne)&&(identical(other.branchPhonenumberTwo, branchPhonenumberTwo) || other.branchPhonenumberTwo == branchPhonenumberTwo)&&(identical(other.branchEmailOne, branchEmailOne) || other.branchEmailOne == branchEmailOne)&&(identical(other.branchEmailTwo, branchEmailTwo) || other.branchEmailTwo == branchEmailTwo)&&(identical(other.isUploadingImage, isUploadingImage) || other.isUploadingImage == isUploadingImage)&&(identical(other.isDeletingBranch, isDeletingBranch) || other.isDeletingBranch == isDeletingBranch)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&(identical(other.addressLocationResult, addressLocationResult) || other.addressLocationResult == addressLocationResult)&&const DeepCollectionEquality().equals(other._addressPlacesList, _addressPlacesList)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.selectedLocation, selectedLocation) || other.selectedLocation == selectedLocation)&&(identical(other.locationByIdResponse, locationByIdResponse) || other.locationByIdResponse == locationByIdResponse)&&(identical(other.isGettingLocationByPlaceId, isGettingLocationByPlaceId) || other.isGettingLocationByPlaceId == isGettingLocationByPlaceId)&&const DeepCollectionEquality().equals(other._branchModels, _branchModels));
}


@override
int get hashCode => Object.hashAll([runtimeType,const DeepCollectionEquality().hash(_branches),isLoading,isUpdatingDetails,isAddingBranch,errorMessage,shopName,shopOwner,contactEmail,phoneNumber,mainAddress,selectedAddress,branchName,branchAddress,branchCity,branchPhonenumberOne,branchPhonenumberTwo,branchEmailOne,branchEmailTwo,isUploadingImage,isDeletingBranch,const DeepCollectionEquality().hash(_imageUrls),addressLocationResult,const DeepCollectionEquality().hash(_addressPlacesList),currentLocation,selectedLocation,locationByIdResponse,isGettingLocationByPlaceId,const DeepCollectionEquality().hash(_branchModels)]);

@override
String toString() {
  return 'BranchesState(branches: $branches, isLoading: $isLoading, isUpdatingDetails: $isUpdatingDetails, isAddingBranch: $isAddingBranch, errorMessage: $errorMessage, shopName: $shopName, shopOwner: $shopOwner, contactEmail: $contactEmail, phoneNumber: $phoneNumber, mainAddress: $mainAddress, selectedAddress: $selectedAddress, branchName: $branchName, branchAddress: $branchAddress, branchCity: $branchCity, branchPhonenumberOne: $branchPhonenumberOne, branchPhonenumberTwo: $branchPhonenumberTwo, branchEmailOne: $branchEmailOne, branchEmailTwo: $branchEmailTwo, isUploadingImage: $isUploadingImage, isDeletingBranch: $isDeletingBranch, imageUrls: $imageUrls, addressLocationResult: $addressLocationResult, addressPlacesList: $addressPlacesList, currentLocation: $currentLocation, selectedLocation: $selectedLocation, locationByIdResponse: $locationByIdResponse, isGettingLocationByPlaceId: $isGettingLocationByPlaceId, branchModels: $branchModels)';
}


}

/// @nodoc
abstract mixin class _$BranchesStateCopyWith<$Res> implements $BranchesStateCopyWith<$Res> {
  factory _$BranchesStateCopyWith(_BranchesState value, $Res Function(_BranchesState) _then) = __$BranchesStateCopyWithImpl;
@override @useResult
$Res call({
 List<BranchResponse> branches, bool isLoading, bool isUpdatingDetails, bool isAddingBranch, String? errorMessage, String shopName, String shopOwner, String contactEmail, String phoneNumber, String mainAddress, String selectedAddress, String branchName, String branchAddress, String branchCity, String branchPhonenumberOne, String branchPhonenumberTwo, String branchEmailOne, String branchEmailTwo, bool isUploadingImage, bool isDeletingBranch, List<String> imageUrls, AsyncValue<SuggestionsResponse> addressLocationResult, List<Suggestion> addressPlacesList, LatLng? currentLocation, LatLng? selectedLocation, AsyncValue<LocationResponse> locationByIdResponse, bool isGettingLocationByPlaceId, List<BranchModel> branchModels
});




}
/// @nodoc
class __$BranchesStateCopyWithImpl<$Res>
    implements _$BranchesStateCopyWith<$Res> {
  __$BranchesStateCopyWithImpl(this._self, this._then);

  final _BranchesState _self;
  final $Res Function(_BranchesState) _then;

/// Create a copy of BranchesState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? branches = null,Object? isLoading = null,Object? isUpdatingDetails = null,Object? isAddingBranch = null,Object? errorMessage = freezed,Object? shopName = null,Object? shopOwner = null,Object? contactEmail = null,Object? phoneNumber = null,Object? mainAddress = null,Object? selectedAddress = null,Object? branchName = null,Object? branchAddress = null,Object? branchCity = null,Object? branchPhonenumberOne = null,Object? branchPhonenumberTwo = null,Object? branchEmailOne = null,Object? branchEmailTwo = null,Object? isUploadingImage = null,Object? isDeletingBranch = null,Object? imageUrls = null,Object? addressLocationResult = null,Object? addressPlacesList = null,Object? currentLocation = freezed,Object? selectedLocation = freezed,Object? locationByIdResponse = null,Object? isGettingLocationByPlaceId = null,Object? branchModels = null,}) {
  return _then(_BranchesState(
branches: null == branches ? _self._branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isUpdatingDetails: null == isUpdatingDetails ? _self.isUpdatingDetails : isUpdatingDetails // ignore: cast_nullable_to_non_nullable
as bool,isAddingBranch: null == isAddingBranch ? _self.isAddingBranch : isAddingBranch // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,shopName: null == shopName ? _self.shopName : shopName // ignore: cast_nullable_to_non_nullable
as String,shopOwner: null == shopOwner ? _self.shopOwner : shopOwner // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,selectedAddress: null == selectedAddress ? _self.selectedAddress : selectedAddress // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,branchAddress: null == branchAddress ? _self.branchAddress : branchAddress // ignore: cast_nullable_to_non_nullable
as String,branchCity: null == branchCity ? _self.branchCity : branchCity // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberOne: null == branchPhonenumberOne ? _self.branchPhonenumberOne : branchPhonenumberOne // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberTwo: null == branchPhonenumberTwo ? _self.branchPhonenumberTwo : branchPhonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,branchEmailOne: null == branchEmailOne ? _self.branchEmailOne : branchEmailOne // ignore: cast_nullable_to_non_nullable
as String,branchEmailTwo: null == branchEmailTwo ? _self.branchEmailTwo : branchEmailTwo // ignore: cast_nullable_to_non_nullable
as String,isUploadingImage: null == isUploadingImage ? _self.isUploadingImage : isUploadingImage // ignore: cast_nullable_to_non_nullable
as bool,isDeletingBranch: null == isDeletingBranch ? _self.isDeletingBranch : isDeletingBranch // ignore: cast_nullable_to_non_nullable
as bool,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,addressLocationResult: null == addressLocationResult ? _self.addressLocationResult : addressLocationResult // ignore: cast_nullable_to_non_nullable
as AsyncValue<SuggestionsResponse>,addressPlacesList: null == addressPlacesList ? _self._addressPlacesList : addressPlacesList // ignore: cast_nullable_to_non_nullable
as List<Suggestion>,currentLocation: freezed == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,selectedLocation: freezed == selectedLocation ? _self.selectedLocation : selectedLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,locationByIdResponse: null == locationByIdResponse ? _self.locationByIdResponse : locationByIdResponse // ignore: cast_nullable_to_non_nullable
as AsyncValue<LocationResponse>,isGettingLocationByPlaceId: null == isGettingLocationByPlaceId ? _self.isGettingLocationByPlaceId : isGettingLocationByPlaceId // ignore: cast_nullable_to_non_nullable
as bool,branchModels: null == branchModels ? _self._branchModels : branchModels // ignore: cast_nullable_to_non_nullable
as List<BranchModel>,
  ));
}


}

// dart format on
