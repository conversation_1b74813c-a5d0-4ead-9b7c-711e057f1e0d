import 'package:build_mate/data/dto/hardware_items_response.dart';
import 'package:build_mate/data/dto/hardware_sub_category_response.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/data/models/category_model_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'inventory_state.freezed.dart';

@freezed
abstract class InventoryState with _$InventoryState {
  factory InventoryState({
    @Default(false) bool isLoading,
    @Default(false) bool isSaving,
    @Default(false) bool saveSuccess,
    String? errorMessage,
    @Default(false) bool isLoadingCategories,
    @Default(false) bool isLoadingSubCategories,
    @Default(false) bool isLoadingBranches,
    @Default([]) List<BranchResponse> branches,
    @Default([]) List<CategoryResponse> categories,
    @Default([]) List<HardwareSubCategoryResponse> subCategories,
    int? selectedCategoryId,
    int? selectedSubCategoryId,
    int? selectedBranchId,
    HardwareSubCategoryResponse? selectedSubCategoryObject,
    @Default('') String productName,
    @Default('') String productDescription,
    @Default('') String productPrice,
    @Default([]) List<String> imageUrls,
    @Default([false, false, false, false]) List<bool> imageUploadingStates,
    @Default([]) List<bool> imageLoadingStates,
    @Default(0) int currentImageIndex,
    @Default(false) bool isScrolling,
    @Default([]) List<HardwareItem> products,
    @Default(false) bool isLoadingProducts,
    @Default(-1) int selectedBranchIndex,
    @Default(false) bool isLoadingBranchUsers,
    @Default(false) bool isAllSelected,
    @Default(0) int allProductsCount,
    @Default(false) bool isDeleting,
    @Default(0) int filterCategoryId,
    @Default(0) int selectedFilterId,
  }) = _InventoryState;
}
