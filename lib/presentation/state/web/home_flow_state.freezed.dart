// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of '../home_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$HomeFlowState {

 List<ServiceIconsResponse> get services; bool get isLoadingServices;
/// Create a copy of HomeFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HomeFlowStateCopyWith<HomeFlowState> get copyWith => _$HomeFlowStateCopyWithImpl<HomeFlowState>(this as HomeFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HomeFlowState&&const DeepCollectionEquality().equals(other.services, services)&&(identical(other.isLoadingServices, isLoadingServices) || other.isLoadingServices == isLoadingServices));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(services),isLoadingServices);

@override
String toString() {
  return 'HomeFlowState(services: $services, isLoadingServices: $isLoadingServices)';
}


}

/// @nodoc
abstract mixin class $HomeFlowStateCopyWith<$Res>  {
  factory $HomeFlowStateCopyWith(HomeFlowState value, $Res Function(HomeFlowState) _then) = _$HomeFlowStateCopyWithImpl;
@useResult
$Res call({
 List<ServiceIconsResponse> services, bool isLoadingServices
});




}
/// @nodoc
class _$HomeFlowStateCopyWithImpl<$Res>
    implements $HomeFlowStateCopyWith<$Res> {
  _$HomeFlowStateCopyWithImpl(this._self, this._then);

  final HomeFlowState _self;
  final $Res Function(HomeFlowState) _then;

/// Create a copy of HomeFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? services = null,Object? isLoadingServices = null,}) {
  return _then(_self.copyWith(
services: null == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as List<ServiceIconsResponse>,isLoadingServices: null == isLoadingServices ? _self.isLoadingServices : isLoadingServices // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _HomeFlowState implements HomeFlowState {
   _HomeFlowState({final  List<ServiceIconsResponse> services = const [], this.isLoadingServices = false}): _services = services;
  

 final  List<ServiceIconsResponse> _services;
@override@JsonKey() List<ServiceIconsResponse> get services {
  if (_services is EqualUnmodifiableListView) return _services;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_services);
}

@override@JsonKey() final  bool isLoadingServices;

/// Create a copy of HomeFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HomeFlowStateCopyWith<_HomeFlowState> get copyWith => __$HomeFlowStateCopyWithImpl<_HomeFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HomeFlowState&&const DeepCollectionEquality().equals(other._services, _services)&&(identical(other.isLoadingServices, isLoadingServices) || other.isLoadingServices == isLoadingServices));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_services),isLoadingServices);

@override
String toString() {
  return 'HomeFlowState(services: $services, isLoadingServices: $isLoadingServices)';
}


}

/// @nodoc
abstract mixin class _$HomeFlowStateCopyWith<$Res> implements $HomeFlowStateCopyWith<$Res> {
  factory _$HomeFlowStateCopyWith(_HomeFlowState value, $Res Function(_HomeFlowState) _then) = __$HomeFlowStateCopyWithImpl;
@override @useResult
$Res call({
 List<ServiceIconsResponse> services, bool isLoadingServices
});




}
/// @nodoc
class __$HomeFlowStateCopyWithImpl<$Res>
    implements _$HomeFlowStateCopyWith<$Res> {
  __$HomeFlowStateCopyWithImpl(this._self, this._then);

  final _HomeFlowState _self;
  final $Res Function(_HomeFlowState) _then;

/// Create a copy of HomeFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? services = null,Object? isLoadingServices = null,}) {
  return _then(_HomeFlowState(
services: null == services ? _self._services : services // ignore: cast_nullable_to_non_nullable
as List<ServiceIconsResponse>,isLoadingServices: null == isLoadingServices ? _self.isLoadingServices : isLoadingServices // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
