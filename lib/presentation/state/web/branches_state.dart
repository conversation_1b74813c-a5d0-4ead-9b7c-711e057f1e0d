import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/data/models/branch_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'branches_state.freezed.dart';

@freezed
abstract class BranchesState with _$BranchesState {
  factory BranchesState({
    @Default([]) List<BranchResponse> branches,
    @Default(false) bool isLoading,
    @Default(false) bool isUpdatingDetails,
    @Default(false) bool isAddingBranch,
    String? errorMessage,
    @Default('') String shopName,
    @Default('') String shopOwner,
    @Default('') String contactEmail,
    @Default('') String phoneNumber,
    @Default('') String mainAddress,
    @Default('') String selectedAddress,
    @Default('') String branchName,
    @Default('') String branchAddress,
    @Default('') String branchCity,
    @Default('') String branchPhonenumberOne,
    @Default('') String branchPhonenumberTwo,
    @Default('') String branchEmailOne,
    @Default('') String branchEmailTwo,
    @Default(false) bool isUploadingImage,
    @Default(false) bool isDeletingBranch,
    @Default([]) List<String> imageUrls,
    required AsyncValue<SuggestionsResponse> addressLocationResult,
    @Default([]) List<Suggestion> addressPlacesList,
    @Default(LatLng(0.0, 0.0)) LatLng? currentLocation,
    LatLng? selectedLocation,
    required AsyncValue<LocationResponse> locationByIdResponse,
    @Default(false) bool isGettingLocationByPlaceId,
    @Default([]) List<BranchModel> branchModels,
  }) = _BranchesState;
}
