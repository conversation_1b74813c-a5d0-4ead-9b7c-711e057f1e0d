// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'inventory_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$InventoryState {

 bool get isLoading; bool get isSaving; bool get saveSuccess; String? get errorMessage; bool get isLoadingCategories; bool get isLoadingSubCategories; bool get isLoadingBranches; List<BranchResponse> get branches; List<CategoryResponse> get categories; List<HardwareSubCategoryResponse> get subCategories; int? get selectedCategoryId; int? get selectedSubCategoryId; int? get selectedBranchId; HardwareSubCategoryResponse? get selectedSubCategoryObject; String get productName; String get productDescription; String get productPrice; List<String> get imageUrls; List<bool> get imageUploadingStates; List<bool> get imageLoadingStates; int get currentImageIndex; bool get isScrolling; List<HardwareItem> get products; bool get isLoadingProducts; int get selectedBranchIndex; bool get isLoadingBranchUsers; bool get isAllSelected; int get allProductsCount; bool get isDeleting; int get filterCategoryId; int get selectedFilterId;
/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InventoryStateCopyWith<InventoryState> get copyWith => _$InventoryStateCopyWithImpl<InventoryState>(this as InventoryState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InventoryState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.saveSuccess, saveSuccess) || other.saveSuccess == saveSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isLoadingCategories, isLoadingCategories) || other.isLoadingCategories == isLoadingCategories)&&(identical(other.isLoadingSubCategories, isLoadingSubCategories) || other.isLoadingSubCategories == isLoadingSubCategories)&&(identical(other.isLoadingBranches, isLoadingBranches) || other.isLoadingBranches == isLoadingBranches)&&const DeepCollectionEquality().equals(other.branches, branches)&&const DeepCollectionEquality().equals(other.categories, categories)&&const DeepCollectionEquality().equals(other.subCategories, subCategories)&&(identical(other.selectedCategoryId, selectedCategoryId) || other.selectedCategoryId == selectedCategoryId)&&(identical(other.selectedSubCategoryId, selectedSubCategoryId) || other.selectedSubCategoryId == selectedSubCategoryId)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.selectedSubCategoryObject, selectedSubCategoryObject) || other.selectedSubCategoryObject == selectedSubCategoryObject)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productDescription, productDescription) || other.productDescription == productDescription)&&(identical(other.productPrice, productPrice) || other.productPrice == productPrice)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&const DeepCollectionEquality().equals(other.imageUploadingStates, imageUploadingStates)&&const DeepCollectionEquality().equals(other.imageLoadingStates, imageLoadingStates)&&(identical(other.currentImageIndex, currentImageIndex) || other.currentImageIndex == currentImageIndex)&&(identical(other.isScrolling, isScrolling) || other.isScrolling == isScrolling)&&const DeepCollectionEquality().equals(other.products, products)&&(identical(other.isLoadingProducts, isLoadingProducts) || other.isLoadingProducts == isLoadingProducts)&&(identical(other.selectedBranchIndex, selectedBranchIndex) || other.selectedBranchIndex == selectedBranchIndex)&&(identical(other.isLoadingBranchUsers, isLoadingBranchUsers) || other.isLoadingBranchUsers == isLoadingBranchUsers)&&(identical(other.isAllSelected, isAllSelected) || other.isAllSelected == isAllSelected)&&(identical(other.allProductsCount, allProductsCount) || other.allProductsCount == allProductsCount)&&(identical(other.isDeleting, isDeleting) || other.isDeleting == isDeleting)&&(identical(other.filterCategoryId, filterCategoryId) || other.filterCategoryId == filterCategoryId)&&(identical(other.selectedFilterId, selectedFilterId) || other.selectedFilterId == selectedFilterId));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,isSaving,saveSuccess,errorMessage,isLoadingCategories,isLoadingSubCategories,isLoadingBranches,const DeepCollectionEquality().hash(branches),const DeepCollectionEquality().hash(categories),const DeepCollectionEquality().hash(subCategories),selectedCategoryId,selectedSubCategoryId,selectedBranchId,selectedSubCategoryObject,productName,productDescription,productPrice,const DeepCollectionEquality().hash(imageUrls),const DeepCollectionEquality().hash(imageUploadingStates),const DeepCollectionEquality().hash(imageLoadingStates),currentImageIndex,isScrolling,const DeepCollectionEquality().hash(products),isLoadingProducts,selectedBranchIndex,isLoadingBranchUsers,isAllSelected,allProductsCount,isDeleting,filterCategoryId,selectedFilterId]);

@override
String toString() {
  return 'InventoryState(isLoading: $isLoading, isSaving: $isSaving, saveSuccess: $saveSuccess, errorMessage: $errorMessage, isLoadingCategories: $isLoadingCategories, isLoadingSubCategories: $isLoadingSubCategories, isLoadingBranches: $isLoadingBranches, branches: $branches, categories: $categories, subCategories: $subCategories, selectedCategoryId: $selectedCategoryId, selectedSubCategoryId: $selectedSubCategoryId, selectedBranchId: $selectedBranchId, selectedSubCategoryObject: $selectedSubCategoryObject, productName: $productName, productDescription: $productDescription, productPrice: $productPrice, imageUrls: $imageUrls, imageUploadingStates: $imageUploadingStates, imageLoadingStates: $imageLoadingStates, currentImageIndex: $currentImageIndex, isScrolling: $isScrolling, products: $products, isLoadingProducts: $isLoadingProducts, selectedBranchIndex: $selectedBranchIndex, isLoadingBranchUsers: $isLoadingBranchUsers, isAllSelected: $isAllSelected, allProductsCount: $allProductsCount, isDeleting: $isDeleting, filterCategoryId: $filterCategoryId, selectedFilterId: $selectedFilterId)';
}


}

/// @nodoc
abstract mixin class $InventoryStateCopyWith<$Res>  {
  factory $InventoryStateCopyWith(InventoryState value, $Res Function(InventoryState) _then) = _$InventoryStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool isSaving, bool saveSuccess, String? errorMessage, bool isLoadingCategories, bool isLoadingSubCategories, bool isLoadingBranches, List<BranchResponse> branches, List<CategoryResponse> categories, List<HardwareSubCategoryResponse> subCategories, int? selectedCategoryId, int? selectedSubCategoryId, int? selectedBranchId, HardwareSubCategoryResponse? selectedSubCategoryObject, String productName, String productDescription, String productPrice, List<String> imageUrls, List<bool> imageUploadingStates, List<bool> imageLoadingStates, int currentImageIndex, bool isScrolling, List<HardwareItem> products, bool isLoadingProducts, int selectedBranchIndex, bool isLoadingBranchUsers, bool isAllSelected, int allProductsCount, bool isDeleting, int filterCategoryId, int selectedFilterId
});


$HardwareSubCategoryResponseCopyWith<$Res>? get selectedSubCategoryObject;

}
/// @nodoc
class _$InventoryStateCopyWithImpl<$Res>
    implements $InventoryStateCopyWith<$Res> {
  _$InventoryStateCopyWithImpl(this._self, this._then);

  final InventoryState _self;
  final $Res Function(InventoryState) _then;

/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? isSaving = null,Object? saveSuccess = null,Object? errorMessage = freezed,Object? isLoadingCategories = null,Object? isLoadingSubCategories = null,Object? isLoadingBranches = null,Object? branches = null,Object? categories = null,Object? subCategories = null,Object? selectedCategoryId = freezed,Object? selectedSubCategoryId = freezed,Object? selectedBranchId = freezed,Object? selectedSubCategoryObject = freezed,Object? productName = null,Object? productDescription = null,Object? productPrice = null,Object? imageUrls = null,Object? imageUploadingStates = null,Object? imageLoadingStates = null,Object? currentImageIndex = null,Object? isScrolling = null,Object? products = null,Object? isLoadingProducts = null,Object? selectedBranchIndex = null,Object? isLoadingBranchUsers = null,Object? isAllSelected = null,Object? allProductsCount = null,Object? isDeleting = null,Object? filterCategoryId = null,Object? selectedFilterId = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,saveSuccess: null == saveSuccess ? _self.saveSuccess : saveSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isLoadingCategories: null == isLoadingCategories ? _self.isLoadingCategories : isLoadingCategories // ignore: cast_nullable_to_non_nullable
as bool,isLoadingSubCategories: null == isLoadingSubCategories ? _self.isLoadingSubCategories : isLoadingSubCategories // ignore: cast_nullable_to_non_nullable
as bool,isLoadingBranches: null == isLoadingBranches ? _self.isLoadingBranches : isLoadingBranches // ignore: cast_nullable_to_non_nullable
as bool,branches: null == branches ? _self.branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as List<CategoryResponse>,subCategories: null == subCategories ? _self.subCategories : subCategories // ignore: cast_nullable_to_non_nullable
as List<HardwareSubCategoryResponse>,selectedCategoryId: freezed == selectedCategoryId ? _self.selectedCategoryId : selectedCategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubCategoryId: freezed == selectedSubCategoryId ? _self.selectedSubCategoryId : selectedSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedBranchId: freezed == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubCategoryObject: freezed == selectedSubCategoryObject ? _self.selectedSubCategoryObject : selectedSubCategoryObject // ignore: cast_nullable_to_non_nullable
as HardwareSubCategoryResponse?,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productDescription: null == productDescription ? _self.productDescription : productDescription // ignore: cast_nullable_to_non_nullable
as String,productPrice: null == productPrice ? _self.productPrice : productPrice // ignore: cast_nullable_to_non_nullable
as String,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,imageUploadingStates: null == imageUploadingStates ? _self.imageUploadingStates : imageUploadingStates // ignore: cast_nullable_to_non_nullable
as List<bool>,imageLoadingStates: null == imageLoadingStates ? _self.imageLoadingStates : imageLoadingStates // ignore: cast_nullable_to_non_nullable
as List<bool>,currentImageIndex: null == currentImageIndex ? _self.currentImageIndex : currentImageIndex // ignore: cast_nullable_to_non_nullable
as int,isScrolling: null == isScrolling ? _self.isScrolling : isScrolling // ignore: cast_nullable_to_non_nullable
as bool,products: null == products ? _self.products : products // ignore: cast_nullable_to_non_nullable
as List<HardwareItem>,isLoadingProducts: null == isLoadingProducts ? _self.isLoadingProducts : isLoadingProducts // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchIndex: null == selectedBranchIndex ? _self.selectedBranchIndex : selectedBranchIndex // ignore: cast_nullable_to_non_nullable
as int,isLoadingBranchUsers: null == isLoadingBranchUsers ? _self.isLoadingBranchUsers : isLoadingBranchUsers // ignore: cast_nullable_to_non_nullable
as bool,isAllSelected: null == isAllSelected ? _self.isAllSelected : isAllSelected // ignore: cast_nullable_to_non_nullable
as bool,allProductsCount: null == allProductsCount ? _self.allProductsCount : allProductsCount // ignore: cast_nullable_to_non_nullable
as int,isDeleting: null == isDeleting ? _self.isDeleting : isDeleting // ignore: cast_nullable_to_non_nullable
as bool,filterCategoryId: null == filterCategoryId ? _self.filterCategoryId : filterCategoryId // ignore: cast_nullable_to_non_nullable
as int,selectedFilterId: null == selectedFilterId ? _self.selectedFilterId : selectedFilterId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryResponseCopyWith<$Res>? get selectedSubCategoryObject {
    if (_self.selectedSubCategoryObject == null) {
    return null;
  }

  return $HardwareSubCategoryResponseCopyWith<$Res>(_self.selectedSubCategoryObject!, (value) {
    return _then(_self.copyWith(selectedSubCategoryObject: value));
  });
}
}


/// @nodoc


class _InventoryState implements InventoryState {
   _InventoryState({this.isLoading = false, this.isSaving = false, this.saveSuccess = false, this.errorMessage, this.isLoadingCategories = false, this.isLoadingSubCategories = false, this.isLoadingBranches = false, final  List<BranchResponse> branches = const [], final  List<CategoryResponse> categories = const [], final  List<HardwareSubCategoryResponse> subCategories = const [], this.selectedCategoryId, this.selectedSubCategoryId, this.selectedBranchId, this.selectedSubCategoryObject, this.productName = '', this.productDescription = '', this.productPrice = '', final  List<String> imageUrls = const [], final  List<bool> imageUploadingStates = const [false, false, false, false], final  List<bool> imageLoadingStates = const [], this.currentImageIndex = 0, this.isScrolling = false, final  List<HardwareItem> products = const [], this.isLoadingProducts = false, this.selectedBranchIndex = -1, this.isLoadingBranchUsers = false, this.isAllSelected = false, this.allProductsCount = 0, this.isDeleting = false, this.filterCategoryId = 0, this.selectedFilterId = 0}): _branches = branches,_categories = categories,_subCategories = subCategories,_imageUrls = imageUrls,_imageUploadingStates = imageUploadingStates,_imageLoadingStates = imageLoadingStates,_products = products;
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isSaving;
@override@JsonKey() final  bool saveSuccess;
@override final  String? errorMessage;
@override@JsonKey() final  bool isLoadingCategories;
@override@JsonKey() final  bool isLoadingSubCategories;
@override@JsonKey() final  bool isLoadingBranches;
 final  List<BranchResponse> _branches;
@override@JsonKey() List<BranchResponse> get branches {
  if (_branches is EqualUnmodifiableListView) return _branches;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branches);
}

 final  List<CategoryResponse> _categories;
@override@JsonKey() List<CategoryResponse> get categories {
  if (_categories is EqualUnmodifiableListView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_categories);
}

 final  List<HardwareSubCategoryResponse> _subCategories;
@override@JsonKey() List<HardwareSubCategoryResponse> get subCategories {
  if (_subCategories is EqualUnmodifiableListView) return _subCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_subCategories);
}

@override final  int? selectedCategoryId;
@override final  int? selectedSubCategoryId;
@override final  int? selectedBranchId;
@override final  HardwareSubCategoryResponse? selectedSubCategoryObject;
@override@JsonKey() final  String productName;
@override@JsonKey() final  String productDescription;
@override@JsonKey() final  String productPrice;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

 final  List<bool> _imageUploadingStates;
@override@JsonKey() List<bool> get imageUploadingStates {
  if (_imageUploadingStates is EqualUnmodifiableListView) return _imageUploadingStates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUploadingStates);
}

 final  List<bool> _imageLoadingStates;
@override@JsonKey() List<bool> get imageLoadingStates {
  if (_imageLoadingStates is EqualUnmodifiableListView) return _imageLoadingStates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageLoadingStates);
}

@override@JsonKey() final  int currentImageIndex;
@override@JsonKey() final  bool isScrolling;
 final  List<HardwareItem> _products;
@override@JsonKey() List<HardwareItem> get products {
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_products);
}

@override@JsonKey() final  bool isLoadingProducts;
@override@JsonKey() final  int selectedBranchIndex;
@override@JsonKey() final  bool isLoadingBranchUsers;
@override@JsonKey() final  bool isAllSelected;
@override@JsonKey() final  int allProductsCount;
@override@JsonKey() final  bool isDeleting;
@override@JsonKey() final  int filterCategoryId;
@override@JsonKey() final  int selectedFilterId;

/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InventoryStateCopyWith<_InventoryState> get copyWith => __$InventoryStateCopyWithImpl<_InventoryState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InventoryState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.saveSuccess, saveSuccess) || other.saveSuccess == saveSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isLoadingCategories, isLoadingCategories) || other.isLoadingCategories == isLoadingCategories)&&(identical(other.isLoadingSubCategories, isLoadingSubCategories) || other.isLoadingSubCategories == isLoadingSubCategories)&&(identical(other.isLoadingBranches, isLoadingBranches) || other.isLoadingBranches == isLoadingBranches)&&const DeepCollectionEquality().equals(other._branches, _branches)&&const DeepCollectionEquality().equals(other._categories, _categories)&&const DeepCollectionEquality().equals(other._subCategories, _subCategories)&&(identical(other.selectedCategoryId, selectedCategoryId) || other.selectedCategoryId == selectedCategoryId)&&(identical(other.selectedSubCategoryId, selectedSubCategoryId) || other.selectedSubCategoryId == selectedSubCategoryId)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.selectedSubCategoryObject, selectedSubCategoryObject) || other.selectedSubCategoryObject == selectedSubCategoryObject)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productDescription, productDescription) || other.productDescription == productDescription)&&(identical(other.productPrice, productPrice) || other.productPrice == productPrice)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&const DeepCollectionEquality().equals(other._imageUploadingStates, _imageUploadingStates)&&const DeepCollectionEquality().equals(other._imageLoadingStates, _imageLoadingStates)&&(identical(other.currentImageIndex, currentImageIndex) || other.currentImageIndex == currentImageIndex)&&(identical(other.isScrolling, isScrolling) || other.isScrolling == isScrolling)&&const DeepCollectionEquality().equals(other._products, _products)&&(identical(other.isLoadingProducts, isLoadingProducts) || other.isLoadingProducts == isLoadingProducts)&&(identical(other.selectedBranchIndex, selectedBranchIndex) || other.selectedBranchIndex == selectedBranchIndex)&&(identical(other.isLoadingBranchUsers, isLoadingBranchUsers) || other.isLoadingBranchUsers == isLoadingBranchUsers)&&(identical(other.isAllSelected, isAllSelected) || other.isAllSelected == isAllSelected)&&(identical(other.allProductsCount, allProductsCount) || other.allProductsCount == allProductsCount)&&(identical(other.isDeleting, isDeleting) || other.isDeleting == isDeleting)&&(identical(other.filterCategoryId, filterCategoryId) || other.filterCategoryId == filterCategoryId)&&(identical(other.selectedFilterId, selectedFilterId) || other.selectedFilterId == selectedFilterId));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,isSaving,saveSuccess,errorMessage,isLoadingCategories,isLoadingSubCategories,isLoadingBranches,const DeepCollectionEquality().hash(_branches),const DeepCollectionEquality().hash(_categories),const DeepCollectionEquality().hash(_subCategories),selectedCategoryId,selectedSubCategoryId,selectedBranchId,selectedSubCategoryObject,productName,productDescription,productPrice,const DeepCollectionEquality().hash(_imageUrls),const DeepCollectionEquality().hash(_imageUploadingStates),const DeepCollectionEquality().hash(_imageLoadingStates),currentImageIndex,isScrolling,const DeepCollectionEquality().hash(_products),isLoadingProducts,selectedBranchIndex,isLoadingBranchUsers,isAllSelected,allProductsCount,isDeleting,filterCategoryId,selectedFilterId]);

@override
String toString() {
  return 'InventoryState(isLoading: $isLoading, isSaving: $isSaving, saveSuccess: $saveSuccess, errorMessage: $errorMessage, isLoadingCategories: $isLoadingCategories, isLoadingSubCategories: $isLoadingSubCategories, isLoadingBranches: $isLoadingBranches, branches: $branches, categories: $categories, subCategories: $subCategories, selectedCategoryId: $selectedCategoryId, selectedSubCategoryId: $selectedSubCategoryId, selectedBranchId: $selectedBranchId, selectedSubCategoryObject: $selectedSubCategoryObject, productName: $productName, productDescription: $productDescription, productPrice: $productPrice, imageUrls: $imageUrls, imageUploadingStates: $imageUploadingStates, imageLoadingStates: $imageLoadingStates, currentImageIndex: $currentImageIndex, isScrolling: $isScrolling, products: $products, isLoadingProducts: $isLoadingProducts, selectedBranchIndex: $selectedBranchIndex, isLoadingBranchUsers: $isLoadingBranchUsers, isAllSelected: $isAllSelected, allProductsCount: $allProductsCount, isDeleting: $isDeleting, filterCategoryId: $filterCategoryId, selectedFilterId: $selectedFilterId)';
}


}

/// @nodoc
abstract mixin class _$InventoryStateCopyWith<$Res> implements $InventoryStateCopyWith<$Res> {
  factory _$InventoryStateCopyWith(_InventoryState value, $Res Function(_InventoryState) _then) = __$InventoryStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool isSaving, bool saveSuccess, String? errorMessage, bool isLoadingCategories, bool isLoadingSubCategories, bool isLoadingBranches, List<BranchResponse> branches, List<CategoryResponse> categories, List<HardwareSubCategoryResponse> subCategories, int? selectedCategoryId, int? selectedSubCategoryId, int? selectedBranchId, HardwareSubCategoryResponse? selectedSubCategoryObject, String productName, String productDescription, String productPrice, List<String> imageUrls, List<bool> imageUploadingStates, List<bool> imageLoadingStates, int currentImageIndex, bool isScrolling, List<HardwareItem> products, bool isLoadingProducts, int selectedBranchIndex, bool isLoadingBranchUsers, bool isAllSelected, int allProductsCount, bool isDeleting, int filterCategoryId, int selectedFilterId
});


@override $HardwareSubCategoryResponseCopyWith<$Res>? get selectedSubCategoryObject;

}
/// @nodoc
class __$InventoryStateCopyWithImpl<$Res>
    implements _$InventoryStateCopyWith<$Res> {
  __$InventoryStateCopyWithImpl(this._self, this._then);

  final _InventoryState _self;
  final $Res Function(_InventoryState) _then;

/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? isSaving = null,Object? saveSuccess = null,Object? errorMessage = freezed,Object? isLoadingCategories = null,Object? isLoadingSubCategories = null,Object? isLoadingBranches = null,Object? branches = null,Object? categories = null,Object? subCategories = null,Object? selectedCategoryId = freezed,Object? selectedSubCategoryId = freezed,Object? selectedBranchId = freezed,Object? selectedSubCategoryObject = freezed,Object? productName = null,Object? productDescription = null,Object? productPrice = null,Object? imageUrls = null,Object? imageUploadingStates = null,Object? imageLoadingStates = null,Object? currentImageIndex = null,Object? isScrolling = null,Object? products = null,Object? isLoadingProducts = null,Object? selectedBranchIndex = null,Object? isLoadingBranchUsers = null,Object? isAllSelected = null,Object? allProductsCount = null,Object? isDeleting = null,Object? filterCategoryId = null,Object? selectedFilterId = null,}) {
  return _then(_InventoryState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,saveSuccess: null == saveSuccess ? _self.saveSuccess : saveSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isLoadingCategories: null == isLoadingCategories ? _self.isLoadingCategories : isLoadingCategories // ignore: cast_nullable_to_non_nullable
as bool,isLoadingSubCategories: null == isLoadingSubCategories ? _self.isLoadingSubCategories : isLoadingSubCategories // ignore: cast_nullable_to_non_nullable
as bool,isLoadingBranches: null == isLoadingBranches ? _self.isLoadingBranches : isLoadingBranches // ignore: cast_nullable_to_non_nullable
as bool,branches: null == branches ? _self._branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as List<CategoryResponse>,subCategories: null == subCategories ? _self._subCategories : subCategories // ignore: cast_nullable_to_non_nullable
as List<HardwareSubCategoryResponse>,selectedCategoryId: freezed == selectedCategoryId ? _self.selectedCategoryId : selectedCategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubCategoryId: freezed == selectedSubCategoryId ? _self.selectedSubCategoryId : selectedSubCategoryId // ignore: cast_nullable_to_non_nullable
as int?,selectedBranchId: freezed == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int?,selectedSubCategoryObject: freezed == selectedSubCategoryObject ? _self.selectedSubCategoryObject : selectedSubCategoryObject // ignore: cast_nullable_to_non_nullable
as HardwareSubCategoryResponse?,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productDescription: null == productDescription ? _self.productDescription : productDescription // ignore: cast_nullable_to_non_nullable
as String,productPrice: null == productPrice ? _self.productPrice : productPrice // ignore: cast_nullable_to_non_nullable
as String,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,imageUploadingStates: null == imageUploadingStates ? _self._imageUploadingStates : imageUploadingStates // ignore: cast_nullable_to_non_nullable
as List<bool>,imageLoadingStates: null == imageLoadingStates ? _self._imageLoadingStates : imageLoadingStates // ignore: cast_nullable_to_non_nullable
as List<bool>,currentImageIndex: null == currentImageIndex ? _self.currentImageIndex : currentImageIndex // ignore: cast_nullable_to_non_nullable
as int,isScrolling: null == isScrolling ? _self.isScrolling : isScrolling // ignore: cast_nullable_to_non_nullable
as bool,products: null == products ? _self._products : products // ignore: cast_nullable_to_non_nullable
as List<HardwareItem>,isLoadingProducts: null == isLoadingProducts ? _self.isLoadingProducts : isLoadingProducts // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchIndex: null == selectedBranchIndex ? _self.selectedBranchIndex : selectedBranchIndex // ignore: cast_nullable_to_non_nullable
as int,isLoadingBranchUsers: null == isLoadingBranchUsers ? _self.isLoadingBranchUsers : isLoadingBranchUsers // ignore: cast_nullable_to_non_nullable
as bool,isAllSelected: null == isAllSelected ? _self.isAllSelected : isAllSelected // ignore: cast_nullable_to_non_nullable
as bool,allProductsCount: null == allProductsCount ? _self.allProductsCount : allProductsCount // ignore: cast_nullable_to_non_nullable
as int,isDeleting: null == isDeleting ? _self.isDeleting : isDeleting // ignore: cast_nullable_to_non_nullable
as bool,filterCategoryId: null == filterCategoryId ? _self.filterCategoryId : filterCategoryId // ignore: cast_nullable_to_non_nullable
as int,selectedFilterId: null == selectedFilterId ? _self.selectedFilterId : selectedFilterId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of InventoryState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HardwareSubCategoryResponseCopyWith<$Res>? get selectedSubCategoryObject {
    if (_self.selectedSubCategoryObject == null) {
    return null;
  }

  return $HardwareSubCategoryResponseCopyWith<$Res>(_self.selectedSubCategoryObject!, (value) {
    return _then(_self.copyWith(selectedSubCategoryObject: value));
  });
}
}

// dart format on
