// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hardware_shop_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$HardwareShopState {

 String get id; String get name; String get branchName; String get contactEmail; String get phoneNumber; String get owner; String get mainAddress; String get selectedAddress; String get shopOwner; String get shopName; String get branchAddress; String get branchCity; String get branchPhonenumberOne; String get branchPhonenumberTwo; String get branchEmailOne; String get branchEmailTwo; bool get isUploadingImage; List<String> get imageUrls;// Add this for storing URLs
 List<Map<String, dynamic>> get branches; bool get isLoading; AsyncValue<SuggestionsResponse> get addressLocationResult; List<Suggestion> get addressPlacesList; String? get errorMessage; LatLng? get currentLocation; LatLng? get selectedLocation; AsyncValue<LocationResponse> get locationByIdResponse; bool get isGettingLocationByPlaceId; List<BranchModel> get branchModels;
/// Create a copy of HardwareShopState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HardwareShopStateCopyWith<HardwareShopState> get copyWith => _$HardwareShopStateCopyWithImpl<HardwareShopState>(this as HardwareShopState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HardwareShopState&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.owner, owner) || other.owner == owner)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.selectedAddress, selectedAddress) || other.selectedAddress == selectedAddress)&&(identical(other.shopOwner, shopOwner) || other.shopOwner == shopOwner)&&(identical(other.shopName, shopName) || other.shopName == shopName)&&(identical(other.branchAddress, branchAddress) || other.branchAddress == branchAddress)&&(identical(other.branchCity, branchCity) || other.branchCity == branchCity)&&(identical(other.branchPhonenumberOne, branchPhonenumberOne) || other.branchPhonenumberOne == branchPhonenumberOne)&&(identical(other.branchPhonenumberTwo, branchPhonenumberTwo) || other.branchPhonenumberTwo == branchPhonenumberTwo)&&(identical(other.branchEmailOne, branchEmailOne) || other.branchEmailOne == branchEmailOne)&&(identical(other.branchEmailTwo, branchEmailTwo) || other.branchEmailTwo == branchEmailTwo)&&(identical(other.isUploadingImage, isUploadingImage) || other.isUploadingImage == isUploadingImage)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&const DeepCollectionEquality().equals(other.branches, branches)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.addressLocationResult, addressLocationResult) || other.addressLocationResult == addressLocationResult)&&const DeepCollectionEquality().equals(other.addressPlacesList, addressPlacesList)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.selectedLocation, selectedLocation) || other.selectedLocation == selectedLocation)&&(identical(other.locationByIdResponse, locationByIdResponse) || other.locationByIdResponse == locationByIdResponse)&&(identical(other.isGettingLocationByPlaceId, isGettingLocationByPlaceId) || other.isGettingLocationByPlaceId == isGettingLocationByPlaceId)&&const DeepCollectionEquality().equals(other.branchModels, branchModels));
}


@override
int get hashCode => Object.hashAll([runtimeType,id,name,branchName,contactEmail,phoneNumber,owner,mainAddress,selectedAddress,shopOwner,shopName,branchAddress,branchCity,branchPhonenumberOne,branchPhonenumberTwo,branchEmailOne,branchEmailTwo,isUploadingImage,const DeepCollectionEquality().hash(imageUrls),const DeepCollectionEquality().hash(branches),isLoading,addressLocationResult,const DeepCollectionEquality().hash(addressPlacesList),errorMessage,currentLocation,selectedLocation,locationByIdResponse,isGettingLocationByPlaceId,const DeepCollectionEquality().hash(branchModels)]);

@override
String toString() {
  return 'HardwareShopState(id: $id, name: $name, branchName: $branchName, contactEmail: $contactEmail, phoneNumber: $phoneNumber, owner: $owner, mainAddress: $mainAddress, selectedAddress: $selectedAddress, shopOwner: $shopOwner, shopName: $shopName, branchAddress: $branchAddress, branchCity: $branchCity, branchPhonenumberOne: $branchPhonenumberOne, branchPhonenumberTwo: $branchPhonenumberTwo, branchEmailOne: $branchEmailOne, branchEmailTwo: $branchEmailTwo, isUploadingImage: $isUploadingImage, imageUrls: $imageUrls, branches: $branches, isLoading: $isLoading, addressLocationResult: $addressLocationResult, addressPlacesList: $addressPlacesList, errorMessage: $errorMessage, currentLocation: $currentLocation, selectedLocation: $selectedLocation, locationByIdResponse: $locationByIdResponse, isGettingLocationByPlaceId: $isGettingLocationByPlaceId, branchModels: $branchModels)';
}


}

/// @nodoc
abstract mixin class $HardwareShopStateCopyWith<$Res>  {
  factory $HardwareShopStateCopyWith(HardwareShopState value, $Res Function(HardwareShopState) _then) = _$HardwareShopStateCopyWithImpl;
@useResult
$Res call({
 String id, String name, String branchName, String contactEmail, String phoneNumber, String owner, String mainAddress, String selectedAddress, String shopOwner, String shopName, String branchAddress, String branchCity, String branchPhonenumberOne, String branchPhonenumberTwo, String branchEmailOne, String branchEmailTwo, bool isUploadingImage, List<String> imageUrls, List<Map<String, dynamic>> branches, bool isLoading, AsyncValue<SuggestionsResponse> addressLocationResult, List<Suggestion> addressPlacesList, String? errorMessage, LatLng? currentLocation, LatLng? selectedLocation, AsyncValue<LocationResponse> locationByIdResponse, bool isGettingLocationByPlaceId, List<BranchModel> branchModels
});




}
/// @nodoc
class _$HardwareShopStateCopyWithImpl<$Res>
    implements $HardwareShopStateCopyWith<$Res> {
  _$HardwareShopStateCopyWithImpl(this._self, this._then);

  final HardwareShopState _self;
  final $Res Function(HardwareShopState) _then;

/// Create a copy of HardwareShopState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? branchName = null,Object? contactEmail = null,Object? phoneNumber = null,Object? owner = null,Object? mainAddress = null,Object? selectedAddress = null,Object? shopOwner = null,Object? shopName = null,Object? branchAddress = null,Object? branchCity = null,Object? branchPhonenumberOne = null,Object? branchPhonenumberTwo = null,Object? branchEmailOne = null,Object? branchEmailTwo = null,Object? isUploadingImage = null,Object? imageUrls = null,Object? branches = null,Object? isLoading = null,Object? addressLocationResult = null,Object? addressPlacesList = null,Object? errorMessage = freezed,Object? currentLocation = freezed,Object? selectedLocation = freezed,Object? locationByIdResponse = null,Object? isGettingLocationByPlaceId = null,Object? branchModels = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,owner: null == owner ? _self.owner : owner // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,selectedAddress: null == selectedAddress ? _self.selectedAddress : selectedAddress // ignore: cast_nullable_to_non_nullable
as String,shopOwner: null == shopOwner ? _self.shopOwner : shopOwner // ignore: cast_nullable_to_non_nullable
as String,shopName: null == shopName ? _self.shopName : shopName // ignore: cast_nullable_to_non_nullable
as String,branchAddress: null == branchAddress ? _self.branchAddress : branchAddress // ignore: cast_nullable_to_non_nullable
as String,branchCity: null == branchCity ? _self.branchCity : branchCity // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberOne: null == branchPhonenumberOne ? _self.branchPhonenumberOne : branchPhonenumberOne // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberTwo: null == branchPhonenumberTwo ? _self.branchPhonenumberTwo : branchPhonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,branchEmailOne: null == branchEmailOne ? _self.branchEmailOne : branchEmailOne // ignore: cast_nullable_to_non_nullable
as String,branchEmailTwo: null == branchEmailTwo ? _self.branchEmailTwo : branchEmailTwo // ignore: cast_nullable_to_non_nullable
as String,isUploadingImage: null == isUploadingImage ? _self.isUploadingImage : isUploadingImage // ignore: cast_nullable_to_non_nullable
as bool,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,branches: null == branches ? _self.branches : branches // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,addressLocationResult: null == addressLocationResult ? _self.addressLocationResult : addressLocationResult // ignore: cast_nullable_to_non_nullable
as AsyncValue<SuggestionsResponse>,addressPlacesList: null == addressPlacesList ? _self.addressPlacesList : addressPlacesList // ignore: cast_nullable_to_non_nullable
as List<Suggestion>,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,currentLocation: freezed == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,selectedLocation: freezed == selectedLocation ? _self.selectedLocation : selectedLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,locationByIdResponse: null == locationByIdResponse ? _self.locationByIdResponse : locationByIdResponse // ignore: cast_nullable_to_non_nullable
as AsyncValue<LocationResponse>,isGettingLocationByPlaceId: null == isGettingLocationByPlaceId ? _self.isGettingLocationByPlaceId : isGettingLocationByPlaceId // ignore: cast_nullable_to_non_nullable
as bool,branchModels: null == branchModels ? _self.branchModels : branchModels // ignore: cast_nullable_to_non_nullable
as List<BranchModel>,
  ));
}

}


/// @nodoc


class _HardwareShopState implements HardwareShopState {
   _HardwareShopState({this.id = '', this.name = '', this.branchName = '', this.contactEmail = '', this.phoneNumber = '', this.owner = '', this.mainAddress = '', this.selectedAddress = '', this.shopOwner = '', this.shopName = '', this.branchAddress = '', this.branchCity = '', this.branchPhonenumberOne = '', this.branchPhonenumberTwo = '', this.branchEmailOne = '', this.branchEmailTwo = '', this.isUploadingImage = false, final  List<String> imageUrls = const [], final  List<Map<String, dynamic>> branches = const [], this.isLoading = false, required this.addressLocationResult, final  List<Suggestion> addressPlacesList = const [], this.errorMessage, this.currentLocation = const LatLng(0.0, 0.0), this.selectedLocation, required this.locationByIdResponse, this.isGettingLocationByPlaceId = false, final  List<BranchModel> branchModels = const []}): _imageUrls = imageUrls,_branches = branches,_addressPlacesList = addressPlacesList,_branchModels = branchModels;
  

@override@JsonKey() final  String id;
@override@JsonKey() final  String name;
@override@JsonKey() final  String branchName;
@override@JsonKey() final  String contactEmail;
@override@JsonKey() final  String phoneNumber;
@override@JsonKey() final  String owner;
@override@JsonKey() final  String mainAddress;
@override@JsonKey() final  String selectedAddress;
@override@JsonKey() final  String shopOwner;
@override@JsonKey() final  String shopName;
@override@JsonKey() final  String branchAddress;
@override@JsonKey() final  String branchCity;
@override@JsonKey() final  String branchPhonenumberOne;
@override@JsonKey() final  String branchPhonenumberTwo;
@override@JsonKey() final  String branchEmailOne;
@override@JsonKey() final  String branchEmailTwo;
@override@JsonKey() final  bool isUploadingImage;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

// Add this for storing URLs
 final  List<Map<String, dynamic>> _branches;
// Add this for storing URLs
@override@JsonKey() List<Map<String, dynamic>> get branches {
  if (_branches is EqualUnmodifiableListView) return _branches;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branches);
}

@override@JsonKey() final  bool isLoading;
@override final  AsyncValue<SuggestionsResponse> addressLocationResult;
 final  List<Suggestion> _addressPlacesList;
@override@JsonKey() List<Suggestion> get addressPlacesList {
  if (_addressPlacesList is EqualUnmodifiableListView) return _addressPlacesList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_addressPlacesList);
}

@override final  String? errorMessage;
@override@JsonKey() final  LatLng? currentLocation;
@override final  LatLng? selectedLocation;
@override final  AsyncValue<LocationResponse> locationByIdResponse;
@override@JsonKey() final  bool isGettingLocationByPlaceId;
 final  List<BranchModel> _branchModels;
@override@JsonKey() List<BranchModel> get branchModels {
  if (_branchModels is EqualUnmodifiableListView) return _branchModels;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branchModels);
}


/// Create a copy of HardwareShopState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HardwareShopStateCopyWith<_HardwareShopState> get copyWith => __$HardwareShopStateCopyWithImpl<_HardwareShopState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HardwareShopState&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.owner, owner) || other.owner == owner)&&(identical(other.mainAddress, mainAddress) || other.mainAddress == mainAddress)&&(identical(other.selectedAddress, selectedAddress) || other.selectedAddress == selectedAddress)&&(identical(other.shopOwner, shopOwner) || other.shopOwner == shopOwner)&&(identical(other.shopName, shopName) || other.shopName == shopName)&&(identical(other.branchAddress, branchAddress) || other.branchAddress == branchAddress)&&(identical(other.branchCity, branchCity) || other.branchCity == branchCity)&&(identical(other.branchPhonenumberOne, branchPhonenumberOne) || other.branchPhonenumberOne == branchPhonenumberOne)&&(identical(other.branchPhonenumberTwo, branchPhonenumberTwo) || other.branchPhonenumberTwo == branchPhonenumberTwo)&&(identical(other.branchEmailOne, branchEmailOne) || other.branchEmailOne == branchEmailOne)&&(identical(other.branchEmailTwo, branchEmailTwo) || other.branchEmailTwo == branchEmailTwo)&&(identical(other.isUploadingImage, isUploadingImage) || other.isUploadingImage == isUploadingImage)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&const DeepCollectionEquality().equals(other._branches, _branches)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.addressLocationResult, addressLocationResult) || other.addressLocationResult == addressLocationResult)&&const DeepCollectionEquality().equals(other._addressPlacesList, _addressPlacesList)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.currentLocation, currentLocation) || other.currentLocation == currentLocation)&&(identical(other.selectedLocation, selectedLocation) || other.selectedLocation == selectedLocation)&&(identical(other.locationByIdResponse, locationByIdResponse) || other.locationByIdResponse == locationByIdResponse)&&(identical(other.isGettingLocationByPlaceId, isGettingLocationByPlaceId) || other.isGettingLocationByPlaceId == isGettingLocationByPlaceId)&&const DeepCollectionEquality().equals(other._branchModels, _branchModels));
}


@override
int get hashCode => Object.hashAll([runtimeType,id,name,branchName,contactEmail,phoneNumber,owner,mainAddress,selectedAddress,shopOwner,shopName,branchAddress,branchCity,branchPhonenumberOne,branchPhonenumberTwo,branchEmailOne,branchEmailTwo,isUploadingImage,const DeepCollectionEquality().hash(_imageUrls),const DeepCollectionEquality().hash(_branches),isLoading,addressLocationResult,const DeepCollectionEquality().hash(_addressPlacesList),errorMessage,currentLocation,selectedLocation,locationByIdResponse,isGettingLocationByPlaceId,const DeepCollectionEquality().hash(_branchModels)]);

@override
String toString() {
  return 'HardwareShopState(id: $id, name: $name, branchName: $branchName, contactEmail: $contactEmail, phoneNumber: $phoneNumber, owner: $owner, mainAddress: $mainAddress, selectedAddress: $selectedAddress, shopOwner: $shopOwner, shopName: $shopName, branchAddress: $branchAddress, branchCity: $branchCity, branchPhonenumberOne: $branchPhonenumberOne, branchPhonenumberTwo: $branchPhonenumberTwo, branchEmailOne: $branchEmailOne, branchEmailTwo: $branchEmailTwo, isUploadingImage: $isUploadingImage, imageUrls: $imageUrls, branches: $branches, isLoading: $isLoading, addressLocationResult: $addressLocationResult, addressPlacesList: $addressPlacesList, errorMessage: $errorMessage, currentLocation: $currentLocation, selectedLocation: $selectedLocation, locationByIdResponse: $locationByIdResponse, isGettingLocationByPlaceId: $isGettingLocationByPlaceId, branchModels: $branchModels)';
}


}

/// @nodoc
abstract mixin class _$HardwareShopStateCopyWith<$Res> implements $HardwareShopStateCopyWith<$Res> {
  factory _$HardwareShopStateCopyWith(_HardwareShopState value, $Res Function(_HardwareShopState) _then) = __$HardwareShopStateCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String branchName, String contactEmail, String phoneNumber, String owner, String mainAddress, String selectedAddress, String shopOwner, String shopName, String branchAddress, String branchCity, String branchPhonenumberOne, String branchPhonenumberTwo, String branchEmailOne, String branchEmailTwo, bool isUploadingImage, List<String> imageUrls, List<Map<String, dynamic>> branches, bool isLoading, AsyncValue<SuggestionsResponse> addressLocationResult, List<Suggestion> addressPlacesList, String? errorMessage, LatLng? currentLocation, LatLng? selectedLocation, AsyncValue<LocationResponse> locationByIdResponse, bool isGettingLocationByPlaceId, List<BranchModel> branchModels
});




}
/// @nodoc
class __$HardwareShopStateCopyWithImpl<$Res>
    implements _$HardwareShopStateCopyWith<$Res> {
  __$HardwareShopStateCopyWithImpl(this._self, this._then);

  final _HardwareShopState _self;
  final $Res Function(_HardwareShopState) _then;

/// Create a copy of HardwareShopState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? branchName = null,Object? contactEmail = null,Object? phoneNumber = null,Object? owner = null,Object? mainAddress = null,Object? selectedAddress = null,Object? shopOwner = null,Object? shopName = null,Object? branchAddress = null,Object? branchCity = null,Object? branchPhonenumberOne = null,Object? branchPhonenumberTwo = null,Object? branchEmailOne = null,Object? branchEmailTwo = null,Object? isUploadingImage = null,Object? imageUrls = null,Object? branches = null,Object? isLoading = null,Object? addressLocationResult = null,Object? addressPlacesList = null,Object? errorMessage = freezed,Object? currentLocation = freezed,Object? selectedLocation = freezed,Object? locationByIdResponse = null,Object? isGettingLocationByPlaceId = null,Object? branchModels = null,}) {
  return _then(_HardwareShopState(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,contactEmail: null == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,owner: null == owner ? _self.owner : owner // ignore: cast_nullable_to_non_nullable
as String,mainAddress: null == mainAddress ? _self.mainAddress : mainAddress // ignore: cast_nullable_to_non_nullable
as String,selectedAddress: null == selectedAddress ? _self.selectedAddress : selectedAddress // ignore: cast_nullable_to_non_nullable
as String,shopOwner: null == shopOwner ? _self.shopOwner : shopOwner // ignore: cast_nullable_to_non_nullable
as String,shopName: null == shopName ? _self.shopName : shopName // ignore: cast_nullable_to_non_nullable
as String,branchAddress: null == branchAddress ? _self.branchAddress : branchAddress // ignore: cast_nullable_to_non_nullable
as String,branchCity: null == branchCity ? _self.branchCity : branchCity // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberOne: null == branchPhonenumberOne ? _self.branchPhonenumberOne : branchPhonenumberOne // ignore: cast_nullable_to_non_nullable
as String,branchPhonenumberTwo: null == branchPhonenumberTwo ? _self.branchPhonenumberTwo : branchPhonenumberTwo // ignore: cast_nullable_to_non_nullable
as String,branchEmailOne: null == branchEmailOne ? _self.branchEmailOne : branchEmailOne // ignore: cast_nullable_to_non_nullable
as String,branchEmailTwo: null == branchEmailTwo ? _self.branchEmailTwo : branchEmailTwo // ignore: cast_nullable_to_non_nullable
as String,isUploadingImage: null == isUploadingImage ? _self.isUploadingImage : isUploadingImage // ignore: cast_nullable_to_non_nullable
as bool,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,branches: null == branches ? _self._branches : branches // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,addressLocationResult: null == addressLocationResult ? _self.addressLocationResult : addressLocationResult // ignore: cast_nullable_to_non_nullable
as AsyncValue<SuggestionsResponse>,addressPlacesList: null == addressPlacesList ? _self._addressPlacesList : addressPlacesList // ignore: cast_nullable_to_non_nullable
as List<Suggestion>,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,currentLocation: freezed == currentLocation ? _self.currentLocation : currentLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,selectedLocation: freezed == selectedLocation ? _self.selectedLocation : selectedLocation // ignore: cast_nullable_to_non_nullable
as LatLng?,locationByIdResponse: null == locationByIdResponse ? _self.locationByIdResponse : locationByIdResponse // ignore: cast_nullable_to_non_nullable
as AsyncValue<LocationResponse>,isGettingLocationByPlaceId: null == isGettingLocationByPlaceId ? _self.isGettingLocationByPlaceId : isGettingLocationByPlaceId // ignore: cast_nullable_to_non_nullable
as bool,branchModels: null == branchModels ? _self._branchModels : branchModels // ignore: cast_nullable_to_non_nullable
as List<BranchModel>,
  ));
}


}

// dart format on
