import 'package:build_mate/data/dto/location_response.dart';
import 'package:build_mate/data/dto/suggestions_response.dart';
import 'package:build_mate/data/models/branch_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'hardware_shop_state.freezed.dart';

@freezed
abstract class HardwareShopState with _$HardwareShopState {
  factory HardwareShopState({
    @Default('') String id,
    @Default('') String name,
    @Default('') String branchName,
    @Default('') String contactEmail,
    @Default('') String phoneNumber,
    @Default('') String owner,
    @Default('') String mainAddress,
    @Default('') String selectedAddress,
    @Default('') String shopOwner,
    @Default('') String shopName,
    @Default('') String branchAddress,
    @Default('') String branchCity,
    @Default('') String branchPhonenumberOne,
    @Default('') String branchPhonenumberTwo,
    @Default('') String branchEmailOne,
    @Default('') String branchEmailTwo,
    @Default(false) bool isUploadingImage,
    @Default([]) List<String> imageUrls, // Add this for storing URLs
    @Default([]) List<Map<String, dynamic>> branches,
    @Default(false) bool isLoading,
    required AsyncValue<SuggestionsResponse> addressLocationResult,
    @Default([]) List<Suggestion> addressPlacesList,
    String? errorMessage,
    @Default(LatLng(0.0, 0.0)) LatLng? currentLocation,
    LatLng? selectedLocation,

    required AsyncValue<LocationResponse> locationByIdResponse,
    @Default(false) bool isGettingLocationByPlaceId,
    @Default([]) List<BranchModel> branchModels,
  }) = _HardwareShopState;
}
