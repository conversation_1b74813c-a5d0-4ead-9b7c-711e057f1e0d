// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'users_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UsersState {

 bool get isLoading; bool get isSaving; bool get saveSuccess; String? get errorMessage; List<String> get users;// Assuming users are represented as a list of strings
 int? get selectedUserId; String? get selectedUserName; List<BranchResponse> get branches; int get selectedBranchIndex; bool get isAllSelected; int get selectedBranchId; bool get isLoadingBranches; String get fullName; String get email; bool get isSavingUser; List<BranchStaffResponse> get branchUsers; bool get isLoadingBranchUsers; bool get isUpdatingUser; bool get isResettingPassword; int get updateProgress; int get allUsersCount; int get passwordResetProgress;
/// Create a copy of UsersState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UsersStateCopyWith<UsersState> get copyWith => _$UsersStateCopyWithImpl<UsersState>(this as UsersState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UsersState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.saveSuccess, saveSuccess) || other.saveSuccess == saveSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&const DeepCollectionEquality().equals(other.users, users)&&(identical(other.selectedUserId, selectedUserId) || other.selectedUserId == selectedUserId)&&(identical(other.selectedUserName, selectedUserName) || other.selectedUserName == selectedUserName)&&const DeepCollectionEquality().equals(other.branches, branches)&&(identical(other.selectedBranchIndex, selectedBranchIndex) || other.selectedBranchIndex == selectedBranchIndex)&&(identical(other.isAllSelected, isAllSelected) || other.isAllSelected == isAllSelected)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.isLoadingBranches, isLoadingBranches) || other.isLoadingBranches == isLoadingBranches)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.isSavingUser, isSavingUser) || other.isSavingUser == isSavingUser)&&const DeepCollectionEquality().equals(other.branchUsers, branchUsers)&&(identical(other.isLoadingBranchUsers, isLoadingBranchUsers) || other.isLoadingBranchUsers == isLoadingBranchUsers)&&(identical(other.isUpdatingUser, isUpdatingUser) || other.isUpdatingUser == isUpdatingUser)&&(identical(other.isResettingPassword, isResettingPassword) || other.isResettingPassword == isResettingPassword)&&(identical(other.updateProgress, updateProgress) || other.updateProgress == updateProgress)&&(identical(other.allUsersCount, allUsersCount) || other.allUsersCount == allUsersCount)&&(identical(other.passwordResetProgress, passwordResetProgress) || other.passwordResetProgress == passwordResetProgress));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,isSaving,saveSuccess,errorMessage,const DeepCollectionEquality().hash(users),selectedUserId,selectedUserName,const DeepCollectionEquality().hash(branches),selectedBranchIndex,isAllSelected,selectedBranchId,isLoadingBranches,fullName,email,isSavingUser,const DeepCollectionEquality().hash(branchUsers),isLoadingBranchUsers,isUpdatingUser,isResettingPassword,updateProgress,allUsersCount,passwordResetProgress]);

@override
String toString() {
  return 'UsersState(isLoading: $isLoading, isSaving: $isSaving, saveSuccess: $saveSuccess, errorMessage: $errorMessage, users: $users, selectedUserId: $selectedUserId, selectedUserName: $selectedUserName, branches: $branches, selectedBranchIndex: $selectedBranchIndex, isAllSelected: $isAllSelected, selectedBranchId: $selectedBranchId, isLoadingBranches: $isLoadingBranches, fullName: $fullName, email: $email, isSavingUser: $isSavingUser, branchUsers: $branchUsers, isLoadingBranchUsers: $isLoadingBranchUsers, isUpdatingUser: $isUpdatingUser, isResettingPassword: $isResettingPassword, updateProgress: $updateProgress, allUsersCount: $allUsersCount, passwordResetProgress: $passwordResetProgress)';
}


}

/// @nodoc
abstract mixin class $UsersStateCopyWith<$Res>  {
  factory $UsersStateCopyWith(UsersState value, $Res Function(UsersState) _then) = _$UsersStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool isSaving, bool saveSuccess, String? errorMessage, List<String> users, int? selectedUserId, String? selectedUserName, List<BranchResponse> branches, int selectedBranchIndex, bool isAllSelected, int selectedBranchId, bool isLoadingBranches, String fullName, String email, bool isSavingUser, List<BranchStaffResponse> branchUsers, bool isLoadingBranchUsers, bool isUpdatingUser, bool isResettingPassword, int updateProgress, int allUsersCount, int passwordResetProgress
});




}
/// @nodoc
class _$UsersStateCopyWithImpl<$Res>
    implements $UsersStateCopyWith<$Res> {
  _$UsersStateCopyWithImpl(this._self, this._then);

  final UsersState _self;
  final $Res Function(UsersState) _then;

/// Create a copy of UsersState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? isSaving = null,Object? saveSuccess = null,Object? errorMessage = freezed,Object? users = null,Object? selectedUserId = freezed,Object? selectedUserName = freezed,Object? branches = null,Object? selectedBranchIndex = null,Object? isAllSelected = null,Object? selectedBranchId = null,Object? isLoadingBranches = null,Object? fullName = null,Object? email = null,Object? isSavingUser = null,Object? branchUsers = null,Object? isLoadingBranchUsers = null,Object? isUpdatingUser = null,Object? isResettingPassword = null,Object? updateProgress = null,Object? allUsersCount = null,Object? passwordResetProgress = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,saveSuccess: null == saveSuccess ? _self.saveSuccess : saveSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,users: null == users ? _self.users : users // ignore: cast_nullable_to_non_nullable
as List<String>,selectedUserId: freezed == selectedUserId ? _self.selectedUserId : selectedUserId // ignore: cast_nullable_to_non_nullable
as int?,selectedUserName: freezed == selectedUserName ? _self.selectedUserName : selectedUserName // ignore: cast_nullable_to_non_nullable
as String?,branches: null == branches ? _self.branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,selectedBranchIndex: null == selectedBranchIndex ? _self.selectedBranchIndex : selectedBranchIndex // ignore: cast_nullable_to_non_nullable
as int,isAllSelected: null == isAllSelected ? _self.isAllSelected : isAllSelected // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchId: null == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int,isLoadingBranches: null == isLoadingBranches ? _self.isLoadingBranches : isLoadingBranches // ignore: cast_nullable_to_non_nullable
as bool,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,isSavingUser: null == isSavingUser ? _self.isSavingUser : isSavingUser // ignore: cast_nullable_to_non_nullable
as bool,branchUsers: null == branchUsers ? _self.branchUsers : branchUsers // ignore: cast_nullable_to_non_nullable
as List<BranchStaffResponse>,isLoadingBranchUsers: null == isLoadingBranchUsers ? _self.isLoadingBranchUsers : isLoadingBranchUsers // ignore: cast_nullable_to_non_nullable
as bool,isUpdatingUser: null == isUpdatingUser ? _self.isUpdatingUser : isUpdatingUser // ignore: cast_nullable_to_non_nullable
as bool,isResettingPassword: null == isResettingPassword ? _self.isResettingPassword : isResettingPassword // ignore: cast_nullable_to_non_nullable
as bool,updateProgress: null == updateProgress ? _self.updateProgress : updateProgress // ignore: cast_nullable_to_non_nullable
as int,allUsersCount: null == allUsersCount ? _self.allUsersCount : allUsersCount // ignore: cast_nullable_to_non_nullable
as int,passwordResetProgress: null == passwordResetProgress ? _self.passwordResetProgress : passwordResetProgress // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _UsersState implements UsersState {
   _UsersState({this.isLoading = false, this.isSaving = false, this.saveSuccess = false, this.errorMessage, final  List<String> users = const [], this.selectedUserId, this.selectedUserName, final  List<BranchResponse> branches = const [], this.selectedBranchIndex = -1, this.isAllSelected = false, this.selectedBranchId = 0, this.isLoadingBranches = false, this.fullName = '', this.email = '', this.isSavingUser = false, final  List<BranchStaffResponse> branchUsers = const [], this.isLoadingBranchUsers = false, this.isUpdatingUser = false, this.isResettingPassword = false, this.updateProgress = 0, this.allUsersCount = 0, this.passwordResetProgress = 0}): _users = users,_branches = branches,_branchUsers = branchUsers;
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isSaving;
@override@JsonKey() final  bool saveSuccess;
@override final  String? errorMessage;
 final  List<String> _users;
@override@JsonKey() List<String> get users {
  if (_users is EqualUnmodifiableListView) return _users;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_users);
}

// Assuming users are represented as a list of strings
@override final  int? selectedUserId;
@override final  String? selectedUserName;
 final  List<BranchResponse> _branches;
@override@JsonKey() List<BranchResponse> get branches {
  if (_branches is EqualUnmodifiableListView) return _branches;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branches);
}

@override@JsonKey() final  int selectedBranchIndex;
@override@JsonKey() final  bool isAllSelected;
@override@JsonKey() final  int selectedBranchId;
@override@JsonKey() final  bool isLoadingBranches;
@override@JsonKey() final  String fullName;
@override@JsonKey() final  String email;
@override@JsonKey() final  bool isSavingUser;
 final  List<BranchStaffResponse> _branchUsers;
@override@JsonKey() List<BranchStaffResponse> get branchUsers {
  if (_branchUsers is EqualUnmodifiableListView) return _branchUsers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_branchUsers);
}

@override@JsonKey() final  bool isLoadingBranchUsers;
@override@JsonKey() final  bool isUpdatingUser;
@override@JsonKey() final  bool isResettingPassword;
@override@JsonKey() final  int updateProgress;
@override@JsonKey() final  int allUsersCount;
@override@JsonKey() final  int passwordResetProgress;

/// Create a copy of UsersState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UsersStateCopyWith<_UsersState> get copyWith => __$UsersStateCopyWithImpl<_UsersState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UsersState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSaving, isSaving) || other.isSaving == isSaving)&&(identical(other.saveSuccess, saveSuccess) || other.saveSuccess == saveSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&const DeepCollectionEquality().equals(other._users, _users)&&(identical(other.selectedUserId, selectedUserId) || other.selectedUserId == selectedUserId)&&(identical(other.selectedUserName, selectedUserName) || other.selectedUserName == selectedUserName)&&const DeepCollectionEquality().equals(other._branches, _branches)&&(identical(other.selectedBranchIndex, selectedBranchIndex) || other.selectedBranchIndex == selectedBranchIndex)&&(identical(other.isAllSelected, isAllSelected) || other.isAllSelected == isAllSelected)&&(identical(other.selectedBranchId, selectedBranchId) || other.selectedBranchId == selectedBranchId)&&(identical(other.isLoadingBranches, isLoadingBranches) || other.isLoadingBranches == isLoadingBranches)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.isSavingUser, isSavingUser) || other.isSavingUser == isSavingUser)&&const DeepCollectionEquality().equals(other._branchUsers, _branchUsers)&&(identical(other.isLoadingBranchUsers, isLoadingBranchUsers) || other.isLoadingBranchUsers == isLoadingBranchUsers)&&(identical(other.isUpdatingUser, isUpdatingUser) || other.isUpdatingUser == isUpdatingUser)&&(identical(other.isResettingPassword, isResettingPassword) || other.isResettingPassword == isResettingPassword)&&(identical(other.updateProgress, updateProgress) || other.updateProgress == updateProgress)&&(identical(other.allUsersCount, allUsersCount) || other.allUsersCount == allUsersCount)&&(identical(other.passwordResetProgress, passwordResetProgress) || other.passwordResetProgress == passwordResetProgress));
}


@override
int get hashCode => Object.hashAll([runtimeType,isLoading,isSaving,saveSuccess,errorMessage,const DeepCollectionEquality().hash(_users),selectedUserId,selectedUserName,const DeepCollectionEquality().hash(_branches),selectedBranchIndex,isAllSelected,selectedBranchId,isLoadingBranches,fullName,email,isSavingUser,const DeepCollectionEquality().hash(_branchUsers),isLoadingBranchUsers,isUpdatingUser,isResettingPassword,updateProgress,allUsersCount,passwordResetProgress]);

@override
String toString() {
  return 'UsersState(isLoading: $isLoading, isSaving: $isSaving, saveSuccess: $saveSuccess, errorMessage: $errorMessage, users: $users, selectedUserId: $selectedUserId, selectedUserName: $selectedUserName, branches: $branches, selectedBranchIndex: $selectedBranchIndex, isAllSelected: $isAllSelected, selectedBranchId: $selectedBranchId, isLoadingBranches: $isLoadingBranches, fullName: $fullName, email: $email, isSavingUser: $isSavingUser, branchUsers: $branchUsers, isLoadingBranchUsers: $isLoadingBranchUsers, isUpdatingUser: $isUpdatingUser, isResettingPassword: $isResettingPassword, updateProgress: $updateProgress, allUsersCount: $allUsersCount, passwordResetProgress: $passwordResetProgress)';
}


}

/// @nodoc
abstract mixin class _$UsersStateCopyWith<$Res> implements $UsersStateCopyWith<$Res> {
  factory _$UsersStateCopyWith(_UsersState value, $Res Function(_UsersState) _then) = __$UsersStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool isSaving, bool saveSuccess, String? errorMessage, List<String> users, int? selectedUserId, String? selectedUserName, List<BranchResponse> branches, int selectedBranchIndex, bool isAllSelected, int selectedBranchId, bool isLoadingBranches, String fullName, String email, bool isSavingUser, List<BranchStaffResponse> branchUsers, bool isLoadingBranchUsers, bool isUpdatingUser, bool isResettingPassword, int updateProgress, int allUsersCount, int passwordResetProgress
});




}
/// @nodoc
class __$UsersStateCopyWithImpl<$Res>
    implements _$UsersStateCopyWith<$Res> {
  __$UsersStateCopyWithImpl(this._self, this._then);

  final _UsersState _self;
  final $Res Function(_UsersState) _then;

/// Create a copy of UsersState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? isSaving = null,Object? saveSuccess = null,Object? errorMessage = freezed,Object? users = null,Object? selectedUserId = freezed,Object? selectedUserName = freezed,Object? branches = null,Object? selectedBranchIndex = null,Object? isAllSelected = null,Object? selectedBranchId = null,Object? isLoadingBranches = null,Object? fullName = null,Object? email = null,Object? isSavingUser = null,Object? branchUsers = null,Object? isLoadingBranchUsers = null,Object? isUpdatingUser = null,Object? isResettingPassword = null,Object? updateProgress = null,Object? allUsersCount = null,Object? passwordResetProgress = null,}) {
  return _then(_UsersState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSaving: null == isSaving ? _self.isSaving : isSaving // ignore: cast_nullable_to_non_nullable
as bool,saveSuccess: null == saveSuccess ? _self.saveSuccess : saveSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,users: null == users ? _self._users : users // ignore: cast_nullable_to_non_nullable
as List<String>,selectedUserId: freezed == selectedUserId ? _self.selectedUserId : selectedUserId // ignore: cast_nullable_to_non_nullable
as int?,selectedUserName: freezed == selectedUserName ? _self.selectedUserName : selectedUserName // ignore: cast_nullable_to_non_nullable
as String?,branches: null == branches ? _self._branches : branches // ignore: cast_nullable_to_non_nullable
as List<BranchResponse>,selectedBranchIndex: null == selectedBranchIndex ? _self.selectedBranchIndex : selectedBranchIndex // ignore: cast_nullable_to_non_nullable
as int,isAllSelected: null == isAllSelected ? _self.isAllSelected : isAllSelected // ignore: cast_nullable_to_non_nullable
as bool,selectedBranchId: null == selectedBranchId ? _self.selectedBranchId : selectedBranchId // ignore: cast_nullable_to_non_nullable
as int,isLoadingBranches: null == isLoadingBranches ? _self.isLoadingBranches : isLoadingBranches // ignore: cast_nullable_to_non_nullable
as bool,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,isSavingUser: null == isSavingUser ? _self.isSavingUser : isSavingUser // ignore: cast_nullable_to_non_nullable
as bool,branchUsers: null == branchUsers ? _self._branchUsers : branchUsers // ignore: cast_nullable_to_non_nullable
as List<BranchStaffResponse>,isLoadingBranchUsers: null == isLoadingBranchUsers ? _self.isLoadingBranchUsers : isLoadingBranchUsers // ignore: cast_nullable_to_non_nullable
as bool,isUpdatingUser: null == isUpdatingUser ? _self.isUpdatingUser : isUpdatingUser // ignore: cast_nullable_to_non_nullable
as bool,isResettingPassword: null == isResettingPassword ? _self.isResettingPassword : isResettingPassword // ignore: cast_nullable_to_non_nullable
as bool,updateProgress: null == updateProgress ? _self.updateProgress : updateProgress // ignore: cast_nullable_to_non_nullable
as int,allUsersCount: null == allUsersCount ? _self.allUsersCount : allUsersCount // ignore: cast_nullable_to_non_nullable
as int,passwordResetProgress: null == passwordResetProgress ? _self.passwordResetProgress : passwordResetProgress // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
