// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'artisan_portfolio_flow_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ArtisanPortfolioFlowState _$ArtisanPortfolioFlowStateFromJson(
  Map<String, dynamic> json,
) => _ArtisanPortfolioFlowState(
  artisanProfileData:
      json['artisanProfileData'] == null
          ? null
          : ArtisanProfileDataResponse.fromJson(
            json['artisanProfileData'] as Map<String, dynamic>,
          ),
  isLoading: json['isLoading'] as bool? ?? false,
  artisanImages:
      (json['artisanImages'] as List<dynamic>?)
          ?.map((e) => ArtisanImage.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  currentUserId: (json['currentUserId'] as num?)?.toInt() ?? 0,
  averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
  totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
  ratings:
      (json['ratings'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList() ??
      const [],
  ratingsList:
      (json['ratingsList'] as List<dynamic>?)
          ?.map(
            (e) => ArtisanRatingResponse.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  isSavingProfile: json['isSavingProfile'] as bool? ?? false,
);

Map<String, dynamic> _$ArtisanPortfolioFlowStateToJson(
  _ArtisanPortfolioFlowState instance,
) => <String, dynamic>{
  'artisanProfileData': instance.artisanProfileData,
  'isLoading': instance.isLoading,
  'artisanImages': instance.artisanImages,
  'currentUserId': instance.currentUserId,
  'averageRating': instance.averageRating,
  'totalReviews': instance.totalReviews,
  'ratings': instance.ratings,
  'ratingsList': instance.ratingsList,
  'isSavingProfile': instance.isSavingProfile,
};
