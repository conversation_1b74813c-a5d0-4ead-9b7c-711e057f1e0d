import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_settings_state.freezed.dart';

@freezed
abstract class ProfileSettingsState with _$ProfileSettingsState {
  factory ProfileSettingsState({
    @Default(false) bool isLoading,
    @Default('') String profileUrl,
    @Default('') String username,
    @Default(0) int imageVersion,
    String? errorMessage,
  }) = _ProfileSettingsState;
}
