import 'package:build_mate/data/models/chat_models.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'chat_state.freezed.dart';

@freezed
abstract class ChatState with _$ChatState {
  factory ChatState({
    @Default(false) bool isLoading,
    @Default(false) bool isSubscribing,
    @Default(false) bool isSubscribed,
    String? subscriptionError,
    // @Default([]) List<Conversation> conversations
    required AsyncValue<List<Conversation>> conversations,
  }) = _ChatState;
}
