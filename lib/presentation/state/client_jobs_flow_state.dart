import 'package:build_mate/data/dto/get_bids_response.dart';
import 'package:build_mate/data/dto/job_details_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'client_jobs_flow_state.freezed.dart';

@freezed
abstract class ClientJobsFlowState with _$ClientJobsFlowState {
  factory ClientJobsFlowState({
    // @Default([]) List<JobModel> jobs,
    @Default([]) List<JobDetailsResponse> jobs,
    @Default([]) List<GetBidResponse> bids,
    @Default(false) bool isLoading,
    String? error,
    @Default(0) int selectedTabIndex,
    @Default(null) JobDetailsResponse? selectedJob,
    @Default(0) int clientId,
    @Default(0) int openJobsCount,
    @Default(0) int inProgressJobsCount,
    @Default(0) int completedJobsCount,
    @Default(0) int selectedArtisanBidId,
  }) = _ClientJobsFlowState;
}
