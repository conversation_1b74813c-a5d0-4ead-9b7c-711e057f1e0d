import 'package:build_mate/data/dto/service_icons_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';


part 'all_services_state.freezed.dart';

@freezed
abstract class AllServicesState with _$AllServicesState {
  factory AllServicesState({
    @Default([]) List<Map<String, dynamic>> categories,
    @Default([]) List<ServiceIconsResponse>? services,
    @Default(false) bool isLoading,
    @Default(0) int selectedServiceId,
    @Default('') String selectedServiceName,
  }) = _AllServicesState;
}
