import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/auth/web/branches_screen.dart';
import 'package:build_mate/presentation/screens/auth/web/centered_sign_in_screen.dart';
import 'package:build_mate/presentation/screens/auth/web/dashboard_screen.dart';
import 'package:build_mate/presentation/screens/auth/web/inventory_screen.dart';
import 'package:build_mate/presentation/screens/auth/web/setup_company_screen.dart';
import 'package:build_mate/presentation/screens/auth/web/users_screen.dart';

import 'package:build_mate/presentation/screens/home/<USER>/home_screen.dart';
import 'package:build_mate/presentation/screens/home/<USER>/settings/profile_settings_screen.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/shop_products_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/service_provider_home_screen.dart';
import 'package:build_mate/presentation/screens/job/artisan_bids_screen.dart';
import 'package:build_mate/presentation/screens/job/job_offer_details_screen.dart';
import 'package:build_mate/presentation/screens/job/post_job_screen.dart';
import 'package:build_mate/presentation/screens/job/view_artisan_profile_screen.dart';
import 'package:build_mate/presentation/screens/job/view_bid_artisan_profile_screen.dart';
import 'package:build_mate/presentation/screens/onboarding/artisan/artisan_final_profile_details_screen.dart';
import 'package:build_mate/presentation/screens/onboarding/artisan/artisan_profile_details_screen.dart';
import 'package:build_mate/presentation/screens/onboarding/client/user_profile_details.dart';
import 'package:build_mate/presentation/screens/profile/profile_screen.dart';
import 'package:build_mate/presentation/screens/rating/rating_client_screen.dart';
import 'package:build_mate/presentation/screens/services/on_demand_service_screen.dart';
import 'package:build_mate/presentation/screens/services/service_categories_screen.dart';
import 'package:build_mate/presentation/screens/auth/sign_in_screen.dart';
import 'package:build_mate/presentation/screens/onboarding/welcome_screen.dart';
import 'package:build_mate/presentation/screens/splash_screen.dart';
import 'package:build_mate/presentation/screens/onboarding/role_selection_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final goRouterProvider = Provider((ref) {
  return GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: RouteConstants.SIGN_IN_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: SignInScreen());
        },
      ),

      // GoRoute(
      //   path: '/',
      //   name: RouteConstants.GET_STARTED_SCREEN,
      //   pageBuilder: (context, state) {
      //     return const MaterialPage(child: GetStartedScreen());
      //   },
      // ),
      GoRoute(
        path: '/${RouteConstants.PROFILE_SCREEN}',
        name: RouteConstants.PROFILE_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: ServiceProviderProfile());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.RATING_CLIENT_SCREEN}',
        name: RouteConstants.RATING_CLIENT_SCREEN,
        pageBuilder: (context, state) {
          final extras = state.extra as Map<String, dynamic>;
          return MaterialPage(
            child: RatingClientScreen(
              clientName: extras['clientName'] as String,
              clientImageUrl: extras['clientImageUrl'] as String,
            ),
          );
        },
      ),
      GoRoute(
        path: '/${RouteConstants.JOB_OFFER_DETAILS_SCREEN}',
        name: RouteConstants.JOB_OFFER_DETAILS_SCREEN,
        pageBuilder: (context, state) {
          return MaterialPage(child: JobOfferDetailsScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.CLIENT_HOME_SCREEN}',
        name: RouteConstants.CLIENT_HOME_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: ClientHomeScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.SERVICE_PROVIDER_HOME_SCREEN}',
        name: RouteConstants.SERVICE_PROVIDER_HOME_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: ServiceProviderHomeScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.POST_JOB_SCREEN}',
        name: RouteConstants.POST_JOB_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: PostJobScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.SERVICE_CATEGORY_SCREEN}',
        name: RouteConstants.SERVICE_CATEGORY_SCREEN,
        pageBuilder: (context, state) {
          return MaterialPage(
            child: ServiceCategoriesScreen(
              onCategorySelected: (
                String mainCategory,
                String subCategory,
                List<String> subCategories,
              ) {
                // Replace Navigator.pop with context.pop() from go_router
                context.pop();
              },
            ),
          );
        },
      ),
      // GoRoute(
      //   path: '/${RouteConstants.SIGN_IN_SCREEN}',
      //   name: RouteConstants.SIGN_IN_SCREEN,
      //   pageBuilder: (context, state) {
      //     return const MaterialPage(child: SignInScreen());
      //   },
      // ),
      GoRoute(
        path: '/${RouteConstants.USER_PROFILE_DETAILS_SCREEN}',
        name: RouteConstants.USER_PROFILE_DETAILS_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: UserProfileDetailsScreens());
        },
      ),
      GoRoute(
        path: '/welcome',
        name: RouteConstants.WELCOME_SCREEN,
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: '/splash',
        name: RouteConstants.SPLASH_SCREEN,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/role-selection',
        name: RouteConstants.ROLE_SELECTION_SCREEN,
        builder: (context, state) => const RoleSelectionScreen(),
      ),
      GoRoute(
        path: '/artisan-profile-details',
        name: RouteConstants.ARTISAN_PROFILE_DETAILS_SCREEN,
        builder: (context, state) => const ArtisanProfileDetailsScreen(),
      ),
      GoRoute(
        path: '/artisan-final-profile-details',
        name: RouteConstants.ARTISAN_FINAL_PROFILE_DETAILS_SCREEN,
        builder: (context, state) => const ArtisanFinalProfileDetailsScreen(),
      ),
      GoRoute(
        name: RouteConstants.ARTISAN_BIDS_SCREEN,
        path: '/artisan-bids',
        builder: (context, state) {
          return ArtisanBidsScreen();
        },
      ),

      GoRoute(
        name: RouteConstants.VIEW_ARTISAN_PROFILE_SCREEN,
        path: '/view-artisan-profile',
        builder: (context, state) {
          return ViewArtisanProfileScreen();
        },
      ),

      GoRoute(
        name: RouteConstants.ON_DEMAND_SERVICE_SCREEN,
        path: '/ondemand_screen',
        builder: (context, state) {
          return OnDemandServiceScreen();
        },
      ),
      GoRoute(
        path: '/${RouteConstants.HARDWARE_SHOP_DASHBOARD}',
        name: RouteConstants.HARDWARE_SHOP_DASHBOARD,
        builder: (context, state) => const DashboardScreen(),
        routes: [
          GoRoute(
            path: 'branches',
            name: RouteConstants.BRANCHES_SCREEN,
            builder:
                (context, state) =>
                    const DashboardScreen(child: BranchesScreen()),
          ),
          GoRoute(
            path: 'users',
            name: RouteConstants.USERS_SCREEN,
            builder:
                (context, state) => const DashboardScreen(child: UsersScreen()),
          ),
          GoRoute(
            path: 'inventory',
            name: RouteConstants.INVENTORY_SCREEN,
            builder:
                (context, state) =>
                    const DashboardScreen(child: InventoryScreen()),
          ),
        ],
      ),
      GoRoute(
        path: '/${RouteConstants.SETUP_COMPANY_SCREEN}',
        name: RouteConstants.SETUP_COMPANY_SCREEN,
        pageBuilder: (context, state) {
          return const MaterialPage(child: SetupCompanyScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.BRANCH_SIGNIN_SCREEN}',
        name: RouteConstants.BRANCH_SIGNIN_SCREEN,
        pageBuilder: (context, state) {
          return MaterialPage(child: CenteredSignInScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.SHOP_PRODUCTS_VIEW}',
        name: RouteConstants.SHOP_PRODUCTS_VIEW,
        pageBuilder: (context, state) {
          return MaterialPage(child: ShopProductsView());
        },
      ),

      GoRoute(
        path: '/${RouteConstants.CLIENT_SETTINGS_SCREEN}',
        name: RouteConstants.CLIENT_SETTINGS_SCREEN,
        pageBuilder: (context, state) {
          return MaterialPage(child: ProfileSettingsScreen());
        },
      ),
      GoRoute(
        path: '/${RouteConstants.ARTISAN_BID_PROFILE_SCREEN}',
        name: RouteConstants.ARTISAN_BID_PROFILE_SCREEN,
        pageBuilder: (context, state) {
          return MaterialPage(child: ViewBidArtisanProfileScreen());
        },
      ),
    ],
    errorBuilder:
        (context, state) => Scaffold(
          body: Center(child: Text('Route not found: ${state.uri}')),
        ),
  );
});
