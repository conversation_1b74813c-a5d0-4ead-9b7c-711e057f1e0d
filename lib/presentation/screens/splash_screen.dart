import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo
            // Image.asset(
            //   'assets/images/logo.png',
            //   height: 100,
            // ),
            const SizedBox(height: 40),
            
            // Loading indicator
            const CircularProgressIndicator(),
            
            const SizedBox(height: 24),
            
            // Loading text
            Text(
              'Loading your profile...',
              style: MyTypography.Medium.copyWith(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}