import 'dart:convert';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ServiceCategoriesScreen extends ConsumerStatefulWidget {
  final Function(String, String, List<String>)? onCategorySelected;

  const ServiceCategoriesScreen({super.key, this.onCategorySelected});

  @override
  ConsumerState<ServiceCategoriesScreen> createState() =>
      _ServiceCategoriesScreenState();
}

class _ServiceCategoriesScreenState
    extends ConsumerState<ServiceCategoriesScreen> {
  List<Map<String, dynamic>> categories = [];
  List<Map<String, dynamic>> filteredCategories = [];
  bool isLoading = true;
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCategories();
    searchController.addListener(_filterCategories);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void _filterCategories() {
    final query = searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        filteredCategories = List.from(categories);
      } else {
        filteredCategories =
            categories.where((category) {
              final name = category['name'].toString().toLowerCase();
              return name.contains(query);
            }).toList();
      }
    });
  }

  Future<void> _loadCategories() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Load categories from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);
        final loadedCategories = List<Map<String, dynamic>>.from(
          categoriesData['categories'],
        );

        setState(() {
          categories = loadedCategories;
          filteredCategories = loadedCategories;
          isLoading = false;
        });
        return;
      }

      // If no categories in shared preferences, use a fallback
      setState(() {
        categories = _getFallbackCategories();
        filteredCategories = _getFallbackCategories();
        isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      setState(() {
        categories = _getFallbackCategories();
        filteredCategories = _getFallbackCategories();
        isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _getFallbackCategories() {
    // Fallback categories in case we can't load from shared preferences
    return [
      {
        'name': 'Construction',
        'subcategories': [
          'General Contractor',
          'Masonry',
          'Carpentry',
          'Plumbing',
          'Electrical',
          'Roofing',
          'Painting',
          'Flooring',
          'Tiling',
        ],
      },
      {
        'name': 'Home Maintenance',
        'subcategories': [
          'Cleaning',
          'Gardening',
          'Pest Control',
          'Appliance Repair',
          'Furniture Assembly',
          'Window Cleaning',
          'Gutter Cleaning',
        ],
      },
      {
        'name': 'Beauty & Wellness',
        'subcategories': [
          'Hair Styling',
          'Makeup',
          'Massage',
          'Nail Care',
          'Spa Services',
          'Fitness Training',
        ],
      },
      {
        'name': 'Technology',
        'subcategories': [
          'Computer Repair',
          'Phone Repair',
          'Network Setup',
          'Software Installation',
          'Data Recovery',
          'Smart Home Setup',
        ],
      },
      {
        'name': 'Education & Tutoring',
        'subcategories': [
          'Mathematics',
          'Science',
          'Languages',
          'Music',
          'Art',
          'Test Preparation',
        ],
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final customColors = ref.watch(customColorsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0.5,
        title: Text(
          'Select Service Category',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.close,
            color: Theme.of(context).appBarTheme.iconTheme?.color,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body:
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: searchController,
                      style: TextStyle(color: customColors.textPrimaryColor),
                      decoration: InputDecoration(
                        hintText: 'Search categories',
                        hintStyle: TextStyle(
                          color: customColors.textPrimaryColor.withValues(
                            alpha: 0.6,
                          ),
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: customColors.textPrimaryColor.withValues(
                            alpha: 0.6,
                          ),
                        ),
                        filled: true,
                        fillColor: customColors.surfaceVariant.withValues(
                          alpha: 0.3,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.1,
                            ),
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: Theme.of(context).primaryColor,
                            width: 2,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                    ),
                  ),
                  Expanded(
                    child:
                        filteredCategories.isEmpty
                            ? Center(
                              child: Text(
                                'No categories found',
                                style: MyTypography.Medium.copyWith(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.6),
                                ),
                              ),
                            )
                            : ListView.separated(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              itemCount: filteredCategories.length,
                              separatorBuilder:
                                  (context, index) => Divider(
                                    height: 1,
                                    indent: 16,
                                    endIndent: 16,
                                    color: Colors.grey.withAlpha(
                                      (0.2 * 255).round(),
                                    ),
                                  ),
                              itemBuilder: (context, index) {
                                final category =
                                    filteredCategories[index]['name'];

                                return ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 8,
                                  ),
                                  title: Text(
                                    category,
                                    style: MyTypography.SemiBold.copyWith(
                                      color: customColors.textPrimaryColor,
                                      fontSize: 16,
                                    ),
                                  ),
                                  trailing: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16,
                                    color: customColors.textPrimaryColor
                                        .withValues(alpha: 0.6),
                                  ),
                                  onTap: () {
                                    final subcategories =
                                        filteredCategories[index]['subcategories']
                                            as List<dynamic>;
                                    _showSubcategories(
                                      category,
                                      List<String>.from(subcategories),
                                    );
                                  },
                                );
                              },
                            ),
                  ),
                ],
              ),
    );
  }

  void _showSubcategories(String category, List<String> subcategories) {
    final customColors = ref.watch(customColorsProvider);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor:
          Theme.of(context).bottomSheetTheme.backgroundColor ??
          Theme.of(context).cardColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                category,
                style: MyTypography.SemiBold.copyWith(
                  fontSize: 18,
                  color: customColors.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemExtent: 56.0,
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false,
                  itemCount: subcategories.length,
                  itemBuilder: (context, index) {
                    final subcategory = subcategories[index];
                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      title: Text(
                        subcategory,
                        style: MyTypography.Regular.copyWith(
                          color: customColors.textPrimaryColor,
                        ),
                      ),
                      onTap: () {
                        // Close the bottom sheet
                        Navigator.pop(context);

                        // Create a result object with the selected category data
                        final result = {
                          'mainCategory': category,
                          'subCategory': subcategory,
                          'subCategories': subcategories,
                        };

                        if (kDebugMode) {
                          print(
                            'Selected category: $category, subcategory: $subcategory',
                          );
                        }

                        // If callback is provided, use it
                        if (widget.onCategorySelected != null) {
                          widget.onCategorySelected!(
                            category,
                            subcategory,
                            subcategories,
                          );
                        } else {
                          // Otherwise, pop with result data
                          Navigator.pop(context, result);
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
