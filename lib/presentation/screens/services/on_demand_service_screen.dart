import 'package:build_mate/presentation/components/loader/lottie_loader.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OnDemandServiceScreen extends ConsumerWidget {
  const OnDemandServiceScreen({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(mainAxisAlignment: MainAxisAlignment.center, children: [LottieLoader()]));
  }
}
