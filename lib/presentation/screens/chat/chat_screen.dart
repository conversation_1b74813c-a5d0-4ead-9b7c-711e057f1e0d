import 'dart:async';

import 'package:build_mate/presentation/screens/home/<USER>/tabs/messages_tab_view.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/data/models/chat_models.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final messagesProvider = FutureProvider.family<List<Message>, int>((
  ref,
  conversationId,
) async {
  final chatService = ref.watch(chatServiceProvider);
  return chatService.getMessages(conversationId);
});

final chatControllerProvider = StateNotifierProvider.family<
  ChatController,
  AsyncValue<List<Message>>,
  int
>((ref, conversationId) => ChatController(ref, conversationId));

// Provider to track if a message is currently being sent
final isSendingMessageProvider = StateProvider<bool>((ref) => false);

// Add a state provider to track which messages are being deleted
final isDeletingMessageProvider = StateProvider.family<bool, int>(
  (ref, messageId) => false,
);

// Add state providers at the top of the file with other providers
final isTypingProvider = StateProvider.family<bool, int>(
  (ref, conversationId) => false,
);
final otherPartyTypingProvider = StateProvider.family<bool, int>(
  (ref, conversationId) => false,
);

class ChatController extends StateNotifier<AsyncValue<List<Message>>> {
  final Ref _ref;
  final int conversationId;
  RealtimeChannel? _channel;
  RealtimeChannel? _presenceChannel;
  Timer? _typingTimer;
  bool _isTyping = false;
  bool _presenceReady = false;

  ChatController(this._ref, this.conversationId)
    : super(const AsyncValue.loading()) {
    _loadMessages();
    _subscribeToMessages();
    _setupPresence();
  }

  // Set up presence for typing indicators
  void _setupPresence() async {
    final supabase = Supabase.instance.client;
    final channelName = 'presence:$conversationId';

    // Determine if current user is client or artisan
    final isClient = await _isCurrentUserClient();
    final userType = isClient ? 'client' : 'artisan';

    if (kDebugMode) {
      print('[$conversationId] Setting up presence for $userType');
    }

    // Create a presence channel
    _presenceChannel = supabase.channel(channelName);

    // Track presence changes using the newer API
    _presenceChannel!
        .onPresenceSync((payload) {
          if (kDebugMode) {
            print(
              '[$conversationId] Presence sync: ${_presenceChannel!.presenceState()}',
            );
          }
          _updateTypingStatus(_presenceChannel!.presenceState());
        })
        .onPresenceJoin((payload) {
          if (kDebugMode) {
            print('[$conversationId] Presence join: $payload');
          }
          _handlePresenceJoin(payload);
        })
        .onPresenceLeave((payload) {
          if (kDebugMode) {
            print('[$conversationId] Presence leave: $payload');
          }
          _handlePresenceLeave(payload);
        })
        .subscribe((status, error) async {
          if (error != null) {
            if (kDebugMode) {
              print('[$conversationId] Error subscribing to presence: $error');
            }
            _presenceReady = false;
            return;
          }

          if (kDebugMode) {
            print('[$conversationId] Presence channel status: $status');
          }

          // Only track presence after successful subscription
          try {
            // Track the current user's presence
            await _presenceChannel!.track({
              'user_type': userType,
              'conversation_id': conversationId,
              'typing': false,
              'online': true,
              'last_seen': DateTime.now().toIso8601String(),
            });

            // Mark presence as ready
            _presenceReady = true;

            if (kDebugMode) {
              print(
                '[$conversationId] Presence tracking started for $userType',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              print('[$conversationId] Error tracking presence: $e');
            }
            _presenceReady = false;
          }
        });
  }

  // Handle presence join event
  void _handlePresenceJoin(RealtimePresenceJoinPayload payload) {
    try {
      if (kDebugMode) {
        print('[$conversationId] Processing presence join: $payload');
      }

      // Process each new presence
      for (final presence in payload.newPresences) {
        final presenceData = presence.payload; // Access the payload directly
        if (presenceData['user_type'] != null) {
          _checkOtherPartyTyping(presenceData);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error handling presence join: $e');
      }
    }
  }

  // Handle presence leave event
  Future<void> _handlePresenceLeave(
    RealtimePresenceLeavePayload payload,
  ) async {
    try {
      if (kDebugMode) {
        print('[$conversationId] Processing presence leave: $payload');
      }

      // Process each leaving presence
      for (final presence in payload.leftPresences) {
        final presenceData = presence.payload;

        // Get the user type of the leaving presence
        final String leavingUserType = presenceData['user_type'];
        final isClient = await _isCurrentUserClient();
        final String myUserType = isClient ? 'client' : 'artisan';

        // Update typing status if the other party is leaving
        if (leavingUserType != myUserType) {
          if (kDebugMode) {
            print(
              '[$conversationId] Other party ($leavingUserType) left. Setting typing to false.',
            );
          }
          _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
              false;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error handling presence leave: $e');
      }
    }
  }

  // Check if the other party is typing based on a single presence payload
  void _checkOtherPartyTyping(dynamic payload) async {
    try {
      final isClient = await _isCurrentUserClient();
      final otherPartyType = isClient ? 'artisan' : 'client';

      if (payload['user_type'] == otherPartyType && payload['typing'] == true) {
        _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
            true;
      } else if (payload['user_type'] == otherPartyType &&
          payload['typing'] == false) {
        _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
            false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error checking other party typing: $e');
      }
    }
  }

  // Update typing status based on presence state
  void _updateTypingStatus(dynamic presenceState) async {
    try {
      if (presenceState == null) return;

      final isClient = await _isCurrentUserClient();
      final otherPartyType = isClient ? 'artisan' : 'client';

      // Check for other party's typing status
      bool otherPartyTyping = false;

      // Handle the new presence state structure
      if (presenceState is List) {
        // New structure: List of PresenceState objects
        for (final state in presenceState) {
          if (state.presences != null) {
            for (final presence in state.presences) {
              final payload = presence.payload;
              if (payload != null &&
                  payload['user_type'] == otherPartyType &&
                  payload['typing'] == true) {
                otherPartyTyping = true;
                break;
              }
            }
          }
        }
      } else {
        // Old structure: Map of presences
        presenceState.forEach((key, presences) {
          for (final presence in presences) {
            if (presence['user_type'] == otherPartyType &&
                presence['typing'] == true) {
              otherPartyTyping = true;
              break;
            }
          }
        });
      }

      // Update the typing status provider
      _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
          otherPartyTyping;
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error updating typing status: $e');
      }
    }
  }

  // Update typing status
  void updateTypingStatus(bool isTyping) async {
    if (_isTyping == isTyping) return; // No change

    _isTyping = isTyping;
    _ref.read(isTypingProvider(conversationId).notifier).state = isTyping;

    // Only update presence if the channel is ready
    if (!_presenceReady || _presenceChannel == null) {
      if (kDebugMode) {
        print(
          '[$conversationId] Presence channel not ready, skipping typing update',
        );
      }
      return;
    }

    try {
      final isClient = await _isCurrentUserClient();

      // Update presence with typing status
      await _presenceChannel!.track({
        'user_type': isClient ? 'client' : 'artisan',
        'conversation_id': conversationId,
        'typing': isTyping,
        'online': true,
        'last_seen': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('[$conversationId] Updated typing status: $isTyping');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error updating typing status: $e');
      }
    }
  }

  // Start typing indicator with debounce
  void startTyping() {
    // Cancel existing timer
    _typingTimer?.cancel();

    // Update typing status to true
    updateTypingStatus(true);

    // Set timer to stop typing indicator after 2 seconds of inactivity
    _typingTimer = Timer(const Duration(seconds: 2), () {
      updateTypingStatus(false);
    });
  }

  // Stop typing indicator
  void stopTyping() {
    _typingTimer?.cancel();
    updateTypingStatus(false);
  }

  Future<void> _loadMessages() async {
    try {
      final chatService = _ref.read(chatServiceProvider);
      final messages = await chatService.getMessages(conversationId);
      if (kDebugMode) {
        print(
          'Loaded ${messages.length} messages for conversation $conversationId',
        );
      }

      // Mark messages as read
      final isClient = await _isCurrentUserClient();
      await chatService.markMessagesAsRead(
        conversationId,
        isClient ? 'client' : 'artisan',
      );

      state = AsyncValue.data(messages);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading messages: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  void _subscribeToMessages() {
    if (kDebugMode) {
      print(
        'Setting up realtime subscription for conversation $conversationId',
      );
    }
    final supabase = Supabase.instance.client;

    // Create a unique channel name for this conversation
    final channelName = 'can$conversationId';
    if (kDebugMode) {
      print('Subscribing to channel: $channelName');
    }

    // Subscribe to the realtime changes on the messages table for this conversation
    _channel = supabase
        .channel(channelName)
        // Listen to PostgreSQL changes for inserts and updates
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'conversation_id',
            value: conversationId.toString(),
          ),
          callback: (payload) {
            if (kDebugMode) {
              print(
                '[$channelName] Received PostgreSQL change event: ${payload.eventType}',
              );
            }
            if (kDebugMode) {
              print('[$channelName] Payload: ${payload.toString()}');
            }

            // Handle different event types
            if (payload.eventType == PostgresChangeEvent.insert &&
                payload.newRecord.isNotEmpty) {
              try {
                final newMessage = Message.fromJson(payload.newRecord);
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing new message: ${newMessage.id}',
                  );
                }
                _handleNewMessage(newMessage);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing new message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.newRecord}');
                }
              }
            } else if (payload.eventType == PostgresChangeEvent.update &&
                payload.newRecord.isNotEmpty &&
                payload.oldRecord.isNotEmpty) {
              try {
                final updatedMessage = Message.fromJson(payload.newRecord);
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing updated message: ${updatedMessage.id}',
                  );
                }
                _handleUpdatedMessage(updatedMessage);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing updated message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.newRecord}');
                }
              }
            } else if (payload.eventType == PostgresChangeEvent.delete &&
                payload.oldRecord.isNotEmpty) {
              try {
                final deletedMessageId = payload.oldRecord['id'];
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing deleted message from Postgres: $deletedMessageId',
                  );
                }
                handleDeletedMessage(deletedMessageId);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing deleted message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.oldRecord}');
                }
              }
            }
          },
        )
        // Add this broadcast listener for deletion events
        .onBroadcast(
          event: 'shout',
          callback: (payload) {
            if (kDebugMode) {
              print('[$channelName] Received shout broadcast: $payload');
            }

            try {
              // Check if this is a delete message event
              if (payload['type'] == 'delete_message' &&
                  payload['message_id'] != null) {
                final messageId = payload['message_id'];
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing deleted message from shout: $messageId',
                  );
                }

                // Force UI update on main thread
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  final controller = _ref.read(
                    chatControllerProvider(conversationId).notifier,
                  );
                  controller.handleDeletedMessage(messageId);
                });
              }
            } catch (e) {
              if (kDebugMode) {
                print('[$channelName] Error processing shout broadcast: $e');
              }
              if (kDebugMode) {
                print('[$channelName] Payload: $payload');
              }
            }
          },
        )
        .subscribe((status, error) {
          if (error != null) {
            if (kDebugMode) {
              print('[$channelName] Error subscribing to messages: $error');
            }
          } else {
            if (kDebugMode) {
              print(
                '[$channelName] Successfully subscribed to messages channel: $status',
              );
            }
            if (kDebugMode) {
              print(
                '[$channelName] Channel is now active and listening for events',
              );
            }
          }
        });
  }

  // Extract message handling logic to a separate method
  void _handleNewMessage(Message newMessage) {
    if (state.hasValue) {
      if (kDebugMode) {
        print('Handling new message: ${newMessage.id}');
      }

      // Check if message already exists to avoid duplicates
      final messageExists = state.value!.any(
        (msg) =>
            msg.id == newMessage.id ||
            (msg.id < 0 &&
                msg.content == newMessage.content &&
                msg.senderType == newMessage.senderType &&
                msg.senderId == newMessage.senderId),
      );

      if (!messageExists) {
        if (kDebugMode) {
          print('Message is new, adding to state');
        }
        final updatedMessages = [...state.value!];

        // Remove any temporary messages with the same content
        updatedMessages.removeWhere(
          (msg) =>
              msg.id < 0 &&
              msg.content == newMessage.content &&
              msg.senderType == newMessage.senderType &&
              msg.senderId == newMessage.senderId,
        );

        // Add the new message
        updatedMessages.add(newMessage);

        // Sort messages by creation time to ensure correct order
        updatedMessages.sort(
          (a, b) => DateTime.parse(
            a.createdAt,
          ).compareTo(DateTime.parse(b.createdAt)),
        );

        state = AsyncValue.data(updatedMessages);
        if (kDebugMode) {
          print(
            'State updated with new message. Total messages: ${updatedMessages.length}',
          );
        }

        // Mark message as read if it's from the other party
        final chatService = _ref.read(chatServiceProvider);
        _isCurrentUserClient().then((isClient) {
          if (newMessage.senderType != (isClient ? 'client' : 'artisan')) {
            chatService.markMessagesAsRead(
              conversationId,
              isClient ? 'client' : 'artisan',
            );
          }
        });
      } else {
        if (kDebugMode) {
          print('Message already exists, skipping');
        }
      }
    } else {
      if (kDebugMode) {
        print('State has no value, cannot add new message');
      }
    }
  }

  // Add this method to properly handle updated messages
  void _handleUpdatedMessage(Message updatedMessage) {
    if (state.hasValue) {
      if (kDebugMode) {
        print(
          '[$conversationId] Handling updated message: ${updatedMessage.id}',
        );
      }
      final updatedMessages = [...state.value!];
      final index = updatedMessages.indexWhere(
        (msg) => msg.id == updatedMessage.id,
      );

      if (index != -1) {
        // Replace the existing message with the updated one
        if (kDebugMode) {
          print('[$conversationId] Found message at index $index, updating');
        }
        updatedMessages[index] = updatedMessage;
        state = AsyncValue.data(updatedMessages);
        if (kDebugMode) {
          print('[$conversationId] State updated with modified message');
        }
      } else {
        if (kDebugMode) {
          print('[$conversationId] Message not found in state, cannot update');
        }
      }
    } else {
      if (kDebugMode) {
        print('[$conversationId] State has no value, cannot update message');
      }
    }
  }

  // Handle deleted messages
  void handleDeletedMessage(int deletedMessageId) {
    if (kDebugMode) {
      print('[$conversationId] Handling deleted message: $deletedMessageId');
    }

    if (!state.hasValue) {
      if (kDebugMode) {
        print('[$conversationId] State has no value, cannot delete message');
      }
      return;
    }

    // Create a new list without the deleted message
    final updatedMessages =
        state.value!.where((msg) => msg.id != deletedMessageId).toList();

    if (updatedMessages.length != state.value!.length) {
      // Only update state if a message was actually removed
      if (kDebugMode) {
        print(
          '[$conversationId] Message removed, updating state with ${updatedMessages.length} messages',
        );
      }

      // Force a state update by creating a new AsyncValue
      state = AsyncValue.data(updatedMessages);

      if (kDebugMode) {
        print('[$conversationId] State updated after message deletion');
      }
    } else {
      if (kDebugMode) {
        print(
          '[$conversationId] Message not found in state, nothing to delete. ID: $deletedMessageId',
        );
      }
    }
  }

  Future<void> sendMessage(String content) async {
    try {
      final chatService = _ref.read(chatServiceProvider);

      // Determine if current user is client or artisan
      final isClient = await _isCurrentUserClient();

      // Get the current user ID
      final supabase = Supabase.instance.client;
      final supabaseId = supabase.auth.currentUser?.id;

      if (supabaseId == null) {
        if (kDebugMode) {
          print('Error: User not logged in');
        }
        return;
      }

      // Get the user ID based on type
      final userResponse =
          await supabase
              .from(isClient ? 'clients' : 'artisans')
              .select('id')
              .eq('supabase_id', supabaseId)
              .single();

      final userId = userResponse['id'];

      // Show optimistic UI update with a temporary message
      if (state.hasValue) {
        final tempMessage = Message(
          id: -1, // Use negative ID for temp messages
          conversationId: conversationId,
          senderType: isClient ? 'client' : 'artisan',
          senderId: userId,
          content: content,
          isRead: false,
          createdAt: DateTime.now().toIso8601String(),
        );

        // Add temporary message to the UI
        final updatedMessages = [...state.value!, tempMessage];
        state = AsyncValue.data(updatedMessages);
      }

      // Actually send the message
      await chatService.sendMessage(
        conversationId: conversationId,
        senderType: isClient ? 'client' : 'artisan',
        senderId: userId,
        content: content,
      );

      // The real message will replace the temporary one via the realtime subscription
    } catch (e) {
      // Handle error
      if (kDebugMode) {
        print('Error sending message: $e');
      }
    }
  }

  @override
  void dispose() {
    _typingTimer?.cancel();
    _channel?.unsubscribe();
    _presenceChannel?.unsubscribe();
    super.dispose();
  }

  Future<bool> _isCurrentUserClient() async {
    final supabase = Supabase.instance.client;
    final supabaseId = supabase.auth.currentUser?.id;

    if (supabaseId == null) return false;

    // Check if user exists in clients table
    final clientResponse =
        await supabase
            .from('clients')
            .select('id')
            .eq('supabase_id', supabaseId)
            .maybeSingle();

    // If found in clients table, user is a client
    return clientResponse != null;
  }
}

class ChatScreen extends ConsumerStatefulWidget {
  final int conversationId;
  final String otherPartyName;
  final String? otherPartyAvatar;

  const ChatScreen({
    super.key,
    required this.conversationId,
    required this.otherPartyName,
    this.otherPartyAvatar,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _shouldScrollToBottom = true;
  bool _isManualScrolling = false;

  @override
  void initState() {
    super.initState();

    // Add scroll listener with improved logic
    _scrollController.addListener(() {
      if (!_scrollController.hasClients || _isManualScrolling) return;

      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final isAtBottom = maxScroll - currentScroll <= 50;

      if (_shouldScrollToBottom != isAtBottom) {
        setState(() {
          _shouldScrollToBottom = isAtBottom;
        });
      }
    });
    // Set up polling to check for deleted messages
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  // Improved scroll to bottom function
  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;

    try {
      _isManualScrolling = true;
      final maxScroll = _scrollController.position.maxScrollExtent;
      _scrollController
          .animateTo(
            maxScroll,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          )
          .then((_) {
            _isManualScrolling = false;
          });
    } catch (e) {
      _isManualScrolling = false;
      if (kDebugMode) {
        print('Error scrolling to bottom: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(
      chatControllerProvider(widget.conversationId),
    );
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    // Determine if we're in dark mode
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    // Theme-aware colors
    final appBarColor =
        isDarkMode ? customColors.surfaceVariant : darkBlueColor;
    final appBarTextColor =
        isDarkMode ? customColors.textPrimaryColor : Colors.white;
    final appBarIconColor =
        isDarkMode ? customColors.textPrimaryColor : Colors.white;

    // final state = ref.watch(chatViewModelProvider);

    // Add a key based on the messages hash to force rebuild when content changes
    final messagesKey =
        messagesAsync.hasValue
            ? ValueKey(
              messagesAsync.value?.map((m) => '${m.id}:${m.content}').join(','),
            )
            : const ValueKey('loading');

    // Watch for typing indicator from other party
    final otherPartyTyping = ref.watch(
      otherPartyTypingProvider(widget.conversationId),
    );

    // Auto-scroll to bottom when new messages arrive and user was already at bottom
    if (messagesAsync.hasValue &&
        _shouldScrollToBottom &&
        !_isManualScrolling) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: appBarColor,
        elevation: isDarkMode ? 4 : 0,
        shadowColor: isDarkMode ? Colors.black.withValues(alpha: 0.3) : null,
        iconTheme: IconThemeData(color: appBarIconColor),
        title: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CircleAvatar(
                backgroundImage:
                    widget.otherPartyAvatar != null
                        ? NetworkImage(widget.otherPartyAvatar!)
                        : const AssetImage('assets/images/profile_pic.png')
                            as ImageProvider,
                radius: 20,
                backgroundColor:
                    isDarkMode
                        ? customColors.surfaceVariant.withValues(alpha: 0.6)
                        : Colors.grey[200],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.otherPartyName,
                    style: MyTypography.SemiBold.copyWith(
                      fontSize: 18,
                      color: appBarTextColor,
                      letterSpacing: 0.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (otherPartyTyping)
                    Text(
                      'Typing...',
                      style: MyTypography.Regular.copyWith(
                        fontSize: 12,
                        color: appBarTextColor.withValues(alpha: 0.7),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.call, color: appBarIconColor),
            onPressed: () {
              // Handle call action
            },
          ),
          IconButton(
            icon: Icon(Icons.more_vert, color: appBarIconColor),
            onPressed: () {
              // Handle more options
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: GestureDetector(
                // Cancel auto-scroll when user touches the screen
                onTapDown: (_) => setState(() => _shouldScrollToBottom = false),
                child: messagesAsync.when(
                  data: (messages) {
                    if (messages.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color:
                                    isDarkMode
                                        ? customColors.primaryContainer
                                            .withValues(alpha: 0.3)
                                        : darkBlueColor.withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.chat_bubble_outline,
                                size: 48,
                                color:
                                    isDarkMode
                                        ? customColors.textPrimaryColor
                                            .withValues(alpha: 0.7)
                                        : darkBlueColor,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No messages yet',
                              style: MyTypography.SemiBold.copyWith(
                                fontSize: 18,
                                color: customColors.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Start the conversation!',
                              style: MyTypography.Regular.copyWith(
                                fontSize: 16,
                                color: customColors.textPrimaryColor.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    // Ensure messages are sorted with oldest first
                    final sortedMessages = List<Message>.from(messages);
                    sortedMessages.sort(
                      (a, b) => DateTime.parse(
                        a.createdAt,
                      ).compareTo(DateTime.parse(b.createdAt)),
                    );

                    return FutureBuilder<bool>(
                      future:
                          ref
                              .read(
                                chatControllerProvider(
                                  widget.conversationId,
                                ).notifier,
                              )
                              ._isCurrentUserClient(),
                      builder: (context, snapshot) {
                        final isClient =
                            snapshot.data ??
                            true; // Default to client if not loaded yet

                        return ListView.builder(
                          key: messagesKey,
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: sortedMessages.length,
                          itemBuilder: (context, index) {
                            final message = sortedMessages[index];
                            final isFromMe =
                                message.senderType ==
                                (isClient ? 'client' : 'artisan');

                            return _buildMessageBubble(message, isFromMe);
                          },
                        );
                      },
                    );
                  },
                  loading:
                      () => Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isDarkMode
                                ? customColors.textPrimaryColor
                                : darkBlueColor,
                          ),
                        ),
                      ),
                  error:
                      (error, stack) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: customColors.errorColor,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Error loading messages',
                              style: MyTypography.SemiBold.copyWith(
                                fontSize: 18,
                                color: customColors.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              error.toString(),
                              style: MyTypography.Regular.copyWith(
                                fontSize: 14,
                                color: customColors.textPrimaryColor.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                ),
              ),
            ),
            _buildMessageInput(),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Message message, bool isFromMe) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    // Determine if we're in dark mode
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    // Theme-aware colors for message bubbles
    final myMessageColor =
        isDarkMode
            ? customColors.primaryContainer.withValues(alpha: 0.9)
            : darkBlueColor;
    final otherMessageColor =
        isDarkMode
            ? customColors.surfaceVariant.withValues(alpha: 0.8)
            : Colors.grey[200]!;
    final myTextColor =
        isDarkMode ? customColors.textPrimaryColor : Colors.white;
    final otherTextColor =
        isDarkMode ? customColors.textPrimaryColor : Colors.black87;
    final shadowColor =
        isDarkMode
            ? Colors.black.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.05);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16), // Increased spacing
      child: Row(
        mainAxisAlignment:
            isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isFromMe && widget.otherPartyAvatar != null)
            Padding(
              padding: const EdgeInsets.only(right: 12), // Increased spacing
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.3)
                              : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  backgroundImage:
                      widget.otherPartyAvatar != null
                          ? NetworkImage(widget.otherPartyAvatar!)
                          : const AssetImage('assets/images/profile_pic.png')
                              as ImageProvider,
                  radius: 16,
                  backgroundColor:
                      isDarkMode
                          ? customColors.surfaceVariant.withValues(alpha: 0.6)
                          : Colors.grey[200],
                ),
              ),
            ),

          Flexible(
            child: GestureDetector(
              onLongPress: isFromMe ? () => _showMessageOptions(message) : null,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 18, // Increased padding
                  vertical: 14,
                ),
                decoration: BoxDecoration(
                  color: isFromMe ? myMessageColor : otherMessageColor,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20),
                    topRight: const Radius.circular(20),
                    bottomLeft: Radius.circular(isFromMe ? 20 : 4),
                    bottomRight: Radius.circular(isFromMe ? 4 : 20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: shadowColor,
                      blurRadius: isDarkMode ? 8 : 5,
                      offset: const Offset(0, 2),
                      spreadRadius: isDarkMode ? 1 : 0,
                    ),
                  ],
                  // Add subtle border for better definition in dark mode
                  border:
                      isDarkMode
                          ? Border.all(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.1,
                            ),
                            width: 0.5,
                          )
                          : null,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.content,
                      style: MyTypography.Regular.copyWith(
                        color: isFromMe ? myTextColor : otherTextColor,
                        fontSize: 16,
                        height: 1.4, // Better line height
                        letterSpacing: 0.1,
                      ),
                    ),
                    const SizedBox(height: 6), // Increased spacing
                    Text(
                      _formatMessageTime(message.createdAt),
                      style: MyTypography.Regular.copyWith(
                        color:
                            isFromMe
                                ? myTextColor.withValues(alpha: 0.7)
                                : otherTextColor.withValues(alpha: 0.6),
                        fontSize: 12,
                        letterSpacing: 0.1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessageOptions(Message message) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? customColors.surfaceVariant : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar for better UX
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Consumer(
                builder: (context, ref, child) {
                  final isDeleting = ref.watch(
                    isDeletingMessageProvider(message.id),
                  );

                  return ListTile(
                    leading: Icon(Icons.delete, color: customColors.errorColor),
                    title: Text(
                      'Delete Message',
                      style: MyTypography.Medium.copyWith(
                        color: customColors.textPrimaryColor,
                        fontSize: 16,
                      ),
                    ),
                    trailing:
                        isDeleting
                            ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  customColors.errorColor,
                                ),
                              ),
                            )
                            : null,
                    enabled: !isDeleting,
                    onTap: () {
                      Navigator.pop(context);
                      _deleteMessage(message.id);
                    },
                  );
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.edit,
                  color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                ),
                title: Text(
                  'Edit Message',
                  style: MyTypography.Medium.copyWith(
                    color: customColors.textPrimaryColor,
                    fontSize: 16,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _editMessage(message);
                },
              ),
              const SizedBox(height: 8), // Bottom padding
            ],
          ),
        );
      },
    );
  }

  void _deleteMessage(int messageId) async {
    try {
      // Set deleting state to true
      ref.read(isDeletingMessageProvider(messageId).notifier).state = true;

      final chatService = ref.read(chatServiceProvider);
      if (kDebugMode) {
        print('Sending delete request for message ID: $messageId');
      }
      await chatService.deleteMessage(messageId);
      if (kDebugMode) {
        print('Message deletion request sent for ID: $messageId');
      }

      // Manually trigger the deletion in the UI
      final controller = ref.read(
        chatControllerProvider(widget.conversationId).notifier,
      );
      controller.handleDeletedMessage(messageId);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Message deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting message: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to delete message: $e')));
      }
    } finally {
      // Set deleting state back to false
      ref.read(isDeletingMessageProvider(messageId).notifier).state = false;
    }
  }

  void _editMessage(Message message) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    // Use a StatefulBuilder to manage the controller's lifecycle
    String editedContent = message.content;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        // Create a new controller inside the builder
        final TextEditingController controller = TextEditingController(
          text: message.content,
        );

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor:
                  isDarkMode ? customColors.surfaceVariant : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text(
                'Edit Message',
                style: MyTypography.SemiBold.copyWith(
                  color: customColors.textPrimaryColor,
                  fontSize: 20,
                ),
              ),
              content: TextField(
                controller: controller,
                style: MyTypography.Regular.copyWith(
                  color: customColors.textPrimaryColor,
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  hintText: 'Edit your message',
                  hintStyle: TextStyle(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.5),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color:
                          isDarkMode
                              ? customColors.primaryContainer
                              : darkBlueColor,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor:
                      isDarkMode
                          ? customColors.surfaceVariant.withValues(alpha: 0.3)
                          : Colors.grey[50],
                ),
                maxLines: null,
                autofocus: true,
                onChanged: (value) {
                  // Update the content as the user types
                  editedContent = value;
                },
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(dialogContext);
                  },
                  child: Text(
                    'Cancel',
                    style: MyTypography.Medium.copyWith(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.7,
                      ),
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(dialogContext, true);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isDarkMode
                            ? customColors.primaryContainer
                            : darkBlueColor,
                    foregroundColor:
                        isDarkMode
                            ? customColors.textPrimaryColor
                            : Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Save',
                    style: MyTypography.Medium.copyWith(fontSize: 16),
                  ),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // Only proceed if the user pressed Save (result is true)
      if (result == true &&
          editedContent.trim().isNotEmpty &&
          editedContent.trim() != message.content) {
        try {
          final chatService = ref.read(chatServiceProvider);
          chatService.updateMessage(message.id, editedContent.trim());
          if (kDebugMode) {
            print('Message update request sent for ID: ${message.id}');
          }
          // The UI will be updated via the realtime subscription
        } catch (e) {
          if (kDebugMode) {
            print('Error updating message: $e');
          }
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to update message: $e')),
            );
          }
        }
      }
    });
  }

  Widget _buildMessageInput() {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 12,
      ), // Increased padding
      decoration: BoxDecoration(
        color: isDarkMode ? customColors.surfaceVariant : Colors.white,
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.05),
            blurRadius: isDarkMode ? 8 : 5,
            offset: const Offset(0, -2),
            spreadRadius: isDarkMode ? 1 : 0,
          ),
        ],
        // Add subtle border for better definition in dark mode
        border:
            isDarkMode
                ? Border(
                  top: BorderSide(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                    width: 0.5,
                  ),
                )
                : null,
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.attach_file),
              color: customColors.textPrimaryColor.withValues(alpha: 0.6),
              onPressed: () {
                // Handle attachment
              },
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.2)
                              : Colors.black.withValues(alpha: 0.03),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _messageController,
                  style: MyTypography.Regular.copyWith(
                    color: customColors.textPrimaryColor,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    hintStyle: TextStyle(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.5,
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor:
                        isDarkMode
                            ? customColors.surfaceVariant.withValues(alpha: 0.3)
                            : Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  minLines: 1,
                  maxLines: 5,
                  onChanged: (text) {
                    // Trigger typing indicator when user types
                    if (text.isNotEmpty) {
                      final controller = ref.read(
                        chatControllerProvider(widget.conversationId).notifier,
                      );
                      controller.startTyping();
                    }
                  },
                  onSubmitted: (text) {
                    if (text.trim().isNotEmpty) {
                      _sendMessage();
                    }
                  },
                ),
              ),
            ),
            const SizedBox(width: 12), // Increased spacing
            Consumer(
              builder: (context, ref, child) {
                final isLoading = ref.watch(isSendingMessageProvider);
                return Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    backgroundColor:
                        isDarkMode
                            ? customColors.primaryContainer
                            : darkBlueColor,
                    radius: 26, // Slightly larger
                    child:
                        isLoading
                            ? SizedBox(
                              width: 22,
                              height: 22,
                              child: CircularProgressIndicator(
                                color:
                                    isDarkMode
                                        ? customColors.textPrimaryColor
                                        : Colors.white,
                                strokeWidth: 2.5,
                              ),
                            )
                            : IconButton(
                              icon: Icon(
                                Icons.send,
                                color:
                                    isDarkMode
                                        ? customColors.textPrimaryColor
                                        : Colors.white,
                                size: 22,
                              ),
                              onPressed: _sendMessage,
                            ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      final controller = ref.read(
        chatControllerProvider(widget.conversationId).notifier,
      );
      ref.read(isSendingMessageProvider.notifier).state = true;

      // Stop typing indicator when sending a message
      controller.stopTyping();

      controller.sendMessage(_messageController.text.trim()).then((_) {
        _messageController.clear();
        ref.read(isSendingMessageProvider.notifier).state = false;

        // Force scroll to bottom after sending
        setState(() {
          _shouldScrollToBottom = true;
        });

        // Scroll to bottom after a short delay to allow the UI to update
        Future.delayed(const Duration(milliseconds: 100), () {
          _scrollToBottom();
        });
      });
    }
  }

  String _formatMessageTime(String timestamp) {
    final messageTime = DateTime.parse(timestamp).toLocal();
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    // If less than 1 minute ago, show "Now"
    if (difference.inMinutes < 1) {
      return 'Now';
    }
    // If less than 1 hour ago, show "X minutes ago"
    else if (difference.inHours < 1) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    }
    // If today, show time only
    else if (difference.inDays < 1) {
      return DateFormat('h:mm a').format(messageTime);
    }
    // If yesterday
    else if (difference.inDays == 1 ||
        (now.day != messageTime.day &&
            now.difference(DateTime(now.year, now.month, now.day)).inDays ==
                1)) {
      return 'Yesterday, ${DateFormat('h:mm a').format(messageTime)}';
    }
    // If within the last 7 days
    else if (difference.inDays < 7) {
      return '${DateFormat('EEEE').format(messageTime)}, ${DateFormat('h:mm a').format(messageTime)}';
    }
    // Otherwise show date and time
    else {
      return DateFormat('MMM d, h:mm a').format(messageTime);
    }
  }
}
