import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final serviceProviderNavigationIndexProvider = StateProvider<int>((ref) => 0);

class ServiceProviderBottomNavBar extends HookConsumerWidget {
  const ServiceProviderBottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final currentIndex = ref.watch(serviceProviderNavigationIndexProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Container(
      decoration: BoxDecoration(
        color:
            Theme.of(context).bottomNavigationBarTheme.backgroundColor ??
            (themeMode == ThemeMode.light
                ? Colors.white
                : customColors.surfaceVariant),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withAlpha(13),
            blurRadius: 10,
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNavItem(
                context,
                ref,
                0,
                Icons.work_outline,
                Icons.work,
                'Jobs',
              ),
              _buildNavItem(
                context,
                ref,
                1,
                Icons.chat_bubble_outline,
                Icons.chat_bubble,
                'Messages',
              ),
              _buildNavItem(
                context,
                ref,
                2,
                Icons.photo_library_outlined,
                Icons.photo_library,
                'Portfolio',
              ),
              _buildNavItem(
                context,
                ref,
                3,
                Icons.person_outline,
                Icons.person,
                'Settings',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    WidgetRef ref,
    int index,
    IconData outlinedIcon,
    IconData filledIcon,
    String label,
  ) {
    final currentIndex = ref.watch(serviceProviderNavigationIndexProvider);
    final customColors = ref.watch(customColorsProvider);
    final isSelected = currentIndex == index;
    final color =
        isSelected
            ? orangeColor
            : customColors.textPrimaryColor.withValues(alpha: 0.6);

    return InkWell(
      onTap:
          () =>
              ref.read(serviceProviderNavigationIndexProvider.notifier).state =
                  index,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 3,
            width: 30,
            decoration: BoxDecoration(
              color: isSelected ? orangeColor : Colors.transparent,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          Icon(isSelected ? filledIcon : outlinedIcon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: MyTypography.Medium.copyWith(color: color, fontSize: 12),
          ),
        ],
      ),
    );
  }
}
