import 'package:build_mate/presentation/screens/home/<USER>/tabs/home_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/jobs_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/messages_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/profile_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shop_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/screens/home/<USER>/bottom_nav_bar.dart';

class ClientHomeScreen extends HookConsumerWidget {
  const ClientHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(homeNavigationIndexProvider);

    return Scaffold(
      body: _buildBody(currentIndex),
      bottomNavigationBar: const BottomNavBar(),
    );
  }

  Widget _buildBody(int currentIndex) {
    switch (currentIndex) {
      case 0:
        return const HomeTabView();
      case 1:
        return const JobsTabView();
      case 2:
        return const MessagesTabView();
      case 3:
        return const ShopTabView();
      case 4:
        return const ProfileTabView();
      default:
        return const HomeTabView();
    }
  }
}
