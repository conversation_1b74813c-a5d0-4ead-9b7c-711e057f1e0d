import 'package:build_mate/presentation/screens/home/<USER>/tabs/messages_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/portfolio_tab_view.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/settings_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/screens/home/<USER>/service_provider_bottom_nav_bar.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/client_jobs_tab_view.dart';

class ServiceProviderHomeScreen extends HookConsumerWidget {
  const ServiceProviderHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(serviceProviderNavigationIndexProvider);

    return Scaffold(
      body: _buildBody(currentIndex),
      bottomNavigationBar: const ServiceProviderBottomNavBar(),
    );
  }

  Widget _buildBody(int currentIndex) {
    switch (currentIndex) {
      case 0:
        return const ClientJobsTabView();
      case 1:
        return const MessagesTabView();
      case 2:
        return PortfolioTabView();
      case 3:
        return const SettingsTabView();
      default:
        return const ClientJobsTabView();
    }
  }
}
