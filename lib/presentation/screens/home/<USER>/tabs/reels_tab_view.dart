import 'dart:async';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';

class ReelsTabView extends StatelessWidget {
  const ReelsTabView({super.key});

  void _showFullScreenImage(
    BuildContext context,
    Map<String, dynamic> post,
    String heroTag,
    int initialIndex,
  ) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => FullScreenImageView(
              posts: _posts,
              initialIndex: initialIndex,
              heroTag: heroTag,
            ),
      ),
    );
  }

  final List<Map<String, dynamic>> _posts = const [
    {
      'imageUrl': 'assets/images/grid_1.jpeg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '2.5k',
    },
    {
      'imageUrl': 'assets/images/grid_2.jpeg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '1.8k',
    },
    {
      'imageUrl': 'assets/images/grid_3.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '3.2k',
    },
    {
      'imageUrl': 'assets/images/grid_4.jpeg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '4.1k',
    },
    {
      'imageUrl': 'assets/images/grid_5.jpeg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '956',
    },
    {
      'imageUrl': 'assets/images/grid_6.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '2.7k',
    },
    {
      'imageUrl': 'assets/images/grid_7.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.5k',
    },
    {
      'imageUrl': 'assets/images/grid_7.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '3.9k',
    },
    {
      'imageUrl': 'assets/images/grid_8.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '2.3k',
    },
    {
      'imageUrl': 'assets/images/grid_9.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '4.5k',
    },
    {
      'imageUrl': 'assets/images/grid_10.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.7k',
    },
    {
      'imageUrl': 'assets/images/grid_11.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '2.9k',
    },
    {
      'imageUrl': 'assets/images/grid_12.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.2k',
    },
    {
      'imageUrl': 'assets/images/grid_13.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '3.1k',
    },
    {
      'imageUrl': 'assets/images/grid_14.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.9k',
    },
    {
      'imageUrl': 'assets/images/grid_15.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '2.1k',
    },
    {
      'imageUrl': 'assets/images/grid_16.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.4k',
    },
    {
      'imageUrl': 'assets/images/grid_17.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': true,
      'likes': '2.6k',
    },
    {
      'imageUrl': 'assets/images/grid_18.jpg',
      'profileImage': 'assets/images/profile_pic.png',
      'isVideo': false,
      'likes': '1.1k',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 0,
            floating: true,
            pinned: true,
            backgroundColor: darkBlueColor,
            title: Text(
              'Reels',
              style: MyTypography.SemiBold.copyWith(
                color: Colors.white,
                fontSize: 20,
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(1),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 1,
                crossAxisSpacing: 1,
                childAspectRatio: 1,
              ),
              delegate: SliverChildBuilderDelegate((
                BuildContext context,
                int index,
              ) {
                final post = _posts[index];
                final heroTag = 'reel_${post['imageUrl']}_$index';
                return GestureDetector(
                  onTap:
                      () => _showFullScreenImage(context, post, heroTag, index),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Main Image with Hero
                      Hero(
                        tag: heroTag,
                        child: Image.asset(post['imageUrl'], fit: BoxFit.cover),
                      ),
                      // Profile Image
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: CircleAvatar(
                            radius: 12,
                            backgroundImage: AssetImage(post['profileImage']),
                          ),
                        ),
                      ),

                      // Video indicator
                      if (post['isVideo'])
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black.withAlpha(
                                (0.7 * 255).round(),
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),

                      // Bottom gradient
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                Colors.black.withAlpha((0.5 * 255).round()),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Likes count
                      Positioned(
                        bottom: 8,
                        left: 8,
                        child: Row(
                          children: [
                            const Icon(
                              Icons.favorite,
                              color: Colors.white,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              post['likes'],
                              style: MyTypography.Medium.copyWith(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }, childCount: _posts.length),
            ),
          ),
        ],
      ),
    );
  }
}

class FullScreenImageView extends StatefulWidget {
  final List<Map<String, dynamic>> posts;
  final int initialIndex;
  final String heroTag;

  const FullScreenImageView({
    super.key,
    required this.posts,
    required this.initialIndex,
    required this.heroTag,
  });

  @override
  State<FullScreenImageView> createState() => _FullScreenImageViewState();
}

class _FullScreenImageViewState extends State<FullScreenImageView> {
  late PageController _pageController;
  Timer? _timer; // Change to nullable Timer
  int _currentIndex = 0;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _startAutoSlide();
  }

  void _startAutoSlide() {
    // Cancel existing timer if it exists
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_currentIndex < widget.posts.length - 1) {
        _currentIndex++;
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _timer?.cancel();
        Navigator.of(context).pop();
      }
    });
  }

  void _handlePageChange(int index) {
    setState(() => _currentIndex = index);

    if (index == widget.posts.length - 1) {
      _timer?.cancel();
      _timer = Timer(const Duration(seconds: 10), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
      if (_isPaused) {
        _timer?.cancel();
      } else {
        _startAutoSlide();
      }
    });
  }

  // Future<void> _shareCurrentImage() async {
  //   try {
  //     final String imagePath = widget.posts[_currentIndex]['imageUrl'];

  //     // Create a temporary file
  //     final bytes = await rootBundle.load(imagePath);
  //     final tempDir = await getTemporaryDirectory();
  //     final tempFile = File('${tempDir.path}/share_image.jpg');
  //     await tempFile.writeAsBytes(
  //       bytes.buffer.asUint8List(bytes.offsetInBytes, bytes.lengthInBytes),
  //     );

  //     // Share the file
  //     await Share.shareXFiles([
  //       XFile(tempFile.path),
  //     ], text: 'Check out this amazing post from BuildMate!');
  //   } catch (e) {
  //     debugPrint('Error sharing image: $e');
  //   }
  // }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      body: Stack(
        fit: StackFit.expand,
        children: [
          GestureDetector(
            onLongPressStart: (_) => _togglePause(),
            onLongPressEnd: (_) => _togglePause(),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _handlePageChange,
              itemCount: widget.posts.length,
              itemBuilder: (context, index) {
                final post = widget.posts[index];
                return Stack(
                  fit: StackFit.expand,
                  children: [
                    Hero(
                      tag: 'reel_${post['imageUrl']}_$index',
                      child: Image.asset(
                        post['imageUrl'],
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                    ),
                    // Left tap area
                    Positioned(
                      left: 0,
                      top: 0,
                      bottom: 0,
                      width: MediaQuery.of(context).size.width * 0.3,
                      child: GestureDetector(
                        onTap: () {
                          if (_currentIndex > 0) {
                            _currentIndex--;
                            _pageController.animateToPage(
                              _currentIndex,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        },
                        child: Container(color: Colors.transparent),
                      ),
                    ),
                    // Right tap area
                    Positioned(
                      right: 0,
                      top: 0,
                      bottom: 0,
                      width: MediaQuery.of(context).size.width * 0.3,
                      child: GestureDetector(
                        onTap: () {
                          if (_currentIndex < widget.posts.length - 1) {
                            _currentIndex++;
                            _pageController.animateToPage(
                              _currentIndex,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        },
                        child: Container(color: Colors.transparent),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          // Pause indicator
          if (_isPaused)
            Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha((0.5 * 255).round()),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.pause, color: Colors.white, size: 48),
              ),
            ),

          // Close button
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha((0.5 * 255).round()),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 24),
              ),
            ),
          ),

          // Progress indicator
          Positioned(
            top: MediaQuery.of(context).padding.top,
            left: 0,
            right: 0,
            child: Row(
              children: List.generate(
                widget.posts.length,
                (index) => Expanded(
                  child: Container(
                    height: 2,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      color:
                          index == _currentIndex
                              ? Colors.white
                              : Colors.white.withAlpha((0.5 * 255).round()),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Bottom info bar
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(
                16,
              ).copyWith(bottom: MediaQuery.of(context).padding.bottom + 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withAlpha((0.8 * 255).round()),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Profile and likes
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: AssetImage(
                          widget.posts[_currentIndex]['profileImage'],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Row(
                        children: [
                          const Icon(
                            Icons.favorite,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            widget.posts[_currentIndex]['likes'],
                            style: MyTypography.Medium.copyWith(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Share button
                  GestureDetector(
                    onTap: () {},
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha((0.5 * 255).round()),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.share,
                        color: Colors.white,
                        size: 22,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
