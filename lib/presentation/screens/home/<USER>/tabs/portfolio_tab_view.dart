import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:build_mate/data/dto/artisan_rating_response.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:build_mate/presentation/components/artisan_dismissible_image_viewer.dart';
import 'package:build_mate/presentation/state/artisan_portfolio_flow_state.dart';
import 'package:build_mate/presentation/view_models/artisan/artisan_portfolio_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/screens/profile/edit_profile_screen.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

// ignore: must_be_immutable
class PortfolioTabView extends ConsumerStatefulWidget {
  final String coverImageUrl = 'assets/images/profile_pic.png';
  final String profileImageUrl = 'assets/images/profile_pic.png';
  String name = 'Tatenda Kabike'; // Changed to non-final to allow updating
  String profession = 'Electrician';
  String location = 'Harare';
  final double distance = 3.8;
  final double rating = 4;
  final int totalReviews = 5;
  final List<String> certifications = ['Connection', 'Fixing'];
  String serviceType = 'Electrician'; // Changed to non-final to allow updating
  final bool isActive = true;
  final List<String> specializations = ['Connection', 'Fixing'];
  String about =
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco.';
  final String nextAvailableTime = '2.00 am';

  PortfolioTabView({super.key});

  @override
  ConsumerState<PortfolioTabView> createState() => _PortfolioTabViewState();
}

class _PortfolioTabViewState extends ConsumerState<PortfolioTabView> {
  int _selectedTabIndex = 0;

  // Make _workImages non-final so it can be updated
  final List<String> _workImages = [
    'assets/images/grid_1.jpeg',
    'assets/images/grid_2.jpeg',
    'assets/images/grid_3.jpg',
    'assets/images/grid_4.jpeg',
    'assets/images/grid_1.jpeg',
    'assets/images/grid_2.jpeg',
    'assets/images/grid_3.jpg',
    'assets/images/grid_4.jpeg',
    'assets/images/grid_1.jpeg',
  ];

  final List<Map<String, dynamic>> _reviews = [
    {
      'name': 'John Smith',
      'avatar': 'assets/images/profile_pic.png',
      'rating': 5,
      'date': '2 weeks ago',
      'comment':
          'Excellent work! Very professional and completed the job quickly.',
    },
    {
      'name': 'Sarah Johnson',
      'avatar': 'assets/images/profile_pic.png',
      'rating': 4,
      'date': '1 month ago',
      'comment':
          'Good service, fixed my electrical issues efficiently. Would recommend.',
    },
    {
      'name': 'Michael Brown',
      'avatar': 'assets/images/profile_pic.png',
      'rating': 5,
      'date': '2 months ago',
      'comment':
          'Very knowledgeable and helpful. Explained everything clearly.',
    },
  ];

  Widget _buildIconButton(IconData icon) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color:
            themeMode == ThemeMode.light
                ? Colors.white
                : customColors.surfaceVariant.withValues(alpha: 0.9),
        shape: BoxShape.circle,
        border:
            themeMode == ThemeMode.dark
                ? Border.all(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.2),
                  width: 1,
                )
                : null,
      ),
      child: Icon(
        icon,
        color:
            themeMode == ThemeMode.light
                ? Colors.black87
                : customColors.textPrimaryColor,
      ),
    );
  }

  Widget _buildTabs() {
    return Row(
      children: [
        _buildTab('Overview', 0),
        _buildTab('Recent Work', 1),
        _buildTab('Reviews', 2),
      ],
    );
  }

  Widget _buildTab(String text, int index) {
    final isSelected = _selectedTabIndex == index;
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTabIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 24),
        padding: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? orangeColor : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          text,
          style: MyTypography.Medium.copyWith(
            color:
                isSelected
                    ? orangeColor
                    : (themeMode == ThemeMode.light
                        ? Colors.grey
                        : customColors.textPrimaryColor.withValues(alpha: 0.6)),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color:
                themeMode == ThemeMode.light
                    ? Colors.black.withAlpha((0.05 * 255).round())
                    : Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
          ),
        ],
        border:
            themeMode == ThemeMode.dark
                ? Border(
                  top: BorderSide(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                    width: 1,
                  ),
                )
                : null,
      ),
      child: PrimaryButton(
        text: 'Update Profile',
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => EditProfileScreen()),
          );

          // Update profile if result is not null
          if (result != null) {
            setState(() {
              // Update the profile data with the returned values
              widget.name = result['name'];
              widget.profession = result['profession'];
              widget.location = result['location'];
              widget.about = result['about'];
              widget.specializations.clear();
              widget.specializations.addAll(result['specializations']);
            });
          }
        },
        height: 48.0,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadRandomServiceType();
  }

  Future<void> _loadRandomServiceType() async {
    try {
      final String response = await rootBundle.loadString(
        'assets/json/categories.json',
      );
      final data = await json.decode(response);
      final categories = List<Map<String, dynamic>>.from(data['categories']);

      if (categories.isNotEmpty) {
        // Generate a random index
        final random = Random();
        final randomIndex = random.nextInt(categories.length);

        // Get a random category
        final randomCategory = categories[randomIndex];

        // Update the service type
        setState(() {
          widget.serviceType = randomCategory['name'];

          // If the category has subcategories, update specializations
          if (randomCategory['subcategories'] != null &&
              randomCategory['subcategories'].isNotEmpty) {
            // Get up to 3 random subcategories
            final subcategories = List<String>.from(
              randomCategory['subcategories'],
            );
            subcategories.shuffle();
            final selectedSubcategories =
                subcategories.take(min(3, subcategories.length)).toList();

            // Update specializations
            widget.specializations.clear();
            widget.specializations.addAll(selectedSubcategories);
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    // final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover Image and Profile Section
            Stack(
              clipBehavior: Clip.none,
              children: [
                // Cover Image with loading indicator
                Stack(
                  children: [
                    Image.network(
                      state.artisanProfileData?.coverPhoto ?? '',
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;

                        final customColors = ref.watch(customColorsProvider);
                        final themeMode = ref.watch(themeModeProvider);

                        return Shimmer.fromColors(
                          baseColor:
                              themeMode == ThemeMode.light
                                  ? Colors.grey[300]!
                                  : customColors.surfaceVariant.withValues(
                                    alpha: 0.3,
                                  ),
                          highlightColor:
                              themeMode == ThemeMode.light
                                  ? Colors.grey[100]!
                                  : customColors.surfaceVariant.withValues(
                                    alpha: 0.1,
                                  ),
                          period: const Duration(milliseconds: 1500),
                          child: Container(
                            height: 200,
                            width: double.infinity,
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        final customColors = ref.watch(customColorsProvider);
                        final themeMode = ref.watch(themeModeProvider);

                        return Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors:
                                  themeMode == ThemeMode.light
                                      ? [
                                        Colors.grey.shade100,
                                        Colors.grey.shade200,
                                      ]
                                      : [
                                        customColors.surfaceVariant.withValues(
                                          alpha: 0.5,
                                        ),
                                        customColors.surfaceVariant.withValues(
                                          alpha: 0.7,
                                        ),
                                      ],
                            ),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported_rounded,
                                  size: 48,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[600]
                                          : customColors.textPrimaryColor
                                              .withValues(alpha: 0.6),
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  'Unable to load cover image',
                                  style: MyTypography.Medium.copyWith(
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.grey[700]
                                            : customColors.textPrimaryColor
                                                .withValues(alpha: 0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),

                // Contact Actions
                Positioned(
                  top: 40,
                  right: 16,
                  child: Row(
                    children: [_buildIconButton(Icons.edit_note_outlined)],
                  ),
                ),

                // Profile Image
                Positioned(
                  bottom: -40,
                  left: 16,
                  child: Builder(
                    builder: (context) {
                      final customColors = ref.watch(customColorsProvider);
                      final themeMode = ref.watch(themeModeProvider);

                      return CircleAvatar(
                        radius: 40,
                        backgroundColor:
                            themeMode == ThemeMode.light
                                ? Colors.grey[200]
                                : customColors.surfaceVariant.withValues(
                                  alpha: 0.5,
                                ),
                        child: ClipOval(
                          child: Image.network(
                            state.artisanProfileData?.avatar ?? '',
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;

                              return Shimmer.fromColors(
                                baseColor:
                                    themeMode == ThemeMode.light
                                        ? Colors.grey[300]!
                                        : customColors.surfaceVariant
                                            .withValues(alpha: 0.3),
                                highlightColor:
                                    themeMode == ThemeMode.light
                                        ? Colors.grey[100]!
                                        : customColors.surfaceVariant
                                            .withValues(alpha: 0.1),
                                period: const Duration(milliseconds: 1500),
                                child: Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.white
                                            : customColors.surfaceVariant,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[200]
                                          : customColors.surfaceVariant
                                              .withValues(alpha: 0.5),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.person,
                                  size: 40,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[400]
                                          : customColors.textPrimaryColor
                                              .withValues(alpha: 0.4),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 48),

            // Profile Info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Builder(
                builder: (context) {
                  final customColors = ref.watch(customColorsProvider);
                  final themeMode = ref.watch(themeModeProvider);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            state.artisanProfileData?.name ?? '',
                            style: MyTypography.SemiBold.copyWith(
                              fontSize: 24,
                              color: customColors.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Icon(
                              Icons.verified,
                              size: 20,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Rating
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 20),
                          const SizedBox(width: 4),
                          Text(
                            '${widget.rating} (${widget.totalReviews} Reviews)',
                            style: MyTypography.Regular.copyWith(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.grey[800]
                                      : customColors.textPrimaryColor
                                          .withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Divider
                      Divider(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[300]
                                : customColors.textPrimaryColor.withValues(
                                  alpha: 0.2,
                                ),
                        thickness: 1,
                      ),

                      const SizedBox(height: 16),

                      // Service Type Card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              themeMode == ThemeMode.light
                                  ? Colors.teal[900]
                                  : customColors.primaryContainer,
                          borderRadius: BorderRadius.circular(12),
                          border:
                              themeMode == ThemeMode.dark
                                  ? Border.all(
                                    color: customColors.textPrimaryColor
                                        .withValues(alpha: 0.2),
                                    width: 1,
                                  )
                                  : null,
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.work,
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.white
                                      : customColors.textPrimaryColor,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              state
                                      .artisanProfileData
                                      ?.specializations
                                      ?.first
                                      .services
                                      ?.name ??
                                  '',
                              style: MyTypography.SemiBold.copyWith(
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.white
                                        : customColors.textPrimaryColor,
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Tabs
                      _buildTabs(),

                      const SizedBox(height: 24),

                      // Tab Content
                      _buildTabContent(state),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildTabContent(ArtisanPortfolioFlowState state) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildOverviewTab(state);
      case 1:
        return _buildRecentWorkTab(state);
      case 2:
        return _buildReviewsTab();
      default:
        return _buildOverviewTab(state);
    }
  }

  Widget _buildOverviewTab(ArtisanPortfolioFlowState state) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Specialization
        Text(
          'Specialisation',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              (state
                          .artisanProfileData
                          ?.specializations
                          ?.first
                          .specializationTags ??
                      [])
                  .map<Widget>(
                    (spec) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[200]
                                : customColors.surfaceVariant.withValues(
                                  alpha: 0.3,
                                ),
                        borderRadius: BorderRadius.circular(20),
                        border:
                            themeMode == ThemeMode.dark
                                ? Border.all(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.2),
                                  width: 1,
                                )
                                : null,
                      ),
                      child: Text(
                        spec.subCategories?.name ?? '',
                        style: MyTypography.Regular.copyWith(
                          color: customColors.textPrimaryColor,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),

        const SizedBox(height: 24),

        // About
        Text(
          'About',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          state.artisanProfileData?.about ?? '',
          style: MyTypography.Regular.copyWith(
            color:
                themeMode == ThemeMode.light
                    ? Colors.grey[600]
                    : customColors.textPrimaryColor.withValues(alpha: 0.8),
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentWorkTab(ArtisanPortfolioFlowState state) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Remove any extra padding/margin that might be causing white space
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Projects',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 18,
                color: customColors.textPrimaryColor,
              ),
            ),
            TextButton.icon(
              onPressed: () => _showUpdateProjectsDialog(state),
              icon: Icon(
                Icons.edit,
                size: 16,
                color:
                    themeMode == ThemeMode.light
                        ? Colors.teal[700]
                        : customColors.infoColor,
              ),
              label: Text(
                'Edit',
                style: MyTypography.Medium.copyWith(
                  color:
                      themeMode == ThemeMode.light
                          ? Colors.teal[700]
                          : customColors.infoColor,
                ),
              ),
              // Remove any padding that might be causing extra space
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(50, 30),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        // Minimal spacing
        const SizedBox(height: 4),

        // Show empty state if no images
        state.artisanImages.isEmpty
            ? Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.photo_library_outlined,
                      size: 64,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[400]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.4,
                              ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Upload your recent work images',
                      style: MyTypography.Medium.copyWith(
                        fontSize: 16,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[600]
                                : customColors.textPrimaryColor.withValues(
                                  alpha: 0.6,
                                ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextButton.icon(
                      onPressed: () => _showUpdateProjectsDialog(state),
                      icon: Icon(
                        Icons.add_photo_alternate,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.teal[700]
                                : customColors.infoColor,
                      ),
                      label: Text(
                        'Add Images',
                        style: MyTypography.Medium.copyWith(
                          color:
                              themeMode == ThemeMode.light
                                  ? Colors.teal[700]
                                  : customColors.infoColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
            : GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: state.artisanImages.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap:
                      () =>
                          _showImageViewer(context, state.artisanImages, index),
                  child: Hero(
                    tag: 'work_image_$index',
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        state.artisanImages[index].imageUrl ?? '',
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;

                          return Shimmer.fromColors(
                            baseColor:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[300]!
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            highlightColor:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[100]!
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.1,
                                    ),
                            period: const Duration(milliseconds: 1500),
                            child: Container(
                              decoration: BoxDecoration(
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.white
                                        : customColors.surfaceVariant,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: BoxDecoration(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.grey[200]
                                      : customColors.surfaceVariant.withValues(
                                        alpha: 0.5,
                                      ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.broken_image,
                                size: 24,
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.grey[400]
                                        : customColors.textPrimaryColor
                                            .withValues(alpha: 0.4),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
      ],
    );
  }

  Widget _buildReviewsTab() {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Client Reviews',
              style: MyTypography.SemiBold.copyWith(fontSize: 18),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    state.averageRating.toStringAsFixed(1),
                    style: MyTypography.SemiBold.copyWith(
                      color: Colors.amber.shade800,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),

        // Show loading indicator while fetching ratings
        if (viewModel.isLoadingRatings)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 32.0),
              child: Column(
                children: [
                  CircularProgressIndicator(color: Colors.teal[700]),
                  const SizedBox(height: 16),
                  Text(
                    'Loading reviews...',
                    style: MyTypography.Medium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          )
        // Show empty state if no ratings
        else if (state.ratingsList.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                children: [
                  Icon(
                    Icons.star_border_rounded,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No reviews yet',
                    style: MyTypography.Medium.copyWith(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your client reviews will appear here',
                    style: MyTypography.Regular.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          )
        // Show ratings list
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: state.ratingsList.length,
            separatorBuilder:
                (context, index) =>
                    Divider(color: Colors.grey[200], thickness: 1, height: 32),
            itemBuilder: (context, index) {
              final rating = state.ratingsList[index];
              return _buildReviewItem(rating, viewModel);
            },
          ),
      ],
    );
  }

  Widget _buildReviewItem(
    ArtisanRatingResponse review,
    ArtisanPortfolioViewModel viewModel,
  ) {
    final client = review.client;
    final createdAt = review.createdAt;
    final formattedDate = viewModel.formatRatingDate(createdAt ?? '');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Client avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.grey[200],
              backgroundImage:
                  client?.avatar != null
                      ? NetworkImage(client?.avatar ?? '')
                      : null,
              child:
                  client?.avatar == null
                      ? Icon(Icons.person, color: Colors.grey[400])
                      : null,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  client?.name ?? 'Anonymous Client',
                  style: MyTypography.SemiBold,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    ...List.generate(
                      5,
                      (index) => Icon(
                        index < (review.rating ?? 0)
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      formattedDate,
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          review.comments ?? '',
          style: MyTypography.Regular.copyWith(
            color: Colors.grey[700],
            height: 1.4,
          ),
        ),
      ],
    );
  }

  void _showImageViewer(
    BuildContext context,
    List<ArtisanImage> images,
    int initialIndex,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => ArtisanDismissibleImageViewer(
            images: images,
            initialIndex: initialIndex,
          ),
    );
  }

  void _showUpdateProjectsDialog(ArtisanPortfolioFlowState state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Important for custom height
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            // Set height to 80% of screen height
            height: MediaQuery.of(context).size.height * 0.8,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Update Project Images',
                  style: MyTypography.SemiBold.copyWith(fontSize: 18),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: Consumer(
                    builder: (context, ref, _) {
                      final viewModelState = ref.watch(
                        artisanPortfolioViewModelProvider,
                      );
                      final viewModel = ref.watch(
                        artisanPortfolioViewModelProvider.notifier,
                      );

                      return GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8,
                            ),
                        itemCount:
                            viewModelState.artisanImages.length < 21
                                ? viewModelState.artisanImages.length +
                                    1 // Add one more for the "add" button
                                : 21, // Just show 21 images if we have 21 or more
                        itemBuilder: (context, index) {
                          if (index == viewModelState.artisanImages.length &&
                              viewModelState.artisanImages.length < 21) {
                            // Add new image tile
                            final isUploading =
                                viewModel.isUploadingArtisanImage;
                            final uploadProgress =
                                viewModel.artisanImageUploadProgress;

                            return GestureDetector(
                              onTap:
                                  isUploading
                                      ? null
                                      : () => _pickProjectImage(),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child:
                                    isUploading
                                        ? Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            CircularProgressIndicator(
                                              value: uploadProgress,
                                              strokeWidth: 2,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              '${(uploadProgress * 100).toInt()}%',
                                              style: MyTypography
                                                  .Regular.copyWith(
                                                fontSize: 12,
                                              ),
                                            ),
                                          ],
                                        )
                                        : const Icon(
                                          Icons.add_photo_alternate,
                                          size: 40,
                                        ),
                              ),
                            );
                          } else {
                            // Existing image tile
                            final image = viewModelState.artisanImages[index];
                            final isDeleting = viewModel.isImageDeleting(
                              image.id ?? 0,
                            );

                            return Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    image.imageUrl ?? '',
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                    // Add a color filter when deleting to dim the image
                                    color:
                                        isDeleting
                                            ? Colors.black.withAlpha(
                                              (0.5 * 255).round(),
                                            )
                                            : null,
                                    colorBlendMode:
                                        isDeleting ? BlendMode.darken : null,
                                  ),
                                ),

                                // Show delete progress indicator
                                if (isDeleting)
                                  Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const SizedBox(
                                          width: 24,
                                          height: 24,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'Deleting...',
                                          style: MyTypography.Regular.copyWith(
                                            fontSize: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                // Delete button (only show if not deleting)
                                if (!isDeleting)
                                  Positioned(
                                    top: 4,
                                    right: 4,
                                    child: GestureDetector(
                                      onTap: () {
                                        // Show confirmation dialog before deleting
                                        showDialog(
                                          context: context,
                                          builder:
                                              (context) => AlertDialog(
                                                title: const Text(
                                                  'Delete Image',
                                                ),
                                                content: const Text(
                                                  'Are you sure you want to delete this image? This action cannot be undone.',
                                                ),
                                                actions: [
                                                  TextButton(
                                                    onPressed:
                                                        () => Navigator.pop(
                                                          context,
                                                        ),
                                                    child: const Text('Cancel'),
                                                  ),
                                                  TextButton(
                                                    onPressed: () {
                                                      // Close dialog
                                                      Navigator.pop(context);
                                                      // Delete image
                                                      viewModel
                                                          .deleteArtisanImage(
                                                            image.id ?? 0,
                                                            image.imageUrl ??
                                                                '',
                                                          );
                                                    },
                                                    style: TextButton.styleFrom(
                                                      foregroundColor:
                                                          Colors.red,
                                                    ),
                                                    child: const Text('Delete'),
                                                  ),
                                                ],
                                              ),
                                        );
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.black54,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          }
                        },
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),
                PrimaryButton(
                  text: 'Save Changes',
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  height: 48.0,
                ),
              ],
            ),
          ),
    );
  }

  void _pickProjectImage() {
    final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);
    viewModel.pickAndUploadProjectImage(context);
  }
}
