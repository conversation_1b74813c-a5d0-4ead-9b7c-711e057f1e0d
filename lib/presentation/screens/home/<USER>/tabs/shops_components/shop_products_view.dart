import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/search_product_textfield.dart';
import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'product_card.dart';

class ShopProductsView extends ConsumerStatefulWidget {
  const ShopProductsView({super.key});

  @override
  ConsumerState<ShopProductsView> createState() => _ShopProductViewState();
}

class _ShopProductViewState extends ConsumerState<ShopProductsView> {
  Widget _buildProductSkeleton(bool isDarkMode, customColors) {
    return Shimmer.fromColors(
      baseColor:
          isDarkMode
              ? customColors.surfaceVariant.withValues(alpha: 0.3)
              : Colors.grey[300]!,
      highlightColor:
          isDarkMode
              ? customColors.surfaceVariant.withValues(alpha: 0.1)
              : Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color:
              isDarkMode
                  ? customColors.surfaceVariant.withValues(alpha: 0.5)
                  : Colors.grey[300],
          borderRadius: BorderRadius.circular(16),
          border:
              isDarkMode
                  ? Border.all(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                    width: 1,
                  )
                  : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image skeleton
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color:
                      isDarkMode
                          ? customColors.surfaceVariant.withValues(alpha: 0.7)
                          : Colors.grey[400],
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
              ),
            ),
            // Content skeleton
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title skeleton
                    Container(
                      height: 16,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode
                                ? customColors.surfaceVariant.withValues(
                                  alpha: 0.7,
                                )
                                : Colors.grey[400],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(height: 6),
                    // Subtitle skeleton
                    Container(
                      height: 12,
                      width: double.infinity * 0.7,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode
                                ? customColors.surfaceVariant.withValues(
                                  alpha: 0.6,
                                )
                                : Colors.grey[350],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Spacer(),
                    // Price and cart skeleton
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          height: 14,
                          width: 60,
                          decoration: BoxDecoration(
                            color:
                                isDarkMode
                                    ? customColors.surfaceVariant.withValues(
                                      alpha: 0.7,
                                    )
                                    : Colors.grey[400],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Container(
                          height: 22,
                          width: 22,
                          decoration: BoxDecoration(
                            color:
                                isDarkMode
                                    ? customColors.surfaceVariant.withValues(
                                      alpha: 0.6,
                                    )
                                    : Colors.grey[350],
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showProductBottomSheet(BuildContext context, product) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? customColors.surfaceVariant : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) {
        final imageUrl =
            (product.productImages != null && product.productImages!.isNotEmpty)
                ? product.productImages!.first.imageUrl ?? ''
                : '';
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (imageUrl.isNotEmpty)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    imageUrl,
                    height: 160,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              const SizedBox(height: 16),
              Text(
                product.name ?? '',
                style: MyTypography.Medium.copyWith(
                  fontSize: 20,
                  color: customColors.textPrimaryColor,
                ),
              ),
              if (product.hardwareSubCategory?.name != null)
                Text(
                  product.hardwareSubCategory!.name!,
                  style: MyTypography.Light.copyWith(
                    fontSize: 16,
                    color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                  ),
                ),
              const SizedBox(height: 8),
              if (product.price != null)
                Text(
                  '\$${product.price!.toStringAsFixed(2)}',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 18,
                    color: isDarkMode ? Colors.green[400] : Colors.green[700],
                  ),
                ),
              if (product.description != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Text(
                    product.description!,
                    style: MyTypography.Light.copyWith(
                      fontSize: 14,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.8,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopProductsViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor:
            isDarkMode ? customColors.surfaceVariant : darkBlueColor,
        elevation: isDarkMode ? 4 : 0,
        shadowColor: isDarkMode ? Colors.black.withValues(alpha: 0.3) : null,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: isDarkMode ? customColors.textPrimaryColor : Colors.white,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          children: [
            Text(
              state.shopname,
              style: MyTypography.Medium.copyWith(
                color:
                    isDarkMode ? customColors.textPrimaryColor : Colors.white,
                fontSize: 18,
              ),
            ),
            Text(
              state.branchName,
              style: MyTypography.Light.copyWith(
                color:
                    isDarkMode
                        ? customColors.textPrimaryColor.withValues(alpha: 0.85)
                        : Colors.white.withAlpha((0.85 * 255).round()),
                fontSize: 14,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.favorite_border,
              color: isDarkMode ? customColors.textPrimaryColor : Colors.white,
            ),
            onPressed: () {},
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16),
            // Search Bar
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow:
                    isDarkMode
                        ? [
                          BoxShadow(
                            color: Colors.black.withAlpha((0.3 * 255).round()),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : [
                          BoxShadow(
                            color: Colors.grey.withAlpha((0.08 * 255).round()),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
              ),
              child: const SearchProductTextField(),
            ),
            SizedBox(height: 24),
            // Products Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  state.selectedSubcategoryName ?? 'All Products',
                  style: MyTypography.Bold.copyWith(
                    fontSize: 22,
                    color: customColors.textPrimaryColor,
                  ),
                ),
                Row(
                  children: [
                    const SizedBox(width: 8),
                    if (state.selectedSubcategoryId != null)
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isDarkMode
                                  ? customColors.surfaceVariant
                                  : Colors.grey[200],
                          foregroundColor: customColors.textPrimaryColor,
                          elevation: isDarkMode ? 4 : 0,
                          shadowColor:
                              isDarkMode
                                  ? Colors.black.withValues(alpha: 0.3)
                                  : null,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                            side:
                                isDarkMode
                                    ? BorderSide(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.1),
                                      width: 1,
                                    )
                                    : BorderSide.none,
                          ),
                          textStyle: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () async {
                          await ref
                              .read(shopProductsViewModelProvider.notifier)
                              .resetFilter();
                        },
                        child: Row(
                          children: [
                            Icon(Icons.refresh, size: 18, color: orangeColor),
                            const SizedBox(width: 6),
                            Text(
                              'Reset',
                              style: MyTypography.Medium.copyWith(
                                color: customColors.textPrimaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16),
            // Products Grid or Skeleton
            Expanded(
              child:
                  state.isLoadingProducts
                      ? GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 16,
                          childAspectRatio: 0.75,
                        ),
                        itemCount: 6,
                        itemBuilder: (context, index) {
                          return _buildProductSkeleton(
                            isDarkMode,
                            customColors,
                          );
                        },
                      )
                      : state.products.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/empty_cart.svg',
                              width: 120,
                              height: 120,
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'No products found',
                              style: MyTypography.Medium.copyWith(
                                fontSize: 18,
                                color: customColors.textPrimaryColor.withValues(
                                  alpha: 0.6,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                      : GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 16,
                          childAspectRatio: 0.75,
                        ),
                        itemCount: state.products.length,
                        itemBuilder: (context, index) {
                          final product = state.products[index];
                          final imageUrl =
                              (product.productImages != null &&
                                      product.productImages!.isNotEmpty)
                                  ? product.productImages!.first.imageUrl ?? ''
                                  : '';
                          return GestureDetector(
                            onTap:
                                () => _showProductBottomSheet(context, product),
                            child: ProductCard(
                              imageUrl: imageUrl,
                              title: product.name ?? '',
                              subtitle: product.hardwareSubCategory?.name ?? '',
                              price:
                                  product.price != null
                                      ? '\$${product.price!.toStringAsFixed(2)}'
                                      : '',
                              isPremium: false,
                              backgroundColor: Theme.of(context).cardColor,
                            ),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
