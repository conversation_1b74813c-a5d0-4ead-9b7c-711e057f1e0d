import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:async';

class SearchShopTextField extends ConsumerStatefulWidget {
  const SearchShopTextField({super.key});
  @override
  ConsumerState<SearchShopTextField> createState() =>
      _SearchShopTextFieldState();
}

class _SearchShopTextFieldState extends ConsumerState<SearchShopTextField> {
  Timer? _debounceTimer;
  final TextEditingController _distanceController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  String _lastSearch = '';

  @override
  void initState() {
    super.initState();
    // Prevent controller text from being reset on rebuilds by not setting it from state
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _distanceController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onDistanceChanged(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final distance = double.tryParse(value);
      if (distance != null && distance > 0) {
        final viewModel = ref.read(shopsViewModelProvider.notifier);
        viewModel.setRadius(distance);
        final currentLocation = ref.read(shopsViewModelProvider).userLocation;
        if (currentLocation != null) {
          viewModel.getNearbyShops(
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            distance: distance,
          );
        }
      }
    });
  }

  void _onSearchChanged(String value) {
    _lastSearch = value;
    _debounceTimer?.cancel();

    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      // Only call search if the value hasn't changed during debounce
      if (_searchController.text == _lastSearch) {
        final viewModel = ref.read(shopsViewModelProvider.notifier);
        viewModel.updateSearchQueryString(value);
      }
    });
  }

  @override
  void didUpdateWidget(covariant SearchShopTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Do not reset _searchController.text here
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopsViewModelProvider);
    final viewModel = ref.watch(shopsViewModelProvider.notifier);
    final isLoading = ref.watch(shopsViewModelProvider).isLoading;
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Fix: Ensure state.branchCount and state.radius are not null
    final branchCount = state.branchCount;
    final radius = state.radius;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(25),
        boxShadow:
            themeMode == ThemeMode.light
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha((0.1 * 255).round()),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
        border:
            themeMode == ThemeMode.dark
                ? Border.all(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                  width: 1,
                )
                : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 50,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search for products...',
                      hintStyle: MyTypography.Regular.copyWith(
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.6,
                        ),
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                      ),
                    ),
                    style: MyTypography.Regular.copyWith(
                      fontSize: 16,
                      color: customColors.textPrimaryColor,
                    ),
                    onChanged: _onSearchChanged,
                  ),
                ),
                state.isSearchingProduct == true
                    ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            themeMode == ThemeMode.light
                                ? orangeColor
                                : orangeColor.withValues(alpha: 0.9),
                          ),
                        ),
                      ),
                    )
                    : Container(
                      margin: const EdgeInsets.only(right: 4),
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[100]
                                : customColors.textPrimaryColor.withValues(
                                  alpha: 0.1,
                                ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.search,
                          color: customColors.textPrimaryColor.withValues(
                            alpha: 0.7,
                          ),
                          size: 20,
                        ),
                        onPressed: () {
                          // Trigger search manually if needed
                          if (_searchController.text.isNotEmpty) {
                            _onSearchChanged(_searchController.text);
                          }
                        },
                      ),
                    ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 0.5,
            color: customColors.textPrimaryColor.withValues(alpha: 0.1),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (!state.isAdjusting) ...[
                  Expanded(
                    child: Text(
                      branchCount > 0
                          ? 'Showing $branchCount hardware ${branchCount > 1 ? 'shops' : 'shop'} within ${radius}km'
                          : 'No shops found so far with a radius of $radius',
                      style: MyTypography.Regular.copyWith(
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.7,
                        ),
                        fontSize: 12,
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color:
                          themeMode == ThemeMode.light
                              ? darkBlueColor.withValues(alpha: 0.1)
                              : customColors.primaryContainer.withValues(
                                alpha: 0.3,
                              ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextButton(
                      onPressed: () => viewModel.setAdjustingRadiusState(true),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        'Adjust',
                        style: MyTypography.Medium.copyWith(
                          color:
                              themeMode == ThemeMode.light
                                  ? darkBlueColor
                                  : customColors.textPrimaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ] else
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _distanceController,
                            keyboardType: TextInputType.number,
                            onChanged: _onDistanceChanged,
                            decoration: InputDecoration(
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 0,
                                vertical: 8,
                              ),
                              border: InputBorder.none,
                              hintText: 'Enter distance in km',
                              hintStyle: MyTypography.Regular.copyWith(
                                color: customColors.textPrimaryColor.withValues(
                                  alpha: 0.5,
                                ),
                                fontSize: 12,
                              ),
                              suffixText: state.isLoading ? '' : 'km',
                              suffixStyle: MyTypography.Regular.copyWith(
                                color: customColors.textPrimaryColor.withValues(
                                  alpha: 0.6,
                                ),
                                fontSize: 12,
                              ),
                            ),
                            style: MyTypography.Regular.copyWith(
                              fontSize: 12,
                              color: customColors.textPrimaryColor,
                            ),
                          ),
                        ),
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[100]
                                    : customColors.textPrimaryColor.withValues(
                                      alpha: 0.1,
                                    ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child:
                              isLoading
                                  ? Padding(
                                    padding: const EdgeInsets.all(4),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        themeMode == ThemeMode.light
                                            ? darkBlueColor
                                            : customColors.textPrimaryColor,
                                      ),
                                    ),
                                  )
                                  : IconButton(
                                    icon: Icon(
                                      Icons.close,
                                      size: 14,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.7),
                                    ),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    onPressed: () {
                                      _distanceController.text =
                                          radius.toString();
                                      viewModel.setAdjustingRadiusState(false);
                                    },
                                  ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
