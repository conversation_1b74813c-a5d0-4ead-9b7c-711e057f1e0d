import 'dart:ui';

import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ShopDetailsSheet extends ConsumerWidget {
  const ShopDetailsSheet(this.onClose, {super.key});

  final VoidCallback? onClose;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(shopsViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: isDarkMode ? 15 : 10,
          sigmaY: isDarkMode ? 15 : 10,
        ),
        child: Container(
          decoration: BoxDecoration(
            color:
                isDarkMode
                    ? customColors.surfaceVariant.withValues(alpha: 0.98)
                    : Colors.white.withValues(alpha: 0.95),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            border:
                isDarkMode
                    ? Border.all(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.1,
                      ),
                      width: 1,
                    )
                    : null,
            boxShadow: [
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : Colors.black.withValues(alpha: 0.1),
                blurRadius: isDarkMode ? 15 : 10,
                offset: const Offset(0, -2),
                spreadRadius: isDarkMode ? 2 : 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Stack(
              children: [
                // Close button
                Positioned(
                  top: -12,
                  right: -12,
                  child: IconButton(
                    onPressed: onClose,
                    icon: Icon(
                      Icons.close,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.7,
                      ),
                      size: 24,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    splashRadius: 24,
                    tooltip: 'Close',
                  ),
                ),
                // Main content
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Restaurant image
                    Container(
                      width: 100, // Reduced from 120 to 100
                      height: 100, // Reduced from 120 to 100
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        gradient: LinearGradient(
                          colors:
                              isDarkMode
                                  ? [
                                    customColors.primaryContainer.withValues(
                                      alpha: 0.3,
                                    ),
                                    customColors.primaryContainer.withValues(
                                      alpha: 0.6,
                                    ),
                                  ]
                                  : [
                                    const Color(0xFF87CEEB),
                                    const Color(0xFF4682B4),
                                  ],
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: Stack(
                          children: [
                            // Background gradient
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors:
                                      isDarkMode
                                          ? [
                                            customColors.primaryContainer
                                                .withValues(alpha: 0.3),
                                            customColors.primaryContainer
                                                .withValues(alpha: 0.6),
                                          ]
                                          : [
                                            const Color(0xFF87CEEB),
                                            const Color(0xFF4682B4),
                                          ],
                                ),
                              ),
                            ),
                            // Image
                            SizedBox(
                              width: double.infinity,
                              height: double.infinity,
                              child: Image.asset(
                                'assets/images/halsted_logo.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.4),
                                      size: 32,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    // Restaurant details
                    Expanded(
                      child: Column(
                        mainAxisSize:
                            MainAxisSize.min, // Ensures flexible height
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            state.selectedShop?.hardwareShop?.name ?? '',
                            style: MyTypography.Bold.copyWith(
                              fontSize: 20,
                              color: customColors.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Row(
                            children: [
                              Text(
                                state.selectedShop?.name ?? '',
                                style: MyTypography.Regular.copyWith(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.6),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10), // Reduced from 15 to 10
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Rating
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 4,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.star,
                                      color:
                                          isDarkMode
                                              ? Colors.amber.shade300
                                              : Colors.amber,
                                      size: 16,
                                    ),
                                    SizedBox(width: 2),
                                    Text(
                                      '5',
                                      style: MyTypography.Bold.copyWith(
                                        fontSize: 14,
                                        color: customColors.textPrimaryColor,
                                      ),
                                    ),
                                    SizedBox(width: 2),
                                    Text(
                                      'Rate',
                                      style: MyTypography.Bold.copyWith(
                                        color: customColors.textPrimaryColor
                                            .withValues(alpha: 0.6),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 15),
                              // Time
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_city_sharp,
                                    color: customColors.textPrimaryColor
                                        .withValues(alpha: 0.6),
                                    size: 16,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    '${state.selectedShop?.distanceKm ?? 0} Km away',
                                    style: MyTypography.SemiBold.copyWith(
                                      fontSize: 10,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.6),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 15),
                              // Delivery
                              // const Row(
                              //   children: [
                              //     Icon(
                              //       Icons.delivery_dining,
                              //       color: Colors.grey,
                              //       size: 16,
                              //     ),
                              //     SizedBox(width: 4),
                              //     Text(
                              //       'from \$5',
                              //       style: TextStyle(color: Colors.grey, fontSize: 12),
                              //     ),
                              //   ],
                              // ),
                            ],
                          ),
                          // Phone numbers and emails below rating
                          if (state.selectedShop?.hardwareShop?.phonenumbers !=
                                  null &&
                              state
                                  .selectedShop!
                                  .hardwareShop!
                                  .phonenumbers!
                                  .isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 6.0,
                                bottom: 2.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ...state
                                      .selectedShop!
                                      .hardwareShop!
                                      .phonenumbers!
                                      .where((phone) => phone.trim().isNotEmpty)
                                      .map(
                                        (phone) => Padding(
                                          padding: const EdgeInsets.only(
                                            bottom: 2.0,
                                          ),
                                          child: Column(
                                            children: [
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.phone,
                                                    color:
                                                        isDarkMode
                                                            ? Colors
                                                                .green
                                                                .shade300
                                                            : Colors.green,
                                                    size: 16,
                                                  ),
                                                  SizedBox(width: 6),
                                                  Text(
                                                    phone.trim(),
                                                    style: MyTypography
                                                        .SemiBold.copyWith(
                                                      fontSize: 14,
                                                      color:
                                                          customColors
                                                              .textPrimaryColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              vSpace(4),
                                            ],
                                          ),
                                        ),
                                      ),
                                  if (state
                                              .selectedShop
                                              ?.hardwareShop
                                              ?.emails !=
                                          null &&
                                      state
                                          .selectedShop!
                                          .hardwareShop!
                                          .emails!
                                          .isNotEmpty)
                                    ...state.selectedShop!.hardwareShop!.emails!
                                        .where(
                                          (email) => email.trim().isNotEmpty,
                                        )
                                        .map(
                                          (email) => Padding(
                                            padding: const EdgeInsets.only(
                                              top: 2.0,
                                            ),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.email,
                                                  color:
                                                      isDarkMode
                                                          ? orangeColor
                                                              .withValues(
                                                                alpha: 0.8,
                                                              )
                                                          : orangeColor,
                                                  size: 16,
                                                ),
                                                SizedBox(width: 6),
                                                Text(
                                                  email.trim(),
                                                  style: MyTypography
                                                      .SemiBold.copyWith(
                                                    fontSize: 14,
                                                    color:
                                                        customColors
                                                            .textPrimaryColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                ],
                              ),
                            ),
                          const SizedBox(height: 10), // Reduced from 15 to 10
                          // Order button
                          SizedBox(
                            width: double.infinity, // Added this
                            child: ElevatedButton(
                              onPressed: () {
                                context.pushNamed(
                                  RouteConstants.SHOP_PRODUCTS_VIEW,
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: orangeColor,
                                foregroundColor: Colors.white,
                                elevation: isDarkMode ? 8 : 2,
                                shadowColor:
                                    isDarkMode
                                        ? Colors.black.withValues(alpha: 0.5)
                                        : Colors.black.withValues(alpha: 0.2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                // Enhanced button styling for dark theme
                                side:
                                    isDarkMode
                                        ? BorderSide(
                                          color: orangeColor.withValues(
                                            alpha: 0.3,
                                          ),
                                          width: 1,
                                        )
                                        : null,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'View Shop',
                                    style: MyTypography.SemiBold.copyWith(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.arrow_forward,
                                    size: 18,
                                    color: Colors.white,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
