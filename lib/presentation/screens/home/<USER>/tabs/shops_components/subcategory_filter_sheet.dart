import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SubcategoryFilterSheet extends ConsumerStatefulWidget {
  final List<dynamic> subcategories;
  const SubcategoryFilterSheet({super.key, required this.subcategories});

  @override
  ConsumerState<SubcategoryFilterSheet> createState() =>
      _SubcategoryFilterSheetState();
}

class _SubcategoryFilterSheetState
    extends ConsumerState<SubcategoryFilterSheet> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final state = ref.watch(shopProductsViewModelProvider);
    final viewModel = ref.watch(shopProductsViewModelProvider.notifier);

    final filtered =
        state.search.isEmpty
            ? state.subcategories
            : state.subcategories
                .where(
                  (s) => (s['name'] ?? '').toString().toLowerCase().contains(
                    state.search.toLowerCase(),
                  ),
                )
                .toList();

    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with Filter and Close button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Text(
                  'Filter',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                Spacer(),
                IconButton(
                  icon: Icon(Icons.close, color: colorScheme.onSurface),
                  onPressed: () {
                    viewModel.getProductsByFilter(
                      state.selectedSubcategoryId ?? 0,
                    );
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
          // Search TextField
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search subcategories',
                hintStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: colorScheme.surface,
                prefixIcon: Icon(
                  Icons.search,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                contentPadding: EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 8,
                ),
              ),
              style: TextStyle(color: colorScheme.onSurface),
              onChanged: (val) => viewModel.filterSubCategory(val),
            ),
          ),
          Divider(
            height: 1,
            color: colorScheme.outline.withValues(alpha: 0.15),
          ),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: filtered.length,
              separatorBuilder:
                  (_, __) => Divider(
                    height: 1,
                    color: colorScheme.outline.withValues(alpha: 0.15),
                  ),
              itemBuilder: (context, index) {
                final sub = filtered[index];
                final isSelected = state.selectedSubcategoryId == sub['id'];

                return AnimatedContainer(
                  duration: Duration(milliseconds: 200),
                  curve: Curves.ease,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? colorScheme.primary.withValues(alpha: 0.08)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    title: Text(
                      sub['name'] ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    trailing:
                        isSelected
                            ? Icon(Icons.check, color: colorScheme.primary)
                            : null,
                    onTap: () {
                      viewModel.selectSubcategory(sub['id']);
                      viewModel.setSelectedSubCatagoryName(sub['name']);
                    },
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 2,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    hoverColor: colorScheme.onSurface.withValues(alpha: 0.04),
                    splashColor: colorScheme.onSurface.withValues(alpha: 0.08),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
