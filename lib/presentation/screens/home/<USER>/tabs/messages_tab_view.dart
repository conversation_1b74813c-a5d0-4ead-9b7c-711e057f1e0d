import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:build_mate/data/models/chat_models.dart';
import 'package:build_mate/data/services/chat_service.dart';
import 'package:build_mate/presentation/components/cards/message_card.dart';
import 'package:build_mate/presentation/screens/chat/chat_screen.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final chatServiceProvider = Provider((ref) => ChatService());

final artisanConversationsProvider = FutureProvider.autoDispose<
  List<Conversation>
>((ref) async {
  final chatService = ref.watch(chatServiceProvider);

  try {
    if (kDebugMode) {
      print('Starting to fetch artisan conversations');
    }

    // Get the current user's Supabase ID
    final supabase = Supabase.instance.client;
    final supabaseId = supabase.auth.currentUser?.id;

    if (kDebugMode) {
      print('Current Supabase user ID: $supabaseId');
    }

    if (supabaseId == null) {
      if (kDebugMode) {
        print('No authenticated user found');
      }
      return [];
    }

    // Get the artisan ID from the database using the Supabase ID
    try {
      final artisanResponse =
          await supabase
              .from('artisans')
              .select('id')
              .eq('supabase_id', supabaseId)
              .maybeSingle();

      if (kDebugMode) {
        print('Artisan response: $artisanResponse');
      }

      if (artisanResponse == null) {
        if (kDebugMode) {
          print('No artisan found for Supabase ID: $supabaseId');
        }
        return [];
      }

      final artisanId = artisanResponse['id'];
      if (kDebugMode) {
        print('Fetching conversations for artisan ID: $artisanId');
      }

      // Get conversations for this artisan
      try {
        final conversations = await chatService.getConversations(
          false,
          artisanId,
        );

        if (kDebugMode) {
          print(
            'Found ${conversations.length} conversations for artisan ID: $artisanId',
          );
          for (final conversation in conversations) {
            print(
              'Conversation ID: ${conversation.id}, Client: ${conversation.client?.name}',
            );
          }
        }

        return conversations;
      } catch (e) {
        if (kDebugMode) {
          print('Error in chatService.getConversations: $e');
        }
        rethrow;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error querying artisans table: $e');
      }
      rethrow;
    }
  } catch (e) {
    if (kDebugMode) {
      print('Top-level error in artisanConversationsProvider: $e');
    }
    rethrow;
  }
});

class MessagesTabView extends ConsumerWidget {
  const MessagesTabView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conversationsAsync = ref.watch(artisanConversationsProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Theme-aware colors for app bar
    final appBarColor =
        themeMode == ThemeMode.light
            ? darkBlueColor
            : customColors.surfaceVariant;
    final textColor =
        themeMode == ThemeMode.light
            ? Colors.white
            : customColors.textPrimaryColor;

    return Scaffold(
      backgroundColor:
          themeMode == ThemeMode.light
              ? Colors.grey[100]
              : const Color(
                0xFF1A1A1A,
              ), // Dar,
      body: Column(
        children: [
          Container(
            color: appBarColor,
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Messages',
                      style: MyTypography.SemiBold.copyWith(
                        fontSize: 24,
                        color: textColor,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.search, color: textColor),
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Debug button to manually trigger refresh
          // ElevatedButton(
          //   onPressed: () {
          //     ref.invalidate(artisanConversationsProvider);
          //     // ref.refresh(artisanConversationsProvider);
          //   },
          //   child: const Text('Refresh Conversations'),
          // ),
          Expanded(
            child: conversationsAsync.when(
              data: (conversations) {
                if (conversations.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'No conversations yet',
                          style: MyTypography.Medium.copyWith(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.6,
                            ),
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Debug info
                        if (kDebugMode)
                          FutureBuilder(
                            future:
                                Supabase.instance.client.auth.currentUser?.id !=
                                        null
                                    ? Supabase.instance.client
                                        .from('artisans')
                                        .select('id, name')
                                        .eq(
                                          'supabase_id',
                                          Supabase
                                              .instance
                                              .client
                                              .auth
                                              .currentUser!
                                              .id,
                                        )
                                        .maybeSingle()
                                    : Future.value(null),
                            builder: (context, snapshot) {
                              return Column(
                                children: [
                                  Text(
                                    'Auth state: ${Supabase.instance.client.auth.currentSession != null ? 'Logged in' : 'Not logged in'}',
                                    style: MyTypography.Regular.copyWith(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.7),
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    'User ID: ${Supabase.instance.client.auth.currentUser?.id ?? 'None'}',
                                    style: MyTypography.Regular.copyWith(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.7),
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    'Artisan data: ${snapshot.data ?? 'Loading...'}',
                                    style: MyTypography.Regular.copyWith(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.7),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                      ],
                    ),
                  );
                }

                return ListView.separated(
                  padding: const EdgeInsets.all(16),
                  itemCount: conversations.length,
                  separatorBuilder:
                      (context, index) => const SizedBox(height: 12),
                  itemBuilder: (context, index) {
                    final conversation = conversations[index];
                    // final isClient = false; // This is for artisan view

                    // For artisans, we show the client's information
                    final lastMessage = conversation.lastMessage;

                    return MessageCard(
                      senderName: conversation.client?.name ?? 'Unknown',
                      message: lastMessage?.content ?? 'No messages yet',
                      time:
                          lastMessage != null
                              ? _formatMessageTime(lastMessage.createdAt)
                              : '',
                      avatarUrl:
                          conversation.client?.avatar ??
                          'assets/images/profile_pic.png',
                      isUnread:
                          lastMessage != null ? !lastMessage.isRead : false,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ChatScreen(
                                  conversationId: conversation.id,
                                  otherPartyName:
                                      conversation.client?.name ?? 'Unknown',
                                  otherPartyAvatar: conversation.client?.avatar,
                                ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error:
                  (error, stack) => Center(
                    child: Text(
                      'Error loading conversations: $error',
                      style: MyTypography.Medium.copyWith(
                        color: customColors.errorColor,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatMessageTime(String timestamp) {
    final messageTime = DateTime.parse(timestamp).toLocal();
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    if (difference.inDays == 0) {
      // Today, show time
      return DateFormat('hh:mm a').format(messageTime);
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // Within a week
      return DateFormat('EEEE').format(messageTime); // Day name
    } else {
      // Older
      return DateFormat('MMM d').format(messageTime); // Month and day
    }
  }
}
