import 'dart:async';

import 'package:build_mate/presentation/components/loader/lottie_loader.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/search_shop_textfield.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/shop_details_sheet.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/shops_list_bottom_sheet.dart';
import 'package:build_mate/presentation/state/shops_state.dart';
import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/presentation/widgets/animated_marker.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:build_mate/utils/map_style_helper.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ShopTabView extends ConsumerStatefulWidget {
  const ShopTabView({super.key});
  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ShopTabViewState();
}

class _ShopTabViewState extends ConsumerState<ShopTabView> {
  final Completer<GoogleMapController> _completer = Completer();
  GoogleMapController? mapController;
  bool isMapDragging = false;
  Timer? _dragEndTimer;
  String? _mapStyle;

  // Initial camera position (example coordinates)

  @override
  void initState() {
    super.initState();
    _loadMapStyle();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   ref.read(shopsViewModelProvider.notifier).loadShops();
    // });
  }

  /// Load map style based on current theme
  Future<void> _loadMapStyle() async {
    final themeMode = ref.read(themeModeProvider);
    final mapStyle = await MapStyleHelper.getMapStyleForTheme(themeMode);
    if (mounted) {
      setState(() {
        _mapStyle = mapStyle;
      });
    }
  }

  @override
  void dispose() {
    _dragEndTimer?.cancel();
    mapController?.dispose();
    super.dispose();
  }

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  /// Update map style when theme changes
  Future<void> updateMapStyle() async {
    await _loadMapStyle();
  }

  LatLng? _parseCoordinates(String? pointCoordinates) {
    if (pointCoordinates == null) return null;

    try {
      final cleanCoords = pointCoordinates
          .replaceAll('(', '')
          .replaceAll(')', '')
          .split(',');

      if (cleanCoords.length != 2) return null;

      final lat = double.tryParse(cleanCoords[1].trim());
      final lng = double.tryParse(cleanCoords[0].trim());

      if (lat == null || lng == null) return null;
      return LatLng(lat, lng);
    } catch (e) {
      debugPrint('Error parsing coordinates: $e');
      return null;
    }
  }

  Future<void> animateToNewLocation(
    LatLng newLocation,
    GoogleMapController controller,
  ) async {
    // New location

    final update = CameraUpdate.newCameraPosition(
      CameraPosition(
        target: newLocation,
        zoom: 13.0, // New zoom level
      ),
    );

    await controller.animateCamera(update);
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopsViewModelProvider);
    final viewModel = ref.watch(shopsViewModelProvider.notifier);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Listen for theme changes and update map style
    ref.listen<ThemeMode>(themeModeProvider, (previous, next) {
      if (previous != next) {
        updateMapStyle();
      }
    });

    ref.listen<ShopsState>(shopsViewModelProvider, (previous, next) async {
      if (previous?.currentLocation != next.currentLocation) {
        // Handle the location change
        debugPrint('Location changed: ${next.currentLocation}');
        // updateCameraPosition(next.currentLocation ?? const LatLng(0, 0));
        animateToNewLocation(next.currentLocation, mapController!);
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Stack(
        children: [
          GoogleMap(
            mapType: MapType.normal,
            style: _mapStyle, // Apply theme-aware map style
            initialCameraPosition:
                state.initialPosition ??
                const CameraPosition(
                  target: LatLng(-17.814729046095547, 31.03729227461175),
                  zoom: 8,
                ),
            onMapCreated: onMapCreated,
            liteModeEnabled: false,
            // cloudMapId: '52fb0133a5d03547ea6ed7cc',

            // Enable all visual enhancements
            buildingsEnabled: true,
            trafficEnabled: false,

            // Gesture settings that prevent quality degradation
            tiltGesturesEnabled: false, // Prevents 3D tilt that can blur map
            rotateGesturesEnabled: true,
            scrollGesturesEnabled: true,
            zoomGesturesEnabled: true,

            // UI settings
            compassEnabled: true,

            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            markers: state.markers,
            onTap: (LatLng position) {
              // viewModel.setShowdetailsVisibility(false);
            },
            onCameraMoveStarted: () {
              isMapDragging = true;
              if (state.ishopDetailsSheetVisible) {
                viewModel.setShowdetailsVisibility(false);
              }
            },
            onCameraIdle: () {
              isMapDragging = false;
              _dragEndTimer?.cancel();
              _dragEndTimer = Timer(const Duration(milliseconds: 150), () {
                if (state.selectedShop != null) {
                  viewModel.setShowdetailsVisibility(true);
                }
              });
            },
          ),
          if (state.isLoading) const LottieLoader(),
          if (state.error != null)
            Center(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      themeMode == ThemeMode.light
                          ? Colors.red[50]
                          : Colors.red[900]?.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.red[700]
                              : Colors.red[300],
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Error loading shops',
                      style: MyTypography.SemiBold.copyWith(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.red[700]
                                : Colors.red[300],
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      state.error!,
                      style: MyTypography.Regular.copyWith(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.red[600]
                                : Colors.red[400],
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Top App Bar (back to original position)
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: const SearchShopTextField(),
          ),

          // Bottom Sheet
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutQuart, // Better curve for bottom sheet
            bottom: state.ishopDetailsSheetVisible ? 0 : -200,
            left: 0,
            right: 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: state.ishopDetailsSheetVisible ? 1.0 : 0.0,
              child: ShopDetailsSheet(() {
                viewModel.setShowdetailsVisibility(false);
                viewModel.selectShop(null);
              }),
            ),
          ),

          // Replace the animated ripple markers section with this
          if (!state.isLoading)
            ...state.nearbyShops.map((shop) {
              final position = _parseCoordinates(shop.pointCoordinates);
              if (position == null) return const SizedBox();

              return AnimatedMapMarker(
                position: position,
                mapController: mapController,
                onTap: () {
                  viewModel.selectShop(shop);
                  viewModel.setShowdetailsVisibility(true);
                },
              );
            }),
        ],
      ),
      floatingActionButton:
          state.ishopDetailsSheetVisible
              ? null
              : FloatingActionButton.extended(
                backgroundColor: Theme.of(context).cardColor,
                foregroundColor: customColors.textPrimaryColor,
                elevation: themeMode == ThemeMode.light ? 6 : 8,
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder:
                        (context) => FractionallySizedBox(
                          heightFactor: 0.9,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).cardColor,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                              border:
                                  themeMode == ThemeMode.dark
                                      ? Border.all(
                                        color: customColors.textPrimaryColor
                                            .withValues(alpha: 0.1),
                                        width: 1,
                                      )
                                      : null,
                            ),
                            child: const ShopsListBottomSheet(),
                          ),
                        ),
                  );
                },
                label: Text(
                  'List',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: customColors.textPrimaryColor,
                  ),
                ),
                icon: Icon(Icons.list, color: customColors.textPrimaryColor),
              ),
    );
  }
}
