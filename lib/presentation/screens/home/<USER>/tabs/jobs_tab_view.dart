import 'package:build_mate/data/dto/job_details_response.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/state/client_jobs_flow_state.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';

import 'package:build_mate/presentation/components/cards/job_posted_card.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/presentation/view_models/job/client_jobs_view_model.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

class JobsTabView extends ConsumerStatefulWidget {
  const JobsTabView({super.key});

  @override
  ConsumerState<JobsTabView> createState() => _JobsTabViewState();
}

class _JobsTabViewState extends ConsumerState<JobsTabView> {
  // Add a map to track which jobs are being marked as complete
  final Map<int, bool> _markingCompleteJobs = {};

  @override
  Widget build(BuildContext context) {
    final jobsState = ref.watch(clientJobsViewModelProvider);
    final jobsViewModel = ref.read(clientJobsViewModelProvider.notifier);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Theme-aware colors for app bar
    final appBarColor =
        themeMode == ThemeMode.light
            ? darkBlueColor
            : customColors.surfaceVariant;
    final textColor =
        themeMode == ThemeMode.light
            ? Colors.white
            : customColors.textPrimaryColor;

    return Scaffold(
      backgroundColor:
          themeMode == ThemeMode.light
              ? Colors.grey[100]
              : const Color(
                0xFF1A1A1A,
              ), // Dar
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Container(
            color: appBarColor,
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'My Jobs',
                      style: MyTypography.SemiBold.copyWith(
                        fontSize: 24,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Tab selector
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color:
                  themeMode == ThemeMode.light
                      ? Colors.grey[100]
                      : customColors.surfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border:
                  themeMode == ThemeMode.dark
                      ? Border.all(
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.1,
                        ),
                        width: 1,
                      )
                      : null,
            ),
            child: Row(
              children: [
                _buildTab(
                  'New',
                  0,
                  jobsState.selectedTabIndex,
                  jobsViewModel,
                  ref.watch(clientJobsViewModelProvider),
                  customColors,
                  themeMode,
                ),
                _buildTab(
                  'In Progress',
                  1,
                  jobsState.selectedTabIndex,
                  jobsViewModel,
                  ref.watch(clientJobsViewModelProvider),
                  customColors,
                  themeMode,
                ),
                _buildTab(
                  'Completed',
                  2,
                  jobsState.selectedTabIndex,
                  jobsViewModel,
                  ref.watch(clientJobsViewModelProvider),
                  customColors,
                  themeMode,
                ),
              ],
            ),
          ),

          // Job list section
          Expanded(child: _buildJobsList(jobsState, jobsViewModel)),
        ],
      ),
    );
  }

  Widget _buildJobsList(
    ClientJobsFlowState jobsState,
    ClientJobsViewModel jobsViewModel,
  ) {
    final customColors = ref.watch(customColorsProvider);

    if (jobsState.isLoading) {
      return _buildSkeletonLoaders();
    }

    if (jobsState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: customColors.errorColor),
            const SizedBox(height: 16),
            Text(
              'Error loading jobs',
              style: MyTypography.Medium.copyWith(
                color: customColors.errorColor,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => jobsViewModel.fetchJobsPostedByClient(),
              style: ElevatedButton.styleFrom(
                backgroundColor: customColors.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (jobsState.jobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_outline,
              size: 64,
              color: customColors.textPrimaryColor.withValues(alpha: 0.4),
            ),
            const SizedBox(height: 16),
            Text(
              'No jobs found',
              style: MyTypography.Medium.copyWith(
                fontSize: 16,
                color: customColors.textPrimaryColor.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      );
    }

    // Using ListView.builder for better performance
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: jobsState.jobs.length,
      // Optional: Add a prototype item for better performance estimation
      prototypeItem: JobPostedCard(
        title: 'Sample Job',
        badgeText: 'New',
        categories: ['Category'],
        description: 'Sample description',
        datePosted: '1 day ago',
        status: 'New',
        jobNumber: 1,
        onTap: () {},
        serviceDate: '',
      ),
      itemBuilder: (context, index) {
        final job = jobsState.jobs[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: JobPostedCard(
            title: job.service?.name ?? '',
            badgeText: job.jobTags?.length.toString() ?? '',
            categories:
                job.jobTags
                    ?.map((tag) => tag.subCategory?.name ?? '')
                    .toList() ??
                [],
            description: job.jobDescription ?? '',
            datePosted: job.postDate.toString(),
            status: job.status ?? '',
            bidCount: job.bids?.length ?? 0,
            bidderAvatars:
                job.bids
                    ?.map((bid) => bid.artisan?.avatar ?? '')
                    .where((avatar) => avatar.isNotEmpty)
                    .toList() ??
                [],
            jobNumber: index + 1,
            onTap: () {
              // Navigate to the artisan bids screen
              jobsViewModel.selectedJob(job);
              job.status == 'open'
                  ? context.pushNamed(
                    RouteConstants.ARTISAN_BIDS_SCREEN,
                    extra: job, // Pass the job object to the new screen
                  )
                  : context.pushNamed(
                    RouteConstants.VIEW_ARTISAN_PROFILE_SCREEN,
                    extra: job, // Pass the job object to the new screen
                  );
            },
            serviceDate: job.serviceDate ?? '',
            // Add these new properties for in-progress jobs
            onMarkAsCompletePressed:
                job.status?.toLowerCase() == 'in_progress'
                    ? () => _markJobAsComplete(job, jobsViewModel)
                    : null,
            isMarkingComplete: _markingCompleteJobs[job.id] ?? false,
          ),
        );
      },
    );
  }

  Widget _buildTab(
    String text,
    int index,
    int selectedTabIndex,
    ClientJobsViewModel viewModel,
    ClientJobsFlowState state,
    CustomColors customColors,
    ThemeMode themeMode,
  ) {
    final isSelected = selectedTabIndex == index;

    // Get the job count for this tab
    int jobCount = 0;
    switch (index) {
      case 0:
        jobCount = state.openJobsCount;
        break;
      case 1:
        jobCount = state.inProgressJobsCount;
        break;
      case 2:
        jobCount = state.completedJobsCount;
        break;
    }

    // Create the tab text with count if available
    String tabText = text;
    if (jobCount > 0) {
      tabText = '$text ($jobCount)';
    }

    return Expanded(
      child: GestureDetector(
        onTap: () {
          viewModel.setSelectedTab(index);
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? (themeMode == ThemeMode.light
                        ? Colors.white
                        : customColors.primaryContainer.withValues(alpha: 0.3))
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow:
                isSelected && themeMode == ThemeMode.light
                    ? [
                      BoxShadow(
                        color: orangeColor.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : null,
            border:
                isSelected && themeMode == ThemeMode.dark
                    ? Border.all(
                      color: orangeColor.withValues(alpha: 0.3),
                      width: 1,
                    )
                    : null,
          ),
          child: AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            style:
                isSelected
                    ? MyTypography.SemiBold.copyWith(
                      color: orangeColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    )
                    : MyTypography.Medium.copyWith(
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[600]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.7,
                              ),
                      fontSize: 14,
                    ),
            child: Text(
              tabText,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  // Add a new method to build skeleton loaders
  Widget _buildSkeletonLoaders() {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 5, // Show 5 skeleton items
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 180,
            decoration: BoxDecoration(
              color:
                  themeMode == ThemeMode.light
                      ? Colors.white
                      : customColors.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(
                    context,
                  ).shadowColor.withAlpha((0.1 * 255).round()),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Shimmer.fromColors(
              baseColor:
                  themeMode == ThemeMode.light
                      ? Colors.grey[300]!
                      : customColors.textPrimaryColor.withValues(alpha: 0.1),
              highlightColor:
                  themeMode == ThemeMode.light
                      ? Colors.grey[100]!
                      : customColors.textPrimaryColor.withValues(alpha: 0.05),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and badge
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 150,
                          height: 20,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Container(
                          width: 40,
                          height: 20,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Categories
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 16,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 60,
                          height: 16,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Description
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.surfaceVariant.withValues(
                                  alpha: 0.3,
                                ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 6),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      height: 16,
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.surfaceVariant.withValues(
                                  alpha: 0.3,
                                ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Footer
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 100,
                          height: 16,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Container(
                          width: 80,
                          height: 16,
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.3,
                                    ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Add this method to handle marking a job as complete
  Future<void> _markJobAsComplete(
    JobDetailsResponse job,
    ClientJobsViewModel jobsViewModel,
  ) async {
    // Set the job as marking complete
    setState(() {
      _markingCompleteJobs[job.id!] = true;
    });

    try {
      // Call the view model method to mark the job as complete
      final result = await jobsViewModel.markJobAsComplete(job.id!);

      if (result['success']) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Job marked as complete successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to mark job as complete: ${result['error']}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error marking job as complete: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      // Reset the marking complete state
      if (mounted) {
        setState(() {
          _markingCompleteJobs[job.id!] = false;
        });
      }
    }
  }
}
