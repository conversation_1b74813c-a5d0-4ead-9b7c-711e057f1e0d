import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/presentation/view_models/artisan/artisan_settings_tab_view_model.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SettingsTabView extends ConsumerWidget {
  const SettingsTabView({super.key});

  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
    required WidgetRef ref,
  }) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title.toUpperCase(),
            style: MyTypography.SemiBold.copyWith(
              color:
                  themeMode == ThemeMode.light
                      ? Colors.grey[600]
                      : customColors.textPrimaryColor.withValues(alpha: 0.6),
              fontSize: 13,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color:
                themeMode == ThemeMode.light
                    ? Colors.white
                    : customColors.surfaceVariant.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(12),
            border:
                themeMode == ThemeMode.dark
                    ? Border.all(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.1,
                      ),
                      width: 1,
                    )
                    : null,
            boxShadow:
                themeMode == ThemeMode.light
                    ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : null,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required WidgetRef ref,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return ListTile(
      leading: Icon(
        icon,
        color:
            iconColor ??
            (themeMode == ThemeMode.light
                ? Colors.grey[700]
                : customColors.textPrimaryColor.withValues(alpha: 0.7)),
        size: 22,
      ),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color:
              themeMode == ThemeMode.light
                  ? Colors.grey[800]
                  : customColors.textPrimaryColor,
        ),
      ),
      trailing:
          trailing ??
          Icon(
            Icons.chevron_right,
            color:
                themeMode == ThemeMode.light
                    ? Colors.grey[400]
                    : customColors.textPrimaryColor.withValues(alpha: 0.4),
            size: 20,
          ),
      onTap: onTap,
    );
  }

  String _getThemeModeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(
    BuildContext context,
    ThemeModeNotifier themeModeNotifier,
    ThemeMode currentTheme,
    WidgetRef ref,
  ) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor:
              themeMode == ThemeMode.light
                  ? Colors.white
                  : customColors.surfaceVariant,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side:
                themeMode == ThemeMode.dark
                    ? BorderSide(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.1,
                      ),
                      width: 1,
                    )
                    : BorderSide.none,
          ),
          title: Text(
            'Choose Theme',
            style: MyTypography.SemiBold.copyWith(
              fontSize: 18,
              color: customColors.textPrimaryColor,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildThemeOption(
                context,
                'Light',
                Icons.light_mode,
                ThemeMode.light,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
              _buildThemeOption(
                context,
                'Dark',
                Icons.dark_mode,
                ThemeMode.dark,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
              _buildThemeOption(
                context,
                'System',
                Icons.settings_system_daydream,
                ThemeMode.system,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    IconData icon,
    ThemeMode themeMode,
    ThemeMode currentTheme,
    ThemeModeNotifier themeModeNotifier,
    WidgetRef ref,
  ) {
    final customColors = ref.watch(customColorsProvider);
    final currentThemeMode = ref.watch(themeModeProvider);
    final isSelected = currentTheme == themeMode;

    return ListTile(
      leading: Icon(
        icon,
        color:
            isSelected
                ? orangeColor
                : (currentThemeMode == ThemeMode.light
                    ? Colors.grey[600]
                    : customColors.textPrimaryColor.withValues(alpha: 0.6)),
      ),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color:
              isSelected
                  ? orangeColor
                  : (currentThemeMode == ThemeMode.light
                      ? Colors.grey[800]
                      : customColors.textPrimaryColor),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? Icon(Icons.check, color: orangeColor, size: 20) : null,
      onTap: () {
        themeModeNotifier.setThemeMode(themeMode);
        Navigator.of(context).pop();
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewModel = ref.watch(artisanSettingsTabViewModelProvider.notifier);
    final state = ref.watch(artisanSettingsTabViewModelProvider);
    final themeMode = ref.watch(themeModeProvider);
    final themeModeNotifier = ref.read(themeModeProvider.notifier);
    final customColors = ref.watch(customColorsProvider);

    return Scaffold(
      backgroundColor:
          themeMode == ThemeMode.light
              ? Colors.grey[100]
              : const Color(
                0xFF1A1A1A,
              ), // Darker than app bar for better contrast
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor:
                themeMode == ThemeMode.light
                    ? darkBlueColor
                    : customColors.surfaceVariant,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color:
                    themeMode == ThemeMode.light
                        ? Colors.white
                        : customColors.textPrimaryColor,
              ),
              onPressed: () => context.pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient:
                      themeMode == ThemeMode.light
                          ? LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              darkBlueColor,
                              darkBlueColor.withValues(alpha: 0.9),
                            ],
                          )
                          : LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              customColors.surfaceVariant,
                              customColors.surfaceVariant.withValues(
                                alpha: 0.9,
                              ),
                            ],
                          ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40), // Space for status bar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.white
                                    : customColors.textPrimaryColor.withValues(
                                      alpha: 0.3,
                                    ),
                            width: 2,
                          ),
                          color:
                              themeMode == ThemeMode.light
                                  ? Colors.grey[300]
                                  : customColors.surfaceVariant.withValues(
                                    alpha: 0.5,
                                  ),
                        ),
                        child:
                            state.isLoading
                                ? const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                )
                                : ClipOval(
                                  child:
                                      state.profileUrl.isNotEmpty
                                          ? Image.network(
                                            state.profileUrl,
                                            width: 80,
                                            height: 80,
                                            fit: BoxFit.cover,
                                            errorBuilder: (
                                              context,
                                              error,
                                              stackTrace,
                                            ) {
                                              return Image.asset(
                                                'assets/images/profile_pic.png',
                                                width: 80,
                                                height: 80,
                                                fit: BoxFit.cover,
                                              );
                                            },
                                          )
                                          : Image.asset(
                                            'assets/images/profile_pic.png',
                                            width: 80,
                                            height: 80,
                                            fit: BoxFit.cover,
                                          ),
                                ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        state.isLoading
                            ? 'Loading...'
                            : (state.username.isNotEmpty
                                ? state.username
                                : 'Artisan'),
                        style: MyTypography.SemiBold.copyWith(
                          fontSize: 20,
                          color:
                              themeMode == ThemeMode.light
                                  ? Colors.white
                                  : customColors.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color:
                              state.isAvailable
                                  ? (themeMode == ThemeMode.light
                                      ? Colors.green.withValues(alpha: 0.2)
                                      : Colors.green.withValues(alpha: 0.3))
                                  : (themeMode == ThemeMode.light
                                      ? Colors.red.withValues(alpha: 0.2)
                                      : Colors.red.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color:
                                state.isAvailable
                                    ? Colors.green.withValues(alpha: 0.5)
                                    : Colors.red.withValues(alpha: 0.5),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          state.isAvailable ? 'Available' : 'Unavailable',
                          style: MyTypography.Medium.copyWith(
                            fontSize: 12,
                            color:
                                state.isAvailable
                                    ? Colors.green[700]
                                    : Colors.red[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 24),
                _buildSettingsGroup(
                  title: 'Account',
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.person_off_outlined,
                      title: 'Availability',
                      ref: ref,
                      trailing: Switch(
                        value: state.isAvailable,
                        onChanged: (value) {
                          viewModel.updateAvailability(value);
                        },
                        activeColor: Colors.green,
                      ),
                    ),
                    Divider(
                      height: 1,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.account_circle_outlined,
                      title: 'Account details',
                      ref: ref,
                      onTap: () {},
                    ),
                    Divider(
                      height: 1,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.attach_money,
                      title: 'Subscriptions',
                      ref: ref,
                      onTap: () {},
                    ),
                  ],
                ),
                _buildSettingsGroup(
                  title: 'PREFERENCES',
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.language,
                      title: 'Language',
                      ref: ref,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'English',
                            style: MyTypography.Regular.copyWith(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.grey[600]
                                      : customColors.textPrimaryColor
                                          .withValues(alpha: 0.6),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[400]
                                    : customColors.textPrimaryColor.withValues(
                                      alpha: 0.4,
                                    ),
                            size: 20,
                          ),
                        ],
                      ),
                      onTap: () {},
                    ),
                    Divider(
                      height: 1,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.palette_outlined,
                      title: 'Theme',
                      ref: ref,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getThemeModeText(themeMode),
                            style: MyTypography.Regular.copyWith(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.grey[600]
                                      : customColors.textPrimaryColor
                                          .withValues(alpha: 0.6),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[400]
                                    : customColors.textPrimaryColor.withValues(
                                      alpha: 0.4,
                                    ),
                            size: 20,
                          ),
                        ],
                      ),
                      onTap:
                          () => _showThemeDialog(
                            context,
                            themeModeNotifier,
                            themeMode,
                            ref,
                          ),
                    ),
                    Divider(
                      height: 1,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.notifications,
                      title: 'Notifications',
                      ref: ref,
                      trailing: Switch(
                        value: state.notifications,
                        onChanged: (value) {
                          viewModel.updateNotifications(value);
                        },
                        activeColor: Colors.blue,
                      ),
                    ),
                    Divider(
                      height: 1,
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.textPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.play_circle_outline,
                      title: 'Play in Background',
                      ref: ref,
                      trailing: Switch(
                        value: state.playInBackground,
                        onChanged: (value) {
                          viewModel.updatePlayInBackground(value);
                        },
                        activeColor: Colors.blue,
                      ),
                    ),
                  ],
                ),
                _buildSettingsGroup(
                  title: 'ACCOUNT ACTIONS',
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.logout,
                      title: 'Logout',
                      ref: ref,
                      iconColor: Colors.red,
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Colors.red,
                        size: 20,
                      ),
                      onTap: () async {
                        // Show confirmation dialog
                        final shouldLogout = await showDialog<bool>(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                backgroundColor:
                                    themeMode == ThemeMode.light
                                        ? Colors.white
                                        : customColors.surfaceVariant,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  side:
                                      themeMode == ThemeMode.dark
                                          ? BorderSide(
                                            color: customColors.textPrimaryColor
                                                .withValues(alpha: 0.1),
                                            width: 1,
                                          )
                                          : BorderSide.none,
                                ),
                                title: Text(
                                  'Logout',
                                  style: MyTypography.SemiBold.copyWith(
                                    color: customColors.textPrimaryColor,
                                    fontSize: 18,
                                  ),
                                ),
                                content: Text(
                                  'Are you sure you want to logout?',
                                  style: MyTypography.Regular.copyWith(
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.grey[700]
                                            : customColors.textPrimaryColor
                                                .withValues(alpha: 0.8),
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(false),
                                    child: Text(
                                      'Cancel',
                                      style: MyTypography.Medium.copyWith(
                                        color:
                                            themeMode == ThemeMode.light
                                                ? Colors.grey[600]
                                                : customColors.textPrimaryColor
                                                    .withValues(alpha: 0.7),
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(true),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.red,
                                    ),
                                    child: Text(
                                      'Logout',
                                      style: MyTypography.Medium.copyWith(
                                        color: Colors.red,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                        );

                        if (shouldLogout == true) {
                          await viewModel.logout();
                          if (context.mounted) {
                            context.goNamed(
                              RouteConstants.ROLE_SELECTION_SCREEN,
                            );
                          }
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
