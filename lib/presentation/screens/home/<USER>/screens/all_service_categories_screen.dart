import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/job/post_job_view_model.dart';
import 'package:build_mate/presentation/view_models/user/all_services_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

class AllServiceCategoriesScreen extends ConsumerStatefulWidget {
  const AllServiceCategoriesScreen({super.key});

  @override
  ConsumerState<AllServiceCategoriesScreen> createState() =>
      _AllServiceCategoriesScreenState();
}

class _AllServiceCategoriesScreenState
    extends ConsumerState<AllServiceCategoriesScreen> {
  Future<void> _onServiceTap(int serviceId, String serviceName) async {
    if (kDebugMode) {
      print('Service tapped: $serviceName (ID: $serviceId)');
    }

    // Update both view models with the selected service
    final allServicesViewModel = ref.read(
      allServicesViewModelProvider.notifier,
    );
    allServicesViewModel.setSelectedService(serviceId, serviceName);

    final postJobViewModel = ref.read(postJobViewModelProvider.notifier);
    // Wait for the service ID to be set and subcategories to be loaded
    postJobViewModel.setSelectedServiceId(serviceId, serviceName);

    // Force refresh with the selected service to ensure subcategories are loaded
    postJobViewModel.refreshWithSelectedService();

    if (kDebugMode) {
      print('Service selection complete, navigating back to post job screen');
    }

    // Navigate back to the post job screen
    context.pushNamed(RouteConstants.POST_JOB_SCREEN);
  }

  Widget _buildSkeletonLoader(CustomColors customColors, bool isDarkMode) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.85,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: 21, // Show 21 skeleton items
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          highlightColor: isDarkMode ? Colors.grey[600]! : Colors.grey[100]!,
          period: const Duration(milliseconds: 1500),
          child: Container(
            decoration: BoxDecoration(
              color: isDarkMode ? customColors.surfaceVariant : Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.grey[600] : Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 80,
                  height: 12,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.grey[600] : Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 60,
                  height: 12,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.grey[600] : Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(allServicesViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    // Determine if we're in dark mode
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    // Theme-aware colors
    final appBarColor =
        isDarkMode ? customColors.surfaceVariant : darkBlueColor;
    final textColor = isDarkMode ? customColors.textPrimaryColor : Colors.white;
    final noDataTextColor =
        isDarkMode ? customColors.textPrimaryColor : Colors.black87;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: appBarColor,
        elevation: isDarkMode ? 4 : 0,
        shadowColor: isDarkMode ? Colors.black.withValues(alpha: 0.3) : null,
        title: Text(
          'Service Categories',
          style: MyTypography.SemiBold.copyWith(color: textColor, fontSize: 20),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: textColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body:
          state.isLoading
              ? _buildSkeletonLoader(customColors, isDarkMode)
              : state.services == null || state.services!.isEmpty
              ? Center(
                child: Text(
                  'No services available',
                  style: TextStyle(color: noDataTextColor),
                ),
              )
              : GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: state.services!.length,
                itemBuilder: (context, index) {
                  final service = state.services![index];
                  // Default icon if none is provided
                  final String iconPath =
                      service.icon ?? 'assets/svg/default_service.svg';

                  // Theme-aware colors for service items
                  final containerColor =
                      isDarkMode
                          ? customColors.surfaceVariant.withValues(alpha: 0.7)
                          : const Color.fromARGB(255, 241, 241, 241);
                  final iconContainerColor =
                      isDarkMode
                          ? customColors.surfaceVariant.withValues(alpha: 0.9)
                          : Colors.white;
                  final serviceTextColor =
                      isDarkMode
                          ? customColors.textPrimaryColor
                          : Colors.black87;
                  final shadowColor =
                      isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.05);

                  return GestureDetector(
                    onTap: () {
                      _onServiceTap(
                        service.id ?? 0,
                        service.name ?? 'Unknown Service',
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: containerColor,
                        borderRadius: BorderRadius.circular(12),
                        border:
                            isDarkMode
                                ? Border.all(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.1),
                                  width: 0.5,
                                )
                                : null,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: iconContainerColor,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: shadowColor,
                                  blurRadius: 8,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child:
                                iconPath.endsWith('.svg')
                                    ? SvgPicture.asset(
                                      iconPath,
                                      colorFilter: const ColorFilter.mode(
                                        Colors.red,
                                        BlendMode.srcIn,
                                      ),
                                    )
                                    : Image.asset(iconPath, color: Colors.red),
                          ),
                          const SizedBox(height: 8),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Text(
                              service.name ?? 'Unknown Service',
                              style: MyTypography.Medium.copyWith(
                                fontSize: 14,
                                color: serviceTextColor,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
    );
  }
}
