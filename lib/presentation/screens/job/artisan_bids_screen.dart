import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/job/client_jobs_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ArtisanBidsScreen extends ConsumerStatefulWidget {
  const ArtisanBidsScreen({super.key});

  @override
  ConsumerState<ArtisanBidsScreen> createState() => _ArtisanBidsScreenState();
}

class _ArtisanBidsScreenState extends ConsumerState<ArtisanBidsScreen> {
  @override
  void initState() {
    super.initState();
    // You can add initialization logic here if needed
    // For example, fetch bids for the selected job
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final jobsViewModel = ref.read(clientJobsViewModelProvider.notifier);
      final state = ref.read(clientJobsViewModelProvider);
      if (state.selectedJob != null) {
        jobsViewModel.fetchBidsForJob();
      }
    });
  }

  void _showRejectConfirmationDialog(BuildContext context, int bidId) {
    // Store a local reference to the context
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    bool isRejecting = false; // Track rejection state

    showDialog(
      context: context,
      builder:
          (dialogContext) => StatefulBuilder(
            // Use StatefulBuilder to update dialog state
            builder:
                (context, setDialogState) => AlertDialog(
                  title: Text(
                    'Reject Bid',
                    style: MyTypography.SemiBold.copyWith(fontSize: 18),
                  ),
                  content: Text(
                    'Are you sure you want to reject this artisan\'s bid? This action cannot be undone.',
                    style: MyTypography.Regular.copyWith(
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                      fontSize: 14,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed:
                          isRejecting
                              ? null
                              : () => Navigator.pop(dialogContext),
                      child: Text(
                        'Cancel',
                        style: MyTypography.Medium.copyWith(
                          color:
                              isRejecting
                                  ? Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withValues(alpha: 0.4)
                                  : Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed:
                          isRejecting
                              ? null
                              : () async {
                                // Update dialog state to show loading
                                setDialogState(() {
                                  isRejecting = true;
                                });

                                final jobsViewModel = ref.read(
                                  clientJobsViewModelProvider.notifier,
                                );

                                try {
                                  // Reject the bid
                                  final response = await jobsViewModel
                                      .rejectBid(bidId: bidId);

                                  if (context.mounted) {
                                    // Close dialog first
                                    context.pop();
                                  }

                                  if (response.success == true) {
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Bid rejected successfully',
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                    // Refresh the bids list
                                    jobsViewModel.fetchBidsForJob();
                                  } else {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Failed to reject bid: ${response.errorMessage}',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  // Close dialog if error occurs
                                  if (context.mounted) {
                                    context.pop();
                                  }

                                  scaffoldMessenger.showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Error rejecting bid: ${e.toString()}',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: Colors.red.withAlpha(
                          (0.5 * 255).round(),
                        ),
                        disabledForegroundColor: Colors.white70,
                      ),
                      child:
                          isRejecting
                              ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Rejecting...'),
                                ],
                              )
                              : const Text('Reject'),
                    ),
                  ],
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final jobsViewModel = ref.read(clientJobsViewModelProvider.notifier);
    final state = ref.watch(clientJobsViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Add a map to track loading state for each bid
    final Map<int, bool> acceptingBids = {};

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Artisan Bids',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).appBarTheme.iconTheme?.color,
          ),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Job summary card
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              gradient:
                  themeMode == ThemeMode.light
                      ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Colors.white, Colors.grey[50]!],
                      )
                      : null,
              color:
                  themeMode == ThemeMode.dark
                      ? Theme.of(context).cardColor
                      : null,
              borderRadius: BorderRadius.circular(16),
              boxShadow:
                  themeMode == ThemeMode.light
                      ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.04),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ]
                      : null,
              border: Border.all(
                color:
                    themeMode == ThemeMode.light
                        ? Colors.grey[200]!
                        : customColors.textPrimaryColor.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with job title and status indicator
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              themeMode == ThemeMode.light
                                  ? darkBlueColor.withValues(alpha: 0.1)
                                  : customColors.primaryContainer.withValues(
                                    alpha: 0.3,
                                  ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.work_outline,
                          color:
                              themeMode == ThemeMode.light
                                  ? darkBlueColor
                                  : customColors.textPrimaryColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              state.selectedJob?.service?.name ?? 'Job Title',
                              style: MyTypography.SemiBold.copyWith(
                                fontSize: 18,
                                color: customColors.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.green[100]
                                        : Colors.green[700]?.withValues(
                                          alpha: 0.3,
                                        ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'Active',
                                style: MyTypography.Medium.copyWith(
                                  fontSize: 11,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.green[700]
                                          : Colors.green[300],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Job description
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.grey[50]
                              : customColors.surfaceVariant.withValues(
                                alpha: 0.3,
                              ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[200]!
                                : customColors.textPrimaryColor.withValues(
                                  alpha: 0.2,
                                ),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      state.selectedJob?.jobDescription ??
                          'No description available',
                      style: MyTypography.Regular.copyWith(
                        fontSize: 14,
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.7,
                        ),
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Stats row
                  Row(
                    children: [
                      // Bids count
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: orangeColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: orangeColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 16,
                                color: orangeColor,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${state.selectedJob?.bids?.length ?? 0} Bids',
                                style: MyTypography.SemiBold.copyWith(
                                  fontSize: 13,
                                  color: orangeColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Budget (if available)
                      if (state.selectedJob?.budget != null)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.green[50]
                                      : Colors.green[700]?.withValues(
                                        alpha: 0.2,
                                      ),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.green[200]!
                                        : Colors.green[400]!.withValues(
                                          alpha: 0.3,
                                        ),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.attach_money,
                                  size: 16,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.green[700]
                                          : Colors.green[300],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '\$${state.selectedJob?.budget ?? 'N/A'}',
                                  style: MyTypography.SemiBold.copyWith(
                                    fontSize: 13,
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.green[700]
                                            : Colors.green[300],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Artisan bids list header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'Artisans who responded',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 16,
                color: customColors.textPrimaryColor,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Artisan bids list
          Expanded(
            child:
                state.selectedJob?.bids == null ||
                        state.selectedJob!.bids!.isEmpty
                    ? Center(
                      child: Text(
                        'No bids yet',
                        style: MyTypography.Medium.copyWith(
                          fontSize: 16,
                          color: customColors.textPrimaryColor.withValues(
                            alpha: 0.6,
                          ),
                        ),
                      ),
                    )
                    : StatefulBuilder(
                      builder: (context, setState) {
                        return ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: state.bids.length,
                          itemBuilder: (context, index) {
                            final bid = state.bids[index];
                            final bidId = bid.id ?? 0;
                            final isAccepting = acceptingBids[bidId] ?? false;

                            return Container(
                              width: double.infinity,
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow:
                                    themeMode == ThemeMode.light
                                        ? [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.06,
                                            ),
                                            blurRadius: 10,
                                            offset: const Offset(0, 3),
                                          ),
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.03,
                                            ),
                                            blurRadius: 4,
                                            offset: const Offset(0, 1),
                                          ),
                                        ]
                                        : null,
                                border: Border.all(
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[200]!
                                          : customColors.textPrimaryColor
                                              .withValues(alpha: 0.1),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Artisan info row
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color:
                                            themeMode == ThemeMode.light
                                                ? Colors.grey[50]
                                                : customColors.surfaceVariant
                                                    .withValues(alpha: 0.3),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color:
                                              themeMode == ThemeMode.light
                                                  ? Colors.grey[200]!
                                                  : customColors
                                                      .textPrimaryColor
                                                      .withValues(alpha: 0.2),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          // Enhanced artisan profile picture
                                          Container(
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: orangeColor.withValues(
                                                  alpha: 0.3,
                                                ),
                                                width: 2,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withValues(alpha: 0.1),
                                                  blurRadius: 8,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: CircleAvatar(
                                              radius: 28,
                                              backgroundColor:
                                                  themeMode == ThemeMode.light
                                                      ? Colors.grey[200]
                                                      : customColors
                                                          .surfaceVariant,
                                              backgroundImage:
                                                  bid.artisan?.avatarUrl !=
                                                              null &&
                                                          bid
                                                              .artisan!
                                                              .avatarUrl!
                                                              .isNotEmpty
                                                      ? bid.artisan!.avatarUrl!
                                                              .startsWith(
                                                                'http',
                                                              )
                                                          ? NetworkImage(
                                                                bid
                                                                        .artisan
                                                                        ?.avatarUrl ??
                                                                    '',
                                                              )
                                                              as ImageProvider
                                                          : AssetImage(
                                                            bid
                                                                    .artisan
                                                                    ?.avatarUrl ??
                                                                '',
                                                          )
                                                      : null,
                                              child:
                                                  bid.artisan?.avatarUrl ==
                                                              null ||
                                                          bid
                                                              .artisan!
                                                              .avatarUrl!
                                                              .isEmpty
                                                      ? Icon(
                                                        Icons.person,
                                                        size: 28,
                                                        color: Colors.grey[400],
                                                      )
                                                      : null,
                                            ),
                                          ),
                                          const SizedBox(width: 16),

                                          // Enhanced artisan name and rating
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  bid.artisan?.name ??
                                                      'Unknown Artisan',
                                                  style: MyTypography
                                                      .SemiBold.copyWith(
                                                    fontSize: 17,
                                                    color:
                                                        customColors
                                                            .textPrimaryColor,
                                                  ),
                                                ),
                                                const SizedBox(height: 6),
                                                Row(
                                                  children: [
                                                    // Rating stars
                                                    Row(
                                                      children: List.generate(
                                                        5,
                                                        (index) {
                                                          return Icon(
                                                            index < 5
                                                                ? Icons.star
                                                                : Icons
                                                                    .star_border,
                                                            size: 14,
                                                            color:
                                                                Colors
                                                                    .amber[600],
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    const SizedBox(width: 6),
                                                    Text(
                                                      '5.0',
                                                      style: MyTypography
                                                          .SemiBold.copyWith(
                                                        fontSize: 13,
                                                        color: customColors
                                                            .textPrimaryColor
                                                            .withValues(
                                                              alpha: 0.7,
                                                            ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      '(2 reviews)',
                                                      style: MyTypography
                                                          .Regular.copyWith(
                                                        fontSize: 12,
                                                        color: customColors
                                                            .textPrimaryColor
                                                            .withValues(
                                                              alpha: 0.6,
                                                            ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 4),
                                                // Experience badge
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 2,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        themeMode ==
                                                                ThemeMode.light
                                                            ? Colors.blue[100]
                                                            : Colors.blue[700]
                                                                ?.withValues(
                                                                  alpha: 0.3,
                                                                ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    '3+ years experience',
                                                    style: MyTypography
                                                        .Medium.copyWith(
                                                      fontSize: 10,
                                                      color:
                                                          themeMode ==
                                                                  ThemeMode
                                                                      .light
                                                              ? Colors.blue[700]
                                                              : Colors
                                                                  .blue[300],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          // View Profile and Verified badge
                                          Column(
                                            children: [
                                              const SizedBox(height: 8),
                                              // Verified badge
                                              Container(
                                                padding: const EdgeInsets.all(
                                                  6,
                                                ),
                                                decoration: BoxDecoration(
                                                  color:
                                                      themeMode ==
                                                              ThemeMode.light
                                                          ? Colors.green[100]
                                                          : Colors.green[700]
                                                              ?.withValues(
                                                                alpha: 0.3,
                                                              ),
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Icon(
                                                  Icons.verified,
                                                  size: 16,
                                                  color:
                                                      themeMode ==
                                                              ThemeMode.light
                                                          ? Colors.green[700]
                                                          : Colors.green[300],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // Artisan description
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color:
                                            themeMode == ThemeMode.light
                                                ? Colors.white
                                                : customColors.surfaceVariant
                                                    .withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color:
                                              themeMode == ThemeMode.light
                                                  ? Colors.grey[300]!
                                                  : customColors
                                                      .textPrimaryColor
                                                      .withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.description_outlined,
                                                size: 16,
                                                color: customColors
                                                    .textPrimaryColor
                                                    .withValues(alpha: 0.6),
                                              ),
                                              const SizedBox(width: 6),
                                              Text(
                                                'About this artisan',
                                                style: MyTypography
                                                    .SemiBold.copyWith(
                                                  fontSize: 12,
                                                  color: customColors
                                                      .textPrimaryColor
                                                      .withValues(alpha: 0.7),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            bid.artisan?.about ??
                                                'No description available',
                                            style: MyTypography
                                                .Regular.copyWith(
                                              fontSize: 14,
                                              color: customColors
                                                  .textPrimaryColor
                                                  .withValues(alpha: 0.7),
                                              height: 1.4,
                                            ),
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    // Enhanced Accept/Reject buttons
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color:
                                            themeMode == ThemeMode.light
                                                ? Colors.grey[50]
                                                : customColors.surfaceVariant
                                                    .withValues(alpha: 0.3),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color:
                                              themeMode == ThemeMode.light
                                                  ? Colors.grey[200]!
                                                  : customColors
                                                      .textPrimaryColor
                                                      .withValues(alpha: 0.2),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: OutlinedButton.icon(
                                              onPressed:
                                                  isAccepting
                                                      ? null
                                                      : () {
                                                        _showRejectConfirmationDialog(
                                                          context,
                                                          bid.id ?? 0,
                                                        );
                                                      },
                                              icon: Icon(
                                                Icons.close,
                                                size: 18,
                                                color:
                                                    isAccepting
                                                        ? Colors.grey[400]
                                                        : Colors.red[600],
                                              ),
                                              label: Text(
                                                'Reject',
                                                style: MyTypography
                                                    .SemiBold.copyWith(
                                                  fontSize: 14,
                                                ),
                                              ),
                                              style: OutlinedButton.styleFrom(
                                                foregroundColor:
                                                    Colors.red[600],
                                                side: BorderSide(
                                                  color:
                                                      isAccepting
                                                          ? (themeMode ==
                                                                  ThemeMode
                                                                      .light
                                                              ? Colors
                                                                  .grey[300]!
                                                              : customColors
                                                                  .textPrimaryColor
                                                                  .withValues(
                                                                    alpha: 0.3,
                                                                  ))
                                                          : Colors.red[300]!,
                                                  width: 1.5,
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 14,
                                                    ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                backgroundColor:
                                                    Theme.of(context).cardColor,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: ElevatedButton.icon(
                                              onPressed:
                                                  isAccepting
                                                      ? null
                                                      : () async {
                                                        // Update loading state
                                                        setState(() {
                                                          acceptingBids[bidId] =
                                                              true;
                                                        });

                                                        try {
                                                          // Accept bid logic
                                                          final response =
                                                              await jobsViewModel.acceptBid(
                                                                artisanId:
                                                                    bid
                                                                        .artisan
                                                                        ?.id ??
                                                                    0,
                                                                jobId:
                                                                    bid
                                                                        .job
                                                                        ?.id ??
                                                                    0,
                                                                bidId:
                                                                    bid.id ?? 0,
                                                              );

                                                          if (response['success']) {
                                                            if (context
                                                                .mounted) {
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                    'Bid accepted',
                                                                  ),
                                                                  backgroundColor:
                                                                      Colors
                                                                          .green,
                                                                ),
                                                              );
                                                              context.pop();
                                                            }
                                                          } else {
                                                            if (context
                                                                .mounted) {
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                SnackBar(
                                                                  content: Text(
                                                                    'Failed to accept bid: ${response['error']}',
                                                                  ),
                                                                  backgroundColor:
                                                                      Colors
                                                                          .red,
                                                                ),
                                                              );
                                                            }

                                                            // Reset loading state on error
                                                            setState(() {
                                                              acceptingBids[bidId] =
                                                                  false;
                                                            });
                                                          }
                                                        } catch (e) {
                                                          if (context.mounted) {
                                                            ScaffoldMessenger.of(
                                                              context,
                                                            ).showSnackBar(
                                                              SnackBar(
                                                                content: Text(
                                                                  'Error accepting bid: ${e.toString()}',
                                                                ),
                                                                backgroundColor:
                                                                    Colors.red,
                                                              ),
                                                            );
                                                          }

                                                          // Reset loading state on error
                                                          setState(() {
                                                            acceptingBids[bidId] =
                                                                false;
                                                          });
                                                        }
                                                      },
                                              icon: Icon(
                                                Icons.check_circle,
                                                size: 18,
                                                color:
                                                    isAccepting
                                                        ? Colors.white70
                                                        : Colors.white,
                                              ),
                                              label:
                                                  isAccepting
                                                      ? Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          SizedBox(
                                                            width: 16,
                                                            height: 16,
                                                            child: CircularProgressIndicator(
                                                              strokeWidth: 2,
                                                              valueColor:
                                                                  AlwaysStoppedAnimation<
                                                                    Color
                                                                  >(
                                                                    Colors
                                                                        .white,
                                                                  ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 8,
                                                          ),
                                                          Text(
                                                            'Accepting...',
                                                            style: MyTypography
                                                                .SemiBold.copyWith(
                                                              fontSize: 14,
                                                            ),
                                                          ),
                                                        ],
                                                      )
                                                      : Text(
                                                        'Accept',
                                                        style: MyTypography
                                                            .SemiBold.copyWith(
                                                          fontSize: 14,
                                                        ),
                                                      ),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: orangeColor,
                                                foregroundColor: Colors.white,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 14,
                                                    ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                disabledBackgroundColor:
                                                    orangeColor.withValues(
                                                      alpha: 0.7,
                                                    ),
                                                elevation: 2,
                                                shadowColor: orangeColor
                                                    .withValues(alpha: 0.3),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // View Profile button at the bottom
                                    SizedBox(
                                      width: double.infinity,
                                      child: OutlinedButton.icon(
                                        onPressed: () {
                                          ref
                                              .read(
                                                clientJobsViewModelProvider
                                                    .notifier,
                                              )
                                              .setSelectedArtisanId(
                                                bid.id ?? 0,
                                              );

                                          context.pushNamed(
                                            RouteConstants
                                                .ARTISAN_BID_PROFILE_SCREEN,
                                          );
                                        },
                                        icon: Icon(
                                          Icons.account_circle_outlined,
                                          size: 18,
                                          color:
                                              themeMode == ThemeMode.light
                                                  ? darkBlueColor
                                                  : customColors
                                                      .textPrimaryColor,
                                        ),
                                        label: Text(
                                          'View Full Profile & Portfolio',
                                          style: MyTypography.SemiBold.copyWith(
                                            fontSize: 14,
                                            color:
                                                themeMode == ThemeMode.light
                                                    ? darkBlueColor
                                                    : customColors
                                                        .textPrimaryColor,
                                          ),
                                        ),
                                        style: OutlinedButton.styleFrom(
                                          foregroundColor:
                                              themeMode == ThemeMode.light
                                                  ? darkBlueColor
                                                  : customColors
                                                      .textPrimaryColor,
                                          side: BorderSide(
                                            color:
                                                themeMode == ThemeMode.light
                                                    ? darkBlueColor.withValues(
                                                      alpha: 0.3,
                                                    )
                                                    : customColors
                                                        .textPrimaryColor
                                                        .withValues(alpha: 0.3),
                                            width: 1.5,
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 12,
                                            horizontal: 16,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          backgroundColor:
                                              themeMode == ThemeMode.light
                                                  ? darkBlueColor.withValues(
                                                    alpha: 0.05,
                                                  )
                                                  : customColors.surfaceVariant
                                                      .withValues(alpha: 0.2),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}
