import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:build_mate/presentation/components/dismissible_image_viewer.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/view_models/artisan/artisan_profile_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:build_mate/utils/whatsapp_service.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

class ViewArtisanProfileScreen extends ConsumerStatefulWidget {
  const ViewArtisanProfileScreen({super.key});

  @override
  ConsumerState<ViewArtisanProfileScreen> createState() =>
      _ViewArtisanProfileScreenState();
}

class _ViewArtisanProfileScreenState
    extends ConsumerState<ViewArtisanProfileScreen> {
  @override
  Widget build(BuildContext context) {
    ref.read(artisanProfileViewModelProvider.notifier);
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body:
          state.isLoading
              ? _buildSkeletonLoader(context)
              : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cover Image and Profile Section
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        // Cover Image with loading indicator
                        Stack(
                          children: [
                            Image.network(
                              state.artisanProfileDataResponse?.coverPhoto ??
                                  '',
                              height: 200,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  height: 200,
                                  width: double.infinity,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[200]
                                          : customColors.surfaceVariant,
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      value:
                                          loadingProgress.expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        themeMode == ThemeMode.light
                                            ? darkBlueColor
                                            : customColors.textPrimaryColor,
                                      ),
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  height: 200,
                                  width: double.infinity,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.grey[200]
                                          : customColors.surfaceVariant,
                                  child: Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      size: 40,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.5),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),

                        // Contact Actions
                        Positioned(
                          top: 40,
                          right: 16,
                          child: Row(
                            children: [
                              _buildIconButton(Icons.edit_note_outlined),
                            ],
                          ),
                        ),

                        // Profile Image
                        Positioned(
                          bottom: -40,
                          left: 16,
                          child: CircleAvatar(
                            radius: 40,
                            backgroundColor:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[200]
                                    : customColors.surfaceVariant,
                            child:
                                state.artisanProfileDataResponse?.avatar == null
                                    ? Icon(
                                      Icons.person,
                                      size: 40,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.5),
                                    )
                                    : ClipOval(
                                      child: Image.network(
                                        state
                                                .artisanProfileDataResponse
                                                ?.avatar ??
                                            '',
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                        loadingBuilder: (
                                          context,
                                          child,
                                          loadingProgress,
                                        ) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return Center(
                                            child: CircularProgressIndicator(
                                              value:
                                                  loadingProgress
                                                              .expectedTotalBytes !=
                                                          null
                                                      ? loadingProgress
                                                              .cumulativeBytesLoaded /
                                                          loadingProgress
                                                              .expectedTotalBytes!
                                                      : null,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    themeMode == ThemeMode.light
                                                        ? darkBlueColor
                                                        : customColors
                                                            .textPrimaryColor,
                                                  ),
                                              strokeWidth: 2,
                                            ),
                                          );
                                        },
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Icon(
                                            Icons.person,
                                            size: 40,
                                            color: customColors.textPrimaryColor
                                                .withValues(alpha: 0.5),
                                          );
                                        },
                                      ),
                                    ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 48),

                    // Profile Info
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                state.artisanProfileDataResponse?.name ?? '',
                                style: MyTypography.SemiBold.copyWith(
                                  fontSize: 24,
                                  color: customColors.textPrimaryColor,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Padding(
                                padding: EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.verified,
                                  size: 20,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // Rating
                          Row(
                            children: [
                              const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 20,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${state.rating} (${state.totalReviews} Reviews)',
                                style: MyTypography.Regular.copyWith(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Divider
                          Divider(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.1,
                            ),
                            thickness: 1,
                          ),

                          const SizedBox(height: 16),

                          // Service Type Card
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeMode == ThemeMode.light
                                      ? darkBlueColor
                                      : customColors.primaryContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.work,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.white
                                          : customColors.textPrimaryColor,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    state
                                            .artisanProfileDataResponse
                                            ?.specializations
                                            ?.first
                                            .services
                                            ?.name ??
                                        '',
                                    style: MyTypography.SemiBold.copyWith(
                                      color:
                                          themeMode == ThemeMode.light
                                              ? Colors.white
                                              : customColors.textPrimaryColor,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Tabs
                          _buildTabs(),

                          const SizedBox(height: 24),

                          // Tab Content
                          _buildTabContent(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildIconButton(IconData icon) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color:
            themeMode == ThemeMode.light
                ? Colors.white
                : customColors.surfaceVariant,
        shape: BoxShape.circle,
        border:
            themeMode == ThemeMode.dark
                ? Border.all(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                  width: 1,
                )
                : null,
      ),
      child: Icon(icon, color: customColors.textPrimaryColor),
    );
  }

  Widget _buildTabs() {
    return Row(
      children: [
        _buildTab('Overview', 0),
        _buildTab('Recent Work', 1),
        _buildTab('Reviews', 2),
      ],
    );
  }

  Widget _buildTab(String text, int index) {
    final state = ref.watch(artisanProfileViewModelProvider);
    final viewModel = ref.read(artisanProfileViewModelProvider.notifier);
    final customColors = ref.watch(customColorsProvider);

    final isSelected = state.selectedTabIndex == index;
    return GestureDetector(
      onTap: () {
        viewModel.setSelectedTabIndex(index);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 24),
        padding: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? orangeColor : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          text,
          style: MyTypography.Medium.copyWith(
            color:
                isSelected
                    ? orangeColor
                    : customColors.textPrimaryColor.withValues(alpha: 0.6),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final state = ref.watch(artisanProfileViewModelProvider);

    // Only show contact button if status is not "open"
    final shouldShowContactButton = state.selectedJob?.status != 'open';

    if (!shouldShowContactButton) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeMode == ThemeMode.light
                ? Colors.white
                : const Color(0xFF1E1E1E), // Explicit dark background
        boxShadow:
            themeMode == ThemeMode.light
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha((0.05 * 255).round()),
                    blurRadius: 10,
                  ),
                ]
                : [
                  BoxShadow(
                    color: Colors.black.withAlpha((0.3 * 255).round()),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
        border:
            themeMode == ThemeMode.dark
                ? Border(
                  top: BorderSide(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                )
                : null,
      ),
      child: SafeArea(
        child: PrimaryButton(
          text: 'Contact Details',
          onPressed: () => _showContactDetailsBottomSheet(context),
          height: 48.0,
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    final state = ref.watch(artisanProfileViewModelProvider);

    switch (state.selectedTabIndex) {
      case 0:
        return _buildOverviewTab();
      case 1:
        return _buildRecentWorkTab();
      case 2:
        return _buildReviewsTab();
      default:
        return _buildOverviewTab();
    }
  }

  Widget _buildOverviewTab() {
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Specialization
        Text(
          'Specialisation',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              (state
                          .artisanProfileDataResponse
                          ?.specializations
                          ?.first
                          .specializationTags ??
                      [])
                  .map<Widget>(
                    (spec) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? orangeColor.withValues(alpha: 0.1)
                                : orangeColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color:
                              themeMode == ThemeMode.light
                                  ? orangeColor.withValues(alpha: 0.3)
                                  : orangeColor.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        spec.subCategories?.name ?? '',
                        style: MyTypography.Medium.copyWith(
                          color:
                              themeMode == ThemeMode.light
                                  ? orangeColor.withValues(alpha: 0.9)
                                  : orangeColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),

        const SizedBox(height: 24),

        // About
        Text(
          'About',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          state.artisanProfileDataResponse?.about ?? '',
          style: MyTypography.Regular.copyWith(
            color: customColors.textPrimaryColor.withValues(alpha: 0.7),
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentWorkTab() {
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final artisanImages = state.artisanProfileDataResponse?.artisanImages ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 4),

        // Show empty state if no images
        artisanImages.isEmpty
            ? Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.photo_library_outlined,
                      size: 64,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.4,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No recent work images available',
                      style: MyTypography.Medium.copyWith(
                        fontSize: 16,
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.6,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            )
            : GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: artisanImages.length,
              itemBuilder: (context, index) {
                final imageUrl = artisanImages[index].imageUrl ?? '';
                return GestureDetector(
                  onTap:
                      () => _showImageViewer(
                        context,
                        artisanImages.map((img) => img.imageUrl ?? '').toList(),
                        index,
                      ),
                  child: Hero(
                    tag: 'work_image_$index',
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value:
                                  loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                themeMode == ThemeMode.light
                                    ? darkBlueColor
                                    : customColors.textPrimaryColor,
                              ),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[200]
                                    : customColors.surfaceVariant,
                            child: Icon(
                              Icons.broken_image,
                              color: customColors.textPrimaryColor.withValues(
                                alpha: 0.5,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
      ],
    );
  }

  Widget _buildReviewsTab() {
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Client Reviews',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 18,
                color: customColors.textPrimaryColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color:
                    themeMode == ThemeMode.light
                        ? Colors.amber.shade100
                        : Colors.amber.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${state.artisanProfileDataResponse?.artisanRatings?.isNotEmpty == true ? _calculateAverageRating(state.artisanProfileDataResponse!.artisanRatings!) : 0.0}',
                    style: MyTypography.SemiBold.copyWith(
                      color:
                          themeMode == ThemeMode.light
                              ? Colors.amber.shade800
                              : Colors.amber,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount:
              state.artisanProfileDataResponse?.artisanRatings?.length ?? 0,
          separatorBuilder:
              (context, index) => Divider(
                color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                thickness: 1,
                height: 32,
              ),
          itemBuilder: (context, index) {
            final review =
                state.artisanProfileDataResponse?.artisanRatings?[index];
            return _buildReviewItem(review);
          },
        ),
      ],
    );
  }

  Widget _buildReviewItem(ArtisanRatingData? review) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor:
                  themeMode == ThemeMode.light
                      ? Colors.grey[200]
                      : customColors.surfaceVariant,
              child: ClipOval(
                child: Image.network(
                  review?.client?.avatar ?? '',
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value:
                            loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          themeMode == ThemeMode.light
                              ? darkBlueColor
                              : customColors.textPrimaryColor,
                        ),
                        strokeWidth: 2,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.person,
                      size: 20,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.5,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  review?.client?.name ?? '',
                  style: MyTypography.SemiBold.copyWith(
                    color: customColors.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    ...List.generate(
                      5,
                      (index) => Icon(
                        index < (review?.rating ?? 0)
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimeAgo(review?.createdAt ?? ''),
                      style: MyTypography.Regular.copyWith(
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.6,
                        ),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          review?.comments ?? '',
          style: MyTypography.Regular.copyWith(
            color: customColors.textPrimaryColor.withValues(alpha: 0.8),
            height: 1.4,
          ),
        ),
      ],
    );
  }

  void _showImageViewer(
    BuildContext context,
    List<String> images,
    int initialIndex,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DismissibleImageViewer(
            images: images,
            initialIndex: initialIndex,
          ),
    );
  }

  void _showContactDetailsBottomSheet(BuildContext context) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final state = ref.watch(artisanProfileViewModelProvider);
    final artisanData = state.artisanProfileDataResponse;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              decoration: BoxDecoration(
                color:
                    themeMode == ThemeMode.light
                        ? Colors.white
                        : const Color(0xFF1E1E1E),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12, bottom: 20),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: customColors.textPrimaryColor.withValues(
                          alpha: 0.3,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Header
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Row(
                        children: [
                          Icon(
                            Icons.contact_phone,
                            color: customColors.textPrimaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Contact Details',
                            style: MyTypography.SemiBold.copyWith(
                              fontSize: 20,
                              color: customColors.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Contact information
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          _buildContactItem(
                            icon: Icons.email_outlined,
                            title: 'Email',
                            value: artisanData?.email ?? 'Not provided',
                            onTap:
                                artisanData?.email != null
                                    ? () => _launchEmail(artisanData!.email!)
                                    : null,
                          ),

                          const SizedBox(height: 16),

                          _buildContactItem(
                            icon: Icons.phone_outlined,
                            title: 'Phone Number',
                            value:
                                artisanData?.artisanNumbers?.isNotEmpty == true
                                    ? artisanData!
                                            .artisanNumbers!
                                            .first
                                            .phonenumber ??
                                        'Not provided'
                                    : 'Not provided',
                            onTap:
                                artisanData?.artisanNumbers?.isNotEmpty ==
                                            true &&
                                        artisanData!
                                                .artisanNumbers!
                                                .first
                                                .phonenumber !=
                                            null
                                    ? () => _launchPhone(
                                      artisanData
                                          .artisanNumbers!
                                          .first
                                          .phonenumber!,
                                    )
                                    : null,
                          ),

                          const SizedBox(height: 16),

                          _buildContactItem(
                            icon: Icons.chat_outlined,
                            title: 'WhatsApp',
                            value:
                                artisanData?.whatsappNumber ?? 'Not provided',
                            onTap:
                                artisanData?.whatsappNumber != null
                                    ? () async => await WhatsAppUtils.openWhatsApp(
                                      phoneNumber: artisanData!.whatsappNumber!,
                                      message: 'Hie',
                                    )
                                    // _launchWhatsApp(
                                    //   artisanData!.whatsappNumber!,
                                    // )
                                    : null,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  Widget _buildSkeletonLoader(BuildContext context) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Shimmer.fromColors(
      baseColor:
          themeMode == ThemeMode.light
              ? Colors.grey[300]!
              : customColors.surfaceVariant,
      highlightColor:
          themeMode == ThemeMode.light
              ? Colors.grey[100]!
              : customColors.surfaceVariant.withValues(alpha: 0.5),
      period: const Duration(milliseconds: 1500), // Add animation period
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover image placeholder
            Container(
              height: 200,
              width: double.infinity,
              color:
                  themeMode == ThemeMode.light
                      ? Colors.white
                      : customColors.surfaceVariant,
            ),

            // Profile image placeholder with positioning
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Transform.translate(
                offset: const Offset(0, -40),
                child: CircleAvatar(
                  radius: 40,
                  backgroundColor:
                      themeMode == ThemeMode.light
                          ? Colors.white
                          : customColors.surfaceVariant,
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Profile info placeholders
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 200,
                    height: 24,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 16,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 1,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 50,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Container(
                        width: 80,
                        height: 20,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.surfaceVariant,
                      ),
                      const SizedBox(width: 24),
                      Container(
                        width: 80,
                        height: 20,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.surfaceVariant,
                      ),
                      const SizedBox(width: 24),
                      Container(
                        width: 80,
                        height: 20,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.surfaceVariant,
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(
                    width: 150,
                    height: 20,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    height: 100,
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.white
                            : customColors.surfaceVariant,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimeAgo(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final DateTime date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 365) {
        final years = (difference.inDays / 365).floor();
        return '$years ${years == 1 ? 'year' : 'years'} ago';
      } else if (difference.inDays > 30) {
        final months = (difference.inDays / 30).floor();
        return '$months ${months == 1 ? 'month' : 'months'} ago';
      } else if (difference.inDays > 7) {
        final weeks = (difference.inDays / 7).floor();
        return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
      } else if (difference.inDays > 0) {
        return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return dateString;
    }
  }

  double _calculateAverageRating(List<ArtisanRatingData> ratings) {
    if (ratings.isEmpty) return 0.0;

    double totalRating = 0.0;
    int validRatings = 0;

    for (var rating in ratings) {
      if (rating.rating != null) {
        totalRating += rating.rating!;
        validRatings++;
      }
    }

    if (validRatings == 0) return 0.0;
    return double.parse((totalRating / validRatings).toStringAsFixed(1));
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String value,
    VoidCallback? onTap,
  }) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isClickable = onTap != null && value != 'Not provided';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              themeMode == ThemeMode.light
                  ? Colors.grey[50]
                  : customColors.surfaceVariant.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: customColors.textPrimaryColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isClickable
                        ? (themeMode == ThemeMode.light
                                ? darkBlueColor
                                : orangeColor)
                            .withValues(alpha: 0.1)
                        : customColors.textPrimaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color:
                    isClickable
                        ? (themeMode == ThemeMode.light
                            ? darkBlueColor
                            : orangeColor)
                        : customColors.textPrimaryColor.withValues(alpha: 0.5),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: MyTypography.Medium.copyWith(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.7,
                      ),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: MyTypography.SemiBold.copyWith(
                      color:
                          isClickable
                              ? (themeMode == ThemeMode.light
                                  ? darkBlueColor
                                  : orangeColor)
                              : customColors.textPrimaryColor,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            if (isClickable)
              Icon(
                Icons.arrow_forward_ios,
                color: customColors.textPrimaryColor.withValues(alpha: 0.3),
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Inquiry about your services',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not launch email client'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error launching email: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _launchPhone(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not launch phone dialer'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error launching phone: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _launchWhatsApp(String phoneNumber) async {
    // Remove any formatting and add country code if needed
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If number starts with 0, replace with Zimbabwe country code (+263)
    if (cleanNumber.startsWith('0')) {
      cleanNumber = '263${cleanNumber.substring(1)}';
    }

    // Add + prefix for international format
    if (!cleanNumber.startsWith('+')) {
      cleanNumber = '+$cleanNumber';
    }

    // Try different WhatsApp URL formats
    final List<String> whatsappUrls = [
      'https://api.whatsapp.com/send/?phone=$cleanNumber',
      'https://wa.me/$cleanNumber',
      'intent://send?phone=$cleanNumber#Intent;scheme=whatsapp;package=com.whatsapp;end',
    ];

    bool launched = false;

    for (String urlString in whatsappUrls) {
      try {
        final Uri uri = Uri.parse(urlString);

        // Don't use canLaunchUrl for intent URLs as it may not work properly
        if (urlString.startsWith('intent://')) {
          try {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            launched = true;
            break;
          } catch (e) {
            continue;
          }
        } else {
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            launched = true;
            break;
          }
        }
      } catch (e) {
        continue;
      }
    }

    if (!launched && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('WhatsApp is not installed or could not be opened'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
