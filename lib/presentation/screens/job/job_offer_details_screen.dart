import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:build_mate/presentation/components/dismissible_image_viewer.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_view_model.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_details_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JobOfferDetailsScreen extends ConsumerStatefulWidget {
  const JobOfferDetailsScreen({super.key});

  @override
  ConsumerState<JobOfferDetailsScreen> createState() =>
      _JobOfferDetailsScreenState();
}

class _JobOfferDetailsScreenState extends ConsumerState<JobOfferDetailsScreen> {
  bool _isBidding = false;
  bool _isCanceling = false;

  @override
  void initState() {
    super.initState();
    // Set up post-frame callback to refresh data after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Force refresh jobs data when screen opens
      final jobPostedViewModel = ref.read(jobPostedViewModelProvider.notifier);
      jobPostedViewModel.fetchAvailableJobsForArtisan();

      // Set up a listener for the jobs stream
      jobPostedViewModel.setupRealtimeSubscription();
    });
  }

  @override
  void dispose() {
    // Clean up if needed
    super.dispose();
  }

  String _formatDate(DateTime date) {
    final List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  Widget _buildImageGrid(List<String> images) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Theme-aware colors for error states
    final errorBackgroundColor =
        themeMode == ThemeMode.light
            ? Colors.grey[300]
            : customColors.surfaceVariant.withValues(alpha: 0.5);
    final errorIconColor =
        themeMode == ThemeMode.light
            ? Colors.grey[500]
            : customColors.textPrimaryColor.withValues(alpha: 0.5);

    // Use placeholder images if no images are provided
    final List<String> displayImages =
        images.isEmpty
            ? [
              'assets/images/grid_1.jpeg',
              'assets/images/grid_2.jpeg',
              'assets/images/grid_3.jpg',
              'assets/images/grid_4.jpeg',
            ]
            : images;

    // Limit to maximum 4 images
    final List<String> limitedImages =
        displayImages.length > 4 ? displayImages.sublist(0, 4) : displayImages;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: limitedImages.length,
      itemBuilder: (context, index) {
        final String imageUrl = limitedImages[index];
        final bool isNetworkImage = imageUrl.startsWith('http');

        return GestureDetector(
          onTap: () => _showImageViewer(context, limitedImages, index),
          child: Hero(
            tag: 'job_image_$index',
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  isNetworkImage
                      ? Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          if (kDebugMode) {
                            print('Error loading network image: $error');
                          }
                          return Container(
                            color: errorBackgroundColor,
                            child: Icon(
                              Icons.broken_image,
                              color: errorIconColor,
                              size: 40,
                            ),
                          );
                        },
                      )
                      : Image.asset(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          if (kDebugMode) {
                            print('Error loading asset image: $error');
                          }
                          return Container(
                            color: errorBackgroundColor,
                            child: Icon(
                              Icons.broken_image,
                              color: errorIconColor,
                              size: 40,
                            ),
                          );
                        },
                      ),
            ),
          ),
        );
      },
    );
  }

  void _showImageViewer(
    BuildContext context,
    List<String> images,
    int initialIndex,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DismissibleImageViewer(
            images: images,
            initialIndex: initialIndex,
          ),
    );
  }

  void _showBidConfirmationDialog(BuildContext context) {
    final scaffoldMessengerState = ScaffoldMessenger.of(context);
    final jobPostedViewModel = ref.read(jobPostedViewModelProvider.notifier);
    final jobPostedState = ref.read(jobPostedViewModelProvider);
    final customColors = ref.read(customColorsProvider);
    final themeMode = ref.read(themeModeProvider);

    if (kDebugMode) {
      print('Showing bid confirmation dialog');
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor:
                themeMode == ThemeMode.light
                    ? Colors.white
                    : customColors.surfaceVariant,
            title: Text(
              'Express Interest',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 18,
                color: customColors.textPrimaryColor,
              ),
            ),
            content: Text(
              'Are you interested in this job? The client will be notified of your interest and may contact you for further details.',
              style: MyTypography.Regular.copyWith(
                color: customColors.textPrimaryColor.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: MyTypography.Medium.copyWith(
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.grey[700]
                            : customColors.textPrimaryColor.withValues(
                              alpha: 0.7,
                            ),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (kDebugMode) {
                    print('Confirm button pressed');
                  }

                  // Close dialog first
                  Navigator.pop(context);

                  // Set bidding state to true to show loading indicator
                  setState(() {
                    _isBidding = true;
                  });

                  try {
                    if (kDebugMode) {
                      print('Calling bid method');
                    }

                    // Use the jobPostedViewModel
                    await jobPostedViewModel.bid(
                      jobPostedState.selectedJob ??
                          PostedJobModel(
                            id: 0,
                            title: '',
                            clientName: '',
                            description: '',
                            budget: 0.0,
                            categories: [],
                            images: [],
                            status: '',
                            serviceDate: DateTime.now(),
                            postDate: DateTime.now(),
                            bids: [],
                            hasMyBid: false,
                          ),
                    );

                    if (kDebugMode) {
                      print('bid completed successfully');
                    }

                    // Show success message
                    if (mounted) {
                      // ScaffoldMessenger.of(scaffoldMessengerState).showSnackBar(
                      //   const SnackBar(
                      //     content: Text(
                      //       'You have expressed interest in this job!',
                      //     ),
                      //     backgroundColor: Colors.green,
                      //   ),
                      // );

                      _showSuccessMessage(
                        scaffoldMessengerState,
                        'You have expressed interest in this job!',
                      );
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('Error in bid: $e');
                    }

                    // Show error message if bid fails
                    if (mounted) {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      //   SnackBar(
                      //     content: Text(
                      //       'Failed to express interest: ${e.toString()}',
                      //     ),
                      //     backgroundColor: Colors.red,
                      //   ),
                      // );
                      _showErrorMessage(
                        scaffoldMessengerState,
                        'Failed to express interest: ${e.toString()}',
                      );
                    }
                  } finally {
                    // Reset bidding state
                    if (mounted) {
                      setState(() {
                        _isBidding = false;
                      });
                    }
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: orangeColor),
                child: Text(
                  'Confirm Interest',
                  style: MyTypography.Medium.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _showSuccessMessage(ScaffoldMessengerState messenger, String message) {
    final customColors = ref.read(customColorsProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: customColors.successColor,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorMessage(ScaffoldMessengerState messenger, String message) {
    final customColors = ref.read(customColorsProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: customColors.errorColor,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showCancelBidConfirmationDialog(BuildContext context) {
    // Capture all necessary references before showing the dialog
    final jobPostedViewModel = ref.read(
      jobPostedDetailsViewModelProvider.notifier,
    );
    final jobPostedState = ref.read(jobPostedDetailsViewModelProvider);
    final job = jobPostedState.job;
    final customColors = ref.read(customColorsProvider);
    final themeMode = ref.read(themeModeProvider);

    // Store context reference before showing dialog
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // Check if a job is selected
    if (job == null) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: const Text('No job selected. Please try again.'),
          backgroundColor: customColors.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            backgroundColor:
                themeMode == ThemeMode.light
                    ? Colors.white
                    : customColors.surfaceVariant,
            title: Text(
              'Cancel Interest',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 18,
                color: customColors.textPrimaryColor,
              ),
            ),
            content: Text(
              'Are you sure you want to cancel your interest in this job?',
              style: MyTypography.Regular.copyWith(
                color: customColors.textPrimaryColor.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => navigator.pop(),
                child: Text(
                  'No, Keep Interest',
                  style: MyTypography.Medium.copyWith(
                    color:
                        themeMode == ThemeMode.light
                            ? Colors.grey[700]
                            : customColors.textPrimaryColor.withValues(
                              alpha: 0.7,
                            ),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Close dialog first
                  navigator.pop();

                  // Set canceling state to true to show loading indicator
                  if (mounted) {
                    setState(() {
                      _isCanceling = true;
                    });
                  }

                  try {
                    // Use the jobPostedViewModel
                    await jobPostedViewModel.cancelBid();

                    // Show success message
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(
                          content: Text('Your interest has been canceled.'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    // Show error message if cancelBid fails
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            'Failed to cancel interest: ${e.toString()}',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } finally {
                    // Reset canceling state
                    if (mounted) {
                      setState(() {
                        _isCanceling = false;
                      });
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                ),
                child: Text(
                  'Yes, Cancel Interest',
                  style: MyTypography.Medium.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final jobPostedState = ref.watch(jobPostedViewModelProvider);
    final job = jobPostedState.selectedJob;
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Theme-aware colors
    final headerColor =
        themeMode == ThemeMode.light
            ? const Color(0xFF0A1B3D)
            : customColors.surfaceVariant;
    final headerTextColor =
        themeMode == ThemeMode.light
            ? Colors.white
            : customColors.textPrimaryColor;

    // If no job is selected, show a loading indicator or error message
    if (job == null) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final clientName = job.clientName;
    final clientImageUrl =
        job.clientAvatar ?? 'assets/images/avatar_placeholder.png';
    final jobTitle = job.title;
    final serviceTags = job.categories;
    final location = "Harare, Zimbabwe"; // This should come from the job model
    final description = job.description;
    final serviceDate = job.serviceDate;
    // final serviceTimes = DateFormat('MMM dd, yyyy').format(job.serviceDate);
    final jobImages = job.images;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Column(
        children: [
          // Header Section
          Container(
            color: headerColor,
            child: SafeArea(
              bottom: false,
              child: Column(
                children: [
                  // AppBar
                  AppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: Icon(Icons.arrow_back, color: headerTextColor),
                      onPressed: () => context.pop(),
                    ),
                    title: Text(
                      'New job offer',
                      style: MyTypography.SemiBold.copyWith(
                        color: headerTextColor,
                        fontSize: 18,
                      ),
                    ),
                  ),

                  // Client Profile Section
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor:
                              themeMode == ThemeMode.light
                                  ? Colors.grey[300]
                                  : customColors.surfaceVariant.withValues(
                                    alpha: 0.5,
                                  ),
                          backgroundImage:
                              clientImageUrl.startsWith('http')
                                  ? NetworkImage(clientImageUrl)
                                  : AssetImage(clientImageUrl) as ImageProvider,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          clientName,
                          style: MyTypography.SemiBold.copyWith(
                            color: headerTextColor,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // White Content Section
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Job Title
                        Text(
                          jobTitle,
                          style: MyTypography.Bold.copyWith(
                            color: customColors.textPrimaryColor,
                            fontSize: 24,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Service Tags
                        if (serviceTags.isNotEmpty) ...[
                          SizedBox(
                            height: 32,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: serviceTags.length,
                              itemBuilder: (context, index) {
                                // Create theme-aware tag colors
                                final tagColors =
                                    themeMode == ThemeMode.light
                                        ? [
                                          const Color(0xFFE3F2FD), // Light blue
                                          const Color(
                                            0xFFE8F5E9,
                                          ), // Light green
                                          const Color(
                                            0xFFFFF3E0,
                                          ), // Light orange
                                        ]
                                        : [
                                          customColors.primaryContainer
                                              .withValues(alpha: 0.3),
                                          customColors.surfaceVariant
                                              .withValues(alpha: 0.4),
                                          customColors.surfaceVariant
                                              .withValues(alpha: 0.5),
                                        ];
                                final textColors =
                                    themeMode == ThemeMode.light
                                        ? [
                                          const Color(0xFF1565C0), // Dark blue
                                          const Color(0xFF2E7D32), // Dark green
                                          const Color(
                                            0xFFE65100,
                                          ), // Dark orange
                                        ]
                                        : [
                                          customColors.textPrimaryColor,
                                          customColors.textPrimaryColor,
                                          customColors.textPrimaryColor,
                                        ];

                                // Use modulo to cycle through colors if there are more than 3 tags
                                final colorIndex = index % tagColors.length;

                                return Padding(
                                  padding: EdgeInsets.only(
                                    right:
                                        index != serviceTags.length - 1 ? 8 : 0,
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: tagColors[colorIndex],
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              themeMode == ThemeMode.light
                                                  ? Colors.black.withAlpha(
                                                    (0.05 * 255).round(),
                                                  )
                                                  : Colors.black.withValues(
                                                    alpha: 0.2,
                                                  ),
                                          blurRadius: 2,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                      border:
                                          themeMode == ThemeMode.dark
                                              ? Border.all(
                                                color: customColors
                                                    .textPrimaryColor
                                                    .withValues(alpha: 0.1),
                                                width: 0.5,
                                              )
                                              : null,
                                    ),
                                    child: Text(
                                      serviceTags[index],
                                      style: MyTypography.Medium.copyWith(
                                        fontSize: 12,
                                        color: textColors[colorIndex],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        // Location
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.blue[400]
                                      : customColors.infoColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: location,
                                    style: MyTypography.Medium.copyWith(
                                      color:
                                          themeMode == ThemeMode.light
                                              ? Colors.blue[400]
                                              : customColors.infoColor,
                                      fontSize: 16,
                                    ),
                                  ),
                                  TextSpan(
                                    text: ' (2.5 km)',
                                    style: MyTypography.Light.copyWith(
                                      color:
                                          themeMode == ThemeMode.light
                                              ? Colors.grey[600]
                                              : customColors.textPrimaryColor
                                                  .withValues(alpha: 0.6),
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // New Offer Tag and Budget
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.teal,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                transitionBuilder: (
                                  Widget child,
                                  Animation<double> animation,
                                ) {
                                  return FadeTransition(
                                    opacity: animation,
                                    child: ScaleTransition(
                                      scale: animation,
                                      child: child,
                                    ),
                                  );
                                },
                                child: Text(
                                  'New offer (${job.bids.length} bids)',
                                  key: ValueKey<String>(
                                    "bids-${job.bids.length}",
                                  ), // Key changes when bid count changes
                                  style: MyTypography.Medium.copyWith(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFF4C02), // Orange color
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                'Budget: \$${job.budget.toStringAsFixed(0)}',
                                style: MyTypography.Medium.copyWith(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        // Budget explanation text
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 4.0,
                            bottom: 16.0,
                          ),
                          child: Text(
                            'Estimated budget for this service',
                            style: MyTypography.Regular.copyWith(
                              color:
                                  themeMode == ThemeMode.light
                                      ? Colors.grey[600]
                                      : customColors.textPrimaryColor
                                          .withValues(alpha: 0.6),
                              fontSize: 12,
                            ),
                          ),
                        ),

                        // Service Details Section
                        Text(
                          'Service needed by:',
                          style: MyTypography.SemiBold.copyWith(
                            color: customColors.textPrimaryColor,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color:
                                themeMode == ThemeMode.light
                                    ? Colors.grey[100]
                                    : customColors.surfaceVariant.withValues(
                                      alpha: 0.5,
                                    ),
                            borderRadius: BorderRadius.circular(8),
                            border:
                                themeMode == ThemeMode.dark
                                    ? Border.all(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.1),
                                      width: 1,
                                    )
                                    : null,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 16,
                                color:
                                    themeMode == ThemeMode.light
                                        ? Colors.blue[400]
                                        : customColors.infoColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _formatDate(serviceDate),
                                style: MyTypography.Medium.copyWith(
                                  color: customColors.textPrimaryColor,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Service Description
                        Text(
                          'Service description',
                          style: MyTypography.SemiBold.copyWith(
                            color: customColors.textPrimaryColor,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          description,
                          style: MyTypography.Medium.copyWith(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.8,
                            ),
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Job Images Section (conditionally displayed)
                        if (jobImages.isNotEmpty) ...[
                          Text(
                            'Job Images',
                            style: MyTypography.SemiBold.copyWith(
                              color: customColors.textPrimaryColor,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(height: 1),
                          _buildImageGrid(jobImages),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom Action Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color:
                      themeMode == ThemeMode.light
                          ? Colors.black.withAlpha((0.05 * 255).round())
                          : Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                ),
              ],
              border:
                  themeMode == ThemeMode.dark
                      ? Border(
                        top: BorderSide(
                          color: customColors.textPrimaryColor.withValues(
                            alpha: 0.1,
                          ),
                          width: 1,
                        ),
                      )
                      : null,
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        _isBidding || _isCanceling ? null : () => context.pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]
                              : customColors.surfaceVariant.withValues(
                                alpha: 0.5,
                              ),
                      foregroundColor: customColors.textPrimaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side:
                            themeMode == ThemeMode.dark
                                ? BorderSide(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.2),
                                  width: 1,
                                )
                                : BorderSide.none,
                      ),
                      disabledBackgroundColor:
                          themeMode == ThemeMode.light
                              ? Colors.grey[200]?.withAlpha((0.7 * 255).round())
                              : customColors.surfaceVariant.withValues(
                                alpha: 0.3,
                              ),
                      disabledForegroundColor: customColors.textPrimaryColor
                          .withAlpha((0.5 * 255).round()),
                    ),
                    child: Text('Back', style: MyTypography.SemiBold),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child:
                      job.hasMyBid
                          ? ElevatedButton(
                            onPressed:
                                _isCanceling
                                    ? null
                                    : () => _showCancelBidConfirmationDialog(
                                      context,
                                    ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[400],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              disabledBackgroundColor: Colors.red[400]
                                  ?.withAlpha((0.7 * 255).round()),
                            ),
                            child:
                                _isCanceling
                                    ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Canceling...',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    )
                                    : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.cancel_outlined),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Cancel Interest',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                          )
                          : ElevatedButton(
                            onPressed:
                                _isBidding
                                    ? null
                                    : () => _showBidConfirmationDialog(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: orangeColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              disabledBackgroundColor: orangeColor.withAlpha(
                                (0.7 * 255).round(),
                              ),
                            ),
                            child:
                                _isBidding
                                    ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Processing...',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    )
                                    : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.handshake_outlined),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Express Interest',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                          ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
