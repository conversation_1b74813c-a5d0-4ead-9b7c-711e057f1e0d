import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';

class RatingClientScreen extends StatefulWidget {
  final String clientName;
  final String clientImageUrl;

  const RatingClientScreen({
    super.key,
    required this.clientName,
    required this.clientImageUrl,
  });

  @override
  State<RatingClientScreen> createState() => _RatingClientScreenState();
}

class _RatingClientScreenState extends State<RatingClientScreen> {
  int _rating = 0;
  final TextEditingController _thoughtsController = TextEditingController();
  final int _maxCharacters = 500;

  @override
  void dispose() {
    _thoughtsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(), // Updated to use GoRouter
        ),
        title: Text(
          'Rating Client',
          style: MyTypography.SemiBold.copyWith(fontSize: 18),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rate your client',
              style: MyTypography.SemiBold.copyWith(fontSize: 24),
            ),
            const SizedBox(height: 8),
            Text(
              'We value our client\'s professionalism and clear communication. Thank you for being exceptional!',
              style: MyTypography.Regular.copyWith(
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 24),

            // Client Info
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Colors.red[100],
                  backgroundImage: AssetImage(widget.clientImageUrl),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.clientName,
                      style: MyTypography.SemiBold.copyWith(fontSize: 16),
                    ),
                    Text(
                      'Client',
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Rating Stars
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How was it?',
                  style: MyTypography.Medium.copyWith(fontSize: 16),
                ),
                const SizedBox(height: 12),
                Row(
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _rating = index + 1;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Icon(
                          Icons.star,
                          size: 32,
                          color:
                              index < _rating ? Colors.amber : Colors.grey[300],
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Thoughts Input
            Text(
              'Leave your thoughts',
              style: MyTypography.Medium.copyWith(fontSize: 16),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _thoughtsController,
              maxLength: _maxCharacters,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'Type here',
                hintStyle: TextStyle(color: Colors.grey[400]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.orange),
                ),
                counterText: 'Write in $_maxCharacters Character',
              ),
            ),
            const SizedBox(height: 32),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _rating > 0 ? _handleSubmit : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  disabledBackgroundColor: Colors.grey[300],
                ),
                child: Text(
                  'Submit',
                  style: MyTypography.SemiBold.copyWith(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    final rating = _rating;
    final thoughts = _thoughtsController.text;

    if (kDebugMode) {
      print('Rating: $rating');
    }
    if (kDebugMode) {
      print('Thoughts: $thoughts');
    }

    context.pop(); // Updated to use GoRouter
  }
}
