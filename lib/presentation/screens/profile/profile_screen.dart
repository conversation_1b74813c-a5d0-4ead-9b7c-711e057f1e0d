import 'package:build_mate/presentation/screens/profile/service_provider_profile_screen.dart';
import 'package:flutter/material.dart';

class ServiceProviderProfile extends StatelessWidget {
  const ServiceProviderProfile({super.key});

  @override
  Widget build(BuildContext context) {
    return ServiceProviderProfileScreen(
      coverImageUrl: 'assets/images/profile_pic.png',
      profileImageUrl: 'assets/images/profile_pic.png',
      name: '<PERSON><PERSON>',
      profession: 'Electrician',
      location: 'Mabushi, FCT',
      distance: 2.5,
      rating: 4.5,
      totalReviews: 102,
      certifications: ['cert1', 'cert2', 'cert3'],
      serviceType: 'Electrical Services',
      isActive: true,
      specializations: ['Repairing', 'Painting', 'Custom Design'],
      about:
          'Skills required in carpentry include proficiency with hand and power tools, understanding of construct techniques and materials, mathematical and technical skills for measurements and...',
      nextAvailableTime: '2.00 am',
    );
  }
}
