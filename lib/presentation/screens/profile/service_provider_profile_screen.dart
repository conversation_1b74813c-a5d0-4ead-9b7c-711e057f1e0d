import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class ServiceProviderProfileScreen extends StatelessWidget {
  final String coverImageUrl;
  final String profileImageUrl;
  final String name;
  final String profession;
  final String location;
  final double distance;
  final double rating;
  final int totalReviews;
  final List<String> certifications;
  final String serviceType;
  final bool isActive;
  final List<String> specializations;
  final String about;
  final String nextAvailableTime;

  const ServiceProviderProfileScreen({
    super.key,
    required this.coverImageUrl,
    required this.profileImageUrl,
    required this.name,
    required this.profession,
    required this.location,
    required this.distance,
    required this.rating,
    required this.totalReviews,
    required this.certifications,
    required this.serviceType,
    required this.isActive,
    required this.specializations,
    required this.about,
    required this.nextAvailableTime,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover Image and Profile Section
            Stack(
              clipBehavior: Clip.none,
              children: [
                // Cover Image
                Image.asset(
                  coverImageUrl,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),

                // Contact Actions
                Positioned(
                  top: 40,
                  right: 16,
                  child: Row(
                    children: [
                      _buildIconButton(Icons.message_outlined),
                      const SizedBox(width: 12),
                      _buildIconButton(Icons.call_outlined),
                    ],
                  ),
                ),

                // Profile Image
                Positioned(
                  bottom: -40,
                  left: 16,
                  child: CircleAvatar(
                    radius: 40,
                    backgroundImage: AssetImage(profileImageUrl),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 48),

            // Profile Info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profession Tag
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.teal[50],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      profession,
                      style: MyTypography.Medium.copyWith(
                        color: Colors.teal,
                        fontSize: 14,
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Name and Certifications
                  Row(
                    children: [
                      Text(
                        name,
                        style: MyTypography.SemiBold.copyWith(fontSize: 24),
                      ),
                      const SizedBox(width: 8),
                      ...certifications.map(
                        (cert) => Padding(
                          padding: const EdgeInsets.only(right: 4),
                          child: Icon(
                            Icons.verified,
                            size: 20,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Location and Bookmark
                  Row(
                    children: [
                      Icon(Icons.bookmark_outline, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Text(
                        'Bookmark',
                        style: MyTypography.Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 24),
                      Icon(Icons.location_on_outlined, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Text(
                        '$location (${distance.toString()} mi)',
                        style: MyTypography.Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Rating
                  Row(
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        '$rating ($totalReviews Reviews)',
                        style: MyTypography.Regular.copyWith(
                          color: Colors.grey[800],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Service Type Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.teal[900],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Service Type',
                              style: MyTypography.Regular.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              serviceType,
                              style: MyTypography.SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha((0.2 * 255).round()),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            isActive ? 'Active' : 'Inactive',
                            style: MyTypography.Medium.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Tabs
                  _buildTabs(),

                  const SizedBox(height: 24),

                  // Specialization
                  Text(
                    'Specialisation',
                    style: MyTypography.SemiBold.copyWith(fontSize: 18),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        specializations
                            .map(
                              (spec) => Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(spec),
                              ),
                            )
                            .toList(),
                  ),

                  const SizedBox(height: 24),

                  // About
                  Text(
                    'About',
                    style: MyTypography.SemiBold.copyWith(fontSize: 18),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    about,
                    style: MyTypography.Regular.copyWith(
                      color: Colors.grey[600],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildIconButton(IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
      child: Icon(icon, color: Colors.black87),
    );
  }

  Widget _buildTabs() {
    return Row(
      children: [
        _buildTab('Overview', true),
        _buildTab('Recent Work', false),
        _buildTab('Reviews', false),
      ],
    );
  }

  Widget _buildTab(String text, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 24),
      padding: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isSelected ? Colors.orange : Colors.transparent,
            width: 2,
          ),
        ),
      ),
      child: Text(
        text,
        style: MyTypography.Medium.copyWith(
          color: isSelected ? Colors.orange : Colors.grey,
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black.withAlpha((0.05 * 255).round()), blurRadius: 10),
        ],
      ),
      child: Row(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Next Available:',
                style: MyTypography.Regular.copyWith(color: Colors.grey),
              ),
              Text(nextAvailableTime, style: MyTypography.SemiBold),
            ],
          ),
          const SizedBox(width: 24),
          Expanded(
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Check availability',
                style: MyTypography.SemiBold.copyWith(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
