import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/presentation/view_models/user/onboarding/artisan_profile_details_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';

class ArtisanProfileDetailsScreen extends ConsumerStatefulWidget {
  const ArtisanProfileDetailsScreen({super.key});

  @override
  ConsumerState<ArtisanProfileDetailsScreen> createState() =>
      _ArtisanProfileDetailsScreenState();
}

class _ArtisanProfileDetailsScreenState
    extends ConsumerState<ArtisanProfileDetailsScreen> {
  late ArtisanProfileDetailsViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = ref.read(artisanProfileDetailsViewModelProvider.notifier);
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  void continueToNextScreen() {
    if (!viewModel.formKey.currentState!.validate()) {
      return;
    }

    // Update the state with the form values
    viewModel.prepareForNextScreen();

    // Navigate to the final profile screen
    context.pushNamed(RouteConstants.ARTISAN_FINAL_PROFILE_DETAILS_SCREEN);
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(artisanProfileDetailsViewModelProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
        centerTitle: true,
        backgroundColor: darkBlueColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: viewModel.formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cover Image
              GestureDetector(
                onTap: () async {
                  if (!viewModel.isUploadingCover) {
                    try {
                      await viewModel.selectCoverImage();
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error uploading cover image: $e'),
                          ),
                        );
                      }
                    }
                  }
                },
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      height: 150,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        image:
                            viewModel.coverImageFile != null
                                ? DecorationImage(
                                  image: FileImage(viewModel.coverImageFile!),
                                  fit: BoxFit.cover,
                                  opacity:
                                      viewModel.isUploadingCover ? 0.5 : 1.0,
                                )
                                : (state.coverImageUrl.isNotEmpty
                                    ? DecorationImage(
                                      image: NetworkImage(state.coverImageUrl),
                                      fit: BoxFit.cover,
                                    )
                                    : null),
                      ),
                      child:
                          (viewModel.coverImageFile == null &&
                                  state.coverImageUrl.isEmpty)
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_a_photo,
                                      color: Colors.grey[600],
                                      size: 40,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Add Cover Photo',
                                      style: MyTypography.Medium.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : Align(
                                alignment: Alignment.bottomRight,
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: CircleAvatar(
                                    backgroundColor: Colors.white.withAlpha(
                                      (0.7 * 255).round(),
                                    ),
                                    child: Icon(
                                      Icons.edit,
                                      color: darkBlueColor,
                                    ),
                                  ),
                                ),
                              ),
                    ),

                    // Progress indicator overlay
                    if (viewModel.isUploadingCover)
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            value: viewModel.coverUploadProgress,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                            strokeWidth: 3,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${(viewModel.coverUploadProgress * 100).toInt()}%',
                            style: MyTypography.SemiBold.copyWith(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              // Profile Image
              Transform.translate(
                offset: const Offset(0, -40),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: GestureDetector(
                    onTap: () async {
                      if (!viewModel.isUploadingProfile) {
                        try {
                          await viewModel.selectProfileImage();
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Error uploading profile image: $e',
                                ),
                              ),
                            );
                          }
                        }
                      }
                    },
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.grey[200],
                          backgroundImage:
                              viewModel.profileImageFile != null
                                  ? FileImage(viewModel.profileImageFile!)
                                  : (state.avatarUrl.isNotEmpty
                                      ? NetworkImage(state.avatarUrl)
                                          as ImageProvider
                                      : null),
                          child:
                              (viewModel.profileImageFile == null &&
                                      state.avatarUrl.isEmpty)
                                  ? Icon(
                                    Icons.person,
                                    size: 50,
                                    color: Colors.grey[400],
                                  )
                                  : null,
                        ),

                        // Progress indicator overlay
                        if (viewModel.isUploadingProfile)
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.black.withAlpha(
                                (0.5 * 255).round(),
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  value: viewModel.profileUploadProgress,
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                  strokeWidth: 3,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${(viewModel.profileUploadProgress * 100).toInt()}%',
                                  style: MyTypography.SemiBold.copyWith(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        // Camera icon
                        if (!viewModel.isUploadingProfile)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: orangeColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              // Form Fields
              Padding(
                padding: const EdgeInsets.fromLTRB(24.0, 0, 24.0, 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Full Name
                    PrimaryFormField(
                      controller: viewModel.fullNameController,
                      labelText: 'Full Name',
                      hintText: 'Enter your full name',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your full name';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Email
                    PrimaryFormField(
                      controller: viewModel.emailController,
                      labelText: 'Email',
                      hintText: 'Enter your email address',
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!RegExp(
                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                        ).hasMatch(value)) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Phone Number
                    PrimaryFormField(
                      controller: viewModel.phoneNumberController,
                      labelText: 'Phone Number',
                      hintText: 'Enter your phone number',
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Second Phone Number (Optional)
                    PrimaryFormField(
                      controller: viewModel.secondPhoneNumberController,
                      labelText: 'Second Phone Number (Optional)',
                      hintText: 'Enter your second phone number',
                      keyboardType: TextInputType.phone,
                    ),

                    const SizedBox(height: 16),

                    // WhatsApp Number
                    PrimaryFormField(
                      controller: viewModel.whatsappNumberController,
                      labelText: 'WhatsApp Number',
                      hintText: 'Enter your WhatsApp number',
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your WhatsApp number';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Address
                    PrimaryFormField(
                      controller: viewModel.addressController,
                      labelText: 'Address',
                      hintText: 'Enter your address',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your address';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // National ID
                    PrimaryFormField(
                      controller: viewModel.nationalIdController,
                      labelText: 'National ID',
                      hintText: 'Enter your national ID number',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your national ID';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 40),

                    // Save Button
                    PrimaryButton(
                      text: 'Continue',
                      onPressed: continueToNextScreen,
                      height: 50,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
