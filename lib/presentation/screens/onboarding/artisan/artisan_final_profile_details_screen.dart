import 'package:build_mate/presentation/view_models/artisan/artisan_final_profile_details_view_model.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/screens/services/service_categories_screen.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ArtisanFinalProfileDetailsScreen extends ConsumerWidget {
  const ArtisanFinalProfileDetailsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewModel = ref.watch(
      artisanFinalProfileDetailsViewModelProvider.notifier,
    );
    final state = ref.watch(artisanFinalProfileDetailsViewModelProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Complete Your Profile',
          style: MyTypography.SemiBold.copyWith(fontSize: 18),
        ),
        centerTitle: true,
      ),
      body:
          state.isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tell us about your services',
                      style: MyTypography.SemiBold.copyWith(fontSize: 20),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Select your main service category and specializations',
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Main Service Category Selection
                    Text(
                      'Main Service Category',
                      style: MyTypography.SemiBold.copyWith(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          useSafeArea: false,
                          builder: (BuildContext context) {
                            return Dialog.fullscreen(
                              child: ServiceCategoriesScreen(
                                onCategorySelected: (
                                  String mainCategory,
                                  String subCategory,
                                  List<String> subCategories,
                                ) {
                                  viewModel.onCategorySelected(
                                    mainCategory,
                                    subCategory,
                                    subCategories,
                                  );
                                  Navigator.pop(context);
                                },
                              ),
                            );
                          },
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (state
                                      .selectedMainCategory
                                      .isNotEmpty) ...[
                                    Text(
                                      state.selectedMainCategory,
                                      style: MyTypography.Regular.copyWith(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                  ],
                                  Text(
                                    state.selectedSubCategories.isNotEmpty
                                        ? state
                                            .selectedSubCategories
                                            .first // Show first selected subcategory
                                        : 'Select Your Main Service',
                                    style: MyTypography.Regular.copyWith(
                                      color:
                                          state.selectedSubCategories.isNotEmpty
                                              ? Colors.black87
                                              : Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Related Services / Specializations
                    if (state.selectedMainCategory.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      Text(
                        'Your Specializations',
                        style: MyTypography.SemiBold.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select all that apply to your services',
                        style: MyTypography.Regular.copyWith(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            state.relatedSubCategories.map((subCategory) {
                              final isSelected = state.selectedSubCategories
                                  .contains(subCategory);
                              return FilterChip(
                                label: Text(subCategory),
                                selected: isSelected,
                                onSelected: (bool selected) {
                                  if (selected) {
                                    viewModel.addSubCategory(subCategory);
                                  } else {
                                    viewModel.removeSubCategory(subCategory);
                                  }
                                },
                                selectedColor: orangeColor.withAlpha((0.2 * 255).round()),
                                checkmarkColor: orangeColor,
                                labelStyle: MyTypography.Medium.copyWith(
                                  color:
                                      isSelected ? orangeColor : Colors.black87,
                                ),
                              );
                            }).toList(),
                      ),
                    ],

                    // About section
                    const SizedBox(height: 24),
                    Text(
                      'About Your Services',
                      style: MyTypography.SemiBold.copyWith(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tell clients about your experience, skills, and the services you offer',
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: viewModel.aboutController,
                      maxLines: 5,
                      onChanged: (value) {
                        viewModel.updateAbout(value);
                      },
                      decoration: InputDecoration(
                        hintText: 'Describe your services and expertise...',
                        hintStyle: MyTypography.Regular.copyWith(
                          color: Colors.grey,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: orangeColor),
                        ),
                        contentPadding: const EdgeInsets.all(16),
                      ),
                    ),

                    // National ID Document Upload
                    const SizedBox(height: 24),
                    Text(
                      'Upload National ID Document',
                      style: MyTypography.SemiBold.copyWith(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please upload a clear image of your national ID document',
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () {
                        if (!viewModel.isUploadingNationalId) {
                          viewModel.pickNationalIdImage(context);
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        height: 160, // Rectangle shape
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[100],
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            if (state.nationalIdImage.isNotEmpty)
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  state.nationalIdImage,
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                  loadingBuilder: (
                                    context,
                                    child,
                                    loadingProgress,
                                  ) {
                                    if (loadingProgress == null) return child;
                                    return Center(
                                      child: CircularProgressIndicator(
                                        value:
                                            loadingProgress
                                                        .expectedTotalBytes !=
                                                    null
                                                ? loadingProgress
                                                        .cumulativeBytesLoaded /
                                                    loadingProgress
                                                        .expectedTotalBytes!
                                                : null,
                                      ),
                                    );
                                  },
                                ),
                              )
                            else if (viewModel.nationalIdImageFile != null)
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.file(
                                  viewModel.nationalIdImageFile!,
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              )
                            else
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.upload_file,
                                    size: 40,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'Tap to upload ID document',
                                    style: MyTypography.Medium.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Supported formats: JPG, PNG',
                                    style: MyTypography.Regular.copyWith(
                                      color: Colors.grey[500],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                              
                            // Upload progress indicator (only when uploading)
                            if (viewModel.isUploadingNationalId)
                              Positioned(
                                bottom: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withAlpha((0.7 * 255).round()),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    '${(viewModel.nationalIdUploadProgress * 100).toInt()}%',
                                    style: MyTypography.Medium.copyWith(
                                      color: Colors.white,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),
                    PrimaryButton(
                      text: 'Complete Profile',
                      isLoading: state.isLoading,
                      onPressed:
                          () => viewModel.saveArtisanFinalDetails(context),
                    ),
                  ],
                ),
              ),
    );
  }
}
