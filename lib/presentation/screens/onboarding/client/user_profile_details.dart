import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/user/onboarding/user_profile_details_view_model.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';

class UserProfileDetailsScreens extends ConsumerStatefulWidget {
  const UserProfileDetailsScreens({super.key});

  @override
  ConsumerState<UserProfileDetailsScreens> createState() =>
      _UserProfileDetailsScreensState();
}

class _UserProfileDetailsScreensState
    extends ConsumerState<UserProfileDetailsScreens> {
  @override
  void initState() {
    super.initState();

    // Delay permission request to ensure it shows properly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkLocationPermission();
    });

    // final user = ref.read(userProvider);
    // viewModel.fullNameController.text = user.displayName ?? '';
    // viewModel.emailController.text = user.email ?? '';
    // viewModel.updateAvatarUrl(user.photoURL ?? '');
  }

  UserProfileDetailsViewModel get viewModel =>
      ref.read(userProfileDetailsViewModelProvider.notifier);

  Future<void> _checkLocationPermission() async {
    try {
      final status = await viewModel.checkLocationPermission();

      if (status.isPermanentlyDenied) {
        // User permanently denied permission, show dialog to open settings
        if (mounted) {
          _showPermissionSettingsDialog();
        }
      } else if (status.isGranted) {
        // Permission granted, get current location
        await viewModel.getCurrentLocation();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      }
    }
  }

  void _showPermissionSettingsDialog() {
    showDialog(
      context: context,
      builder:
          (BuildContext context) => AlertDialog(
            title: Text('Location Permission Required'),
            content: Text(
              'BuildMate needs location access to show nearby jobs and services. Please enable it in settings.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  openAppSettings();
                },
                child: Text('Open Settings'),
              ),
            ],
          ),
    );
  }

  Future<void> _saveProfile() async {
    try {
      final success = await viewModel.saveProfile(context);

      if (success && mounted) {
        // Navigate to welcome screen after successful profile submission
        context.pushReplacementNamed(RouteConstants.WELCOME_SCREEN);
      }
    } catch (e) {
      if (mounted) {
        if (kDebugMode) {
          print(e.toString());
        }
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(userProfileDetailsViewModelProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Complete Your Profile',
          style: MyTypography.SemiBold.copyWith(fontSize: 18),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SafeArea(
        child: Form(
          key: viewModel.formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),

                // Profile Image
                GestureDetector(
                  onTap: () async {
                    try {
                      await viewModel.selectImage();
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(
                          context,
                        ).showSnackBar(SnackBar(content: Text(e.toString())));
                      }
                    }
                  },
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundColor: Colors.grey[200],
                        backgroundImage:
                            viewModel.imageFile != null
                                ? FileImage(viewModel.imageFile!)
                                : (state.avatarUrl.isNotEmpty
                                    ? NetworkImage(state.avatarUrl)
                                        as ImageProvider
                                    : null),
                        child:
                            (viewModel.imageFile == null &&
                                    state.avatarUrl.isEmpty)
                                ? Icon(
                                  Icons.person,
                                  size: 60,
                                  color: Colors.grey[400],
                                )
                                : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.teal[700],
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Full Name
                PrimaryFormField(
                  controller: viewModel.fullNameController,
                  labelText: 'Full Name',
                  hintText: 'Enter your full name',
                  onChanged: (value) {
                    viewModel.updateFullName(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your full name';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Email
                PrimaryFormField(
                  controller: viewModel.emailController,
                  labelText: 'Email',
                  hintText: 'Enter your email address',
                  keyboardType: TextInputType.emailAddress,
                  onChanged: (value) {
                    viewModel.updateEmail(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email';
                    }
                    if (!RegExp(
                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                    ).hasMatch(value)) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Phone Number
                PrimaryFormField(
                  controller: viewModel.phoneNumberController,
                  labelText: 'Phone Number',
                  hintText: 'Enter your phone number',
                  keyboardType: TextInputType.phone,
                  onChanged: (value) {
                    viewModel.updatePhoneNumber(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your phone number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Second Phone Number (Optional)
                PrimaryFormField(
                  controller: viewModel.secondPhoneNumberController,
                  labelText: 'Second Phone Number (Optional)',
                  hintText: 'Enter your second phone number',
                  keyboardType: TextInputType.phone,
                  onChanged: (value) {
                    viewModel.updateSecondPhoneNumber(value);
                  },
                ),

                const SizedBox(height: 16),

                // WhatsApp Number
                PrimaryFormField(
                  controller: viewModel.whatsappNumberController,
                  labelText: 'WhatsApp Number',
                  hintText: 'Enter your WhatsApp number',
                  keyboardType: TextInputType.phone,
                  onChanged: (value) {
                    viewModel.updateWhatsappNumber(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your WhatsApp number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Address
                PrimaryFormField(
                  controller: viewModel.addressController,
                  labelText: 'Address',
                  hintText: 'Enter your address',
                  onChanged: (value) {
                    viewModel.updateAddress(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your address';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // National ID
                PrimaryFormField(
                  controller: viewModel.nationalIdController,
                  labelText: 'National ID',
                  hintText: 'Enter your national ID number',
                  onChanged: (value) {
                    viewModel.updateNationalId(value);
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your national ID';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 40),

                // Location permission button
                // TextButton.icon(
                //   onPressed: () {
                //     _checkLocationPermission();
                //   },
                //   icon: Icon(Icons.my_location, color: Colors.teal[700]),
                //   label: Text(
                //     'Use current location',
                //     style: TextStyle(color: Colors.teal[700]),
                //   ),
                // ),

                // const SizedBox(height: 20),

                // Save Button
                PrimaryButton(
                  text: 'Save Profile',
                  onPressed: () => _saveProfile(),
                  isLoading: state.isUpdatingProfile,
                  height: 50,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
