import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class WelcomeScreen extends ConsumerStatefulWidget {
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen> {
  bool _isClient = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkUserRole();
  }

  Future<void> _checkUserRole() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _isClient = prefs.getBool('is_client') ?? true;
        _isLoading = false;
      });
      if (kDebugMode) {
        print('User role: ${_isClient ? 'Client' : 'Service Provider'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking user role: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToHomeScreen() {
    if (_isClient) {
      context.pushReplacementNamed(RouteConstants.CLIENT_HOME_SCREEN);
    } else {
      context.pushReplacementNamed(RouteConstants.SERVICE_PROVIDER_HOME_SCREEN);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            children: [
              // Logo at the top
              SizedBox(height: screenHeight * 0.08),
              
              // Main illustration
              Expanded(
                child: Center(
                  child: SvgPicture.asset(
                    'assets/svg/welcome.svg',
                    height: screenHeight * 0.35,
                  ),
                ),
              ),
              
              // Welcome text and buttons
              Column(
                children: [
                  Text(
                    'Welcome to BuildMate',
                    style: MyTypography.Bold.copyWith(
                      fontSize: 24,
                      color: const Color(0xFF0A2463), // Dark blue color
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Your profile has been created successfully!',
                    style: MyTypography.Regular.copyWith(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),
                  
                  // Continue to home button
                  _isLoading
                      ? const CircularProgressIndicator()
                      : PrimaryButton(
                          text: 'Continue to Home',
                          width: screenWidth,
                          onPressed: _navigateToHomeScreen,
                        ),
                  
                  SizedBox(height: screenHeight * 0.08),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
