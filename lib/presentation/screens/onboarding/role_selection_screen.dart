import 'package:build_mate/data/shared_preferences/preferences_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class RoleSelectionScreen extends ConsumerWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenHeight = MediaQuery.of(context).size.height;
    final customColors = ref.watch(customColorsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            children: [
              SizedBox(height: screenHeight * 0.06),

              // App logo
              Image.asset('assets/images/logo.png', height: 60),

              SizedBox(height: screenHeight * 0.04),

              // Welcome text - now in orange color
              Text(
                'Welcome to BuildMate',
                style: MyTypography.Bold.copyWith(
                  fontSize: 28,
                  color: orangeColor, // Using the orange color from colors.dart
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Subtitle
              Text(
                'Connect with construction professionals or find projects that match your skills',
                style: MyTypography.Regular.copyWith(
                  fontSize: 16,
                  color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),

              // Reduced gap between subtitle and role cards
              SizedBox(height: screenHeight * 0.01), // Reduced from 0.06
              // Role selection cards
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Client role card
                    _buildRoleCard(
                      context: context,
                      ref: ref,
                      title: 'I need a service',
                      description:
                          'Find skilled professionals for your construction projects',
                      iconPath:
                          'assets/svg/client.svg', // Use an existing SVG from your assets
                      onTap: () {
                        // Save role selection in preferences (true for client)
                        ref
                            .read(preferencesProvider)
                            .setIsClient(isClient: true);

                        // Navigate to sign in as client
                        context.pushNamed(
                          RouteConstants.SIGN_IN_SCREEN,
                          queryParameters: {'role': 'client'},
                        );
                      },
                    ),

                    SizedBox(height: screenHeight * 0.03),

                    // Service provider role card
                    _buildRoleCard(
                      context: context,
                      ref: ref,
                      title: 'I provide services',
                      description:
                          'Showcase your skills and find construction projects',
                      iconPath:
                          'assets/svg/service_provider.svg', // Use an existing SVG from your assets
                      onTap: () {
                        // Save role selection in preferences (false for service provider)
                        ref
                            .read(preferencesProvider)
                            .setIsClient(isClient: false);

                        // Navigate to sign in as service provider
                        context.pushNamed(
                          RouteConstants.SIGN_IN_SCREEN,
                          queryParameters: {'role': 'provider'},
                        );
                      },
                    ),
                  ],
                ),
              ),

              // Skip button at bottom
              SizedBox(height: screenHeight * 0.02),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required BuildContext context,
    required WidgetRef ref,
    required String title,
    required String description,
    required String iconPath,
    required VoidCallback onTap,
  }) {
    final customColors = ref.watch(customColorsProvider);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: customColors.textPrimaryColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Role icon - now with orange background and icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: orangeColor.withAlpha(
                  (0.1 * 255).round(),
                ), // Light orange background
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: SvgPicture.asset(
                  iconPath,
                  width: 30,
                  height: 30,
                  colorFilter: const ColorFilter.mode(
                    orangeColor, // Using orange color for the SVG
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Role text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: MyTypography.SemiBold.copyWith(
                      fontSize: 18,
                      color: customColors.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: MyTypography.Regular.copyWith(
                      fontSize: 14,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.7,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Arrow icon - now in orange
            Icon(
              Icons.arrow_forward_ios,
              color: orangeColor, // Using orange color for the arrow
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
