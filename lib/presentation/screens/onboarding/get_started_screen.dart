import 'package:build_mate/presentation/components/button/plain_button.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/cards/message_card.dart';
import 'package:build_mate/presentation/components/cards/service_worker_card.dart';
import 'package:build_mate/presentation/components/cards/user_profile_card.dart';
import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/components/tiles/service_tile.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class GetStartedScreen extends StatelessWidget {
  const GetStartedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Components')),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            PrimaryButton(
              text: 'Log in',
              width: getScreenWidth(context),
              isLoading: false,
              onPressed: () {
                context.pushNamed(
                  RouteConstants.RATING_CLIENT_SCREEN,
                  extra: {
                    'clientName': 'Tatenda Kabike',
                    'clientImageUrl': 'assets/images/profile_pic.png',
                  },
                );
              },
            ),

            PlainButton(
              text: 'Plain Button',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(RouteConstants.PROFILE_SCREEN);
              },
            ),

            PlainButton(
              text: 'Client Home Screen',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(RouteConstants.CLIENT_HOME_SCREEN);
              },
            ),

            PlainButton(
              text: 'Service Provider Home Screen',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(RouteConstants.SERVICE_PROVIDER_HOME_SCREEN);
              },
            ),

            PlainButton(
              text: 'Post a job',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(RouteConstants.POST_JOB_SCREEN);
              },
            ),
            PlainButton(
              text: 'Search categories',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(RouteConstants.SERVICE_CATEGORY_SCREEN);
              },
            ),

            PlainButton(
              text: 'View Job Description',
              width: getScreenWidth(context),
              onPressed: () {
                context.pushNamed(
                  RouteConstants.JOB_OFFER_DETAILS_SCREEN,
                  extra: {
                    'clientName': 'Saydul Moon',
                    'clientImageUrl': 'assets/images/profile_pic.png',
                    'jobTitle': 'AC installation',
                    'location': 'Benin City',
                    'serviceTimes': [
                      'Sunday 09:00 AM - 11:00AM, 02:00 PM - 04:00 PM',
                      'Monday 08:00 AM - 10:00AM',
                    ],
                    'description':
                        'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco.',
                  },
                );
              },
            ),

            ServiceWorkerCard(
              name: 'Jacob Jones',
              profession: 'Electrician',
              location: 'New York',
              rating: 4.9,
              totalRatings: 40,
              imageUrl: 'assets/images/profile_pic.png',
              activeOffers: 1,
              price: 250.00,
              certifications: ['cert1', 'cert2', 'cert3'],
              onTap: () {
                // Handle card tap
              },
            ),

            MessageCard(
              senderName: 'Brooklyn Simmons',
              message: "Sorry I'll be lateabout 15 mi...",
              time: '08:40 AM',
              avatarUrl: 'assets/images/profile_pic.png',
              onTap: () {
                // Handle message tap
              },
            ),

            UserProfileCard(
              name: 'John Doe',
              location: 'Benin City',
              avatarUrl: 'assets/images/profile_pic.png',
              rating: 4.9,
              totalRatings: 40,
              isVerified: true,
              isActive: true,
              onChatPressed: () {
                // Handle chat button press
              },
              onCallPressed: () {
                // Handle call button press
              },
            ),
            ServiceTile(
              serviceName: 'Painter',
              iconPath: 'assets/images/profile_pic.png',
              onTap: () {
                // Handle tile tap
              },
            ),
          ],
        ),
      ),
    );
  }
}
