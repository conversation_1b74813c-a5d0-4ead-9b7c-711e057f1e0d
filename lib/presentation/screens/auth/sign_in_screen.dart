import 'package:build_mate/presentation/screens/auth/mobile/sign_in_screen_mobile.dart';
import 'package:build_mate/presentation/screens/auth/web/sign_in_screen_web.dart';
import 'package:build_mate/presentation/view_models/auth/signin_view_model.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SignInScreen extends ConsumerWidget {
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewModel = ref.watch(signinViewModelProvider.notifier);
    final state = ref.watch(signinViewModelProvider);
    final size = MediaQuery.sizeOf(context);

    // Determine if we're on a large screen (web or tablet)
    final isLargeScreen = kIsWeb || size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: <PERSON><PERSON><PERSON>(
        child:
            isLargeScreen
                ? SignInScreenWeb(viewModel: viewModel, state: state)
                : SignInScreenMobile(viewModel: viewModel, state: state),
      ),
    );
  }
}
