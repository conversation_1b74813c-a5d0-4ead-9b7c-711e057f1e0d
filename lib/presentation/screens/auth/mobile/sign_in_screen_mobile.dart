import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/button/social_login_button.dart';
import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/presentation/state/signin_flow_state.dart';
import 'package:build_mate/presentation/view_models/auth/signin_view_model.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SignInScreenMobile extends ConsumerWidget {
  final SigninViewModel viewModel;
  final SigninFlowState state;

  const SignInScreenMobile({
    super.key,
    required this.viewModel,
    required this.state,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customColors = ref.watch(customColorsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: _buildLoginForm(context, customColors),
    );
  }

  Widget _buildLoginForm(BuildContext context, CustomColors customColors) {
    return Form(
      key: viewModel.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 40),
          Text(
            'Log in to your',
            style: MyTypography.Bold.copyWith(
              fontSize: 28,
              color: customColors.textPrimaryColor,
            ),
          ),
          Text(
            'BuildMate',
            style: MyTypography.Bold.copyWith(
              fontSize: 28,
              color: orangeColor, // Keep brand color consistent
            ),
          ),
          const SizedBox(height: 32),

          // Email Field
          PrimaryFormField(
            controller: viewModel.emailController,
            labelText: 'Email',
            hintText: '<EMAIL>',
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) => viewModel.updateEmail(value),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // Password Field
          PrimaryFormField(
            controller: viewModel.passwordController,
            labelText: 'Password',
            hintText: '••••••',
            obscureText: viewModel.obscurePassword,
            onChanged: (value) => viewModel.updatePassword(value),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
            suffixIcon: IconButton(
              icon: Icon(
                viewModel.obscurePassword
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: customColors.textPrimaryColor.withValues(alpha: 0.6),
              ),
              onPressed: () => viewModel.toggleObscurePassword(),
            ),
          ),
          const SizedBox(height: 16),

          // Remember Me Checkbox
          Row(
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: viewModel.rememberMe,
                  onChanged: (value) => viewModel.toggleRememberMe(value),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: BorderSide(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.3),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Remember me',
                style: MyTypography.Regular.copyWith(
                  fontSize: 14,
                  color: customColors.textPrimaryColor,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => viewModel.handleResetPassword(context: context),
                child: Text(
                  'Forgot Password?',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: orangeColor, // Keep brand color consistent
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Login Button
          PrimaryButton(
            text: 'Log in',
            onPressed: () => viewModel.handleSignIn(context: context),
            height: 50.0,
            isLoading: state.isLoggingIn,
          ),
          const SizedBox(height: 24),

          // Or Divider
          Row(
            children: [
              Expanded(
                child: Divider(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.3),
                  thickness: 0.5,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'or continue with',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: customColors.textPrimaryColor.withValues(alpha: 0.6),
                  ),
                ),
              ),
              Expanded(
                child: Divider(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.3),
                  thickness: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Social Login Buttons
          Column(
            children: [
              SocialLoginButton.google(
                onPressed: () => viewModel.handleGoogleSignIn(context: context),
                width: double.infinity,
                context: context,
              ),
              const SizedBox(height: 12),
              SocialLoginButton.apple(
                onPressed: () => viewModel.handleAppleSignIn(context: context),
                width: double.infinity,
                context: context,
              ),
              const SizedBox(height: 12),
              SocialLoginButton.facebook(
                onPressed:
                    () => viewModel.handleFacebookSignIn(context: context),
                width: double.infinity,
                context: context,
              ),
            ],
          ),
          const SizedBox(height: 32),

          // Sign Up Link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Don\'t have an account? ',
                style: MyTypography.Regular.copyWith(
                  fontSize: 14,
                  color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                ),
              ),
              GestureDetector(
                onTap: () => viewModel.navigateToSignUp(context: context),
                child: Text(
                  'Sign Up',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: orangeColor, // Keep brand color consistent
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
