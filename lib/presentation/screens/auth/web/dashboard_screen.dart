import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/auth/web/dashboard_content.dart';

class DashboardScreen extends StatefulWidget {
  final Widget? child;
  
  const DashboardScreen({super.key, this.child});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  
  @override
  void initState() {
    super.initState();
    
    // Set initial index based on current route
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final String currentPath = GoRouterState.of(context).matchedLocation;
     
        if (currentPath.contains('branches')) {
          setState(() => _selectedIndex = 1);
        } else if (currentPath.contains('users')) {
          setState(() => _selectedIndex = 2);
        } else if (currentPath.contains('inventory')) {
          setState(() => _selectedIndex = 3);
        }
      
    });
  }

  NavigationRailDestination _buildNavDestination(IconData icon, String label) {
    return NavigationRailDestination(
      icon: Icon(icon, color: Colors.white70),
      selectedIcon: Icon(icon, color: Colors.white),
      label: Text(
        label,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Row(
          children: [
            // Standard NavigationRail
            NavigationRail(
              selectedIndex: _selectedIndex,
              backgroundColor: const Color(0xFF1E2A4A),
              extended: true,
              minWidth: 72,
              minExtendedWidth: 240,
              leading: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Icon(Icons.store, color: Colors.white, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Simon',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              destinations: [
                _buildNavDestination(Icons.dashboard, 'Dashboard'),
                _buildNavDestination(Icons.shop, 'Branches'),
                _buildNavDestination(Icons.people, 'Users / Staff'),
                _buildNavDestination(Icons.list_alt, 'Inventory'),
                _buildNavDestination(Icons.payment, 'Settings'),
                _buildNavDestination(Icons.announcement, 'Promote'),
                _buildNavDestination(Icons.local_offer, 'Orders'),
                _buildNavDestination(Icons.logout, 'Logout'),
              ],
              onDestinationSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                });
                
                // Handle navigation based on menu item index
                switch (index) {
                  case 0: // Dashboard
                    context.goNamed(RouteConstants.HARDWARE_SHOP_DASHBOARD);
                    break;
                  case 1: // Branches
                    context.goNamed(RouteConstants.BRANCHES_SCREEN);
                    break;
                  case 2: // Users
                    context.goNamed(RouteConstants.USERS_SCREEN);
                    break;
                  case 3: // Inventory
                    context.goNamed(RouteConstants.INVENTORY_SCREEN);
                    break;
                  case 4: // Settings
                    // Add navigation when route is available
                    break;
                  case 5: // Promote
                    // Add navigation when route is available
                    break;
                  case 6: // Orders
                    // Add navigation when route is available
                    break;
                  case 7: // Logout
                    // Handle logout logic here
                    break;
                }
              },
              labelType: NavigationRailLabelType.none,
              useIndicator: true,
              indicatorColor: Colors.white.withAlpha((0.1 * 255).round()),
            ),
            
            // Main content
            Expanded(
              child: widget.child ?? const DashboardContent(),
            ),
          ],
        ),
      ),
    );
  }
}
