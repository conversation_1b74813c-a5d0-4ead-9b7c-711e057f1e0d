import 'dart:async';

import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/view_models/web/branches_view_model.dart';
import 'package:build_mate/data/dto/responses_dto/branch_response.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:flutter/foundation.dart';

class BranchesScreen extends ConsumerStatefulWidget {
  const BranchesScreen({super.key});

  @override
  ConsumerState<BranchesScreen> createState() => _BranchesScreenState();
}

class _BranchesScreenState extends ConsumerState<BranchesScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Controllers for branch form fields
  final TextEditingController _branchNameController = TextEditingController();
  final TextEditingController _branchAddressController =
      TextEditingController();
  final TextEditingController _branchCityController = TextEditingController();
  final TextEditingController _branchPhoneController = TextEditingController();
  final TextEditingController _branchSecondPhoneController =
      TextEditingController();
  final TextEditingController _branchEmailController = TextEditingController();
  final TextEditingController _branchSecondEmailController =
      TextEditingController();

  final TextEditingController dialogBranchLatController =
      TextEditingController();
  final TextEditingController dialogBranchLngController =
      TextEditingController();

  final TextEditingController dialogBranchNameController =
      TextEditingController();
  final TextEditingController dialogBranchAddressController =
      TextEditingController();
  final TextEditingController dialogBranchCityController =
      TextEditingController();
  final TextEditingController dialogBranchPhoneController =
      TextEditingController();
  final TextEditingController dialogBranchEmailController =
      TextEditingController();
  final TextEditingController dialogBranchPhoneTwoController =
      TextEditingController();
  final TextEditingController dialogBranchEmailTwoController =
      TextEditingController();
  // Selected branch for details view
  BranchResponse? _selectedBranch;

  // Address suggestions
  final List<String> _addressSuggestions = [];
  final bool _isLoadingAddressSuggestions = false;
  final FocusNode _addressFocusNode = FocusNode();
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    // No need to fetch branches here as the ViewModel constructor does it
  }

  @override
  void dispose() {
    _branchNameController.dispose();
    _branchAddressController.dispose();
    _branchCityController.dispose();
    _branchPhoneController.dispose();
    _branchSecondPhoneController.dispose();
    _branchEmailController.dispose();
    _branchSecondEmailController.dispose();
    _addressFocusNode.dispose();
    dialogBranchNameController.dispose();
    dialogBranchAddressController.dispose();
    dialogBranchCityController.dispose();
    dialogBranchPhoneController.dispose();
    dialogBranchEmailController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Method to show edit branch dialog
  void _showEditBranchDialog(BranchResponse branch) {
    if (kDebugMode) {
      print(
        'Opening edit dialog for branch: ${branch.branchName} (ID: ${branch.id})',
      );
    }

    // Populate controllers with branch data
    _branchNameController.text = branch.branchName ?? '';
    _branchAddressController.text = branch.address ?? '';
    _branchCityController.text = branch.city ?? '';

    // Reset secondary controllers
    _branchSecondPhoneController.text = '';
    _branchSecondEmailController.text = '';

    // Get phone numbers if available
    if (branch.branchPhonenumbers != null &&
        branch.branchPhonenumbers!.isNotEmpty) {
      _branchPhoneController.text =
          branch.branchPhonenumbers!.first.phonenumber ?? '';

      // If there's a second phone number
      if (branch.branchPhonenumbers!.length > 1) {
        _branchSecondPhoneController.text =
            branch.branchPhonenumbers![1].phonenumber ?? '';
      }
    } else {
      _branchPhoneController.text = '';
    }

    // Get emails if available
    if (branch.branchEmails != null && branch.branchEmails!.isNotEmpty) {
      _branchEmailController.text = branch.branchEmails!.first.email ?? '';

      // If there's a second email
      if (branch.branchEmails!.length > 1) {
        _branchSecondEmailController.text = branch.branchEmails![1].email ?? '';
      }
    } else {
      _branchEmailController.text = '';
    }

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissal by tapping outside
      builder:
          (dialogContext) => Consumer(
            builder: (context, ref, child) {
              // Watch the isUpdatingDetails state
              final isUpdating = ref.watch(
                branchViewModelProivder.select(
                  (state) => state.isUpdatingDetails,
                ),
              );

              return PopScope(
                // Prevent back button from closing dialog during update
                canPop: !isUpdating,
                child: Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Container(
                    width:
                        MediaQuery.of(context).size.width *
                        0.5, // 50% of screen width
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Dialog title
                        const Text(
                          'Edit Branch',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Form content
                        Form(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                controller: _branchNameController,
                                decoration: const InputDecoration(
                                  labelText: 'Branch Name*',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Branch name is required';
                                  }
                                  return null;
                                },
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchAddressController,
                                decoration: const InputDecoration(
                                  labelText: 'Address*',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Address is required';
                                  }
                                  return null;
                                },
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchCityController,
                                decoration: const InputDecoration(
                                  labelText: 'City*',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'City is required';
                                  }
                                  return null;
                                },
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),

                              // Phone numbers section
                              const Text(
                                'Contact Information',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),

                              TextFormField(
                                controller: _branchPhoneController,
                                decoration: const InputDecoration(
                                  labelText: 'Primary Phone Number',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.phone,
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchSecondPhoneController,
                                decoration: const InputDecoration(
                                  labelText:
                                      'Secondary Phone Number (Optional)',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.phone,
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchEmailController,
                                decoration: const InputDecoration(
                                  labelText: 'Primary Email',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.emailAddress,
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchSecondEmailController,
                                decoration: const InputDecoration(
                                  labelText: 'Secondary Email (Optional)',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.emailAddress,
                                enabled: !isUpdating,
                              ),
                              const SizedBox(height: 12),
                              const Text(
                                '* Required fields',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            if (isUpdating)
                              const Expanded(
                                child: Center(
                                  child: Column(
                                    children: [
                                      CircularProgressIndicator(),
                                      SizedBox(height: 8),
                                      Text(
                                        'Saving changes...',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            else ...[
                              TextButton(
                                onPressed: () => Navigator.pop(dialogContext),
                                child: const Text('Cancel'),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: orangeColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                ),
                                onPressed: () {
                                  if (_formKey.currentState!.validate()) {
                                    _handleBranchUpdate(branch, dialogContext);
                                  }
                                },
                                child: const Text('Save Changes'),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
    );
  }

  // Method to handle branch update
  void _handleBranchUpdate(
    BranchResponse branch,
    BuildContext dialogContext,
  ) async {
    if (kDebugMode) {
      print('Handling branch update for branch ID: ${branch.id}');
    }

    final viewModel = ref.read(branchViewModelProivder.notifier);

    // Create updated branch data
    final updatedBranch = {
      'id': branch.id,
      'branch_name': _branchNameController.text,
      'address': _branchAddressController.text,
      'city': _branchCityController.text,
      'phone': _branchPhoneController.text,
      'second_phone': _branchSecondPhoneController.text,
      'email': _branchEmailController.text,
      'second_email': _branchSecondEmailController.text,
    };

    if (kDebugMode) {
      print('Updated branch data: $updatedBranch');
    }

    // Validate data in the ViewModel
    if (!viewModel.validateBranchData(updatedBranch)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all required fields')),
      );
      return;
    }

    try {
      // Call view model to update branch
      await viewModel.updateBranch(updatedBranch);

      // Update selected branch if it's the one being edited
      if (_selectedBranch?.id == branch.id) {
        // Get updated branch from ViewModel
        final updatedSelectedBranch = viewModel.getBranchById(branch.id ?? 0);

        if (updatedSelectedBranch != null) {
          setState(() {
            _selectedBranch = updatedSelectedBranch;
          });
        }
      }

      // Only close the dialog after successful update
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Branch updated successfully')),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating branch: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update branch: ${e.toString()}')),
        );
      }
    }
  }

  // Method to show add branch dialog
  void _showAddBranchDialog() {
    final viewModel = ref.read(branchViewModelProivder.notifier);
    // Clear all controllers
    _branchNameController.clear();
    _branchAddressController.clear();
    _branchCityController.clear();
    _branchPhoneController.clear();
    _branchSecondPhoneController.clear();
    _branchEmailController.clear();
    _branchSecondEmailController.clear();

    if (kDebugMode) {
      print('Opening add branch dialog');
    }

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissal by tapping outside
      builder:
          (dialogContext) => Consumer(
            builder: (context, ref, child) {
              // Watch the isAddingBranch state
              final isAdding = ref.watch(
                branchViewModelProivder.select((state) => state.isAddingBranch),
              );

              return PopScope(
                // Prevent back button from closing dialog during add
                canPop: !isAdding,
                child: Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Container(
                    width:
                        MediaQuery.of(context).size.width *
                        0.5, // 50% of screen width
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Dialog title
                        const Text(
                          'Add New Branch',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Form content
                        Form(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                controller: _branchNameController,
                                decoration: const InputDecoration(
                                  labelText: 'Branch Name*',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setBranchName(value);
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Branch name is required';
                                  }
                                  return null;
                                },
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 16),
                              PrimaryFormField(
                                controller: _branchAddressController,
                                labelText: 'Address',
                                hintText: 'Start typing to search address',
                                focusNode: _addressFocusNode,
                                onChanged: (value) {
                                  // Cancel any previous timer
                                  _debounceTimer?.cancel();

                                  viewModel.setBranchAddress(value);

                                  // Set a new timer
                                  _debounceTimer = Timer(
                                    const Duration(milliseconds: 500),
                                    () {
                                      // Use the view model to handle search
                                      viewModel.searchDropOffAddress(
                                        place: value,
                                      );
                                    },
                                  );
                                },
                                // Add a suffix icon to show loading state
                                // suffixIcon:
                                //     !state.isLoading
                                //         ? const SizedBox(
                                //           height: 20,
                                //           width: 20,
                                //           child: Padding(
                                //             padding: EdgeInsets.all(8.0),
                                //             child: CircularProgressIndicator(
                                //               strokeWidth: 2,
                                //               color: orangeColor,
                                //             ),
                                //           ),
                                //         )
                                //         : null,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter branch address';
                                  }
                                  return null;
                                },
                              ),
                              Consumer(
                                builder: (context, ref, child) {
                                  final addressState = ref.watch(
                                    branchViewModelProivder,
                                  );
                                  final isLoading = addressState.isLoading;
                                  final suggestions =
                                      addressState.addressPlacesList;

                                  // Convert suggestions to text
                                  final addressTexts =
                                      suggestions
                                          .map(
                                            (suggestion) =>
                                                suggestion
                                                    .placePrediction
                                                    ?.text
                                                    ?.text ??
                                                '',
                                          )
                                          .where((text) => text.isNotEmpty)
                                          .toList();

                                  if (addressTexts.isEmpty && !isLoading) {
                                    return const SizedBox.shrink();
                                  }

                                  return Container(
                                    margin: const EdgeInsets.only(top: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(
                                        color: Colors.grey.shade300,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(
                                            (0.1 * 255).round(),
                                          ),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child:
                                        isLoading
                                            ? const Padding(
                                              padding: EdgeInsets.all(12),
                                              child: Center(
                                                child: SizedBox(
                                                  width: 20,
                                                  height: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                      ),
                                                ),
                                              ),
                                            )
                                            : ListView.builder(
                                              shrinkWrap: true,
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount: addressTexts.length,
                                              itemBuilder: (context, index) {
                                                final suggestion =
                                                    suggestions[index];
                                                final text =
                                                    addressTexts[index];
                                                final placeId =
                                                    suggestion
                                                        .placePrediction
                                                        ?.placeId ??
                                                    '';

                                                return InkWell(
                                                  onTap: () {
                                                    // Use view model to handle selection
                                                    viewModel
                                                        .selectAddressSuggestion(
                                                          text,
                                                          placeId,
                                                        );

                                                    viewModel.setBranchAddress(
                                                      text,
                                                    );

                                                    viewModel.setBranchAddress(
                                                      text,
                                                    );

                                                    // Update the text field with selected address
                                                    _branchAddressController
                                                        .text = text;

                                                    // Get location data for the selected place
                                                    viewModel
                                                        .getLocationByPlaceId(
                                                          placeId: placeId,
                                                        );

                                                    // Clear suggestions list by resetting the state
                                                    viewModel
                                                        .clearAddressSuggestions();

                                                    // Update dialog state to reflect changes
                                                    // setDialogState(() {});
                                                  },
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          vertical: 12,
                                                          horizontal: 16,
                                                        ),
                                                    child: Text(text),
                                                  ),
                                                );
                                              },
                                            ),
                                  );
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchCityController,
                                decoration: const InputDecoration(
                                  labelText: 'City*',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setBranchCity(value);
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'City is required';
                                  }
                                  return null;
                                },
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 16),

                              // Phone numbers section
                              const Text(
                                'Contact Information',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),

                              TextFormField(
                                controller: _branchPhoneController,
                                decoration: const InputDecoration(
                                  labelText: 'Primary Phone Number*',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setPhoneNumber(value);
                                },
                                keyboardType: TextInputType.phone,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Primary phone number is required';
                                  }
                                  return null;
                                },
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchSecondPhoneController,
                                decoration: const InputDecoration(
                                  labelText:
                                      'Secondary Phone Number (Optional)',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setBranchPhonenumberTwo(value);
                                },
                                keyboardType: TextInputType.phone,
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchEmailController,
                                decoration: const InputDecoration(
                                  labelText: 'Primary Email*',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setBranchEmailOne(value);
                                },
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Primary email is required';
                                  }
                                  return null;
                                },
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _branchSecondEmailController,
                                decoration: const InputDecoration(
                                  labelText: 'Secondary Email (Optional)',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (value) {
                                  viewModel.setBranchEmailTwo(value);
                                },
                                keyboardType: TextInputType.emailAddress,
                                enabled: !isAdding,
                              ),
                              const SizedBox(height: 12),
                              const Text(
                                '* Required fields',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            if (isAdding)
                              const Expanded(
                                child: Center(
                                  child: Column(
                                    children: [
                                      CircularProgressIndicator(),
                                      SizedBox(height: 8),
                                      Text(
                                        'Adding branch...',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            else ...[
                              TextButton(
                                onPressed: () => Navigator.pop(dialogContext),
                                child: const Text('Cancel'),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: orangeColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                ),
                                onPressed: () {
                                  if (_formKey.currentState!.validate()) {
                                    _handleAddBranch(dialogContext);
                                  }
                                },
                                child: const Text('Add Branch'),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
    );
  }

  // Method to handle adding a new branch
  void _handleAddBranch(BuildContext dialogContext) async {
    if (kDebugMode) {
      print('Handling add branch');
    }

    final viewModel = ref.read(branchViewModelProivder.notifier);

    // Create branch data from form fields
    final branchData = {
      'branch_name': _branchNameController.text,
      'address': _branchAddressController.text,
      'city': _branchCityController.text,
      'phone': _branchPhoneController.text,
      'second_phone': _branchSecondPhoneController.text,
      'email': _branchEmailController.text,
      'second_email': _branchSecondEmailController.text,
    };

    if (kDebugMode) {
      print('New branch data: $branchData');
    }

    // Validate data in the ViewModel
    if (!viewModel.validateBranchData(branchData)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all required fields')),
      );
      return;
    }

    try {
      // Set isAddingBranch to true to show the loading indicator in the dialog
      viewModel.setIsAddingBranch(true);

      // Call view model to add branch
      await viewModel.addBranch();

      // Set isAddingBranch to false after successful addition
      viewModel.setIsAddingBranch(false);

      // Close the dialog after successful addition
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Branch added successfully')),
        );
      }
    } catch (e) {
      // Set isAddingBranch to false in case of error
      viewModel.setIsAddingBranch(false);

      if (kDebugMode) {
        print('Error adding branch: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to add branch: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(branchViewModelProivder);
    final viewModel = ref.watch(branchViewModelProivder.notifier);
    return Container(
      color: const Color(0xFFF9FAFC),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation header
          Padding(padding: const EdgeInsets.all(24), child: _buildHeader()),

          const Divider(height: 1),

          // Branches content - now placed directly below the header
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Branch list header with title and add button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'All Branches',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          if (kDebugMode) {
                            print('Add Branch button clicked');
                          }
                          _showAddBranchDialog();
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('Add Branch'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: orangeColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Branch content
                  if (state.isLoading)
                    const Expanded(
                      child: Center(child: CircularProgressIndicator()),
                    )
                  else if (state.errorMessage != null)
                    Expanded(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              state.errorMessage ?? 'An error occurred',
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => viewModel.fetchBranches(),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      ),
                    )
                  else if (state.branches.isEmpty)
                    const Expanded(
                      child: Center(
                        child: Text(
                          'No branches found. Add your first branch!',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    )
                  else
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left side - Branches table (70% width)
                          Expanded(
                            flex: 7,
                            child: _buildBranchesTable(state.branches),
                          ),

                          // Right side - Branch details card (30% width)
                          if (_selectedBranch != null) ...[
                            const SizedBox(width: 24),
                            Expanded(
                              flex: 3,
                              child: _buildBranchDetailsCard(_selectedBranch!),
                            ),
                          ],
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBranchesTable(List<BranchResponse> branches) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((0.1 * 255).round()),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: DataTable(
          columnSpacing: 20,
          headingRowColor: WidgetStateProperty.all(Colors.grey.shade50),
          columns: const [
            DataColumn(
              label: Text(
                'Branch Name',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'Address',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'City',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'Actions',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
          rows:
              branches.map((branch) {
                return DataRow(
                  selected: _selectedBranch?.id == branch.id,
                  onSelectChanged: (_) {
                    setState(() {
                      _selectedBranch = branch;
                    });
                  },
                  cells: [
                    DataCell(Text(branch.branchName ?? 'N/A')),
                    DataCell(Text(branch.address ?? 'N/A')),
                    DataCell(Text(branch.city ?? 'N/A')),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit, color: Colors.blue),
                            onPressed: () => _showEditBranchDialog(branch),
                            tooltip: 'Edit Branch',
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _showDeleteBranchDialog(branch),
                            tooltip: 'Delete Branch',
                          ),
                          IconButton(
                            icon: const Icon(
                              Icons.visibility,
                              color: Colors.green,
                            ),
                            onPressed: () {
                              setState(() {
                                _selectedBranch = branch;
                              });
                            },
                            tooltip: 'View Details',
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildBranchDetailsCard(BranchResponse branch) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((0.1 * 255).round()),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with close button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: orangeColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    branch.branchName ?? 'Branch Details',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _selectedBranch = null;
                    });
                  },
                  tooltip: 'Close Details',
                ),
              ],
            ),
          ),

          // Branch details content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Location section
                  _buildDetailSection('Location', Icons.location_on, [
                    _buildDetailItem('Address', branch.address ?? 'N/A'),
                    _buildDetailItem('City', branch.city ?? 'N/A'),
                  ]),

                  const Divider(height: 32),

                  // Contact section - Phone numbers
                  _buildDetailSection(
                    'Phone Numbers',
                    Icons.phone,
                    branch.branchPhonenumbers != null &&
                            branch.branchPhonenumbers!.isNotEmpty
                        ? branch.branchPhonenumbers!
                            .map(
                              (phone) => _buildDetailItem(
                                branch.branchPhonenumbers!.indexOf(phone) == 0
                                    ? 'Primary'
                                    : 'Secondary',
                                phone.phonenumber ?? 'N/A',
                                actionIcon: Icons.call,
                                onAction: () {},
                              ),
                            )
                            .toList()
                        : [
                          _buildDetailItem(
                            'Phone',
                            'No phone numbers available',
                          ),
                        ],
                  ),

                  const Divider(height: 32),

                  // Contact section - Emails
                  _buildDetailSection(
                    'Email Addresses',
                    Icons.email,
                    branch.branchEmails != null &&
                            branch.branchEmails!.isNotEmpty
                        ? branch.branchEmails!
                            .map(
                              (email) => _buildDetailItem(
                                branch.branchEmails!.indexOf(email) == 0
                                    ? 'Primary'
                                    : 'Secondary',
                                email.email ?? 'N/A',
                                actionIcon: Icons.mail,
                                onAction: () {},
                              ),
                            )
                            .toList()
                        : [
                          _buildDetailItem(
                            'Email',
                            'No email addresses available',
                          ),
                        ],
                  ),

                  const SizedBox(height: 24),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton.icon(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: orangeColor,
                        ),
                        icon: const Icon(Icons.edit),
                        label: const Text('Edit'),
                        onPressed: () => _showEditBranchDialog(branch),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.delete),
                        label: const Text('Delete'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () => _showDeleteBranchDialog(branch),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, IconData icon, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: orangeColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...items,
      ],
    );
  }

  Widget _buildDetailItem(
    String label,
    String value, {
    IconData? actionIcon,
    VoidCallback? onAction,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          if (actionIcon != null && onAction != null)
            IconButton(
              icon: Icon(actionIcon, size: 20, color: orangeColor),
              onPressed: onAction,
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
              tooltip: 'Contact via $label',
            ),
        ],
      ),
    );
  }

  // void _showBranchFormDialog() {
  //   final viewModel = ref.read(branchViewModelProivder.notifier);
  //   final state = ref.watch(branchViewModelProivder);

  //   showDialog(
  //     context: context,
  //     builder:
  //         (dialogContext) => StatefulBuilder(
  //           builder: (context, setDialogState) {
  //             return AlertDialog(
  //               backgroundColor: Colors.white,
  //               title: Text(
  //                 'Add Branch',
  //                 style: const TextStyle(
  //                   fontSize: 20,
  //                   fontWeight: FontWeight.bold,
  //                 ),
  //               ),
  //               content: SizedBox(
  //                 width: 600,
  //                 child: SingleChildScrollView(
  //                   child: Column(
  //                     mainAxisSize: MainAxisSize.min,
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       PrimaryFormField(
  //                         controller: dialogBranchNameController,
  //                         labelText: 'Branch Name',
  //                         hintText: 'Enter branch name',
  //                         onChanged: (value) {
  //                           viewModel.setBranchName(value);
  //                         },
  //                         validator: (value) {
  //                           if (value == null || value.isEmpty) {
  //                             return 'Please enter branch name';
  //                           }
  //                           return null;
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),
  //                       // Address field with autocomplete
  //                       Column(
  //                         crossAxisAlignment: CrossAxisAlignment.start,
  //                         children: [
  //                           PrimaryFormField(
  //                             controller: dialogBranchAddressController,
  //                             labelText: 'Address',
  //                             hintText: 'Start typing to search address',
  //                             focusNode: _addressFocusNode,
  //                             onChanged: (value) {
  //                               // Cancel any previous timer
  //                               _debounceTimer?.cancel();
  //                               // Set a new timer
  //                               _debounceTimer = Timer(
  //                                 const Duration(milliseconds: 500),
  //                                 () {
  //                                   // Use the view model to handle search
  //                                   viewModel.searchDropOffAddress(
  //                                     place: value,
  //                                   );
  //                                 },
  //                               );
  //                             },
  //                             // Add a suffix icon to show loading state
  //                             suffixIcon:
  //                                 state.isLoading
  //                                     ? const SizedBox(
  //                                       height: 20,
  //                                       width: 20,
  //                                       child: Padding(
  //                                         padding: EdgeInsets.all(8.0),
  //                                         child: CircularProgressIndicator(
  //                                           strokeWidth: 2,
  //                                           color: orangeColor,
  //                                         ),
  //                                       ),
  //                                     )
  //                                     : null,
  //                             validator: (value) {
  //                               if (value == null || value.isEmpty) {
  //                                 return 'Please enter branch address';
  //                               }
  //                               return null;
  //                             },
  //                           ),
  //                           // Address suggestions
  //                           Consumer(
  //                             builder: (context, ref, child) {
  //                               final addressState = ref.watch(
  //                                 branchViewModelProivder,
  //                               );
  //                               final isLoading = addressState.isLoading;
  //                               final suggestions =
  //                                   addressState.addressPlacesList;

  //                               // Convert suggestions to text
  //                               final addressTexts =
  //                                   suggestions
  //                                       .map(
  //                                         (suggestion) =>
  //                                             suggestion
  //                                                 .placePrediction
  //                                                 ?.text
  //                                                 ?.text ??
  //                                             '',
  //                                       )
  //                                       .where((text) => text.isNotEmpty)
  //                                       .toList();

  //                               if (addressTexts.isEmpty && !isLoading) {
  //                                 return const SizedBox.shrink();
  //                               }

  //                               return Container(
  //                                 margin: const EdgeInsets.only(top: 4),
  //                                 decoration: BoxDecoration(
  //                                   color: Colors.white,
  //                                   border: Border.all(
  //                                     color: Colors.grey.shade300,
  //                                   ),
  //                                   borderRadius: BorderRadius.circular(8),
  //                                   boxShadow: [
  //                                     BoxShadow(
  //                                       color: Colors.black.withOpacity(0.1),
  //                                       blurRadius: 4,
  //                                       offset: const Offset(0, 2),
  //                                     ),
  //                                   ],
  //                                 ),
  //                                 child:
  //                                     isLoading
  //                                         ? const Padding(
  //                                           padding: EdgeInsets.all(12),
  //                                           child: Center(
  //                                             child: SizedBox(
  //                                               width: 20,
  //                                               height: 20,
  //                                               child:
  //                                                   CircularProgressIndicator(
  //                                                     strokeWidth: 2,
  //                                                   ),
  //                                             ),
  //                                           ),
  //                                         )
  //                                         : ListView.builder(
  //                                           shrinkWrap: true,
  //                                           physics:
  //                                               const NeverScrollableScrollPhysics(),
  //                                           itemCount: addressTexts.length,
  //                                           itemBuilder: (context, index) {
  //                                             final suggestion =
  //                                                 suggestions[index];
  //                                             final text = addressTexts[index];
  //                                             final placeId =
  //                                                 suggestion
  //                                                     .placePrediction
  //                                                     ?.placeId ??
  //                                                 '';

  //                                             return InkWell(
  //                                               onTap: () {
  //                                                 // Use view model to handle selection
  //                                                 viewModel
  //                                                     .selectAddressSuggestion(
  //                                                       text,
  //                                                       placeId,
  //                                                     );

  //                                                 viewModel.setBranchAddress(
  //                                                   text,
  //                                                 );

  //                                                 // Update the text field with selected address
  //                                                 _branchAddressController
  //                                                     .text = text;

  //                                                 // Get location data for the selected place
  //                                                 viewModel
  //                                                     .getLocationByPlaceId(
  //                                                       placeId: placeId,
  //                                                     );

  //                                                 // Clear suggestions list by resetting the state
  //                                                 viewModel
  //                                                     .clearAddressSuggestions();

  //                                                 // Update dialog state to reflect changes
  //                                                 setDialogState(() {});
  //                                               },
  //                                               child: Padding(
  //                                                 padding:
  //                                                     const EdgeInsets.symmetric(
  //                                                       vertical: 12,
  //                                                       horizontal: 16,
  //                                                     ),
  //                                                 child: Text(text),
  //                                               ),
  //                                             );
  //                                           },
  //                                         ),
  //                               );
  //                             },
  //                           ),
  //                         ],
  //                       ),
  //                       const SizedBox(height: 16),
  //                       PrimaryFormField(
  //                         controller: dialogBranchCityController,
  //                         labelText: 'City',
  //                         hintText: 'Enter city',
  //                         onChanged: (value) {
  //                           viewModel.setBranchCity(value);
  //                         },
  //                         validator: (value) {
  //                           if (value == null || value.isEmpty) {
  //                             return 'Please enter city';
  //                           }
  //                           return null;
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),
  //                       Row(
  //                         children: [
  //                           Expanded(
  //                             child: Column(
  //                               crossAxisAlignment: CrossAxisAlignment.start,
  //                               children: [
  //                                 const Text(
  //                                   'Latitude',
  //                                   style: TextStyle(
  //                                     fontSize: 14,
  //                                     fontWeight: FontWeight.w500,
  //                                     color: Colors.black87,
  //                                   ),
  //                                 ),
  //                                 const SizedBox(height: 8),
  //                                 state.isGettingLocationByPlaceId
  //                                     ? const TextShimmerLoader(
  //                                       height: 48,
  //                                       width: double.infinity,
  //                                       borderRadius: 8,
  //                                     )
  //                                     : PrimaryFormField(
  //                                       controller: dialogBranchLatController,
  //                                       hintText: 'e.g. -17.824858',
  //                                       keyboardType:
  //                                           const TextInputType.numberWithOptions(
  //                                             decimal: true,
  //                                           ),
  //                                       labelText: 'Latitude',
  //                                     ),
  //                               ],
  //                             ),
  //                           ),
  //                           const SizedBox(width: 16),
  //                           Expanded(
  //                             child: Column(
  //                               crossAxisAlignment: CrossAxisAlignment.start,
  //                               children: [
  //                                 const Text(
  //                                   'Longitude',
  //                                   style: TextStyle(
  //                                     fontSize: 14,
  //                                     fontWeight: FontWeight.w500,
  //                                     color: Colors.black87,
  //                                   ),
  //                                 ),
  //                                 const SizedBox(height: 8),
  //                                 state.isGettingLocationByPlaceId
  //                                     ? const TextShimmerLoader(
  //                                       height: 48,
  //                                       width: double.infinity,
  //                                       borderRadius: 8,
  //                                     )
  //                                     : PrimaryFormField(
  //                                       controller: dialogBranchLngController,
  //                                       hintText: 'e.g. 31.053028',
  //                                       keyboardType:
  //                                           const TextInputType.numberWithOptions(
  //                                             decimal: true,
  //                                           ),
  //                                       labelText: 'longitude',
  //                                     ),
  //                               ],
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                       Consumer(
  //                         builder: (context, ref, child) {
  //                           final locationState = ref.watch(
  //                             branchViewModelProivder,
  //                           );

  //                           // Update lat/lng controllers when location data is available
  //                           if (locationState.currentLocation != null &&
  //                               !locationState.isGettingLocationByPlaceId) {
  //                             // Use a post-frame callback to avoid setState during build
  //                             WidgetsBinding.instance.addPostFrameCallback((_) {
  //                               dialogBranchLatController.text =
  //                                   locationState.currentLocation!.latitude
  //                                       .toString();
  //                               dialogBranchLngController.text =
  //                                   locationState.currentLocation!.longitude
  //                                       .toString();
  //                             });
  //                           }

  //                           return const SizedBox.shrink(); // This widget doesn't render anything
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),
  //                       PrimaryFormField(
  //                         controller: dialogBranchPhoneController,
  //                         labelText: 'Phone Number',
  //                         hintText: 'Enter branch phone number',
  //                         keyboardType: TextInputType.phone,
  //                         onChanged:
  //                             (value) =>
  //                                 viewModel.setBranchPhonenumberOne(value),
  //                       ),
  //                       const SizedBox(height: 16),
  //                       PrimaryFormField(
  //                         controller: dialogBranchPhoneTwoController,
  //                         labelText: 'Phone Number (Optional)',
  //                         hintText: 'Enter branch second phone number',
  //                         keyboardType: TextInputType.phone,
  //                         onChanged:
  //                             (value) =>
  //                                 viewModel.setBranchPhonenumberTwo(value),
  //                       ),
  //                       const SizedBox(height: 16),
  //                       PrimaryFormField(
  //                         controller: dialogBranchEmailController,
  //                         labelText: 'Email',
  //                         hintText: 'Enter branch email',
  //                         keyboardType: TextInputType.emailAddress,
  //                         onChanged:
  //                             (value) => viewModel.setBranchEmailOne(value),
  //                       ),
  //                       const SizedBox(height: 16),
  //                       PrimaryFormField(
  //                         controller: dialogBranchEmailTwoController,
  //                         labelText: 'Email (Optional)',
  //                         hintText: 'Enter branch email',
  //                         keyboardType: TextInputType.emailAddress,
  //                         onChanged:
  //                             (value) => viewModel.setBranchEmailTwo(value),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //               actions: [
  //                 TextButton(
  //                   onPressed: () => Navigator.pop(context),
  //                   style: TextButton.styleFrom(
  //                     foregroundColor: Colors.grey[700],
  //                   ),
  //                   child: const Text('Cancel'),
  //                 ),
  //                 ElevatedButton(
  //                   onPressed: () {
  //                     // Validate form fields - only require primary fields
  //                     if (dialogBranchNameController.text.isEmpty ||
  //                         dialogBranchAddressController.text.isEmpty ||
  //                         dialogBranchCityController.text.isEmpty ||
  //                         dialogBranchPhoneController.text.isEmpty ||
  //                         dialogBranchEmailController.text.isEmpty) {
  //                       ScaffoldMessenger.of(context).showSnackBar(
  //                         const SnackBar(
  //                           content: Text('Please fill all required fields'),
  //                         ),
  //                       );
  //                       return;
  //                     }

  //                     // Update view model state with form values
  //                     viewModel.setBranchName(dialogBranchNameController.text);
  //                     viewModel.setBranchAddress(
  //                       dialogBranchAddressController.text,
  //                     );
  //                     viewModel.setBranchCity(dialogBranchCityController.text);
  //                     viewModel.setBranchPhonenumberOne(
  //                       _branchPhoneController.text,
  //                     );
  //                     viewModel.setBranchPhonenumberTwo(
  //                       dialogBranchPhoneTwoController.text,
  //                     ); // This can be empty
  //                     viewModel.setBranchEmailOne(_branchEmailController.text);
  //                     viewModel.setBranchEmailTwo(
  //                       dialogBranchEmailTwoController.text,
  //                     ); // This can be empty

  //                     // Parse latitude and longitude if provided
  //                     if (dialogBranchLatController.text.isNotEmpty &&
  //                         dialogBranchLngController.text.isNotEmpty) {
  //                       try {
  //                         final lat = double.parse(
  //                           dialogBranchLatController.text,
  //                         );
  //                         final lng = double.parse(
  //                           dialogBranchLngController.text,
  //                         );
  //                         viewModel.setCurrentLocation(LatLng(lat, lng));
  //                       } catch (e) {
  //                         if (kDebugMode) {
  //                           print('Error parsing coordinates: $e');
  //                         }
  //                       }
  //                     }

  //                     // Clear controllers and reset state
  //                     dialogBranchNameController.clear();
  //                     dialogBranchAddressController.clear();
  //                     dialogBranchCityController.clear();
  //                     dialogBranchPhoneController.clear();
  //                     dialogBranchPhoneTwoController.clear();
  //                     dialogBranchEmailController.clear();
  //                     dialogBranchEmailTwoController.clear();
  //                     dialogBranchLatController.clear();
  //                     dialogBranchLngController.clear();

  //                     Navigator.pop(context);
  //                   },
  //                   style: ElevatedButton.styleFrom(
  //                     backgroundColor: orangeColor,
  //                     foregroundColor: Colors.white,
  //                     padding: const EdgeInsets.symmetric(
  //                       horizontal: 24,
  //                       vertical: 12,
  //                     ),
  //                     shape: RoundedRectangleBorder(
  //                       borderRadius: BorderRadius.circular(8),
  //                     ),
  //                   ),
  //                   child: Text('Add'),
  //                 ),
  //               ],
  //             );
  //           },
  //         ),
  //   );
  // }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          'Branches',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        // Search bar
        Container(
          width: 300,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              const Icon(Icons.search, color: Colors.grey, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search',
                    border: InputBorder.none,
                    hintStyle: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.notifications_none, size: 24),
          onPressed: () {},
        ),
        IconButton(
          icon: const Icon(Icons.email_outlined, size: 24),
          onPressed: () {},
        ),
        const SizedBox(width: 8),
        // Profile
        Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey.shade300,
              child: const Icon(Icons.person, color: Colors.white),
            ),
            const SizedBox(width: 8),
            const Text(
              'Kenvin Harry',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ],
    );
  }

  // Method to show delete branch confirmation dialog
  void _showDeleteBranchDialog(BranchResponse branch) {
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Delete Branch'),
            content: Text(
              'Are you sure you want to delete "${branch.branchName}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext),
                child: const Text('Cancel'),
              ),
              Consumer(
                builder: (context, ref, child) {
                  final isDeletingBranch = ref.watch(
                    branchViewModelProivder.select(
                      (state) => state.isDeletingBranch,
                    ),
                  );

                  return ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    onPressed:
                        isDeletingBranch
                            ? null
                            : () => _handleBranchDelete(branch, dialogContext),
                    child:
                        isDeletingBranch
                            ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text('Deleting...'),
                              ],
                            )
                            : const Text('Delete'),
                  );
                },
              ),
            ],
          ),
    );
  }

  // Method to handle branch deletion
  void _handleBranchDelete(
    BranchResponse branch,
    BuildContext dialogContext,
  ) async {
    if (kDebugMode) {
      print('Handling branch deletion for branch ID: ${branch.id}');
    }

    final viewModel = ref.read(branchViewModelProivder.notifier);

    try {
      final success = await viewModel.deleteBranch(branch.id ?? 0);

      if (!mounted) return;
      // Close the dialog
      context.pop();

      if (success) {
        // If the deleted branch was selected, clear the selection
        if (_selectedBranch?.id == branch.id) {
          setState(() {
            _selectedBranch = null;
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Branch deleted successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete branch'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Close the dialog

      context.pop();

      if (kDebugMode) {
        print('Error deleting branch: $e');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting branch: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
