import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter/material.dart';

class CenteredSignInScreen extends StatefulWidget {
  const CenteredSignInScreen({super.key});

  @override
  State<CenteredSignInScreen> createState() => _CenteredSignInScreenState();
}

class _CenteredSignInScreenState extends State<CenteredSignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _toggleObscurePassword() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _toggleRememberMe(bool? value) {
    setState(() {
      _rememberMe = value ?? false;
    });
  }

  void _handleSignIn() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate sign-in process
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sign in successful! (Static demo)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _handleForgotPassword() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Forgot password feature not implemented yet'),
      ),
    );
  }

  // void _handleSignUp() {
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     const SnackBar(content: Text('Sign up feature not implemented yet')),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final formWidth = screenWidth * 0.4; // 40% of screen width
    final minWidth = 400.0; // Minimum width for mobile responsiveness
    final maxWidth = 600.0; // Maximum width for very large screens

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA), // Light gray background
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA), // Light gray
              Color(0xFFE9ECEF), // Slightly darker gray
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(
              vertical: 40.0,
              horizontal: 20.0,
            ),
            child: SizedBox(
              width: formWidth.clamp(minWidth, maxWidth),
              child: Card(
                elevation: 12,
                shadowColor: Colors.black.withValues(alpha: 0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(48.0),
                  child: _buildLoginForm(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Logo and brand section
          Center(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B45).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Image.asset(
                    'assets/images/logo_white.png',
                    height: 64,
                    errorBuilder:
                        (context, error, stackTrace) => const Icon(
                          Icons.home_work,
                          size: 64,
                          color: Color(0xFFFF6B45),
                        ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'BuildMate',
                  style: MyTypography.Bold.copyWith(
                    fontSize: 32,
                    color: const Color(0xFFFF6B45),
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Professional Construction Platform',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: Colors.grey[600],
                    letterSpacing: 0.2,
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),

          // Welcome text
          Center(
            child: Column(
              children: [
                Text(
                  'Welcome Back',
                  style: MyTypography.Bold.copyWith(
                    fontSize: 28,
                    color: const Color(0xFF1A1A1A),
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Sign in to continue to your account',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 16,
                    color: Colors.grey[600],
                    letterSpacing: 0.1,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 40),

          // Email Field
          PrimaryFormField(
            controller: _emailController,
            labelText: 'Email',
            hintText: '<EMAIL>',
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // Password Field
          PrimaryFormField(
            controller: _passwordController,
            labelText: 'Password',
            hintText: '••••••',
            obscureText: _obscurePassword,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off : Icons.visibility,
                color: Colors.grey,
              ),
              onPressed: _toggleObscurePassword,
            ),
          ),
          const SizedBox(height: 20),

          // Remember Me and Forgot Password
          Row(
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: _rememberMe,
                  onChanged: _toggleRememberMe,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: const BorderSide(color: Colors.grey),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Remember me',
                style: MyTypography.Regular.copyWith(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _handleForgotPassword,
                child: Text(
                  'Forgot Password?',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: const Color(0xFFFF6B45),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 36),

          // Login Button
          PrimaryButton(
            text: 'Log in',
            onPressed: _handleSignIn,
            height: 50.0,
            isLoading: _isLoading,
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final double height;
  final bool isLoading;

  const PrimaryButton({
    required this.text,
    required this.onPressed,
    this.height = 48.0,
    this.isLoading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF6B45),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: isLoading
            ? const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 2.0,
              )
            : Text(
                text,
                style: MyTypography.Bold.copyWith(
                  fontSize: 16,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
      ),
    );
  }
}
