import 'package:build_mate/presentation/components/button/google_sign_in_web_button.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/button/social_login_button.dart';
import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/presentation/state/signin_flow_state.dart';
import 'package:build_mate/presentation/view_models/auth/signin_view_model.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class SignInScreenWeb extends StatelessWidget {
  final SigninViewModel viewModel;
  final SigninFlowState state;

  const SignInScreenWeb({
    super.key,
    required this.viewModel,
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    
    return Row(
      children: [
        // Left side - Brand/Image section (1/2 of screen on web)
        Expanded(
          flex: 5,
          child: Container(
            color: const Color(0xFFFF6B45),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo or brand image
                Image.asset(
                  'assets/images/logo_white.png',
                  height: 80,
                  errorBuilder: (context, error, stackTrace) =>
                      const Icon(Icons.home_work, size: 80, color: Colors.white),
                ),
                const SizedBox(height: 24),
                Text(
                  'BuildMate',
                  style: MyTypography.Bold.copyWith(
                    fontSize: 36,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 48.0),
                  child: Text(
                    'Connect with skilled professionals for your construction projects',
                    textAlign: TextAlign.center,
                    style: MyTypography.Regular.copyWith(
                      fontSize: 18,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Right side - Login form (1/2 of screen on web)
        Expanded(
          flex: 5,
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 450),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(40.0),
                child: _buildLoginForm(context),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm(BuildContext context) {
    return Form(
      key: viewModel.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome Back',
            style: MyTypography.Bold.copyWith(
              fontSize: 32,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Sign in to continue',
            style: MyTypography.Regular.copyWith(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),

          // Email Field
          PrimaryFormField(
            controller: viewModel.emailController,
            labelText: 'Email',
            hintText: '<EMAIL>',
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) => viewModel.updateEmail(value),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // Password Field
          PrimaryFormField(
            controller: viewModel.passwordController,
            labelText: 'Password',
            hintText: '••••••',
            obscureText: viewModel.obscurePassword,
            onChanged: (value) => viewModel.updatePassword(value),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
            suffixIcon: IconButton(
              icon: Icon(
                viewModel.obscurePassword
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: Colors.grey,
              ),
              onPressed: () => viewModel.toggleObscurePassword(),
            ),
          ),
          const SizedBox(height: 16),

          // Remember Me Checkbox
          Row(
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: viewModel.rememberMe,
                  onChanged: (value) => viewModel.toggleRememberMe(value),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: const BorderSide(color: Colors.grey),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Remember me',
                style: MyTypography.Regular.copyWith(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => viewModel.handleResetPassword(context: context),
                child: Text(
                  'Forgot Password?',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: const Color(0xFFFF6B45),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Login Button
          PrimaryButton(
            text: 'Log in',
            onPressed: () => viewModel.handleSignIn(context: context),
            height: 50.0,
            isLoading: state.isLoggingIn,
          ),
          const SizedBox(height: 24),

          // Or Divider
          Row(
            children: [
              const Expanded(
                child: Divider(color: Colors.grey, thickness: 0.5),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'or continue with',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              const Expanded(
                child: Divider(color: Colors.grey, thickness: 0.5),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Social Login Buttons
          Column(
            children: [
              kIsWeb 
                  ? GoogleSignInWebButton(
                      onSignInStarted: () { 
                        viewModel.updateIsLoggingIn(true);
                      },
                    )
                  : SocialLoginButton.google(
                      onPressed: () => viewModel.handleGoogleSignIn(context: context),
                      width: double.infinity,
                    ),
              const SizedBox(height: 12),
              SocialLoginButton.apple(
                onPressed: () => viewModel.handleAppleSignIn(context: context),
                width: double.infinity,
              ),
              const SizedBox(height: 12),
              SocialLoginButton.facebook(
                onPressed: () => viewModel.handleFacebookSignIn(context: context),
                width: double.infinity,
              ),
            ],
          ),
          const SizedBox(height: 32),

          // Sign Up Link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Don\'t have an account? ',
                style: MyTypography.Regular.copyWith(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              GestureDetector(
                onTap: () => viewModel.navigateToSignUp(context: context),
                child: Text(
                  'Sign Up',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: const Color(0xFFFF6B45),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
