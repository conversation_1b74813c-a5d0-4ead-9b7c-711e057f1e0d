# Centered Sign-In Screen (Static Version)

A clean, centered sign-in screen that follows the BuildMate design system with only email and password authentication. This is a static implementation without view model integration.

## Features

- **Centered Layout**: The sign-in form is centered on the screen with a card design
- **Email & Password Only**: No social login buttons (Google, Facebook, Apple)
- **Consistent Design**: Follows the same design patterns as the existing sign-in screens
- **Form Validation**: Built-in email and password validation
- **Loading States**: Shows loading indicator during simulated authentication
- **Static Implementation**: No view model dependencies, self-contained state management
- **Remember Me**: Option to remember user credentials (UI only)
- **Forgot Password**: Link to reset password (shows placeholder message)
- **Sign Up Link**: Link to registration (shows placeholder message)

## Usage

### Basic Implementation

```dart
import 'package:build_mate/presentation/screens/auth/web/centered_sign_in_screen.dart';
import 'package:flutter/material.dart';

class MySignInPage extends StatelessWidget {
  const MySignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const CenteredSignInScreen();
  }
}
```

### Using the Demo Screen

```dart
import 'package:build_mate/presentation/screens/auth/web/centered_sign_in_demo.dart';

// In your routing or widget tree
const CenteredSignInDemo()
```

## Design Elements

- **Colors**: Uses BuildMate orange (#FF6B45) for branding and accents
- **Background**: Subtle gradient from light gray (#F8F9FA) to slightly darker gray (#E9ECEF)
- **Typography**: Consistent with MyTypography design system with improved letter spacing
- **Card Design**: Elevated white card with rounded corners (20px radius) and subtle shadow
- **Responsive**: 40% of screen width with min-width (400px) and max-width (600px) constraints
- **Logo**: Enhanced logo with background container and professional tagline
- **Spacing**: Optimized spacing for better visual hierarchy and readability

## Authentication Flow (Static)

1. User enters email and password
2. Form validation checks for valid email format and minimum password length
3. Simulates sign-in process with 2-second delay
4. Shows success message via SnackBar
5. Forgot password and sign-up links show placeholder messages

## Customization

The screen can be customized by:

- Modifying colors in the theme
- Adjusting the card constraints and padding
- Customizing validation rules
- Adding additional form fields if needed

## Dependencies

- Requires `PrimaryFormField` and `PrimaryButton` components
- Uses `MyTypography` for consistent text styling
- No view model dependencies - self-contained StatefulWidget
