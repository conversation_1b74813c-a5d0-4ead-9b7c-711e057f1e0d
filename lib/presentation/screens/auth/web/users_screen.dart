import 'package:build_mate/data/dto/branch_staff_response.dart';
import 'package:build_mate/presentation/components/button/cancel_button.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/view_models/web/users_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:shimmer/shimmer.dart';

class UsersScreen extends ConsumerStatefulWidget {
  const UsersScreen({super.key});

  @override
  ConsumerState<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends ConsumerState<UsersScreen> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(usersViewModelProvider);
    final viewModel = ref.watch(usersViewModelProvider.notifier);
    return Container(
      color: const Color(0xFFF0F4F7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation header
          Padding(padding: const EdgeInsets.all(24), child: _buildHeader()),

          const Divider(height: 1),

          // Main content area with right sidebar
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main content area
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with title and add button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'All Users',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            PrimaryButton(
                              text: 'Add User',
                              onPressed: () => _showAddUserDialog(context),
                              width: 120,
                              height: 40,
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        // Replace the empty content area with table
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Table Header
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Colors.grey.shade200,
                                      ),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'Name',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 3,
                                        child: Text(
                                          'Email',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'Branch',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 120,
                                        child: Text(
                                          'Actions',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Table Content
                                Expanded(
                                  child:
                                      state.isLoadingBranchUsers
                                          ? ListView.builder(
                                            itemCount: 5, // Show 5 shimmer rows
                                            itemBuilder: (context, index) {
                                              return _buildShimmerRow();
                                            },
                                          )
                                          : state.branchUsers.isEmpty
                                          ? Center(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.people_outline,
                                                  size: 48,
                                                  color: Colors.grey.shade400,
                                                ),
                                                const SizedBox(height: 16),
                                                Text(
                                                  'No users found',
                                                  style: TextStyle(
                                                    color: Colors.grey.shade600,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                          : ListView.builder(
                                            itemCount: state.branchUsers.length,
                                            itemBuilder: (context, index) {
                                              final user =
                                                  state.branchUsers[index];
                                              return Container(
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color:
                                                          Colors.grey.shade200,
                                                    ),
                                                  ),
                                                ),
                                                child: ListTile(
                                                  contentPadding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 20,
                                                        vertical: 8,
                                                      ),
                                                  title: SizedBox(
                                                    width: double.infinity,
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Expanded(
                                                          flex: 2,
                                                          child: Text(
                                                            user.name ?? 'N/A',
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style:
                                                                const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 3,
                                                          child: Text(
                                                            user.email ?? 'N/A',
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 2,
                                                          child: Text(
                                                            user.branch?.name ??
                                                                'N/A',
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                          ),
                                                        ),
                                                        Container(
                                                          width: 120,
                                                          alignment:
                                                              Alignment
                                                                  .centerRight,
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              IconButton(
                                                                padding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                constraints:
                                                                    const BoxConstraints(),
                                                                iconSize: 18,
                                                                icon: const Icon(
                                                                  Icons
                                                                      .visibility_outlined,
                                                                ),
                                                                onPressed:
                                                                    () {},
                                                                tooltip:
                                                                    'View User',
                                                              ),
                                                              const SizedBox(
                                                                width: 8,
                                                              ),
                                                              IconButton(
                                                                padding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                constraints:
                                                                    const BoxConstraints(),
                                                                iconSize: 18,
                                                                icon: const Icon(
                                                                  Icons
                                                                      .edit_outlined,
                                                                ),
                                                                onPressed:
                                                                    () => _showEditUserDialog(
                                                                      context,
                                                                      user,
                                                                    ),
                                                                tooltip:
                                                                    'Edit User',
                                                              ),
                                                              const SizedBox(
                                                                width: 8,
                                                              ),
                                                              IconButton(
                                                                padding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                constraints:
                                                                    const BoxConstraints(),
                                                                iconSize: 18,
                                                                icon: Icon(
                                                                  Icons
                                                                      .delete_outline,
                                                                  color:
                                                                      Colors
                                                                          .red
                                                                          .shade400,
                                                                ),
                                                                onPressed:
                                                                    () => _showDeleteConfirmation(
                                                                      context,
                                                                      user,
                                                                    ),
                                                                tooltip:
                                                                    'Delete User',
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Right sidebar for roles
                Container(
                  width: 280, // Fixed width instead of percentage
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      left: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(left: 8, bottom: 16),
                          child: Text(
                            'Branches',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        _buildRoleItem(
                          'All',
                          state.allUsersCount,
                          isSelected: state.isAllSelected,
                          onTap: () => viewModel.getShopUsers(),
                        ),
                        ListView.builder(
                          shrinkWrap: true,
                          itemCount: state.branches.length,
                          itemBuilder: (context, index) {
                            final branch = state.branches[index];
                            return _buildRoleItem(
                              branch.branchName ?? '',
                              branch.branchCount.first.count ?? 0,
                              isSelected: state.selectedBranchIndex == index,
                              onTap:
                                  () => viewModel.selectBranch(
                                    index: index,
                                    branchId: branch.id ?? 0,
                                  ),
                            );
                          },
                        ),
                        vSpace(16),
                        Divider(height: 1, color: Colors.grey.shade200),
                        vSpace(16),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          'Users',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        // Search bar
        Container(
          width: 300,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              const Icon(Icons.search, color: Colors.grey, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search users',
                    border: InputBorder.none,
                    hintStyle: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.notifications_none, size: 24),
          onPressed: () {},
        ),
        IconButton(
          icon: const Icon(Icons.email_outlined, size: 24),
          onPressed: () {},
        ),
        const SizedBox(width: 8),
        // Profile
        Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey.shade300,
              child: const Icon(Icons.person, color: Colors.white),
            ),
            const SizedBox(width: 8),
            const Text(
              'Kenvin Harry',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleItem(
    String title,
    int count, {
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color:
            isSelected
                ? orangeColor.withAlpha((0.1 * 255).round())
                : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isSelected
                  ? orangeColor.withAlpha((0.3 * 255).round())
                  : Colors.grey.shade200,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: MyTypography.Regular.copyWith(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    color: isSelected ? orangeColor : Colors.black87,
                  ),
                ),
                Text(
                  '$count',
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAddUserDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController emailController = TextEditingController();

    // Fetch branches when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usersViewModelProvider.notifier).getBranches();
    });
    final state = ref.watch(usersViewModelProvider);
    final viewModel = ref.watch(usersViewModelProvider.notifier);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Dialog Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Add New User',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Branch Dropdown
                const Text(
                  'Branch',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Consumer(
                  builder: (context, ref, child) {
                    if (state.isLoadingBranches) {
                      return Container(
                        height: 56,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  orangeColor,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Loading branches...',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return DropdownButtonFormField<int>(
                      value:
                          state.selectedBranchId == 0
                              ? null
                              : state.selectedBranchId,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        hintText: 'Select branch',
                      ),
                      items:
                          state.branches.isEmpty
                              ? []
                              : state.branches.map((branch) {
                                return DropdownMenuItem<int>(
                                  value: branch.id,
                                  child: Text(
                                    '${branch.branchName} - ${branch.city ?? ''}',
                                  ),
                                );
                              }).toList(),
                      onChanged: (value) {
                        viewModel.setSelectedBranch(value ?? 0);
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),

                // Name Field
                const Text(
                  'Full Name',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: nameController,
                  onChanged: (value) {
                    viewModel.updateFullName(value);
                  },
                  decoration: InputDecoration(
                    hintText: 'Enter full name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Email Field
                const Text(
                  'Email',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  onChanged: (value) {
                    viewModel.updateEmail(value);
                  },
                  decoration: InputDecoration(
                    hintText: 'Enter email address',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: CancelButton(
                        text: 'Cancel',
                        onPressed: () => Navigator.pop(context),
                        height: 40,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child:
                          state.isSavingUser
                              ? Container(
                                height: 40,
                                decoration: BoxDecoration(
                                  color: orangeColor,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                              : PrimaryButton(
                                text: 'Add User',
                                onPressed: () => _handleUserCreation(context),
                                height: 40,
                              ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) {
      // Clean up controllers
      nameController.dispose();
      emailController.dispose();
    });
  }

  Future<void> _handleUserCreation(BuildContext dialogContext) async {
    // Store context references BEFORE the async operation
    final navigator = Navigator.of(dialogContext);
    // final scaffoldMessengerState = ScaffoldMessenger.of(context);

    try {
      await ref.read(usersViewModelProvider.notifier).createUser();

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      final state = ref.read(usersViewModelProvider);

      if (state.saveSuccess) {
        // Use stored navigator reference instead of dialogContext
        navigator.pop();

        // Show success message using stored reference
        _showSuccessMessage('User successfully created');
      } else if (state.errorMessage != null) {
        // For error message, you'll need to modify _showErrorMessage to accept ScaffoldMessengerState
        // or create a stored reference for error handling
        _showErrorMessage(context, state.errorMessage!);
      }
    } catch (error) {
      // Handle any exceptions during user creation
      if (!mounted) return;

      _showErrorMessage(context, 'Failed to create user: $error');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorMessage(BuildContext context, String error) {
    String displayMessage = error;

    // Handle common error cases
    if (error.contains('email')) {
      displayMessage = 'Please enter a valid email address';
    } else if (error.contains('already in use')) {
      displayMessage = 'This email is already registered';
    } else if (error.contains('branch')) {
      displayMessage = 'Please select a branch';
    } else if (error.contains('name')) {
      displayMessage = 'Please enter a valid name';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                displayMessage,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, BranchStaffResponse user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text('Delete User'),
          content: Text(
            'Are you sure you want to delete ${user.name}? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                final success = await ref
                    .read(usersViewModelProvider.notifier)
                    .deleteUser(user);

                navigator.pop();
                if (success) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        '${user.name} has been deleted successfully',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      backgroundColor: Colors.green.shade600,
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      duration: const Duration(seconds: 4),
                    ),
                  );
                }
              },
              child: Text(
                'Delete',
                style: TextStyle(color: Colors.red.shade600),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showEditUserDialog(BuildContext context, BranchStaffResponse user) {
    // final scaffoldMessengerState = ScaffoldMessenger.of(context);
    final TextEditingController nameController = TextEditingController(
      text: user.name,
    );
    final TextEditingController emailController = TextEditingController(
      text: user.email,
    );
    final viewModel = ref.read(usersViewModelProvider.notifier);

    // Set the selected branch ID when opening the dialog
    viewModel.setSelectedBranch(user.branch?.id ?? 0);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Edit User - ${user.name}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Branch Selection
                const Text(
                  'Branch',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Consumer(
                  builder: (context, ref, child) {
                    final state = ref.watch(usersViewModelProvider);
                    return DropdownButtonFormField<int>(
                      value: user.branch?.id,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      items:
                          state.branches.map((branch) {
                            return DropdownMenuItem<int>(
                              value: branch.id,
                              child: Text(
                                '${branch.branchName} - ${branch.city ?? ''}',
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        viewModel.setSelectedBranch(value ?? 0);
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),

                // Name Field
                const Text(
                  'Full Name',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: nameController,
                  decoration: InputDecoration(
                    hintText: 'Enter full name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Email Field
                const Text(
                  'Email',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: 'Enter email address',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Reset Password Button
                Consumer(
                  builder: (context, ref, _) {
                    final state = ref.watch(usersViewModelProvider);
                    return SizedBox(
                      width: double.infinity,
                      child: TextButton.icon(
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        icon:
                            state.isResettingPassword
                                ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      orangeColor,
                                    ),
                                  ),
                                )
                                : const Icon(Icons.lock_reset_outlined),
                        label: Text(
                          state.isResettingPassword
                              ? 'Resetting Password...'
                              : 'Reset Password',
                        ),
                        onPressed:
                            state.isResettingPassword
                                ? null
                                : () async {
                                  // Store context references BEFORE the async operation
                                  final navigator = Navigator.of(context);
                                  final scaffoldMessenger =
                                      ScaffoldMessenger.of(context);

                                  try {
                                    await viewModel.resetUserPassword(
                                      user.userId ?? 0,
                                    );

                                    // Check if widget is still mounted after async operation
                                    if (!mounted) return;

                                    // Use the stored references (no context usage after async gap)
                                    navigator.pop();

                                    // Show success message
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Password reset successfully',
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  } catch (error) {
                                    // Handle errors appropriately
                                    if (!mounted) return;

                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Failed to reset password: $error',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                },
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: CancelButton(
                        text: 'Cancel',
                        onPressed: () => Navigator.pop(context),
                        height: 40,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Consumer(
                        builder: (context, ref, _) {
                          final state = ref.watch(usersViewModelProvider);
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (state.isUpdatingUser)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: LinearProgressIndicator(
                                      value: state.updateProgress / 100,
                                      minHeight: 4,
                                      backgroundColor: Colors.grey.shade200,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        orangeColor,
                                      ),
                                    ),
                                  ),
                                ),
                              state.isUpdatingUser
                                  ? Container(
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: orangeColor,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Center(
                                      child: Text(
                                        'Updating... ${state.updateProgress.toInt()}%',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  )
                                  : PrimaryButton(
                                    text: 'Save Changes',
                                    onPressed: () async {
                                      final navigator = Navigator.of(context);
                                      if (state.selectedBranchId < 1) {
                                        _showErrorMessage(
                                          context,
                                          'Please select a branch',
                                        );
                                        return;
                                      }
                                      final success = await viewModel
                                          .updateUser(
                                            userId: user.userId ?? 0,
                                            name: nameController.text,
                                            email: emailController.text,
                                          );
                                      if (success && mounted) {
                                        navigator.pop();
                                        _showSuccessMessage(
                                          'User updated successfully',
                                        );
                                      }
                                    },
                                    height: 40,
                                  ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) {
      nameController.dispose();
      emailController.dispose();
    });
  }

  Widget _buildShimmerRow() {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        title: Row(
          children: [
            Expanded(
              flex: 2,
              child: Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 3,
              child: Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 120,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.grey.shade300,
                    highlightColor: Colors.grey.shade100,
                    child: Container(
                      width: 80,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
