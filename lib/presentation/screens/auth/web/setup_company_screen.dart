// ignore_for_file: unused_import

import 'package:build_mate/presentation/view_models/job/post_job_view_model.dart'
    as job_vm;
import 'package:build_mate/theme/colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/textfield/primary_text.dart';
import 'package:build_mate/presentation/view_models/web/setup_company_view_model.dart';
import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import 'dart:async';
import 'package:build_mate/presentation/components/loader/text_shimmer_loader.dart';

class SetupCompanyScreen extends ConsumerStatefulWidget {
  const SetupCompanyScreen({super.key});

  @override
  ConsumerState<SetupCompanyScreen> createState() => _SetupCompanyScreenState();
}

class _SetupCompanyScreenState extends ConsumerState<SetupCompanyScreen> {
  final _formKey = GlobalKey<FormState>();
  final _shopNameController = TextEditingController();
  final _contactEmailController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _mainAddressController = TextEditingController();

  // Branch controllers
  final _branchNameController = TextEditingController();
  final _branchAddressController = TextEditingController();
  final _branchCityController = TextEditingController();
  final _branchPhoneController = TextEditingController();
  final _branchPhoneTwoController = TextEditingController();
  final _branchEmailController = TextEditingController();
  final _branchEmailTwoController = TextEditingController();
  final _branchLatController = TextEditingController();
  final _branchLngController = TextEditingController();

  int _currentStep = 0;

  // Current branch being edited
  Map<String, dynamic>? _currentBranch;
  int? _currentBranchIndex;

  // Address suggestions
  final List<String> _addressSuggestions = [];
  final bool _isLoadingAddressSuggestions = false;
  final FocusNode _addressFocusNode = FocusNode();

  Timer? _debounceTimer;

  // Image upload state
  final bool _isUploadingImages = false;
  final int _uploadedImagesCount = 0;
  final int _totalImagesToUpload = 0;

  @override
  void initState() {
    super.initState();
    // Load existing profile if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(setupCompanyViewModelProvider.notifier)
          .loadHardwareShopProfile();
    });
  }

  @override
  void dispose() {
    _shopNameController.dispose();
    _contactEmailController.dispose();
    _phoneNumberController.dispose();
    _ownerNameController.dispose();
    _mainAddressController.dispose();
    _branchNameController.dispose();
    _branchAddressController.dispose();
    _branchCityController.dispose();
    _branchPhoneController.dispose();
    _branchPhoneTwoController.dispose();
    _branchEmailController.dispose();
    _branchEmailTwoController.dispose();
    _debounceTimer?.cancel(); // Cancel the timer when disposing
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage();

      if (images.isNotEmpty) {
        final viewModel = ref.read(setupCompanyViewModelProvider.notifier);

        for (var image in images) {
          // Read image as bytes
          final bytes = await image.readAsBytes();

          // Upload with progress tracking
          await viewModel.uploadShopImageWithProgress(bytes, image.name);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking images: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking images: ${e.toString()}')),
        );
      }
    }
  }

  Widget _buildImageUploadProgress() {
    final viewModel = ref.watch(setupCompanyViewModelProvider.notifier);
    final uploadingImages = viewModel.getUploadingImages();

    if (uploadingImages.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Uploading ${uploadingImages.length} ${uploadingImages.length == 1 ? 'image' : 'images'}...',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          ...uploadingImages.map((progress) {
            final uploadProgress = progress;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    uploadProgress.fileName,
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: uploadProgress.progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(orangeColor),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // void _editBranch(int index, Map<String, dynamic> branch) {
  //   setState(() {
  //     _currentBranch = branch;
  //     _currentBranchIndex = index;
  //     _branchNameController.text = branch['name'];
  //     _branchAddressController.text = branch['address'];
  //     _branchCityController.text = branch['city'];
  //     _branchPhoneController.text = branch['phones'][0];
  //     _branchEmailController.text = branch['emails'][0];
  //   });
  // }

  // void _deleteBranch(int index) {
  //   ref.read(setupCompanyViewModelProvider.notifier).removeBranch(index);
  // }

  Future<void> _saveCompanyProfile() async {
    if (_formKey.currentState!.validate()) {
      try {
        final viewModel = ref.read(setupCompanyViewModelProvider.notifier);

        // Update shop details from controllers
        viewModel.updateShopDetails(
          name: _shopNameController.text,
          owner: _ownerNameController.text,
          contactEmail: _contactEmailController.text,
          phoneNumber: _phoneNumberController.text,
          mainAddress: _mainAddressController.text,
        );

        // Save to Supabase
        await viewModel.saveHardwareShopProfile();

        if (mounted) {
          // Show success dialog
          _showSuccessDialog();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving profile: ${e.toString()}')),
          );
        }
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Success icon
                const Icon(
                  Icons.check_circle_outline,
                  color: Colors.green,
                  size: 80,
                ),
                const SizedBox(height: 24),

                // Success title
                Text(
                  'Hardware Shop Setup Complete!',
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: orangeColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Success message
                const Text(
                  'Your hardware shop profile has been created successfully. You can now start adding products and manage your branches from the dashboard.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Close dialog and navigate to dashboard screen
                      Navigator.of(context).pop();
                      ref
                          .read(goRouterProvider)
                          .goNamed(RouteConstants.HARDWARE_SHOP_DASHBOARD);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: orangeColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text(
                      'Go to Dashboard',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(setupCompanyViewModelProvider);
    // final viewModel = ref.watch(setupCompanyViewModelProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup Your Hardware Shop'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: orangeColor,
        foregroundColor: whiteColor,
      ),
      body: Container(
        decoration: const BoxDecoration(color: Colors.white),
        child:
            state.isLoading
                ? const Center(
                  child: CircularProgressIndicator(color: orangeColor),
                )
                : Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 900),
                    padding: const EdgeInsets.all(24),
                    child: Card(
                      elevation: 4,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Form(
                          key: _formKey,
                          child: Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: Theme.of(
                                context,
                              ).colorScheme.copyWith(
                                primary: orangeColor,
                                secondary: orangeColor,
                                outline: orangeColor,
                              ),
                            ),
                            child: Stepper(
                              type: StepperType.horizontal,
                              currentStep: _currentStep,
                              onStepContinue: () {
                                if (_currentStep < 2) {
                                  setState(() {
                                    _currentStep += 1;
                                  });
                                } else {
                                  _saveCompanyProfile();
                                }
                              },
                              onStepCancel: () {
                                if (_currentStep > 0) {
                                  setState(() {
                                    _currentStep -= 1;
                                  });
                                }
                              },
                              controlsBuilder: (context, details) {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 20.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      if (_currentStep > 0)
                                        SizedBox(
                                          width: 180,
                                          child: OutlinedButton(
                                            onPressed: details.onStepCancel,
                                            style: OutlinedButton.styleFrom(
                                              foregroundColor: orangeColor,
                                              side: BorderSide(
                                                color: orangeColor,
                                              ),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 12,
                                                  ),
                                              minimumSize:
                                                  const Size.fromHeight(48),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                            child: const Text('Back'),
                                          ),
                                        ),
                                      const SizedBox(width: 16),
                                      PrimaryButton(
                                        text:
                                            _currentStep < 2
                                                ? 'Continue'
                                                : 'Save Profile',
                                        onPressed:
                                            details.onStepContinue ?? () {},
                                        width: 180,
                                        height: 48,
                                      ),
                                    ],
                                  ),
                                );
                              },
                              steps: [
                                Step(
                                  title: const Text('Shop Details'),
                                  content: _buildShopDetailsStep(),
                                  isActive: _currentStep >= 0,
                                ),
                                Step(
                                  title: const Text('Shop Images'),
                                  content: _buildImagesStep(),
                                  isActive: _currentStep >= 1,
                                ),
                                Step(
                                  title: const Text('Branches'),
                                  content: _buildBranchesStep(),
                                  isActive: _currentStep >= 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
      ),
    );
  }

  Widget _buildShopDetailsStep() {
    final viewModel = ref.read(setupCompanyViewModelProvider.notifier);
    // final state = ref.watch(setupCompanyViewModelProvider);
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: orangeColor,
              ),
            ),
            const SizedBox(height: 24),
            PrimaryFormField(
              controller: _shopNameController,
              labelText: 'Shop Name',
              hintText: 'Enter your hardware shop name',
              onChanged: (value) {
                viewModel.setShopname(value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your shop name';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            PrimaryFormField(
              controller: _ownerNameController,
              labelText: 'Shop Owner Name',
              hintText: 'Enter shop owner\'s full name',
              onChanged: (value) {
                viewModel.setShopOwner(value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter shop owner\'s name';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            PrimaryFormField(
              controller: _contactEmailController,
              labelText: 'Contact Email',
              hintText: 'Enter your business email',
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) {
                viewModel.setContactEmail(value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your contact email';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value)) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            PrimaryFormField(
              controller: _phoneNumberController,
              labelText: 'Phone Number',
              hintText: 'Enter your business phone number',
              keyboardType: TextInputType.phone,
              onChanged: (value) {
                viewModel.setPhoneNumber(value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            PrimaryFormField(
              controller: _mainAddressController,
              labelText: 'Main Address',
              hintText: 'Enter main shop address',
              onChanged: (value) {
                viewModel.setMainAddress(value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter main shop address';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesStep() {
    final state = ref.watch(setupCompanyViewModelProvider);
    final viewModel = ref.watch(setupCompanyViewModelProvider.notifier);

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Shop Images',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Upload images of your hardware shop to showcase your business',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 20),

          // Image upload button
          OutlinedButton.icon(
            onPressed: viewModel.isAnyImageUploading() ? null : _pickImages,
            icon: const Icon(Icons.add_photo_alternate),
            label: const Text('Add Images'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),

          // Upload progress indicator
          _buildImageUploadProgress(),

          const SizedBox(height: 20),

          // Image preview grid
          if (state.imageUrls.isNotEmpty)
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemCount: state.imageUrls.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 1.0,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          state.imageUrls[index],
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            if (kDebugMode) {
                              print('Error loading image: $error');
                            }
                            return Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.broken_image,
                                color: Colors.grey[400],
                                size: 24,
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value:
                                    loadingProgress.expectedTotalBytes != null
                                        ? loadingProgress
                                                .cumulativeBytesLoaded /
                                            loadingProgress.expectedTotalBytes!
                                        : null,
                                color: orangeColor,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    Positioned(
                      top: 5,
                      right: 5,
                      child: GestureDetector(
                        onTap: () {
                          viewModel.removeShopImageUrl(index);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            )
          else
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Text(
                  'No images selected yet',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBranchesStep() {
    final state = ref.watch(setupCompanyViewModelProvider);
    final viewModel = ref.watch(setupCompanyViewModelProvider.notifier);

    return Container(
      constraints: const BoxConstraints(maxWidth: 1000),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Branches',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  // Clear form for new branch
                  setState(() {
                    _currentBranch = null;
                    _currentBranchIndex = null;
                    _branchNameController.clear();
                    _branchAddressController.clear();
                    _branchCityController.clear();
                    _branchPhoneController.clear();
                    _branchPhoneTwoController.clear();
                    _branchEmailController.clear();
                    _branchEmailTwoController.clear();
                    _branchLatController.clear();
                    _branchLngController.clear();
                  });

                  // Show branch form dialog
                  _showBranchFormDialog();
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Branch'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Add all your hardware shop branches',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 20),

          // Branches list
          if (state.branchModels.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: state.branchModels.length,
              itemBuilder: (context, index) {
                final branch = state.branchModels[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              branch.branchName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  icon: const Icon(
                                    Icons.edit,
                                    color: Colors.blue,
                                  ),
                                  onPressed: () {
                                    // Set the current branch index
                                    setState(() {
                                      _currentBranchIndex = index;
                                    });

                                    // Load branch data for editing
                                    viewModel.loadBranchForEditing(index);

                                    // Update controllers with branch data
                                    _branchNameController.text =
                                        branch.branchName;
                                    _branchAddressController.text =
                                        branch.address;
                                    _branchCityController.text = branch.city;
                                    _branchPhoneController.text =
                                        branch.phonenumberOne;
                                    _branchPhoneTwoController.text =
                                        branch.phonenumberTwo ?? '';
                                    _branchEmailController.text =
                                        branch.emailOne;
                                    _branchEmailTwoController.text =
                                        branch.emailTwo ?? '';
                                    _branchLatController.text =
                                        branch.latitude.toString();
                                    _branchLngController.text =
                                        branch.longitude.toString();

                                    // Show branch form dialog
                                    _showBranchFormDialog();
                                  },
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed:
                                      () => viewModel.removeBranchModel(index),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text('Address: ${branch.address}, ${branch.city}'),
                        const SizedBox(height: 4),
                        Text(
                          'Phone: ${branch.phonenumberOne}${branch.phonenumberTwo != null && branch.phonenumberTwo!.isNotEmpty ? ', ${branch.phonenumberTwo}' : ''}',
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Email: ${branch.emailOne}${branch.emailTwo != null && branch.emailTwo!.isNotEmpty ? ', ${branch.emailTwo}' : ''}',
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Location: ${branch.latitude}, ${branch.longitude}',
                        ),
                      ],
                    ),
                  ),
                );
              },
            )
          else
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Text(
                  'No branches added yet',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showBranchFormDialog() {
    final viewModel = ref.read(setupCompanyViewModelProvider.notifier);
    final state = ref.watch(setupCompanyViewModelProvider);

    showDialog(
      context: context,
      builder:
          (dialogContext) => StatefulBuilder(
            builder: (context, setDialogState) {
              return AlertDialog(
                backgroundColor: Colors.white,
                title: Text(
                  _currentBranchIndex == null ? 'Add Branch' : 'Edit Branch',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SizedBox(
                  width: 600,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        PrimaryFormField(
                          controller: _branchNameController,
                          labelText: 'Branch Name',
                          hintText: 'Enter branch name',
                          onChanged: (value) {
                            viewModel.setBranchName(value);
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter branch name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        // Address field with autocomplete
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            PrimaryFormField(
                              controller: _branchAddressController,
                              labelText: 'Address',
                              hintText: 'Start typing to search address',
                              focusNode: _addressFocusNode,
                              onChanged: (value) {
                                // Cancel any previous timer
                                _debounceTimer?.cancel();

                                // Set a new timer
                                _debounceTimer = Timer(
                                  const Duration(milliseconds: 500),
                                  () {
                                    // Use the view model to handle search
                                    viewModel.searchDropOffAddress(
                                      place: value,
                                    );
                                  },
                                );
                              },
                              // Add a suffix icon to show loading state
                              suffixIcon:
                                  state.isLoading
                                      ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: Padding(
                                          padding: EdgeInsets.all(8.0),
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: orangeColor,
                                          ),
                                        ),
                                      )
                                      : null,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter branch address';
                                }
                                return null;
                              },
                            ),
                            // Address suggestions
                            Consumer(
                              builder: (context, ref, child) {
                                final addressState = ref.watch(
                                  setupCompanyViewModelProvider,
                                );
                                final isLoading = addressState.isLoading;
                                final suggestions =
                                    addressState.addressPlacesList;

                                // Convert suggestions to text
                                final addressTexts =
                                    suggestions
                                        .map(
                                          (suggestion) =>
                                              suggestion
                                                  .placePrediction
                                                  ?.text
                                                  ?.text ??
                                              '',
                                        )
                                        .where((text) => text.isNotEmpty)
                                        .toList();

                                if (addressTexts.isEmpty && !isLoading) {
                                  return const SizedBox.shrink();
                                }

                                return Container(
                                  margin: const EdgeInsets.only(top: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha((0.1 * 255).round()),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child:
                                      isLoading
                                          ? const Padding(
                                            padding: EdgeInsets.all(12),
                                            child: Center(
                                              child: SizedBox(
                                                width: 20,
                                                height: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                    ),
                                              ),
                                            ),
                                          )
                                          : ListView.builder(
                                            shrinkWrap: true,
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            itemCount: addressTexts.length,
                                            itemBuilder: (context, index) {
                                              final suggestion =
                                                  suggestions[index];
                                              final text = addressTexts[index];
                                              final placeId =
                                                  suggestion
                                                      .placePrediction
                                                      ?.placeId ??
                                                  '';

                                              return InkWell(
                                                onTap: () {
                                                  // Use view model to handle selection
                                                  viewModel
                                                      .selectAddressSuggestion(
                                                        text,
                                                        placeId,
                                                      );

                                                  viewModel.setBranchAddress(
                                                    text,
                                                  );

                                                  // Update the text field with selected address
                                                  _branchAddressController
                                                      .text = text;

                                                  // Get location data for the selected place
                                                  viewModel
                                                      .getLocationByPlaceId(
                                                        placeId: placeId,
                                                      );

                                                  // Clear suggestions list by resetting the state
                                                  viewModel
                                                      .clearAddressSuggestions();

                                                  // Update dialog state to reflect changes
                                                  setDialogState(() {});
                                                },
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        vertical: 12,
                                                        horizontal: 16,
                                                      ),
                                                  child: Text(text),
                                                ),
                                              );
                                            },
                                          ),
                                );
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        PrimaryFormField(
                          controller: _branchCityController,
                          labelText: 'City',
                          hintText: 'Enter city',
                          onChanged: (value) {
                            viewModel.setBranchCity(value);
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter city';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Latitude',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  state.isGettingLocationByPlaceId
                                      ? const TextShimmerLoader(
                                        height: 48,
                                        width: double.infinity,
                                        borderRadius: 8,
                                      )
                                      : PrimaryFormField(
                                        controller: _branchLatController,
                                        hintText: 'e.g. -17.824858',
                                        keyboardType:
                                            const TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        labelText: 'Latitude',
                                      ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Longitude',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  state.isGettingLocationByPlaceId
                                      ? const TextShimmerLoader(
                                        height: 48,
                                        width: double.infinity,
                                        borderRadius: 8,
                                      )
                                      : PrimaryFormField(
                                        controller: _branchLngController,
                                        hintText: 'e.g. 31.053028',
                                        keyboardType:
                                            const TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        labelText: 'longitude',
                                      ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Consumer(
                          builder: (context, ref, child) {
                            final locationState = ref.watch(
                              setupCompanyViewModelProvider,
                            );

                            // Update lat/lng controllers when location data is available
                            if (locationState.currentLocation != null &&
                                !locationState.isGettingLocationByPlaceId) {
                              // Use a post-frame callback to avoid setState during build
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _branchLatController.text =
                                    locationState.currentLocation!.latitude
                                        .toString();
                                _branchLngController.text =
                                    locationState.currentLocation!.longitude
                                        .toString();
                              });
                            }

                            return const SizedBox.shrink(); // This widget doesn't render anything
                          },
                        ),
                        const SizedBox(height: 16),
                        PrimaryFormField(
                          controller: _branchPhoneController,
                          labelText: 'Phone Number',
                          hintText: 'Enter branch phone number',
                          keyboardType: TextInputType.phone,
                          onChanged:
                              (value) =>
                                  viewModel.setBranchPhonenumberOne(value),
                        ),
                        const SizedBox(height: 16),
                        PrimaryFormField(
                          controller: _branchPhoneTwoController,
                          labelText: 'Phone Number (Optional)',
                          hintText: 'Enter branch second phone number',
                          keyboardType: TextInputType.phone,
                          onChanged:
                              (value) =>
                                  viewModel.setBranchPhonenumberTwo(value),
                        ),
                        const SizedBox(height: 16),
                        PrimaryFormField(
                          controller: _branchEmailController,
                          labelText: 'Email',
                          hintText: 'Enter branch email',
                          keyboardType: TextInputType.emailAddress,
                          onChanged:
                              (value) => viewModel.setBranchEmailOne(value),
                        ),
                        const SizedBox(height: 16),
                        PrimaryFormField(
                          controller: _branchEmailTwoController,
                          labelText: 'Email (Optional)',
                          hintText: 'Enter branch email',
                          keyboardType: TextInputType.emailAddress,
                          onChanged:
                              (value) => viewModel.setBranchEmailTwo(value),
                        ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                    ),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Validate form fields - only require primary fields
                      if (_branchNameController.text.isEmpty ||
                          _branchAddressController.text.isEmpty ||
                          _branchCityController.text.isEmpty ||
                          _branchPhoneController.text.isEmpty ||
                          _branchEmailController.text.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Please fill all required fields'),
                          ),
                        );
                        return;
                      }

                      // Update view model state with form values
                      viewModel.setBranchName(_branchNameController.text);
                      viewModel.setBranchAddress(_branchAddressController.text);
                      viewModel.setBranchCity(_branchCityController.text);
                      viewModel.setBranchPhonenumberOne(
                        _branchPhoneController.text,
                      );
                      viewModel.setBranchPhonenumberTwo(
                        _branchPhoneTwoController.text,
                      ); // This can be empty
                      viewModel.setBranchEmailOne(_branchEmailController.text);
                      viewModel.setBranchEmailTwo(
                        _branchEmailTwoController.text,
                      ); // This can be empty

                      // Parse latitude and longitude if provided
                      if (_branchLatController.text.isNotEmpty &&
                          _branchLngController.text.isNotEmpty) {
                        try {
                          final lat = double.parse(_branchLatController.text);
                          final lng = double.parse(_branchLngController.text);
                          viewModel.setCurrentLocation(LatLng(lat, lng));
                        } catch (e) {
                          if (kDebugMode) {
                            print('Error parsing coordinates: $e');
                          }
                        }
                      }

                      // Add or update branch based on _currentBranchIndex
                      if (_currentBranchIndex != null) {
                        // Update existing branch
                        viewModel.updateBranchModel(_currentBranchIndex!);
                      } else {
                        // Add new branch
                        viewModel.addBranchModel();
                      }

                      // Clear controllers and reset state
                      _branchNameController.clear();
                      _branchAddressController.clear();
                      _branchCityController.clear();
                      _branchPhoneController.clear();
                      _branchPhoneTwoController.clear();
                      _branchEmailController.clear();
                      _branchEmailTwoController.clear();
                      _branchLatController.clear();
                      _branchLngController.clear();
                      _currentBranchIndex = null;

                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: orangeColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(_currentBranchIndex == null ? 'Add' : 'Update'),
                  ),
                ],
              );
            },
          ),
    );
  }

  // Future<void> _getAddressSuggestions(String query) async {
  //   if (query.length < 3) {
  //     setState(() {
  //       _addressSuggestions = [];
  //       _isLoadingAddressSuggestions = false;
  //     });
  //     return;
  //   }

  //   setState(() {
  //     _isLoadingAddressSuggestions = true;
  //   });

  //   try {
  //     // Here you would typically call a geocoding or places API
  //     // For example, using Google Places API or similar service
  //     // This is a placeholder implementation
  //     await Future.delayed(const Duration(milliseconds: 500));

  //     // Mock suggestions for demonstration
  //     final suggestions = [
  //       '$query, Main Street, Harare',
  //       '$query, Central Avenue, Bulawayo',
  //       '$query, First Street, Mutare',
  //     ];

  //     setState(() {
  //       _addressSuggestions = suggestions;
  //       _isLoadingAddressSuggestions = false;
  //     });
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('Error getting address suggestions: $e');
  //     }

  //     setState(() {
  //       _addressSuggestions = [];
  //       _isLoadingAddressSuggestions = false;
  //     });
  //   }
  // }

}
