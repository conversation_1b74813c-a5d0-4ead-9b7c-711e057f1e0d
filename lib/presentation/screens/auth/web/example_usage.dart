import 'package:build_mate/presentation/screens/auth/web/centered_sign_in_screen.dart';
import 'package:flutter/material.dart';

/// Example showing different ways to use the CenteredSignInScreen

// 1. Direct usage in a route
class SignInRoute extends StatelessWidget {
  const SignInRoute({super.key});

  @override
  Widget build(BuildContext context) {
    return const CenteredSignInScreen();
  }
}

// 2. Usage in a main app
class ExampleApp extends StatelessWidget {
  const ExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BuildMate Sign In',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const CenteredSignInScreen(),
    );
  }
}

// 3. Usage with navigation
class NavigationExample extends StatelessWidget {
  const NavigationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BuildMate',
      initialRoute: '/signin',
      routes: {
        '/signin': (context) => const CenteredSignInScreen(),
        '/home': (context) => const HomeScreen(),
      },
    );
  }
}

// Placeholder home screen for navigation example
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: const Center(
        child: Text('Welcome to BuildMate!'),
      ),
    );
  }
}
