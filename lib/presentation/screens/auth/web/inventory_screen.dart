// ignore: unused_import
import 'package:build_mate/core/supabase_client.dart';
import 'package:build_mate/data/dto/hardware_items_response.dart';
import 'package:build_mate/data/dto/hardware_sub_category_response.dart';
import 'package:build_mate/presentation/components/button/cancel_button.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/view_models/web/inventory_view_model.dart';
import 'package:build_mate/presentation/widgets/shimmer/product_shimmer.dart';

class AppScrollBehavior extends ScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
  };
}

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});
  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InventoryScreenState();
}
// Custom scroll behavior that enables drag scrolling with mouse

class _InventoryScreenState extends ConsumerState<InventoryScreen> {
  // Add this map to track current image index for each product
  final Map<int, int> _currentImageIndices = {};

  // Form controllers for add product dialog
  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();

  @override
  void dispose() {
    _productNameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(inventoryViewModelProvider);
    final viewModel = ref.watch(inventoryViewModelProvider.notifier);
    return Container(
      color: const Color(0xFFF0F4F7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation header
          Padding(padding: const EdgeInsets.all(24), child: _buildHeader()),

          const Divider(height: 1),

          // Inventory content with categories sidebar
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main content area
                Expanded(
                  flex: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Inventory list header with title and add button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'All Products',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                OutlinedButton.icon(
                                  onPressed: () {
                                    _showFilterDialog(context);
                                  },
                                  icon: const Icon(Icons.filter_list, size: 20),
                                  label: Consumer(
                                    builder: (context, ref, child) {
                                      final hasFilters = ref.watch(
                                        inventoryViewModelProvider.select(
                                          (state) =>
                                              state.filterCategoryId != 0,
                                        ),
                                      );
                                      return Row(
                                        children: [
                                          const Text('Filter'),
                                          if (hasFilters) ...[
                                            const SizedBox(width: 4),
                                            Container(
                                              padding: const EdgeInsets.all(4),
                                              decoration: const BoxDecoration(
                                                color: Colors.blue,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Text(
                                                '1',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ],
                                      );
                                    },
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    side: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                    foregroundColor: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                PrimaryButton(
                                  text: 'Add Product',
                                  onPressed: () {
                                    _showAddProductBottomSheet(
                                      context,
                                      isEditing: false,
                                    );
                                  },
                                  width: 150,
                                  height: 40,
                                  isLoading: state.isSaving,
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Product listing
                        Expanded(
                          child:
                              state.isLoadingProducts
                                  ? ListView.builder(
                                    itemCount:
                                        5, // Show 5 shimmer items while loading
                                    itemBuilder: (context, index) {
                                      return const ProductShimmer();
                                    },
                                  )
                                  : ListView.builder(
                                    itemCount: state.products.length,
                                    itemBuilder: (context, index) {
                                      final product = state.products[index];
                                      return _buildProductCard(product);
                                    },
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Categories sidebar
                Container(
                  width: MediaQuery.sizeOf(context).width * 0.18,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      left: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(left: 8, bottom: 16),
                          child: Text(
                            'Category',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),

                        _buildRoleItem(
                          'All',
                          state.allProductsCount,
                          isSelected: state.isAllSelected,
                          onTap: () => viewModel.selectAllBranchProducts(),
                        ),
                        ListView.builder(
                          shrinkWrap: true,
                          itemCount: state.branches.length,
                          itemBuilder: (context, index) {
                            final branch = state.branches[index];
                            return _buildRoleItem(
                              branch.branchName ?? '',
                              branch.branchCount.first.count ?? 0,
                              isSelected: state.selectedBranchIndex == index,
                              onTap:
                                  () => viewModel.selectBranch(
                                    index: index,
                                    branchId: branch.id ?? 0,
                                  ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showMessage({required String message, required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isError ? Colors.green.shade600 : Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Widget _buildProductCard(HardwareItem product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image container
                Container(
                  width: 180,
                  height: 180,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child:
                      product.productImages != null &&
                              product.productImages!.isNotEmpty
                          ? Stack(
                            children: [
                              MouseRegion(
                                cursor: SystemMouseCursors.grab,
                                child: PageView.builder(
                                  itemCount: product.productImages?.length,
                                  scrollBehavior: AppScrollBehavior(),
                                  onPageChanged: (index) {
                                    setState(() {
                                      _currentImageIndices[product.id ?? 0] =
                                          index;
                                    });
                                  },
                                  itemBuilder: (context, index) {
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        product
                                                .productImages?[index]
                                                .imageUrl ??
                                            '',
                                        fit: BoxFit.cover,
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Center(
                                            child: Icon(
                                              Icons.error_outline,
                                              size: 40,
                                              color: Colors.grey.shade400,
                                            ),
                                          );
                                        },
                                        loadingBuilder: (
                                          context,
                                          child,
                                          loadingProgress,
                                        ) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return Center(
                                            child: CircularProgressIndicator(
                                              value:
                                                  loadingProgress
                                                              .expectedTotalBytes !=
                                                          null
                                                      ? loadingProgress
                                                              .cumulativeBytesLoaded /
                                                          loadingProgress
                                                              .expectedTotalBytes!
                                                      : null,
                                            ),
                                          );
                                        },
                                      ),
                                    );
                                  },
                                ),
                              ),
                              // Image counter indicator
                              if (product.productImages!.length > 1)
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.transparent,
                                          Colors.black.withAlpha(
                                            (0.2 * 255).round(),
                                          ),
                                          Colors.black.withAlpha(
                                            (0.5 * 255).round(),
                                          ),
                                        ],
                                        stops: const [0.0, 0.5, 1.0],
                                      ),
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(8),
                                        bottomRight: Radius.circular(8),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children:
                                          product.productImages!.asMap().entries.map((
                                            entry,
                                          ) {
                                            final isActive =
                                                entry.key ==
                                                (_currentImageIndices[product
                                                            .id ??
                                                        0] ??
                                                    0);
                                            return AnimatedContainer(
                                              duration: const Duration(
                                                milliseconds: 200,
                                              ),
                                              width: isActive ? 8 : 6,
                                              height: isActive ? 8 : 6,
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 2,
                                                  ),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color:
                                                    isActive
                                                        ? Colors.white
                                                        : Colors.white
                                                            .withAlpha(
                                                              (0.5 * 255)
                                                                  .round(),
                                                            ),
                                                border: Border.all(
                                                  color: Colors.white.withAlpha(
                                                    (0.3 * 255).round(),
                                                  ),
                                                  width: 0.5,
                                                ),
                                                boxShadow:
                                                    isActive
                                                        ? [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withAlpha(
                                                                  (0.3 * 255)
                                                                      .round(),
                                                                ),
                                                            blurRadius: 2,
                                                            spreadRadius: 0,
                                                          ),
                                                        ]
                                                        : null,
                                              ),
                                            );
                                          }).toList(),
                                    ),
                                  ),
                                ),
                            ],
                          )
                          : Center(
                            child: Icon(
                              Icons.image,
                              size: 60,
                              color: Colors.grey.shade400,
                            ),
                          ),
                ),
                const SizedBox(width: 24),

                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Category
                      Text(
                        product.hardwareSubCategory?.hardwareCategory?.name ??
                            '',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),

                      // Product name
                      Text(
                        product.name ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Description
                      Text(
                        product.description ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),

                // Price section
                SizedBox(
                  width: 200,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Price
                      Text(
                        '\$${product.price ?? 0.0}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Edit and Delete buttons positioned at bottom right
            Positioned(
              bottom: 0,
              right: 0,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Edit button
                  OutlinedButton.icon(
                    onPressed: () => _editProduct(product),
                    icon: const Icon(Icons.edit_outlined, size: 16),
                    label: const Text('Edit'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      side: BorderSide(color: Colors.grey.shade300),
                      minimumSize: const Size(70, 32),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Delete button
                  OutlinedButton.icon(
                    onPressed: () => _showDeleteConfirmationDialog(product),
                    icon: const Icon(Icons.delete_outline, size: 16),
                    label: const Text('Delete'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      side: const BorderSide(color: Colors.red),
                      foregroundColor: Colors.red,
                      minimumSize: const Size(80, 32),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          'Inventory',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        // Search bar
        Container(
          width: 300,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              const Icon(Icons.search, color: Colors.grey, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search products',
                    border: InputBorder.none,
                    hintStyle: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.notifications_none, size: 24),
          onPressed: () {},
        ),
        IconButton(
          icon: const Icon(Icons.email_outlined, size: 24),
          onPressed: () {},
        ),
        const SizedBox(width: 8),
        // Profile
        Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey.shade300,
              child: const Icon(Icons.person, color: Colors.white),
            ),
            const SizedBox(width: 8),
            const Text(
              'Kenvin Harry',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleItem(
    String title,
    int count, {
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color:
            isSelected
                ? orangeColor.withAlpha((0.1 * 255).round())
                : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isSelected
                  ? orangeColor.withAlpha((0.3 * 255).round())
                  : Colors.grey.shade200,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: MyTypography.Regular.copyWith(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    color: isSelected ? orangeColor : Colors.black87,
                  ),
                ),
                Text(
                  '$count',
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build category items

  void _showAddProductBottomSheet(
    BuildContext context, {
    bool isEditing = false,
    int? productId,
  }) {
    // Load categories when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(inventoryViewModelProvider.notifier).getCategories();
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (BuildContext dialogContext) => Container(
            width: MediaQuery.of(dialogContext).size.width * 0.5,
            height: MediaQuery.of(dialogContext).size.height * 0.9,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Add New Product',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                // Form content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Branch',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Consumer(
                          builder: (context, ref, child) {
                            final inventoryState = ref.watch(
                              inventoryViewModelProvider,
                            );
                            final inventoryViewModel = ref.watch(
                              inventoryViewModelProvider.notifier,
                            );

                            if (inventoryState.isLoadingBranches) {
                              return Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: Row(
                                    children: [
                                      SizedBox(width: 12),
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        'Loading branches...',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return DropdownButtonFormField<int>(
                              value: inventoryState.selectedBranchId,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                hintText: 'Select a branch',
                              ),
                              items:
                                  inventoryState.branches
                                      .where((branch) => branch.id != null)
                                      .map((branch) {
                                        return DropdownMenuItem<int>(
                                          value: branch.id!,
                                          child: Text(
                                            '${branch.branchName} - ${branch.city ?? 'Unknown Branch}'}',
                                          ),
                                        );
                                      })
                                      .toList(),
                              onChanged: (int? newValue) {
                                inventoryViewModel.setSelectedBranch(newValue);
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                        // Category dropdown
                        const Text(
                          'Category',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Consumer(
                          builder: (context, ref, child) {
                            final inventoryState = ref.watch(
                              inventoryViewModelProvider,
                            );
                            final inventoryViewModel = ref.watch(
                              inventoryViewModelProvider.notifier,
                            );

                            if (inventoryState.isLoadingCategories) {
                              return Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: Row(
                                    children: [
                                      SizedBox(width: 12),
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        'Loading categories...',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return DropdownButtonFormField<int>(
                              value: inventoryState.selectedCategoryId,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                hintText: 'Select a category',
                              ),
                              items:
                                  inventoryState.categories
                                      .where((category) => category.id != null)
                                      .map((category) {
                                        return DropdownMenuItem<int>(
                                          value: category.id!,
                                          child: Text(
                                            category.name ?? 'Unknown Category',
                                          ),
                                        );
                                      })
                                      .toList(),
                              onChanged: (int? newValue) {
                                inventoryViewModel.setSelectedCategory(
                                  newValue,
                                );
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 16),

                        // Subcategory dropdown
                        const Text(
                          'Subcategory',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Consumer(
                          builder: (context, ref, child) {
                            final inventoryState = ref.watch(
                              inventoryViewModelProvider,
                            );
                            final inventoryViewModel = ref.watch(
                              inventoryViewModelProvider.notifier,
                            );

                            if (inventoryState.isLoadingSubCategories) {
                              return Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: Row(
                                    children: [
                                      SizedBox(width: 12),
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        'Loading subcategories...',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return DropdownButtonFormField<int>(
                              value:
                                  inventoryState.selectedSubCategoryObject?.id,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                hintText: 'Select a subcategory',
                              ),
                              items:
                                  inventoryState.subCategories
                                      .where(
                                        (subCategory) => subCategory.id != null,
                                      )
                                      .map((subCategory) {
                                        return DropdownMenuItem<int>(
                                          value: subCategory.id!,
                                          child: Text(
                                            subCategory.name ??
                                                'Unknown Subcategory',
                                          ),
                                        );
                                      })
                                      .toList(),
                              onChanged:
                                  inventoryState.selectedCategoryId != null
                                      ? (int? newValue) {
                                        if (newValue != null) {
                                          // Find the selected subcategory object
                                          final selectedSubCategory =
                                              inventoryState.subCategories
                                                  .firstWhere(
                                                    (subCategory) =>
                                                        subCategory.id ==
                                                        newValue,
                                                  );

                                          // Set the entire subcategory object
                                          inventoryViewModel
                                              .setSelectedSubCategoryObj(
                                                selectedSubCategory,
                                              );
                                        } else {
                                          // Clear selection if null
                                          inventoryViewModel
                                              .setSelectedSubCategory(null);
                                        }
                                      }
                                      : null,
                            );
                          },
                        ),
                        const SizedBox(height: 16),

                        // Product name
                        const Text(
                          'Product Name',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _productNameController,
                          onChanged: (value) {
                            ref
                                .read(inventoryViewModelProvider.notifier)
                                .updateProductName(value);
                          },
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            hintText: 'Enter product name',
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Description
                        const Text(
                          'Description',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _descriptionController,
                          onChanged: (value) {
                            ref
                                .read(inventoryViewModelProvider.notifier)
                                .updateProductDescription(value);
                          },
                          maxLines: 3,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            hintText: 'Enter product description',
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Price
                        const Text(
                          'Price',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _priceController,
                          onChanged: (value) {
                            ref
                                .read(inventoryViewModelProvider.notifier)
                                .updateProductPrice(value);
                          },
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            hintText: 'Enter price',
                            prefixText: '\$ ',
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Product images
                        const Text(
                          'Product Images (Maximum 4)',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                                childAspectRatio: 1,
                              ),
                          itemCount: 4,
                          itemBuilder: (context, index) {
                            return _buildImageSelector(index);
                          },
                        ),
                        const SizedBox(height: 32),

                        // Action buttons
                        Row(
                          children: [
                            Expanded(
                              child: SizedBox(
                                height: 50,
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _clearForm();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.grey.shade300,
                                    foregroundColor: Colors.grey.shade700,
                                    elevation: 0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: const Text(
                                    'Cancel',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Consumer(
                                builder: (context, ref, child) {
                                  final isSaving = ref.watch(
                                    inventoryViewModelProvider.select(
                                      (state) => state.isSaving,
                                    ),
                                  );

                                  return PrimaryButton(
                                    text:
                                        isSaving
                                            ? ''
                                            : (isEditing
                                                ? 'Update Product'
                                                : 'Add Product'),
                                    onPressed: () {
                                      if (!isSaving) {
                                        if (isEditing && productId != null) {
                                          _updateProduct(productId);
                                        } else {
                                          _addProduct();
                                        }
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildImageSelector(int index) {
    return Consumer(
      builder: (context, ref, child) {
        final inventoryState = ref.watch(inventoryViewModelProvider);
        final inventoryViewModel = ref.watch(
          inventoryViewModelProvider.notifier,
        );

        final hasImage =
            index < inventoryState.imageUrls.length &&
            inventoryState.imageUrls[index].isNotEmpty;
        final isUploading =
            index < inventoryState.imageUploadingStates.length &&
            inventoryState.imageUploadingStates[index];

        return GestureDetector(
          onTap: isUploading ? null : () => _selectImage(index),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade300,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.shade50,
            ),
            child:
                isUploading
                    ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Uploading...',
                            style: TextStyle(fontSize: 10, color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                    : hasImage
                    ? Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: NetworkImage(
                                  inventoryState.imageUrls[index],
                                ),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap:
                                () => inventoryViewModel.removeProductImage(
                                  index,
                                ),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                    : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_photo_alternate_outlined,
                          size: 32,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Add Image',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
          ),
        );
      },
    );
  }

  void _selectImage(int index) {
    final inventoryViewModel = ref.read(inventoryViewModelProvider.notifier);
    inventoryViewModel.uploadProductImage(index);
  }

  Future<void> _addProduct() async {
    final inventoryViewModel = ref.read(inventoryViewModelProvider.notifier);
    await inventoryViewModel.saveProduct();

    // Guard against widget being disposed
    if (!mounted) return;

    final state = ref.read(inventoryViewModelProvider);
    if (state.saveSuccess) {
      _clearForm();
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Product saved successfully')),
      );
    } else if (state.errorMessage != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save product: ${state.errorMessage}'),
        ),
      );
    }
  }

  // Add new method to populate form with product data

  // Add method to edit product
  Future<void> _editProduct(HardwareItem product) async {
    final viewModel = ref.read(inventoryViewModelProvider.notifier);

    // First, load categories and subcategories
    // await viewModel.getCategories();

    // Set text field values
    _productNameController.text = product.name ?? '';
    _descriptionController.text = product.description ?? '';
    _priceController.text = product.price?.toString() ?? '';

    // Set branch and wait for any state updates
    viewModel.setSelectedBranch(product.branchId);

    // Set category and wait for subcategories to load
    if (product.hardwareSubCategory?.hardwareCategory != null) {
      viewModel.setSelectedCategory(
        product.hardwareSubCategory!.hardwareCategory!.id,
      );

      // Wait for subcategories to load
      await Future.delayed(const Duration(milliseconds: 300));

      // Set subcategory
      if (product.hardwareSubCategory != null) {
        viewModel.setSelectedSubCategoryObj(
          HardwareSubCategoryResponse(
            id: product.hardwareSubCategory!.id,
            name: product.hardwareSubCategory!.name,
            hardwareCategoryId:
                product.hardwareSubCategory!.hardwareCategory?.id,
          ),
        );
      }
    }

    // Set product state values
    viewModel.updateProductName(product.name ?? '');
    viewModel.updateProductPrice('${product.price ?? 0}');
    viewModel.updateProductDescription(product.description ?? '');

    // Set images
    final imageUrls =
        product.productImages?.map((img) => img.imageUrl ?? '').toList() ?? [];
    viewModel.setImageUrls(imageUrls);

    if (!mounted) return;
    // Show bottom sheet with pre-populated data
    _showAddProductBottomSheet(context, isEditing: true, productId: product.id);
  }

  // Add this method to show filter dialog
  void _showFilterDialog(BuildContext context) {
    // Load categories when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(inventoryViewModelProvider.notifier).getCategories();
    });

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Filter Products'),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          content: SizedBox(
            width: 400,
            child: Consumer(
              builder: (context, ref, _) {
                final state = ref.watch(inventoryViewModelProvider);
                final inventoryViewModel = ref.watch(
                  inventoryViewModelProvider.notifier,
                );

                if (state.isLoadingCategories) {
                  return Container(
                    height: 56,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Row(
                        children: [
                          SizedBox(width: 12),
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Loading categories...',
                            style: TextStyle(color: Colors.grey, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return DropdownButtonFormField<int>(
                  value: state.selectedCategoryId,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    hintText: 'Select a category',
                  ),
                  items:
                      state.categories
                          .where((category) => category.id != null)
                          .map((category) {
                            return DropdownMenuItem<int>(
                              value: category.id!,
                              child: Text(category.name ?? 'Unknown Category'),
                            );
                          })
                          .toList(),
                  onChanged: (int? newValue) {
                    // inventoryViewModel.setSelectedCategory(newValue);
                    inventoryViewModel.setFilteredCategory(newValue ?? 0);
                    inventoryViewModel.filterProductByBranchCategoryId(
                      newValue ?? 0,
                    );
                  },
                );
              },
            ),
          ),
          actionsPadding: const EdgeInsets.all(16),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CancelButton(
                  text: 'Cancel',
                  onPressed: () => Navigator.pop(context),
                  width: 100,
                  height: 40,
                ),
                const SizedBox(width: 8),
                Consumer(
                  builder: (context, ref, _) {
                    return PrimaryButton(
                      text: 'Apply',
                      onPressed: () {
                        ref
                            .read(inventoryViewModelProvider.notifier)
                            .applyFilters();
                        Navigator.pop(context);
                      },
                      width: 100,
                      height: 40,
                    );
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Method to show bottom sheet for both add and edit
  // void _showAddProductBottomSheet(
  //   BuildContext context, {
  //   bool isEditing = false,
  //   int? productId,
  // }) {
  //   // Load categories when dialog opens
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     ref.read(inventoryViewModelProvider.notifier).getCategories();
  //   });

  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder:
  //         (BuildContext dialogContext) => Container(
  //           width: MediaQuery.of(dialogContext).size.width * 0.5,
  //           height: MediaQuery.of(dialogContext).size.height * 0.9,
  //           decoration: const BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.only(
  //               topLeft: Radius.circular(20),
  //               topRight: Radius.circular(20),
  //             ),
  //           ),
  //           child: Column(
  //             children: [
  //               // Header with dynamic title
  //               Container(
  //                 padding: const EdgeInsets.all(20),
  //                 decoration: BoxDecoration(
  //                   color: Colors.grey.shade50,
  //                   borderRadius: const BorderRadius.only(
  //                     topLeft: Radius.circular(20),
  //                     topRight: Radius.circular(20),
  //                   ),
  //                 ),
  //                 child: Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Text(
  //                       isEditing ? 'Edit Product' : 'Add New Product',
  //                       style: const TextStyle(
  //                         fontSize: 20,
  //                         fontWeight: FontWeight.bold,
  //                       ),
  //                     ),
  //                     IconButton(
  //                       onPressed: () {
  //                         Navigator.pop(context);
  //                         _clearForm();
  //                       },
  //                       icon: const Icon(Icons.close),
  //                     ),
  //                   ],
  //                 ),
  //               ),

  //               // Form content
  //               Expanded(
  //                 child: SingleChildScrollView(
  //                   padding: const EdgeInsets.all(20),
  //                   child: Column(
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       const Text(
  //                         'Branch',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       Consumer(
  //                         builder: (context, ref, child) {
  //                           final inventoryState = ref.watch(
  //                             inventoryViewModelProvider,
  //                           );
  //                           final inventoryViewModel = ref.watch(
  //                             inventoryViewModelProvider.notifier,
  //                           );

  //                           if (inventoryState.isLoadingBranches) {
  //                             return Container(
  //                               height: 56,
  //                               decoration: BoxDecoration(
  //                                 border: Border.all(
  //                                   color: Colors.grey.shade300,
  //                                 ),
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               child: const Center(
  //                                 child: Row(
  //                                   children: [
  //                                     SizedBox(width: 12),
  //                                     SizedBox(
  //                                       width: 16,
  //                                       height: 16,
  //                                       child: CircularProgressIndicator(
  //                                         strokeWidth: 2,
  //                                       ),
  //                                     ),
  //                                     SizedBox(width: 12),
  //                                     Text(
  //                                       'Loading branches...',
  //                                       style: TextStyle(
  //                                         color: Colors.grey,
  //                                         fontSize: 16,
  //                                       ),
  //                                     ),
  //                                   ],
  //                                 ),
  //                               ),
  //                             );
  //                           }

  //                           return DropdownButtonFormField<int>(
  //                             value: inventoryState.selectedBranchId,
  //                             decoration: InputDecoration(
  //                               border: OutlineInputBorder(
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               hintText: 'Select a branch',
  //                             ),
  //                             items:
  //                                 inventoryState.branches
  //                                     .where((branch) => branch.id != null)
  //                                     .map((branch) {
  //                                       return DropdownMenuItem<int>(
  //                                         value: branch.id!,
  //                                         child: Text(
  //                                           '${branch.branchName} - ${branch.city ?? 'Unknown Branch}'}',
  //                                         ),
  //                                       );
  //                                     })
  //                                     .toList(),
  //                             onChanged: (int? newValue) {
  //                               inventoryViewModel.setSelectedBranch(newValue);
  //                             },
  //                           );
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),
  //                       // Category dropdown
  //                       const Text(
  //                         'Category',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       Consumer(
  //                         builder: (context, ref, child) {
  //                           final inventoryState = ref.watch(
  //                             inventoryViewModelProvider,
  //                           );
  //                           final inventoryViewModel = ref.watch(
  //                             inventoryViewModelProvider.notifier,
  //                           );

  //                           if (inventoryState.isLoadingCategories) {
  //                             return Container(
  //                               height: 56,
  //                               decoration: BoxDecoration(
  //                                 border: Border.all(
  //                                   color: Colors.grey.shade300,
  //                                 ),
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               child: const Center(
  //                                 child: Row(
  //                                   children: [
  //                                     SizedBox(width: 12),
  //                                     SizedBox(
  //                                       width: 16,
  //                                       height: 16,
  //                                       child: CircularProgressIndicator(
  //                                         strokeWidth: 2,
  //                                       ),
  //                                     ),
  //                                     SizedBox(width: 12),
  //                                     Text(
  //                                       'Loading categories...',
  //                                       style: TextStyle(
  //                                         color: Colors.grey,
  //                                         fontSize: 16,
  //                                       ),
  //                                     ),
  //                                   ],
  //                                 ),
  //                               ),
  //                             );
  //                           }

  //                           return DropdownButtonFormField<int>(
  //                             value: inventoryState.selectedCategoryId,
  //                             decoration: InputDecoration(
  //                               border: OutlineInputBorder(
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               hintText: 'Select a category',
  //                             ),
  //                             items:
  //                                 inventoryState.categories
  //                                     .where((category) => category.id != null)
  //                                     .map((category) {
  //                                       return DropdownMenuItem<int>(
  //                                         value: category.id!,
  //                                         child: Text(
  //                                           category.name ?? 'Unknown Category',
  //                                         ),
  //                                       );
  //                                     })
  //                                     .toList(),
  //                             onChanged: (int? newValue) {
  //                               inventoryViewModel.setSelectedCategory(
  //                                 newValue,
  //                               );
  //                             },
  //                           );
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),

  //                       // Subcategory dropdown
  //                       const Text(
  //                         'Subcategory',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       Consumer(
  //                         builder: (context, ref, child) {
  //                           final inventoryState = ref.watch(
  //                             inventoryViewModelProvider,
  //                           );
  //                           final inventoryViewModel = ref.watch(
  //                             inventoryViewModelProvider.notifier,
  //                           );

  //                           if (inventoryState.isLoadingSubCategories) {
  //                             return Container(
  //                               height: 56,
  //                               decoration: BoxDecoration(
  //                                 border: Border.all(
  //                                   color: Colors.grey.shade300,
  //                                 ),
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               child: const Center(
  //                                 child: Row(
  //                                   children: [
  //                                     SizedBox(width: 12),
  //                                     SizedBox(
  //                                       width: 16,
  //                                       height: 16,
  //                                       child: CircularProgressIndicator(
  //                                         strokeWidth: 2,
  //                                       ),
  //                                     ),
  //                                     SizedBox(width: 12),
  //                                     Text(
  //                                       'Loading subcategories...',
  //                                       style: TextStyle(
  //                                         color: Colors.grey,
  //                                         fontSize: 16,
  //                                       ),
  //                                     ),
  //                                   ],
  //                                 ),
  //                               ),
  //                             );
  //                           }

  //                           return DropdownButtonFormField<int>(
  //                             value:
  //                                 inventoryState.selectedSubCategoryObject?.id,
  //                             decoration: InputDecoration(
  //                               border: OutlineInputBorder(
  //                                 borderRadius: BorderRadius.circular(8),
  //                               ),
  //                               hintText: 'Select a subcategory',
  //                             ),
  //                             items:
  //                                 inventoryState.subCategories
  //                                     .where(
  //                                       (subCategory) => subCategory.id != null,
  //                                     )
  //                                     .map((subCategory) {
  //                                       return DropdownMenuItem<int>(
  //                                         value: subCategory.id!,
  //                                         child: Text(
  //                                           subCategory.name ??
  //                                               'Unknown Subcategory',
  //                                         ),
  //                                       );
  //                                     })
  //                                     .toList(),
  //                             onChanged:
  //                                 inventoryState.selectedCategoryId != null
  //                                     ? (int? newValue) {
  //                                       if (newValue != null) {
  //                                         // Find the selected subcategory object
  //                                         final selectedSubCategory =
  //                                             inventoryState.subCategories
  //                                                 .firstWhere(
  //                                                   (subCategory) =>
  //                                                       subCategory.id ==
  //                                                       newValue,
  //                                                 );

  //                                         // Set the entire subcategory object
  //                                         inventoryViewModel
  //                                             .setSelectedSubCategoryObj(
  //                                               selectedSubCategory,
  //                                             );
  //                                       } else {
  //                                         // Clear selection if null
  //                                         inventoryViewModel
  //                                             .setSelectedSubCategory(null);
  //                                       }
  //                                     }
  //                                     : null,
  //                           );
  //                         },
  //                       ),
  //                       const SizedBox(height: 16),

  //                       // Product name
  //                       const Text(
  //                         'Product Name',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       TextFormField(
  //                         controller: _productNameController,
  //                         onChanged: (value) {
  //                           ref
  //                               .read(inventoryViewModelProvider.notifier)
  //                               .updateProductName(value);
  //                         },
  //                         decoration: InputDecoration(
  //                           border: OutlineInputBorder(
  //                             borderRadius: BorderRadius.circular(8),
  //                           ),
  //                           hintText: 'Enter product name',
  //                         ),
  //                       ),
  //                       const SizedBox(height: 16),

  //                       // Description
  //                       const Text(
  //                         'Description',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       TextFormField(
  //                         controller: _descriptionController,
  //                         onChanged: (value) {
  //                           ref
  //                               .read(inventoryViewModelProvider.notifier)
  //                               .updateProductDescription(value);
  //                         },
  //                         maxLines: 3,
  //                         decoration: InputDecoration(
  //                           border: OutlineInputBorder(
  //                             borderRadius: BorderRadius.circular(8),
  //                           ),
  //                           hintText: 'Enter product description',
  //                         ),
  //                       ),
  //                       const SizedBox(height: 16),

  //                       // Price
  //                       const Text(
  //                         'Price',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       TextFormField(
  //                         controller: _priceController,
  //                         onChanged: (value) {
  //                           ref
  //                               .read(inventoryViewModelProvider.notifier)
  //                               .updateProductPrice(value);
  //                         },
  //                         keyboardType: TextInputType.number,
  //                         decoration: InputDecoration(
  //                           border: OutlineInputBorder(
  //                             borderRadius: BorderRadius.circular(8),
  //                           ),
  //                           hintText: 'Enter price',
  //                           prefixText: '\$ ',
  //                         ),
  //                       ),
  //                       const SizedBox(height: 16),

  //                       // Product images
  //                       const Text(
  //                         'Product Images (Maximum 4)',
  //                         style: TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       const SizedBox(height: 8),
  //                       GridView.builder(
  //                         shrinkWrap: true,
  //                         physics: const NeverScrollableScrollPhysics(),
  //                         gridDelegate:
  //                             const SliverGridDelegateWithFixedCrossAxisCount(
  //                               crossAxisCount: 4,
  //                               crossAxisSpacing: 12,
  //                               mainAxisSpacing: 12,
  //                               childAspectRatio: 1,
  //                             ),
  //                         itemCount: 4,
  //                         itemBuilder: (context, index) {
  //                           return _buildImageSelector(index);
  //                         },
  //                       ),
  //                       const SizedBox(height: 32),

  //                       // Action buttons
  //                       Row(
  //                         children: [
  //                           Expanded(
  //                             child: SizedBox(
  //                               height: 50,
  //                               child: ElevatedButton(
  //                                 onPressed: () {
  //                                   Navigator.pop(context);
  //                                   _clearForm();
  //                                 },
  //                                 style: ElevatedButton.styleFrom(
  //                                   backgroundColor: Colors.grey.shade300,
  //                                   foregroundColor: Colors.grey.shade700,
  //                                   elevation: 0,
  //                                   shape: RoundedRectangleBorder(
  //                                     borderRadius: BorderRadius.circular(8),
  //                                   ),
  //                                 ),
  //                                 child: const Text(
  //                                   'Cancel',
  //                                   style: TextStyle(
  //                                     fontWeight: FontWeight.w600,
  //                                   ),
  //                                 ),
  //                               ),
  //                             ),
  //                           ),
  //                           const SizedBox(width: 16),
  //                           Expanded(
  //                             child: Consumer(
  //                               builder: (context, ref, child) {
  //                                 final isSaving = ref.watch(
  //                                   inventoryViewModelProvider.select(
  //                                     (state) => state.isSaving,
  //                                   ),
  //                                 );

  //                                 return PrimaryButton(
  //                                   text:
  //                                       isSaving
  //                                           ? ''
  //                                           : (isEditing
  //                                               ? 'Update Product'
  //                                               : 'Add Product'),
  //                                   onPressed: () {
  //                                     if (!isSaving) {
  //                                       if (isEditing && productId != null) {
  //                                         _updateProduct(productId);
  //                                       } else {
  //                                         _addProduct();
  //                                       }
  //                                     }
  //                                   },
  //                                 );
  //                               },
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //   );
  // }

  // Add method to handle product update
  Future<void> _updateProduct(int productId) async {
    final inventoryViewModel = ref.read(inventoryViewModelProvider.notifier);

    // Call editProduct with the product ID
    await inventoryViewModel.editProduct(productId);

    if (!mounted) return;

    final state = ref.read(inventoryViewModelProvider);
    if (state.saveSuccess) {
      _clearForm();
      Navigator.pop(context);

      // Refresh the products list
      if (state.isAllSelected) {
        await inventoryViewModel.selectAllBranchProducts();
      } else if (state.selectedBranchId != null) {
        await inventoryViewModel.selectBranch(
          index: state.selectedBranchIndex,
          branchId: state.selectedBranchId,
        );
      }

      _showMessage(message: 'Product updated successfully', isError: false);
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Product updated successfully')),
      // );
    } else if (state.errorMessage != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update product: ${state.errorMessage}'),
        ),
      );
    }
  }

  void _clearForm() {
    final inventoryViewModel = ref.read(inventoryViewModelProvider.notifier);
    setState(() {
      inventoryViewModel.clearSelections();
      _productNameController.clear();
      _descriptionController.clear();
      _priceController.clear();
    });
  }

  // Add this new method to show delete confirmation dialog
  Future<void> _showDeleteConfirmationDialog(HardwareItem product) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final navigator = Navigator.of(context);
        return Consumer(
          builder: (context, ref, child) {
            final isDeleting = ref.watch(
              inventoryViewModelProvider.select((state) => state.isDeleting),
            );

            return AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Confirm Delete',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade200),
                ],
              ),
              contentPadding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
              content: SingleChildScrollView(
                child: ListBody(
                  children: <Widget>[
                    Text('Are you sure you want to delete "${product.name}"?'),
                    const SizedBox(height: 8),
                    Text(
                      'This action cannot be undone.',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              actionsPadding: EdgeInsets.zero,
              actions: <Widget>[
                Column(
                  children: [
                    const SizedBox(height: 16),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: Colors.grey.shade200,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed:
                                isDeleting
                                    ? null
                                    : () => Navigator.of(context).pop(),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color:
                                    isDeleting
                                        ? Colors.grey.shade400
                                        : Colors.grey.shade600,
                              ),
                            ),
                          ),
                          TextButton(
                            onPressed:
                                isDeleting
                                    ? null
                                    : () async {
                                      try {
                                        await ref
                                            .read(
                                              inventoryViewModelProvider
                                                  .notifier,
                                            )
                                            .deleteProduct(product.id ?? 0);
                                        if (mounted) {
                                          _showMessage(
                                            message:
                                                'Product deleted successfully',
                                            isError: false,
                                          );
                                          navigator.pop();
                                          // Navigator.of(context).pop();
                                        }
                                      } catch (e) {
                                        if (mounted) {
                                          _showMessage(
                                            message:
                                                'Failed to delete product: $e',
                                            isError: true,
                                          );
                                          // ScaffoldMessenger.of(
                                          //   context,
                                          // ).showSnackBar(
                                          //   SnackBar(
                                          //     content: Text(
                                          //       'Failed to delete product: $e',
                                          //     ),
                                          //   ),
                                          // );
                                        }
                                      }
                                    },
                            child: Row(
                              children: [
                                if (isDeleting) ...[
                                  const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.red,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                ],
                                Text(
                                  isDeleting ? 'Deleting...' : 'Delete',
                                  style: TextStyle(
                                    color:
                                        isDeleting
                                            ? Colors.red.shade300
                                            : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }
}
