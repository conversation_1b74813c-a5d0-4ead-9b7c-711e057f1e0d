import 'package:build_mate/data/dto/service_icons_response.dart';
import 'package:build_mate/presentation/components/loader/grid_shimmer_loader.dart';
import 'package:build_mate/presentation/components/loader/text_shimmer_loader.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/user/home_view_model.dart';
import 'package:build_mate/presentation/view_models/job/post_job_view_model.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../screens/home/<USER>/screens/all_service_categories_screen.dart';

class ServiceCategoriesSection extends ConsumerWidget {
  const ServiceCategoriesSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final viewModel = ref.watch(homeViewModelProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            state.isLoadingServices
                ? const TextShimmerLoader(height: 20, width: 100)
                : Text(
                  'Services',
                  style: MyTypography.SemiBold.copyWith(
                    fontSize: 18,
                    color: customColors.textPrimaryColor,
                  ),
                ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AllServiceCategoriesScreen(),
                  ),
                );
              },
              child:
                  state.isLoadingServices
                      ? const TextShimmerLoader(height: 20, width: 50)
                      : Text(
                        'More',
                        style: MyTypography.Medium.copyWith(
                          color: orangeColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),
          ],
        ),
        const SizedBox(height: 2), // Further reduced from 4 to 2
        // Use the new GridShimmerLoader component
        if (state.isLoadingServices)
          const GridShimmerLoader(
            itemCount: 6,
            crossAxisCount: 3,
            childAspectRatio: 0.85,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          )
        else if (state.services.isNotEmpty)
          GridView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            padding: EdgeInsets.zero,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.85,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: state.services.length > 6 ? 6 : state.services.length,
            itemBuilder: (context, index) {
              final service = state.services[index];
              return _buildServiceItem(
                context,
                ref,
                service,
                index,
                state.services.length,
              );
            },
          )
        else
          SizedBox(
            height: 130,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'No services available',
                    style: MyTypography.Medium.copyWith(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.6,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => viewModel.fetchServices(),
                    child: Text(
                      'Refresh',
                      style: MyTypography.Medium.copyWith(color: orangeColor),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildServiceItem(
    BuildContext context,
    WidgetRef ref,
    ServiceIconsResponse service,
    int index,
    int totalItems,
  ) {
    final customColors = ref.watch(customColorsProvider);

    return GestureDetector(
      onTap: () {
        // Update the PostJobViewModel with the selected service
        final postJobViewModel = ref.read(postJobViewModelProvider.notifier);
        postJobViewModel.setSelectedServiceId(
          service.id ?? 0,
          service.name ?? 'Unknown Service',
        );

        // Navigate to post job screen
        context.pushNamed(RouteConstants.POST_JOB_SCREEN);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: customColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                color: customColors.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(
                            context,
                          ).shadowColor.withAlpha((0.05 * 255).round()),
                          blurRadius: 8,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child:
                        service.icon != null && service.icon!.isNotEmpty
                            ? service.icon!.toLowerCase().endsWith('.svg')
                                ? SvgPicture.asset(
                                  service.icon!,
                                  colorFilter: ColorFilter.mode(
                                    orangeColor,
                                    BlendMode.srcIn,
                                  ),
                                )
                                : Image.asset(
                                  service.icon!,
                                  errorBuilder:
                                      (context, error, stackTrace) => Icon(
                                        Icons.home_repair_service,
                                        color: orangeColor,
                                      ),
                                )
                            : Icon(
                              Icons.home_repair_service,
                              color: orangeColor,
                            ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      service.name ?? 'Unknown Service',
                      style: MyTypography.Medium.copyWith(
                        fontSize: 12,
                        color: customColors.textPrimaryColor,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
