// ignore_for_file: unused_import

import 'package:build_mate/presentation/view_models/job/post_job_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:go_router/go_router.dart';

class PopularServicesSection extends ConsumerWidget {
  const PopularServicesSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<Map<String, dynamic>> services = [
      {
        'title': 'Borehole Drilling',
        'image': 'assets/images/borehole_drilling.png',
      },
      {'title': 'Machinery', 'image': 'assets/images/machinery.jpg'},
      {'title': 'Bricks', 'image': 'assets/images/bricks.jpg'},
      {'title': 'Quarry & Sand', 'image': 'assets/images/quarry_sand.jpg'},
    ];

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'On-Demand Services',
              style: MyTypography.SemiBold.copyWith(fontSize: 18),
            ),
            TextButton(
              onPressed: () {},
              child: Text(
                '',
                style: MyTypography.Medium.copyWith(color: orangeColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16), // Further reduced from 8 to 4
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.0,
          padding: EdgeInsets.zero, // Add this to remove any default padding
          children:
              services.map((service) {
                return GestureDetector(
                  onTap: () {
                    // Handle service tap
                    // Print position clicked

                    switch (service['title']) {
                      case 'Borehole Drilling':
                        final postJobViewModel = ref.read(
                          postJobViewModelProvider.notifier,
                        );
                        postJobViewModel.setSelectedServiceId(
                          24,
                          service['title'],
                        );
                        context.pushNamed(
                          RouteConstants.ON_DEMAND_SERVICE_SCREEN,
                        );
                        break;
                      case 'Machinery':
                        final postJobViewModel = ref.read(
                          postJobViewModelProvider.notifier,
                        );
                        postJobViewModel.setSelectedServiceId(
                          26,
                          service['title'],
                        );
                        context.pushNamed(RouteConstants.POST_JOB_SCREEN);
                        break;
                      case 'Bricks':
                        final postJobViewModel = ref.read(
                          postJobViewModelProvider.notifier,
                        );
                        postJobViewModel.setSelectedServiceId(
                          27,
                          service['title'],
                        );
                        context.pushNamed(RouteConstants.POST_JOB_SCREEN);
                        break;
                      case 'Quarry & Sand':
                        final postJobViewModel = ref.read(
                          postJobViewModelProvider.notifier,
                        );
                        postJobViewModel.setSelectedServiceId(
                          28,
                          service['title'],
                        );
                        context.pushNamed(RouteConstants.POST_JOB_SCREEN);
                        break;
                      default:
                    }

                    if (kDebugMode) {
                      print('Tapped on ${service['title']}');
                    }
                    //                 final postJobViewModel = ref.read(postJobViewModelProvider.notifier);
                    // postJobViewModel.setSelectedServiceId(
                    //   service.id ?? 0,
                    //   service.name ?? 'Unknown Service',
                    // );
                    // Navigate to post job screen
                    // context.pushNamed(RouteConstants.POST_JOB_SCREEN);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha((0.1 * 255).round()),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          // Background image
                          Image.asset(service['image'], fit: BoxFit.cover),
                          // Subtle overall tint
                          Container(
                            color: Colors.black.withAlpha(
                              (0.2 * 255).round(),
                            ), // Subtle dark tint over the entire image
                          ),
                          // Gradient overlay for better text visibility
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withAlpha(
                                    (0.85 * 255).round(),
                                  ), // Increased from 0.7 to 0.85 for darker text area
                                ],
                                stops: const [0.4, 1.0],
                              ),
                            ),
                          ),
                          // Service title
                          Positioned(
                            bottom: 12,
                            left: 12,
                            right: 12,
                            child: Text(
                              service['title'],
                              style: MyTypography.SemiBold.copyWith(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}
