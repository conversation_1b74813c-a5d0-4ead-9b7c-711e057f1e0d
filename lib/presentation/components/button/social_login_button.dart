import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class SocialLoginButton extends StatelessWidget {
  final String text;
  final String iconPath;
  final Color backgroundColor;
  final Color textColor;
  final VoidCallback onPressed;
  final double height;
  final double? width;
  final double borderWidth;
  final Color borderColor;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final double iconSize;
  final double iconSpacing;
  final double fontSize;

  const SocialLoginButton({
    super.key,
    required this.text,
    required this.iconPath,
    required this.onPressed,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.height = 50,
    this.width,
    this.borderWidth = 0.5,
    this.borderColor = Colors.grey,
    this.borderRadius = 8,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
    this.iconSize = 24,
    this.iconSpacing = 12,
    this.fontSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: BorderSide(color: borderColor, width: borderWidth),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(iconPath, width: iconSize, height: iconSize),
            SizedBox(width: iconSpacing),
            Text(
              text,
              style: MyTypography.Medium.copyWith(
                fontSize: fontSize,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Factory constructors for common social platforms
  factory SocialLoginButton.google({
    required VoidCallback onPressed,
    String text = 'Log in with Google',
    double? width,
    double height = 50,
    BuildContext? context,
  }) {
    return SocialLoginButton(
      text: text,
      iconPath: 'assets/images/google_logo.png',
      backgroundColor:
          context != null ? Theme.of(context).cardColor : Colors.white,
      textColor:
          context != null
              ? Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black87
              : Colors.black87,
      borderColor:
          context != null ? Theme.of(context).dividerColor : Colors.grey,
      onPressed: onPressed,
      width: width,
      height: height,
    );
  }

  factory SocialLoginButton.apple({
    required VoidCallback onPressed,
    String text = 'Log in with Apple',
    double? width,
    double height = 50,
    BuildContext? context,
  }) {
    return SocialLoginButton(
      text: text,
      iconPath: 'assets/images/apple_logo.png',
      backgroundColor:
          context != null ? Theme.of(context).cardColor : Colors.white,
      textColor:
          context != null
              ? Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black87
              : Colors.black87,
      borderColor:
          context != null ? Theme.of(context).dividerColor : Colors.grey,
      onPressed: onPressed,
      width: width,
      height: height,
    );
  }

  factory SocialLoginButton.facebook({
    required VoidCallback onPressed,
    String text = 'Log in with Facebook',
    double? width,
    double height = 50,
    BuildContext? context,
  }) {
    return SocialLoginButton(
      text: text,
      iconPath: 'assets/images/facebook_logo.png',
      backgroundColor:
          context != null ? Theme.of(context).cardColor : Colors.white,
      textColor:
          context != null
              ? Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black87
              : Colors.black87,
      borderColor:
          context != null ? Theme.of(context).dividerColor : Colors.grey,
      onPressed: onPressed,
      width: width,
      height: height,
    );
  }
}
