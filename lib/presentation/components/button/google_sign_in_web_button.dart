import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class GoogleSignInWebButton extends ConsumerStatefulWidget {
  final Function() onSignInStarted;
  final double width;
  final double height;

  const GoogleSignInWebButton({
    super.key,
    required this.onSignInStarted,
    this.width = double.infinity,
    this.height = 50,
  });

  @override
  ConsumerState<GoogleSignInWebButton> createState() => _GoogleSignInWebButtonState();
}

class _GoogleSignInWebButtonState extends ConsumerState<GoogleSignInWebButton> {
  bool _isLoading = false;
  final _supabase = Supabase.instance.client;

  @override
  void initState() {
    super.initState();
    
    // Listen for auth state changes
    _supabase.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;
      
      if (event == AuthChangeEvent.signedIn && session != null) {
        if (kDebugMode) {
          print('User signed in: ${session.user.id}');
        }
        
        // Navigate to setup company screen
        ref.read(goRouterProvider).goNamed(RouteConstants.SETUP_COMPANY_SCREEN);
      }
    });
  }

  Future<void> _handleSignIn() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      if (kDebugMode) {
        print('Starting Supabase Google OAuth flow directly');
      }
      
      // Call the callback to notify the parent that sign-in has started
      widget.onSignInStarted();
      
      // Use Supabase's OAuth flow directly
      final redirectUrl = '${Uri.base.origin}/auth/callback';
      
      if (kDebugMode) {
        print('Redirect URL: $redirectUrl');
      }
      
      await _supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: redirectUrl,
      );
      
      if (kDebugMode) {
        print('OAuth flow initiated, page will redirect');
      }
      
      // The page will redirect to Google and then back to the callback URL
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign-In Error: $error');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _handleSignIn,
        icon: _isLoading 
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Image.asset(
                'assets/images/google_logo.png',
                height: 24,
                width: 24,
                errorBuilder: (context, error, stackTrace) => 
                    const Icon(Icons.g_mobiledata, size: 24),
              ),
        label: Text(_isLoading ? 'Signing in...' : 'Sign in with Google'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
            side: const BorderSide(color: Colors.grey, width: 1),
          ),
        ),
      ),
    );
  }
}
