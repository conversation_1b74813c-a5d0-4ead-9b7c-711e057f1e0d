import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class TextShimmerLoader extends StatelessWidget {
  final double height;
  final double width;
  final double borderRadius;
  
  const TextShimmerLoader({
    super.key,
    required this.height,
    required this.width,
    this.borderRadius = 4,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}