import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';

class ServiceWorkerCard extends StatelessWidget {
  final String name;
  final String profession;
  final String location;
  final double rating;
  final int totalRatings;
  final String imageUrl;
  final int activeOffers;
  final double price;
  final List<String> certifications;
  final VoidCallback? onTap;

  const ServiceWorkerCard({
    super.key,
    required this.name,
    required this.profession,
    required this.location,
    required this.rating,
    required this.totalRatings,
    required this.imageUrl,
    required this.activeOffers,
    required this.price,
    this.certifications = const [],
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha((0.1 * 255).round()),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Image
                CircleAvatar(radius: 24, backgroundImage: AssetImage(imageUrl)),
                const SizedBox(width: 12),
                // Name and Certifications
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: MyTypography.SemiBold.copyWith(fontSize: 16),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children:
                            certifications
                                .map(
                                  (cert) => Padding(
                                    padding: const EdgeInsets.only(right: 4),
                                    child: Icon(
                                      Icons.verified,
                                      size: 16,
                                      color: Colors.blue,
                                    ),
                                  ),
                                )
                                .toList(),
                      ),
                    ],
                  ),
                ),
                // Save Button
                IconButton(
                  icon: const Icon(Icons.bookmark_outline),
                  onPressed: () {},
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Profession and Location
            Row(
              children: [
                Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  profession,
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  location,
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Rating
            Row(
              children: [
                const Icon(Icons.star, size: 16, color: Colors.amber),
                const SizedBox(width: 4),
                Text(
                  '$rating ($totalRatings+ Rating)',
                  style: MyTypography.Medium.copyWith(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Active Offers and Price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active · $activeOffers offer',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 14,
                    color: orangeColor,
                  ),
                ),
                Text(
                  '\$${price.toStringAsFixed(2)}',
                  style: MyTypography.Bold.copyWith(fontSize: 16),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
