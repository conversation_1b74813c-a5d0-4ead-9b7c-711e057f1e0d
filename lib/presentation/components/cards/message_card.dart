import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MessageCard extends ConsumerWidget {
  final String senderName;
  final String message;
  final String time;
  final String avatarUrl;
  final bool isUnread;
  final VoidCallback onTap;

  const MessageCard({
    super.key,
    required this.senderName,
    required this.message,
    required this.time,
    required this.avatarUrl,
    this.isUnread = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final brightness = MediaQuery.of(context).platformBrightness;

    // Determine if we're in dark mode
    final isDarkMode =
        themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system && brightness == Brightness.dark);

    if (kDebugMode) {
      print('Building MessageCard for $senderName');
      print('Avatar URL: $avatarUrl');
      print('Is network image: ${avatarUrl.startsWith('http')}');
    }

    // Enhanced theme-aware colors for better contrast
    final cardBackgroundColor =
        isDarkMode
            ? customColors.surfaceVariant.withValues(
              alpha: 0.8,
            ) // More opaque for better contrast
            : Colors.white;

    final cardBorderColor =
        isDarkMode
            ? customColors.textPrimaryColor.withValues(
              alpha: 0.15,
            ) // Slightly more visible border
            : Colors.transparent;

    final cardShadowColor =
        isDarkMode
            ? Colors.black.withValues(alpha: 0.4) // Stronger shadow for depth
            : Colors.black.withValues(alpha: 0.05);

    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: 4,
      ), // Add spacing between cards
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(
            16,
          ), // Slightly larger radius for modern look
          splashColor:
              isDarkMode
                  ? customColors.textPrimaryColor.withValues(alpha: 0.1)
                  : darkBlueColor.withValues(alpha: 0.1),
          highlightColor:
              isDarkMode
                  ? customColors.textPrimaryColor.withValues(alpha: 0.05)
                  : darkBlueColor.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.all(
              16,
            ), // Increased padding for better touch targets
            decoration: BoxDecoration(
              color: cardBackgroundColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: cardShadowColor,
                  blurRadius: isDarkMode ? 8 : 10,
                  offset: const Offset(0, 2),
                  spreadRadius: isDarkMode ? 1 : 0,
                ),
              ],
              border: Border.all(
                color: cardBorderColor,
                width: isDarkMode ? 1 : 0,
              ),
              // Add subtle gradient for depth in dark mode
              gradient:
                  isDarkMode
                      ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          customColors.surfaceVariant.withValues(alpha: 0.9),
                          customColors.surfaceVariant.withValues(alpha: 0.7),
                        ],
                      )
                      : null,
            ),
            child: Row(
              children: [
                // Enhanced Avatar with better contrast
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 26, // Slightly larger for better visual hierarchy
                    backgroundImage:
                        avatarUrl.startsWith('http')
                            ? NetworkImage(avatarUrl) as ImageProvider
                            : AssetImage(avatarUrl),
                    backgroundColor:
                        isDarkMode
                            ? customColors.surfaceVariant.withValues(alpha: 0.6)
                            : Colors.grey[200],
                    // Add a subtle border for better definition
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color:
                              isDarkMode
                                  ? customColors.textPrimaryColor.withValues(
                                    alpha: 0.1,
                                  )
                                  : Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16), // Increased spacing
                // Enhanced Message Content with better typography
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        senderName,
                        style: MyTypography.SemiBold.copyWith(
                          fontSize: 17, // Slightly larger for better hierarchy
                          color: customColors.textPrimaryColor,
                          letterSpacing: 0.2, // Better letter spacing
                        ),
                      ),
                      const SizedBox(height: 6), // Increased spacing
                      Text(
                        message,
                        style: MyTypography.Regular.copyWith(
                          fontSize:
                              15, // Slightly larger for better readability
                          color: customColors.textPrimaryColor.withValues(
                            alpha:
                                isDarkMode
                                    ? 0.8
                                    : 0.7, // Better contrast in dark mode
                          ),
                          height: 1.3, // Better line height
                          letterSpacing: 0.1,
                        ),
                        maxLines: 2, // Allow 2 lines for better content preview
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12), // Increased spacing
                // Enhanced Time and Status indicators
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      time,
                      style: MyTypography.Medium.copyWith(
                        // Changed to Medium for better visibility
                        fontSize: 13, // Slightly larger
                        color: customColors.textPrimaryColor.withValues(
                          alpha:
                              isDarkMode
                                  ? 0.7
                                  : 0.6, // Better contrast in dark mode
                        ),
                        letterSpacing: 0.1,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (isUnread)
                      Container(
                        width: 10, // Slightly larger
                        height: 10,
                        decoration: BoxDecoration(
                          color: orangeColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: orangeColor.withValues(
                                alpha: isDarkMode ? 0.5 : 0.3,
                              ),
                              blurRadius: isDarkMode ? 6 : 4,
                              spreadRadius: isDarkMode ? 2 : 1,
                            ),
                          ],
                          // Add a subtle border for better definition in dark mode
                          border:
                              isDarkMode
                                  ? Border.all(
                                    color: orangeColor.withValues(alpha: 0.8),
                                    width: 1,
                                  )
                                  : null,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
