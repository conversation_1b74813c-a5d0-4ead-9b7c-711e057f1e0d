import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class CustomDropdown<T> extends StatelessWidget {
  final T? value;
  final List<T> items;
  final String hint;
  final Function(T?) onChanged;
  final String Function(T) itemToString;

  const CustomDropdown({
    super.key,
    required this.value,
    required this.items,
    required this.hint,
    required this.onChanged,
    required this.itemToString,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          isExpanded: true,
          hint: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              hint,
              style: MyTypography.Regular.copyWith(color: Colors.grey[600]),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          items:
              items.map((T item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: Text(itemToString(item), style: MyTypography.Regular),
                );
              }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }
}

//Usage
// class Category {
//   final String id;
//   final String name;
  
//   Category({required this.id, required this.name});
// }

// CustomDropdown<Category>(
//   value: selectedCategory,
//   items: categories,
//   hint: 'Select category',
//   onChanged: (Category? newValue) {
//     setState(() {
//       selectedCategory = newValue;
//     });
//   },
//   itemToString: (Category item) => item.name,
// ),