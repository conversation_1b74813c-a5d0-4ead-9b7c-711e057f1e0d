<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Build Mate</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>build_mate</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select job images</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to take job photos</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs access to microphone when used</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>BuildMate needs access to location to show nearby jobs and services.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>BuildMate needs access to location to show nearby jobs and services.</string>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location when open to show nearby shops.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location when in the background to show nearby shops.</string>
	<key>com.google.android.geo.API_KEY</key>
	<string>AIzaSyCD54HcF47akr02ra7nSq8wuBfmxs0d8rw</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location when open to show nearby shops.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location when in the background to show nearby shops.</string>
</dict>
</plist>
