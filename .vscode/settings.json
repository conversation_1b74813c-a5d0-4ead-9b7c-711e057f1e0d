{"editor.quickSuggestions": {"comments": "on", "strings": "on", "other": "on"}, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": true, "editor.suggest.snippetsPreventQuickSuggestions": true, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "matchingDocuments"}, "editor.inlineSuggest.enabled": true, "editor.suggestSelection": "first", "dart.previewFlutterUiGuides": true, "dart.openDevTools": "flutter", "dart.debugExternalPackageLibraries": true, "dart.debugSdkLibraries": true}